<?php
// created: 2025-02-26 15:19:18
$G<PERSON><PERSON><PERSON><PERSON>["dictionary"]["Employee"] = array (
  'table' => 'users',
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
    ),
    'user_name' => 
    array (
      'name' => 'user_name',
      'vname' => 'LBL_USER_NAME',
      'type' => 'user_name',
      'dbType' => 'varchar',
      'len' => '100',
      'importable' => 'required',
    ),
    'user_hash' => 
    array (
      'name' => 'user_hash',
      'vname' => 'LBL_USER_HASH',
      'type' => 'varchar',
      'len' => '32',
      'reportable' => false,
      'importable' => 'false',
    ),
    'system_generated_password' => 
    array (
      'name' => 'system_generated_password',
      'vname' => 'LBL_SYSTEM_GENERATED_PASSWORD',
      'type' => 'bool',
      'required' => true,
      'reportable' => false,
      'massupdate' => false,
      'default' => '0',
    ),
    'pwd_last_changed' => 
    array (
      'name' => 'pwd_last_changed',
      'vname' => 'LBL_PSW_MODIFIED',
      'type' => 'datetime',
      'massupdate' => false,
    ),
    'authenticate_id' => 
    array (
      'name' => 'authenticate_id',
      'vname' => 'LBL_AUTHENTICATE_ID',
      'type' => 'varchar',
      'len' => '100',
      'reportable' => false,
      'importable' => 'false',
    ),
    'incomcrm_login' => 
    array (
      'name' => 'incomcrm_login',
      'vname' => 'LBL_INCOMCRM_LOGIN',
      'type' => 'bool',
      'default' => '1',
      'reportable' => false,
      'massupdate' => false,
      'importable' => false,
      'display_default' => '0',
    ),
    'first_name' => 
    array (
      'name' => 'first_name',
      'vname' => 'LBL_FIRST_NAME',
      'dbType' => 'varchar',
      'type' => 'name',
      'len' => '50',
    ),
    'last_name' => 
    array (
      'name' => 'last_name',
      'vname' => 'LBL_LAST_NAME',
      'dbType' => 'varchar',
      'type' => 'name',
      'len' => '100',
      'importable' => 'required',
    ),
    'full_name' => 
    array (
      'name' => 'full_name',
      'rname' => 'full_name',
      'vname' => 'LBL_NAME',
      'type' => 'name',
      'fields' => 
      array (
        0 => 'first_name',
        1 => 'last_name',
      ),
      'source' => 'non-db',
      'sort_on' => 'last_name',
      'sort_on2' => 'first_name',
      'db_concat_fields' => 
      array (
        0 => 'first_name',
        1 => 'last_name',
      ),
      'len' => '510',
    ),
    'name' => 
    array (
      'name' => 'name',
      'rname' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'varchar',
      'source' => 'non-db',
      'len' => '510',
      'db_concat_fields' => 
      array (
        0 => 'first_name',
        1 => 'last_name',
      ),
      'importable' => 'false',
    ),
    'reports_to_id' => 
    array (
      'name' => 'reports_to_id',
      'vname' => 'LBL_REPORTS_TO_ID',
      'type' => 'id',
    ),
    'reports_to_name' => 
    array (
      'name' => 'reports_to_name',
      'vname' => 'LBL_REPORTS_TO_NAME',
      'type' => 'relate',
      'reportable' => false,
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'reports_to_id',
      'module' => 'Users',
      'duplicate_merge' => 'disabled',
    ),
    'is_admin' => 
    array (
      'name' => 'is_admin',
      'vname' => 'LBL_IS_ADMIN',
      'type' => 'bool',
      'default' => '0',
      'massupdate' => false,
    ),
    'external_auth_only' => 
    array (
      'name' => 'external_auth_only',
      'vname' => 'LBL_EXT_AUTHENTICATE',
      'type' => 'bool',
      'reportable' => false,
      'massupdate' => false,
      'default' => '0',
    ),
    'receive_notifications' => 
    array (
      'name' => 'receive_notifications',
      'vname' => 'LBL_RECEIVE_NOTIFICATIONS',
      'type' => 'bool',
      'default' => '1',
      'massupdate' => false,
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_BY_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_BY',
      'type' => 'varchar',
      'source' => 'non-db',
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_ASSIGNED_TO',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'type' => 'varchar',
      'source' => 'non-db',
      'importable' => 'false',
    ),
    'title' => 
    array (
      'name' => 'title',
      'vname' => 'LBL_TITLE',
      'type' => 'varchar',
      'len' => '50',
    ),
    'department' => 
    array (
      'name' => 'department',
      'vname' => 'LBL_DEPARTMENT',
      'type' => 'enum',
      'options' => 'all_department_dom',
      'dbType' => 'id',
      'massupdate' => false,
    ),
    'phone_home' => 
    array (
      'name' => 'phone_home',
      'vname' => 'LBL_HOME_PHONE',
      'type' => 'varchar',
      'len' => '50',
    ),
    'phone_mobile' => 
    array (
      'name' => 'phone_mobile',
      'vname' => 'LBL_MOBILE_PHONE',
      'type' => 'varchar',
      'len' => '50',
    ),
    'phone_work' => 
    array (
      'name' => 'phone_work',
      'vname' => 'LBL_WORK_PHONE',
      'type' => 'varchar',
      'len' => '50',
    ),
    'phone_other' => 
    array (
      'name' => 'phone_other',
      'vname' => 'LBL_OTHER_PHONE',
      'type' => 'varchar',
      'len' => '50',
    ),
    'phone_fax' => 
    array (
      'name' => 'phone_fax',
      'vname' => 'LBL_FAX_PHONE',
      'type' => 'varchar',
      'len' => '50',
    ),
    'status' => 
    array (
      'name' => 'status',
      'vname' => 'LBL_STATUS',
      'type' => 'enum',
      'len' => '25',
      'options' => 'user_status_dom',
      'importable' => 'required',
      'massupdate' => false,
    ),
    'address_street' => 
    array (
      'name' => 'address_street',
      'vname' => 'LBL_ADDRESS_STREET',
      'type' => 'varchar',
      'len' => '255',
    ),
    'address_permanent' => 
    array (
      'name' => 'address_permanent',
      'vname' => 'LBL_ADDRESS_PERMANENT',
      'type' => 'varchar',
      'len' => '255',
    ),
    'address_temporary' => 
    array (
      'name' => 'address_temporary',
      'vname' => 'LBL_ADDRESS_TEMPORARY',
      'type' => 'varchar',
      'len' => '255',
    ),
    'address_city' => 
    array (
      'name' => 'address_city',
      'vname' => 'LBL_ADDRESS_CITY',
      'type' => 'varchar',
      'len' => '100',
    ),
    'address_state' => 
    array (
      'name' => 'address_state',
      'vname' => 'LBL_ADDRESS_STATE',
      'type' => 'varchar',
      'len' => '100',
    ),
    'address_country' => 
    array (
      'name' => 'address_country',
      'vname' => 'LBL_ADDRESS_COUNTRY',
      'type' => 'varchar',
      'len' => '25',
    ),
    'address_postalcode' => 
    array (
      'name' => 'address_postalcode',
      'vname' => 'LBL_ADDRESS_POSTALCODE',
      'type' => 'varchar',
      'len' => '9',
    ),
    'user_preferences' => 
    array (
      'name' => 'user_preferences',
      'vname' => 'LBL_USER_PREFERENCES',
      'type' => 'text',
      'reportable' => false,
      'comment' => 'deprecated, see table user_preferences',
      'importable' => 'false',
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'required' => true,
      'reportable' => false,
    ),
    'portal_only' => 
    array (
      'name' => 'portal_only',
      'vname' => 'LBL_PORTAL_ONLY_USER',
      'type' => 'bool',
      'massupdate' => false,
    ),
    'employee_status' => 
    array (
      'name' => 'employee_status',
      'vname' => 'LBL_EMPLOYEE_STATUS',
      'type' => 'varchar',
      'function' => 
      array (
        'name' => 'getEmployeeStatusOptions',
        'returns' => 'html',
        'include' => 'modules/Employees/EmployeeStatus.php',
      ),
      'len' => '25',
    ),
    'messenger_id' => 
    array (
      'name' => 'messenger_id',
      'vname' => 'LBL_MESSENGER_ID',
      'type' => 'varchar',
      'len' => '25',
    ),
    'messenger_type' => 
    array (
      'name' => 'messenger_type',
      'vname' => 'LBL_MESSENGER_TYPE',
      'type' => 'varchar',
      'function' => 
      array (
        'name' => 'getMessengerTypeOptions',
        'returns' => 'html',
        'include' => 'modules/Employees/EmployeeStatus.php',
      ),
      'len' => '25',
    ),
    'calls' => 
    array (
      'name' => 'calls',
      'type' => 'link',
      'relationship' => 'calls_users',
      'source' => 'non-db',
      'vname' => 'LBL_CALLS',
    ),
    'meetings' => 
    array (
      'name' => 'meetings',
      'type' => 'link',
      'relationship' => 'meetings_users',
      'source' => 'non-db',
      'vname' => 'LBL_MEETINGS',
    ),
    'contacts_sync' => 
    array (
      'name' => 'contacts',
      'type' => 'link',
      'relationship' => 'contacts_users',
      'source' => 'non-db',
      'vname' => 'LBL_CONTACTS_SYNC',
    ),
    'reports_to_link' => 
    array (
      'name' => 'reports_to_link',
      'type' => 'link',
      'relationship' => 'user_direct_reports',
      'link_type' => 'one',
      'side' => 'right',
      'source' => 'non-db',
      'vname' => 'LBL_REPORTS_TO',
    ),
    'email1' => 
    array (
      'name' => 'email1',
      'vname' => 'LBL_EMAIL',
      'type' => 'varchar',
      'function' => 
      array (
        'name' => 'getEmailAddressWidget',
        'returns' => 'html',
      ),
      'source' => 'non-db',
      'group' => 'email1',
      'merge_filter' => 'enabled',
    ),
    'email_addresses' => 
    array (
      'name' => 'email_addresses',
      'type' => 'link',
      'relationship' => 'users_email_addresses',
      'module' => 'EmailAddress',
      'bean_name' => 'EmailAddress',
      'source' => 'non-db',
      'vname' => 'LBL_EMAIL_ADDRESSES',
      'reportable' => false,
    ),
    'email_addresses_primary' => 
    array (
      'name' => 'email_addresses_primary',
      'type' => 'link',
      'relationship' => 'users_email_addresses_primary',
      'source' => 'non-db',
      'vname' => 'LBL_EMAIL_ADDRESS_PRIMARY',
      'duplicate_merge' => 'disabled',
    ),
    'aclroles' => 
    array (
      'name' => 'aclroles',
      'type' => 'link',
      'relationship' => 'acl_roles_users',
      'source' => 'non-db',
      'side' => 'right',
      'vname' => 'LBL_ROLES',
    ),
    'is_group' => 
    array (
      'name' => 'is_group',
      'vname' => 'LBL_GROUP_USER',
      'type' => 'bool',
      'massupdate' => false,
    ),
    'c_accept_status_fields' => 
    array (
      'name' => 'c_accept_status_fields',
      'rname' => 'id',
      'relationship_fields' => 
      array (
        'id' => 'accept_status_id',
        'accept_status' => 'accept_status_name',
      ),
      'vname' => 'LBL_LIST_ACCEPT_STATUS',
      'type' => 'relate',
      'link' => 'calls',
      'link_type' => 'relationship_info',
      'source' => 'non-db',
      'importable' => 'false',
    ),
    'm_accept_status_fields' => 
    array (
      'name' => 'm_accept_status_fields',
      'rname' => 'id',
      'relationship_fields' => 
      array (
        'id' => 'accept_status_id',
        'accept_status' => 'accept_status_name',
      ),
      'vname' => 'LBL_LIST_ACCEPT_STATUS',
      'type' => 'relate',
      'link' => 'meetings',
      'link_type' => 'relationship_info',
      'source' => 'non-db',
      'importable' => 'false',
    ),
    'accept_status_id' => 
    array (
      'name' => 'accept_status_id',
      'type' => 'varchar',
      'source' => 'non-db',
      'vname' => 'LBL_LIST_ACCEPT_STATUS',
      'importable' => 'false',
    ),
    'accept_status_name' => 
    array (
      'name' => 'accept_status_name',
      'type' => 'enum',
      'source' => 'non-db',
      'vname' => 'LBL_LIST_ACCEPT_STATUS',
      'options' => 'dom_meeting_accept_status',
      'massupdate' => false,
    ),
    'prospect_lists' => 
    array (
      'name' => 'prospect_lists',
      'type' => 'link',
      'relationship' => 'prospect_list_users',
      'module' => 'ProspectLists',
      'source' => 'non-db',
      'vname' => 'LBL_PROSPECT_LIST',
    ),
    'work_start_date' => 
    array (
      'name' => 'work_start_date',
      'vname' => 'LBL_DATE_START',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'work_end_date' => 
    array (
      'name' => 'work_end_date',
      'vname' => 'LBL_DATE_END',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'gender' => 
    array (
      'name' => 'gender',
      'vname' => 'LBL_GENDER',
      'type' => 'enum',
      'options' => 'gender_list',
      'len' => '20',
    ),
    'academic_level' => 
    array (
      'name' => 'academic_level',
      'vname' => 'LBL_ACADEMIC_LEVEL',
      'type' => 'varchar',
      'len' => '250',
    ),
    'specialized' => 
    array (
      'name' => 'specialized',
      'vname' => 'LBL_SPECIALIZED',
      'type' => 'varchar',
      'len' => '250',
    ),
    'id_citizen' => 
    array (
      'name' => 'id_citizen',
      'vname' => 'LBL_ID_CITIZEN',
      'type' => 'varchar',
      'len' => '20',
    ),
    'issued_date' => 
    array (
      'name' => 'issued_date',
      'vname' => 'LBL_ISSUED_DATE',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'issued_by' => 
    array (
      'name' => 'issued_by',
      'vname' => 'LBL_ISSUED_BY',
      'type' => 'varchar',
      'audited' => true,
      'massupdate' => false,
    ),
    'insurance_number' => 
    array (
      'name' => 'insurance_number',
      'vname' => 'LBL_INSURANCE_NUMBER',
      'type' => 'varchar',
      'len' => '50',
      'audited' => true,
      'massupdate' => false,
    ),
    'insurance_month' => 
    array (
      'name' => 'insurance_month',
      'vname' => 'LBL_INSURANCE_MONTH',
      'type' => 'varchar',
      'len' => '50',
      'audited' => true,
      'massupdate' => false,
    ),
    'maternity_leave' => 
    array (
      'name' => 'maternity_leave',
      'vname' => 'LBL_MATERNITY_LEAVE',
      'type' => 'varchar',
      'len' => '50',
      'audited' => true,
      'massupdate' => false,
    ),
    'seniority' => 
    array (
      'name' => 'seniority',
      'vname' => 'LBL_SENIORITY',
      'type' => 'varchar',
      'len' => 250,
      'source' => 'non-db',
    ),
    'contract_type' => 
    array (
      'name' => 'contract_type',
      'vname' => 'LBL_CONTRACT_TYPE',
      'type' => 'enum',
      'options' => 'emp_contract_type_dom',
      'len' => '5',
      'audited' => true,
      'massupdate' => true,
    ),
    'contract_number' => 
    array (
      'name' => 'contract_number',
      'vname' => 'LBL_CONTRACT_NUMBER',
      'type' => 'varchar',
      'len' => '50',
      'audited' => true,
      'massupdate' => false,
    ),
    'salary' => 
    array (
      'name' => 'salary',
      'vname' => 'LBL_SALARY',
      'type' => 'varchar',
      'len' => '50',
      'audited' => true,
      'massupdate' => false,
    ),
    'contract_date_start' => 
    array (
      'name' => 'contract_date_start',
      'vname' => 'LBL_CONTRACT_DATE_START',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'contract_date_end' => 
    array (
      'name' => 'contract_date_end',
      'vname' => 'LBL_CONTRACT_DATE_END',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'remaining_day' => 
    array (
      'name' => 'remaining_day',
      'vname' => 'LBL_REMAINING_DAY',
      'type' => 'varchar',
      'audited' => true,
      'massupdate' => false,
    ),
    'contract12_number' => 
    array (
      'name' => 'contract12_number',
      'vname' => 'LBL_CONTRACT_NUMBER',
      'type' => 'varchar',
      'len' => '50',
      'audited' => true,
      'massupdate' => false,
    ),
    'contract12_salary' => 
    array (
      'name' => 'contract12_salary',
      'VNAME' => 'LBL_SALARY',
      'type' => 'varchar',
      'len' => '50',
      'audited' => true,
      'massupdate' => false,
    ),
    'contract12_date_start' => 
    array (
      'name' => 'contract12_date_start',
      'vname' => 'LBL_CONTRACT_DATE_START',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'contract12_date_end' => 
    array (
      'name' => 'contract12_date_end',
      'vname' => 'LBL_CONTRACT_DATE_END',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'appendix_date' => 
    array (
      'name' => 'appendix_date',
      'vname' => 'LBL_APPENDIX_DATE',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'contract_unspecified_number' => 
    array (
      'name' => 'contract_unspecified_number',
      'vname' => 'LBL_CONTRACT_NUMBER',
      'type' => 'varchar',
      'len' => '50',
      'audited' => true,
      'massupdate' => false,
    ),
    'contract_unspecified_salary' => 
    array (
      'name' => 'contract_unspecified_salary',
      'VNAME' => 'LBL_SALARY',
      'type' => 'varchar',
      'len' => '50',
      'audited' => true,
      'massupdate' => false,
    ),
    'contract_unspecified_date_start' => 
    array (
      'name' => 'contract_unspecified_date_start',
      'vname' => 'LBL_CONTRACT_DATE_START',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'contract_unspecified_date_end' => 
    array (
      'name' => 'contract_unspecified_date_end',
      'vname' => 'LBL_CONTRACT_DATE_END',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'contract_unspecified_appendix_date' => 
    array (
      'name' => 'contract_unspecified_appendix_date',
      'vname' => 'LBL_APPENDIX_DATE',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'appendix_note' => 
    array (
      'name' => 'appendix_note',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
    ),
    'notes' => 
    array (
      'name' => 'notes',
      'type' => 'link',
      'relationship' => 'user_notes',
      'module' => 'Notes',
      'bean_name' => 'Note',
      'source' => 'non-db',
      'vname' => 'LBL_NOTES',
    ),
    'rowindex' => 
    array (
      'name' => 'rowindex',
      'vname' => 'LBL_ROWINDEX',
      'type' => 'int',
      'len' => '6',
    ),
    'note' => 
    array (
      'name' => 'note',
      'vname' => 'LBL_NOTE',
      'type' => 'text',
      'reportable' => false,
      'comment' => 'note of employee',
      'importable' => 'false',
    ),
    'allow_branches' => 
    array (
      'name' => 'allow_branches',
      'type' => 'link',
      'relationship' => 'users_branches',
      'source' => 'non-db',
      'vname' => 'LBL_ALLOW_BRANCHES',
      'comment' => 'List branches, which user can see data',
    ),
    'enable_users' => 
    array (
      'name' => 'enable_users',
      'type' => 'link',
      'relationship' => 'users_access_other_enable',
      'source' => 'non-db',
      'module' => 'Users',
      'bean_name' => 'User',
      'vname' => 'LBL_ENABLE_USERS',
    ),
    'disable_users' => 
    array (
      'name' => 'disable_users',
      'type' => 'link',
      'relationship' => 'users_access_other_disable',
      'source' => 'non-db',
      'module' => 'Users',
      'bean_name' => 'User',
      'vname' => 'LBL_DISABLE_USERS',
    ),
    'table_params' => 
    array (
      'name' => 'table_params',
      'vname' => 'LBL_TABLE_PARAMS',
      'type' => 'text',
    ),
    'SecurityGroups' => 
    array (
      'name' => 'SecurityGroups',
      'type' => 'link',
      'relationship' => 'securitygroups_users',
      'source' => 'non-db',
      'module' => 'SecurityGroups',
      'bean_name' => 'SecurityGroup',
      'vname' => 'LBL_SECURITYGROUPS',
    ),
    'securitygroup_noninher_fields' => 
    array (
      'name' => 'securitygroup_noninher_fields',
      'rname' => 'id',
      'relationship_fields' => 
      array (
        'id' => 'securitygroup_noninherit_id',
        'noninheritable' => 'securitygroup_noninheritable',
      ),
      'vname' => 'LBL_USER_NAME',
      'type' => 'relate',
      'link' => 'SecurityGroups',
      'link_type' => 'relationship_info',
      'source' => 'non-db',
      'Importable' => false,
      'duplicate_merge' => 'disabled',
    ),
    'securitygroup_noninherit_id' => 
    array (
      'name' => 'securitygroup_noninherit_id',
      'type' => 'varchar',
      'source' => 'non-db',
      'vname' => 'LBL_securitygroup_noninherit_id',
    ),
    'securitygroup_noninheritable' => 
    array (
      'name' => 'securitygroup_noninheritable',
      'vname' => 'LBL_SECURITYGROUP_NONINHERITABLE',
      'type' => 'bool',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'branch_id' => 
    array (
      'name' => 'branch_id',
      'vname' => 'LBL_BRANCH_ID',
      'type' => 'enum',
      'options' => 'all_branch_dom',
      'dbType' => 'id',
      'massupdate' => false,
    ),
    'user_code' => 
    array (
      'name' => 'user_code',
      'vname' => 'LBL_USER_CODE',
      'type' => 'varchar',
      'len' => 50,
      'comment' => 'Code of the User',
    ),
    'salutation' => 
    array (
      'name' => 'salutation',
      'vname' => 'LBL_SALUTATION',
      'type' => 'enum',
      'options' => 'salutation_dom',
      'massupdate' => false,
      'len' => '10',
      'comment' => 'User salutation (e.g., Mr, Ms)',
    ),
    'teams' => 
    array (
      'name' => 'teams',
      'vname' => 'LBL_TEAMS',
      'type' => 'enum',
      'options' => 'teams_dom',
      'dbType' => 'id',
      'massupdate' => false,
    ),
    'hierarchical' => 
    array (
      'name' => 'hierarchical',
      'vname' => 'LBL_HIERARCHICAL',
      'len' => 30,
      'type' => 'enum',
      'options' => 'hierarchical_dom',
      'massupdate' => false,
    ),
    'birthdate' => 
    array (
      'name' => 'birthdate',
      'vname' => 'LBL_BIRTHDATE',
      'massupdate' => false,
      'type' => 'date',
      'comment' => 'The birthdate of the employee',
    ),
    'assign_work' => 
    array (
      'name' => 'assign_work',
      'vname' => 'LBL_ASSIGN_WORK',
      'type' => 'text',
    ),
    'location_area' => 
    array (
      'name' => 'location_area',
      'vname' => 'LBL_LOCATION_AREA',
      'len' => 30,
      'type' => 'enum',
      'options' => 'user_area_dom',
      'massupdate' => false,
    ),
    'login_on' => 
    array (
      'name' => 'login_on',
      'vname' => 'LBL_LOGIN_ON',
      'type' => 'enum',
      'options' => 'user_logged_on_dom',
      'len' => 20,
      'massupdate' => false,
    ),
    'avartar_image' => 
    array (
      'name' => 'avartar_image',
      'vname' => 'LBL_AVARTAR_IMAGE',
      'type' => 'varchar',
      'len' => 100,
    ),
    'branch_admin' => 
    array (
      'name' => 'branch_admin',
      'vname' => 'LBL_BRANCH_ADMIN',
      'type' => 'bool',
      'default' => '0',
      'massupdate' => false,
    ),
    'team_groups' => 
    array (
      'name' => 'team_groups',
      'type' => 'link',
      'relationship' => 'team_groups_users',
      'source' => 'non-db',
      'vname' => 'LBL_TEAM_GROUPS',
    ),
    'allow_teams' => 
    array (
      'name' => 'allow_teams',
      'type' => 'link',
      'relationship' => 'users_teams',
      'source' => 'non-db',
      'vname' => 'LBL_ALLOW_TEAMS',
      'comment' => 'List teams, which user can see data',
    ),
    'allow_departments' => 
    array (
      'name' => 'allow_departments',
      'type' => 'link',
      'relationship' => 'users_departments',
      'source' => 'non-db',
      'vname' => 'LBL_ALLOW_DEPARTMENTS',
      'comment' => 'List departments, which user can see data',
    ),
    'tqt_warehouses' => 
    array (
      'name' => 'tqt_warehouses',
      'type' => 'link',
      'relationship' => 'users_tqt_warehouses',
      'source' => 'non-db',
      'module' => 'TQT_Warehouses',
      'bean_name' => 'TQT_Warehouse',
      'vname' => 'LBL_TQT_WAREHOUSES',
    ),
  ),
  'indices' => 
  array (
  ),
  'relationships' => 
  array (
    'user_direct_reports' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Users',
      'rhs_table' => 'users',
      'rhs_key' => 'reports_to_id',
      'relationship_type' => 'one-to-many',
    ),
    'users_email_addresses' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'EmailAddresses',
      'rhs_table' => 'email_addresses',
      'rhs_key' => 'id',
      'relationship_type' => 'many-to-many',
      'join_table' => 'email_addr_bean_rel',
      'join_key_lhs' => 'bean_id',
      'join_key_rhs' => 'email_address_id',
      'relationship_role_column' => 'bean_module',
      'relationship_role_column_value' => 'Users',
    ),
    'users_email_addresses_primary' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'EmailAddresses',
      'rhs_table' => 'email_addresses',
      'rhs_key' => 'id',
      'relationship_type' => 'many-to-many',
      'join_table' => 'email_addr_bean_rel',
      'join_key_lhs' => 'bean_id',
      'join_key_rhs' => 'email_address_id',
      'relationship_role_column' => 'primary_address',
      'relationship_role_column_value' => '1',
    ),
  ),
  'custom_fields' => false,
);
?>
