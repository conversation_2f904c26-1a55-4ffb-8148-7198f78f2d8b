incomCRM.language.setLanguage('Data_Locks', {"LBL_ID":"ID","LBL_DATE_ENTERED":"Ng\u00e0y t\u1ea1o","LBL_DATE_MODIFIED":"Ng\u00e0y c\u1eadp nh\u1eadt","LBL_MODIFIED":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_MODIFIED_ID":"ID ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_MODIFIED_NAME":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_CREATED":"Ng\u01b0\u1eddi t\u1ea1o","LBL_CREATED_ID":"ID ng\u01b0\u1eddi t\u1ea1o","LBL_DESCRIPTION":"Ghi ch\u00fa","LBL_DELETED":"\u0110\u00e3 x\u00f3a?","LBL_NAME":"Ti\u00eau \u0111\u1ec1","LBL_CREATED_USER":"Ng\u01b0\u1eddi t\u1ea1o","LBL_MODIFIED_USER":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_ASSIGNED_TO":"Nv.Ql\u00fd","LBL_ASSIGNED_TO_ID":"ID Nv.Ql\u00fd","LBL_ASSIGNED_TO_NAME":"Nv.Ql\u00fd","LBL_ASSIGNED_TO_USER":"Nv.Ql\u00fd","LBL_ASSIGNED_USER":"Nv.Ql\u00fd","LBL_MODULE_NAME":"Kh\u00f3a\/M\u1edf kh\u00f3a D\u1eef li\u1ec7u","LBL_MODULE_TITLE":"Kh\u00f3a\/M\u1edf kh\u00f3a D\u1eef li\u1ec7u","LNK_NEW_RECORD":"Th\u00eam m\u1edbi","LNK_LIST":"Danh s\u00e1ch","LBL_NEW_FORM_TITLE":"Th\u00eam m\u1edbi","LBL_SEARCH_FORM_TITLE":"T\u00ecm ki\u1ebfm","LBL_LIST_FORM_TITLE":"Danh s\u00e1ch Kh\u00f3a\/M\u1edf kh\u00f3a d\u1eef li\u1ec7u","LBL_STATUS":"Lo\u1ea1i","LBL_OBJECTS":"\u0110\u1ed1i t\u01b0\u1ee3ng","LBL_DATE_START":"T\u1eeb ng\u00e0y","LBL_DATE_END":"\u0110\u1ebfn ng\u00e0y","LBL_BRANCH_FIELD":"Chi nh\u00e1nh c\u1ea7n kho\u00e1","LBL_DATE_START_FROM":"T\u1eeb ng\u00e0y T\u1eeb","LBL_DATE_START_TO":"T\u1eeb ng\u00e0y \u0110\u1ebfn","LBL_DATE_END_FROM":"\u0110\u1ebfn ng\u00e0y T\u1eeb","LBL_DATE_END_TO":"\u0110\u1ebfn ng\u00e0y \u0110\u1ebfn"});