<?php
// created: 2025-03-04 02:03:58
$pruneDatabase = array (
  0 => 'INSERT INTO accounts_opportunities (id, opportunity_id, account_id, date_modified, deleted) VALUES ("37a33ac5-80b1-01ce-c951-67a420bfa263", "322b9926-966f-56ff-8294-67a420af8124", "6253467b-3963-3b3e-8759-67a18b8e4100", "2025-02-06 02:42:15", "1");',
  1 => 'INSERT INTO accounts_opportunities (id, opportunity_id, account_id, date_modified, deleted) VALUES ("4a6e5bb7-b35e-6cb8-c3ba-67a588d75b11", "4307f17c-814d-b5a0-c467-67a5880b5172", "363ede57-ce5f-dc93-abce-5d5d36563670", "2025-02-07 06:28:54", "1");',
  2 => 'INSERT INTO accounts_opportunities (id, opportunity_id, account_id, date_modified, deleted) VALUES ("6863883c-2c3f-5e71-f439-67b2e2d80165", "44ac2d52-f1ac-625d-a711-67aebfd7239c", "d15e9abc-39f9-9890-bc8a-612f0279e921", "2025-02-18 02:37:52", "1");',
  3 => 'INSERT INTO accounts_opportunities (id, opportunity_id, account_id, date_modified, deleted) VALUES ("753e220c-359e-5922-ab2c-67b3f2d87ee1", "44ac2d52-f1ac-625d-a711-67aebfd7239c", "d15e9abc-39f9-9890-bc8a-612f0279e921", "2025-02-18 02:37:52", "1");',
  4 => 'INSERT INTO accounts_opportunities (id, opportunity_id, account_id, date_modified, deleted) VALUES ("98490b59-f438-53aa-d8a1-67a5aaa0c5e2", "df300416-12a3-bfbe-1513-67a5aace6fc2", "e704c546-5764-3a0c-f7e3-60d6a7a0aa24", "2025-02-07 06:53:11", "1");',
  5 => 'INSERT INTO cash_reasons (id, name, date_entered, date_modified, modified_user_id, created_by, description, deleted, reason_type, accounting_type, list_expense, ignore_summary, reason_opp_type, ignore_banks, cash_group, debt_type, is_net_cost, cash_project_group) VALUES ("c229c0d6-ded3-20bf-496a-6203351c86cb", "test test", "2022-02-09 03:33:17", "2025-02-18 06:14:41", "1", "1", "", "1", "1", "131", "0", "0", "1", "1", "6", "131", "1", "");',
  6 => 'INSERT INTO cashes_collect_details (id, deleted, date_modified, modified_by, cash_id, account_id, opportunity_id, amount, date_record, notes) VALUES ("278b8a7c-11fd-f338-9dea-67b3e56fc1ba", "1", "2025-02-18 01:41:54", "627d3b02-1dea-cd40-ca0a-5f96342ef270", "d413a913-a894-df1f-4927-650276b34292", "8e47e06a-e8e5-e1a8-e47c-638db2170ebc", "ea964e39-4206-abc7-a250-67b3e543f547", "1000000", "2025-02-18", "[SYS] Hủy từ đơn hàng");',
  7 => 'INSERT INTO notifications (id, deleted, date_modified, created_by, assigned_user_id, parent_id, parent_type, notes, remind, pushed, watched) VALUES ("f72e5dac-7709-11ee-856f-00163e3b7e12", "1", "2023-10-30 09:51:58", "1e1668f2-b0d4-5001-0a4d-4c8b539c7437", "9f7eea57-38e7-c8f2-7d29-57dc073ed056", "4e68d80d-0238-34fe-112b-653f7727f86d", "Opportunities", "", "0", "1", "0");',
  8 => 'INSERT INTO notifications (id, deleted, date_modified, created_by, assigned_user_id, parent_id, parent_type, notes, remind, pushed, watched) VALUES ("6858cc34-cf22-11ef-badc-00163e3b7e12", "1", "2025-01-10 07:13:35", "627d3b02-1dea-cd40-ca0a-5f96342ef270", "dfd64c2f-adf4-9bf4-24a3-5d5c745cb796", "9636fd6a-46e2-05ba-8b0d-6780c82b2610", "Opportunities", "", "0", "1", "0");',
  9 => 'INSERT INTO opportunities (id, date_entered, date_modified, modified_user_id, created_by, deleted, name, description, assigned_user_id, branch_id, department_id, opportunity_type, campaign_id, lead_source, amount, amount_usdollar, currency_id, date_closed, sales_stage, probability, priority, estimated_revenue, opportunity_scope, date_start_process, object, expected_output, actual_output, contract_revenue, actual_receipts, order_number, prod_lock, delivered_all, contact_name, vat_tax, vat_amount, vat_more_amt, vat_other_amt, discount_amount, cost_amount, comm_more_amount, incurred_amount, incurred_other, incurred_domestic, incurred_foreign, commission_amount, arise_cost_amount, profit_amount, rate_usd, is_locked, processing_stage, processing_content, amount_collected, amount_owed, handle_user_id, parent_id, payment_status, money_deposit, money_refund, is_target, account_targets, causes_failure, failure_notes, debt_order, payment_type, account_address, account_tax_code, shipping_fee, no_currency, account_phone, account_email, date_payment, order_type, quote_number, contract_number, contract_date, order_discount, payment_period, project_id, project_name, bank_account_id, disable_auto_price, quotation_date, quotation_version, location_nearby, rate_id, rate_foreign, service_total, service_used, service_left, vat_stock, ordered_merge, warranty_period, table_params, ignore_payment, re_calc_profit, is_auto_cpps, total_amount_in, date_return, prod_returned, vat_spec_amt, vat_imp_amt, vat_total_amount, ext_incurred_amount, int_incurred_amount, is_import, ttt_date_shipment, ttt_date_contract, ttt_date_declaration, ttt_date_storage, ttt_date_arrival, ttt_date_clearance, ttt_fee_amount, ttt_insurance_number, ttt_insurance_date, ttt_insurance_amount, ttt_insurance_extension, ttt_insurance_coop, ttt_date_lc_payment, ttt_lc_number, ttt_date_lc_expired, ttt_lc_amount, ttt_lc_reiss_amt, ttt_lc_repair_amt, ttt_lc_payment_amt, ttt_lc_bank_name, ttt_invoice_number, ttt_invoice_date, ttt_bol, ttt_bol_date, ttt_packinglist, ttt_co, ttt_milltest, ttt_dost, ttt_condi_trading, ttt_declaration, ttt_declaration_number, ttt_declaration_amount, ttt_vat_amount, ttt_verification, ttt_verification_amount, ttt_vat_import, ttt_customs_fee, ttt_clearance, ttt_arrival_notice, ttt_discharging_port, ttt_vessel, ttt_vessel_vn, ttt_owed_contract, ttt_storage_fee, ttt_vessel_fee, ttt_repair_cont_fee, ttt_deposit_cont_fee, ttt_expected_output, ttt_cnt_amount, ttt_foreign_amount, ttt_actual_output, ttt_commodity, ttt_tolerance, ttt_shipment_method, ttt_partial_shipment, ttt_transhipment, ttt_packing, ttt_quality, ttt_order_condi, total_foreign_amount, order_purchase, order_date, quotes_note, shipping_time, payment_method, shipping_note, failure_cause, date_record, is_export_vat, accounting_category_type, holding_time, holding_start, holding_end) VALUES ("322b9926-966f-56ff-8294-67a420af8124", "2025-02-06 02:39:41", "2025-02-06 02:42:15", "1", "8528d7d7-052a-ad55-8591-6017bd1e7408", "1", "PTB-2502-0004", "", "8528d7d7-052a-ad55-8591-6017bd1e7408", "2", "3", "3", "", "", "0", "0", "-99", "2025-02-06", "Cancel", "0", "", "0", "", "2025-02-06", "Proposal", "1", "5", "0", "0", "", "1", "0", "", "-1", "0", "0", "0", "0", "410005", "0", "0", "0", "0", "0", "0", "410005", "-410005", "", "0", "", "undefined", "0", "0", "", "", "", "0", "0", "0", "0", "", "test hủy", "0", "TM", "Sài Thành", "", "0", "0", "**********", "", "", "Maintenance", "", "", "", "", "", "", "", "ee901ddc-9ddb-884e-a360-65322ec9a19f", "0", "", "", "10.8413973,106.6659861", "-99", "1", "0", "", "0", "0", "0", "", "YTo0OntzOjE0OiJvcmRlcl9kaXNjb3VudCI7ZDowO3M6MTY6InByb2R1Y3RfZGlzY291bnQiO2k6MDtzOjEwOiJwb2ludF91c2VkIjtpOjA7czoxMToicG9pbnRfdG90YWwiO2Q6MDt9", "0", "0", "0", "0", "", "0", "", "", "", "", "", "0", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "Giá trên đã bao gồm VAT.\\r\\nBao gồm chi phí vận chuyển, lắp đặt tại TPHCM.\\r\\nBảng báo giá có giá trị trong vòng 30 ngày kề từ ngày báo.", "Chia thành nhiều đợt theo từng đơn đặt hàng của bên A Hàng có sẵn (Tối đa trong vòng 3-5 ngày theo từng đơn hàng của bên A)", "Bên A thanh toán cho Bên B bằng phương thức chuyển khoản hoặc tiền mặt 100% tổng giá trị hợp đồng tối đa sau 30 ngày kể từ khi Bên B giao đủ hàng, nghiệm thu, thanh lý và hóa đơn tài chính theo quy định cho Bên A.", "", "", "", "0", "1", "", "", "");',
  10 => 'INSERT INTO opportunities (id, date_entered, date_modified, modified_user_id, created_by, deleted, name, description, assigned_user_id, branch_id, department_id, opportunity_type, campaign_id, lead_source, amount, amount_usdollar, currency_id, date_closed, sales_stage, probability, priority, estimated_revenue, opportunity_scope, date_start_process, object, expected_output, actual_output, contract_revenue, actual_receipts, order_number, prod_lock, delivered_all, contact_name, vat_tax, vat_amount, vat_more_amt, vat_other_amt, discount_amount, cost_amount, comm_more_amount, incurred_amount, incurred_other, incurred_domestic, incurred_foreign, commission_amount, arise_cost_amount, profit_amount, rate_usd, is_locked, processing_stage, processing_content, amount_collected, amount_owed, handle_user_id, parent_id, payment_status, money_deposit, money_refund, is_target, account_targets, causes_failure, failure_notes, debt_order, payment_type, account_address, account_tax_code, shipping_fee, no_currency, account_phone, account_email, date_payment, order_type, quote_number, contract_number, contract_date, order_discount, payment_period, project_id, project_name, bank_account_id, disable_auto_price, quotation_date, quotation_version, location_nearby, rate_id, rate_foreign, service_total, service_used, service_left, vat_stock, ordered_merge, warranty_period, table_params, ignore_payment, re_calc_profit, is_auto_cpps, total_amount_in, date_return, prod_returned, vat_spec_amt, vat_imp_amt, vat_total_amount, ext_incurred_amount, int_incurred_amount, is_import, ttt_date_shipment, ttt_date_contract, ttt_date_declaration, ttt_date_storage, ttt_date_arrival, ttt_date_clearance, ttt_fee_amount, ttt_insurance_number, ttt_insurance_date, ttt_insurance_amount, ttt_insurance_extension, ttt_insurance_coop, ttt_date_lc_payment, ttt_lc_number, ttt_date_lc_expired, ttt_lc_amount, ttt_lc_reiss_amt, ttt_lc_repair_amt, ttt_lc_payment_amt, ttt_lc_bank_name, ttt_invoice_number, ttt_invoice_date, ttt_bol, ttt_bol_date, ttt_packinglist, ttt_co, ttt_milltest, ttt_dost, ttt_condi_trading, ttt_declaration, ttt_declaration_number, ttt_declaration_amount, ttt_vat_amount, ttt_verification, ttt_verification_amount, ttt_vat_import, ttt_customs_fee, ttt_clearance, ttt_arrival_notice, ttt_discharging_port, ttt_vessel, ttt_vessel_vn, ttt_owed_contract, ttt_storage_fee, ttt_vessel_fee, ttt_repair_cont_fee, ttt_deposit_cont_fee, ttt_expected_output, ttt_cnt_amount, ttt_foreign_amount, ttt_actual_output, ttt_commodity, ttt_tolerance, ttt_shipment_method, ttt_partial_shipment, ttt_transhipment, ttt_packing, ttt_quality, ttt_order_condi, total_foreign_amount, order_purchase, order_date, quotes_note, shipping_time, payment_method, shipping_note, failure_cause, date_record, is_export_vat, accounting_category_type, holding_time, holding_start, holding_end) VALUES ("4307f17c-814d-b5a0-c467-67a5880b5172", "2025-02-07 04:13:33", "2025-02-07 06:28:54", "1", "8528d7d7-052a-ad55-8591-6017bd1e7408", "1", "SO-2502-0024", "", "8528d7d7-052a-ad55-8591-6017bd1e7408", "2", "3", "1", "", "", "0", "0", "-99", "2025-02-07", "Cancel", "0", "", "0", "", "2025-02-07", "Proposal", "1", "1", "0", "0", "", "1", "0", "", "-1", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "", "0", "", "", "0", "0", "", "", "", "0", "0", "0", "0", "", "chưa có hàng", "0", "TM", "360 Bến Chương Dương, Phường Cầu Kho, Quận 1, TP.Hồ Chí Minh", "", "0", "0", "68", "", "", "Packages", "BG-25-0018", "", "", "", "", "", "", "ee901ddc-9ddb-884e-a360-65322ec9a19f", "0", "", "", "10.848547,106.6733557", "-99", "1", "0", "", "0", "0", "0", "", "YTozOntzOjE0OiJvcmRlcl9kaXNjb3VudCI7ZDowO3M6MTY6InByb2R1Y3RfZGlzY291bnQiO2k6MDtzOjEwOiJwb2ludF91c2VkIjtpOjA7fQ==", "0", "0", "0", "0", "", "0", "", "", "", "", "", "0", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "Giá trên đã bao gồm VAT.\\r\\nBao gồm chi phí vận chuyển, lắp đặt tại TPHCM.\\r\\nBảng báo giá có giá trị trong vòng 30 ngày kề từ ngày báo.", "Chia thành nhiều đợt theo từng đơn đặt hàng của bên A Hàng có sẵn (Tối đa trong vòng 3-5 ngày theo từng đơn hàng của bên A)", "Bên A thanh toán cho Bên B bằng phương thức chuyển khoản hoặc tiền mặt 100% tổng giá trị hợp đồng tối đa sau 30 ngày kể từ khi Bên B giao đủ hàng, nghiệm thu, thanh lý và hóa đơn tài chính theo quy định cho Bên A.", "", "", "", "0", "1", "", "", "");',
  11 => 'INSERT INTO opportunities (id, date_entered, date_modified, modified_user_id, created_by, deleted, name, description, assigned_user_id, branch_id, department_id, opportunity_type, campaign_id, lead_source, amount, amount_usdollar, currency_id, date_closed, sales_stage, probability, priority, estimated_revenue, opportunity_scope, date_start_process, object, expected_output, actual_output, contract_revenue, actual_receipts, order_number, prod_lock, delivered_all, contact_name, vat_tax, vat_amount, vat_more_amt, vat_other_amt, discount_amount, cost_amount, comm_more_amount, incurred_amount, incurred_other, incurred_domestic, incurred_foreign, commission_amount, arise_cost_amount, profit_amount, rate_usd, is_locked, processing_stage, processing_content, amount_collected, amount_owed, handle_user_id, parent_id, payment_status, money_deposit, money_refund, is_target, account_targets, causes_failure, failure_notes, debt_order, payment_type, account_address, account_tax_code, shipping_fee, no_currency, account_phone, account_email, date_payment, order_type, quote_number, contract_number, contract_date, order_discount, payment_period, project_id, project_name, bank_account_id, disable_auto_price, quotation_date, quotation_version, location_nearby, rate_id, rate_foreign, service_total, service_used, service_left, vat_stock, ordered_merge, warranty_period, table_params, ignore_payment, re_calc_profit, is_auto_cpps, total_amount_in, date_return, prod_returned, vat_spec_amt, vat_imp_amt, vat_total_amount, ext_incurred_amount, int_incurred_amount, is_import, ttt_date_shipment, ttt_date_contract, ttt_date_declaration, ttt_date_storage, ttt_date_arrival, ttt_date_clearance, ttt_fee_amount, ttt_insurance_number, ttt_insurance_date, ttt_insurance_amount, ttt_insurance_extension, ttt_insurance_coop, ttt_date_lc_payment, ttt_lc_number, ttt_date_lc_expired, ttt_lc_amount, ttt_lc_reiss_amt, ttt_lc_repair_amt, ttt_lc_payment_amt, ttt_lc_bank_name, ttt_invoice_number, ttt_invoice_date, ttt_bol, ttt_bol_date, ttt_packinglist, ttt_co, ttt_milltest, ttt_dost, ttt_condi_trading, ttt_declaration, ttt_declaration_number, ttt_declaration_amount, ttt_vat_amount, ttt_verification, ttt_verification_amount, ttt_vat_import, ttt_customs_fee, ttt_clearance, ttt_arrival_notice, ttt_discharging_port, ttt_vessel, ttt_vessel_vn, ttt_owed_contract, ttt_storage_fee, ttt_vessel_fee, ttt_repair_cont_fee, ttt_deposit_cont_fee, ttt_expected_output, ttt_cnt_amount, ttt_foreign_amount, ttt_actual_output, ttt_commodity, ttt_tolerance, ttt_shipment_method, ttt_partial_shipment, ttt_transhipment, ttt_packing, ttt_quality, ttt_order_condi, total_foreign_amount, order_purchase, order_date, quotes_note, shipping_time, payment_method, shipping_note, failure_cause, date_record, is_export_vat, accounting_category_type, holding_time, holding_start, holding_end) VALUES ("df300416-12a3-bfbe-1513-67a5aace6fc2", "2025-02-07 06:37:44", "2025-02-07 06:53:11", "1", "1", "1", "SO-2502-0029", "", "1", "2", "3", "1", "", "", "52000", "0", "-99", "2025-02-07", "Cancel", "0", "", "52000", "", "2025-02-07", "Proposal", "1", "13", "52000", "52000", "", "1", "0", "", "-1", "0", "0", "0", "0", "90884.95", "0", "0", "0", "0", "0", "0", "90884.95", "-38884.95", "", "0", "", "", "0", "52000", "", "", "", "0", "0", "0", "0", "", "test", "0", "TM", "Test 10001", "", "0", "0", "Test 10001", "", "", "Packages", "BG-25-0023", "", "", "", "", "", "", "ee901ddc-9ddb-884e-a360-65322ec9a19f", "0", "", "", "10.***************,106.**************", "-99", "1", "0", "", "0", "0", "0", "", "YTozOntzOjE0OiJvcmRlcl9kaXNjb3VudCI7ZDowO3M6MTY6InByb2R1Y3RfZGlzY291bnQiO2k6MDtzOjEwOiJwb2ludF91c2VkIjtpOjA7fQ==", "0", "0", "0", "0", "", "0", "", "", "", "", "", "0", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "Giá trên đã bao gồm VAT.\\r\\nBao gồm chi phí vận chuyển, lắp đặt tại TPHCM.\\r\\nBảng báo giá có giá trị trong vòng 30 ngày kề từ ngày báo.", "Chia thành nhiều đợt theo từng đơn đặt hàng của bên A Hàng có sẵn (Tối đa trong vòng 3-5 ngày theo từng đơn hàng của bên A)", "Bên A thanh toán cho Bên B bằng phương thức chuyển khoản hoặc tiền mặt 100% tổng giá trị hợp đồng tối đa sau 30 ngày kể từ khi Bên B giao đủ hàng, nghiệm thu, thanh lý và hóa đơn tài chính theo quy định cho Bên A.", "", "", "", "0", "1", "", "", "");',
  12 => 'INSERT INTO opportunities (id, date_entered, date_modified, modified_user_id, created_by, deleted, name, description, assigned_user_id, branch_id, department_id, opportunity_type, campaign_id, lead_source, amount, amount_usdollar, currency_id, date_closed, sales_stage, probability, priority, estimated_revenue, opportunity_scope, date_start_process, object, expected_output, actual_output, contract_revenue, actual_receipts, order_number, prod_lock, delivered_all, contact_name, vat_tax, vat_amount, vat_more_amt, vat_other_amt, discount_amount, cost_amount, comm_more_amount, incurred_amount, incurred_other, incurred_domestic, incurred_foreign, commission_amount, arise_cost_amount, profit_amount, rate_usd, is_locked, processing_stage, processing_content, amount_collected, amount_owed, handle_user_id, parent_id, payment_status, money_deposit, money_refund, is_target, account_targets, causes_failure, failure_notes, debt_order, payment_type, account_address, account_tax_code, shipping_fee, no_currency, account_phone, account_email, date_payment, order_type, quote_number, contract_number, contract_date, order_discount, payment_period, project_id, project_name, bank_account_id, disable_auto_price, quotation_date, quotation_version, location_nearby, rate_id, rate_foreign, service_total, service_used, service_left, vat_stock, ordered_merge, warranty_period, table_params, ignore_payment, re_calc_profit, is_auto_cpps, total_amount_in, date_return, prod_returned, vat_spec_amt, vat_imp_amt, vat_total_amount, ext_incurred_amount, int_incurred_amount, is_import, ttt_date_shipment, ttt_date_contract, ttt_date_declaration, ttt_date_storage, ttt_date_arrival, ttt_date_clearance, ttt_fee_amount, ttt_insurance_number, ttt_insurance_date, ttt_insurance_amount, ttt_insurance_extension, ttt_insurance_coop, ttt_date_lc_payment, ttt_lc_number, ttt_date_lc_expired, ttt_lc_amount, ttt_lc_reiss_amt, ttt_lc_repair_amt, ttt_lc_payment_amt, ttt_lc_bank_name, ttt_invoice_number, ttt_invoice_date, ttt_bol, ttt_bol_date, ttt_packinglist, ttt_co, ttt_milltest, ttt_dost, ttt_condi_trading, ttt_declaration, ttt_declaration_number, ttt_declaration_amount, ttt_vat_amount, ttt_verification, ttt_verification_amount, ttt_vat_import, ttt_customs_fee, ttt_clearance, ttt_arrival_notice, ttt_discharging_port, ttt_vessel, ttt_vessel_vn, ttt_owed_contract, ttt_storage_fee, ttt_vessel_fee, ttt_repair_cont_fee, ttt_deposit_cont_fee, ttt_expected_output, ttt_cnt_amount, ttt_foreign_amount, ttt_actual_output, ttt_commodity, ttt_tolerance, ttt_shipment_method, ttt_partial_shipment, ttt_transhipment, ttt_packing, ttt_quality, ttt_order_condi, total_foreign_amount, order_purchase, order_date, quotes_note, shipping_time, payment_method, shipping_note, failure_cause, date_record, is_export_vat, accounting_category_type, holding_time, holding_start, holding_end) VALUES ("44ac2d52-f1ac-625d-a711-67aebfd7239c", "2025-02-14 03:59:42", "2025-02-18 02:37:52", "627d3b02-1dea-cd40-ca0a-5f96342ef270", "627d3b02-1dea-cd40-ca0a-5f96342ef270", "1", "PREVIEW-SO-2502-0044", "", "627d3b02-1dea-cd40-ca0a-5f96342ef270", "2", "3", "1", "", "", "2000000", "0", "-99", "2025-02-18", "Cancel", "0", "", "2000000", "", "2025-02-18", "Proposal", "1", "100", "2000000", "2000000", "", "0", "0", "", "-1", "0", "0", "0", "0", "4500000", "0", "0", "0", "0", "0", "0", "4500000", "-2500000", "1", "0", "", "", "", "2000000", "", "", "1", "0", "0", "0", "0", "", "", "0", "TM", "215 Norodom Boulevard, Sangkat tole Khan Chamkar Morn, 12301 Phnom Penh, Cambodia", "", "0", "0", "**********", "", "", "Packages", "", "", "", "", "", "", "", "ee901ddc-9ddb-884e-a360-65322ec9a19f", "0", "", "", "10.8474292,106.6689339", "-99", "1", "0", "", "0", "0", "0", "", "YTozOntzOjE0OiJvcmRlcl9kaXNjb3VudCI7ZDowO3M6MTY6InByb2R1Y3RfZGlzY291bnQiO2k6MDtzOjEwOiJwb2ludF91c2VkIjtpOjA7fQ==", "0", "0", "0", "0", "", "0", "", "", "", "", "", "0", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "Giá trên đã bao gồm VAT.\\r\\nBao gồm chi phí vận chuyển, lắp đặt tại TPHCM.\\r\\nBảng báo giá có giá trị trong vòng 30 ngày kề từ ngày báo.", "Chia thành nhiều đợt theo từng đơn đặt hàng của bên A Hàng có sẵn (Tối đa trong vòng 3-5 ngày theo từng đơn hàng của bên A)", "Bên A thanh toán cho Bên B bằng phương thức chuyển khoản hoặc tiền mặt 100% tổng giá trị hợp đồng tối đa sau 30 ngày kể từ khi Bên B giao đủ hàng, nghiệm thu, thanh lý và hóa đơn tài chính theo quy định cho Bên A.", "", "", "", "0", "1", "", "", "");',
  13 => 'INSERT INTO opportunities_tasks (id, date_entered, date_modified, modified_user_id, created_by, deleted, name, description, status, current_step, next_step, date_start, date_end, times_total, opportunity_id) VALUES ("e3cea772-35d1-453e-a6d8-67a4218ca490", "2025-02-06 02:40:44", "2025-02-06 02:42:15", "8528d7d7-052a-ad55-8591-6017bd1e7408", "8528d7d7-052a-ad55-8591-6017bd1e7408", "1", "1", "", "Processing", "1", "", "2025-02-06 02:40:00", "", "", "");',
  14 => 'INSERT INTO opportunities_tasks (id, date_entered, date_modified, modified_user_id, created_by, deleted, name, description, status, current_step, next_step, date_start, date_end, times_total, opportunity_id) VALUES ("65227047-ea70-072e-dcde-67a58873b6c5", "2025-02-07 04:13:33", "2025-02-07 06:28:55", "8528d7d7-052a-ad55-8591-6017bd1e7408", "8528d7d7-052a-ad55-8591-6017bd1e7408", "1", "1", "", "Processing", "1", "", "2025-02-07 04:13:00", "", "", "");',
  15 => 'INSERT INTO opportunities_tasks (id, date_entered, date_modified, modified_user_id, created_by, deleted, name, description, status, current_step, next_step, date_start, date_end, times_total, opportunity_id) VALUES ("2d07961c-56a3-d878-e4bb-67a5aa227c8f", "2025-02-07 06:37:44", "2025-02-07 06:53:11", "1", "1", "1", "1", "", "Processing", "1", "", "2025-02-07 06:37:00", "", "", "");',
  16 => 'INSERT INTO opportunities_tqt_products (id, deleted, date_modified, modified_user_id, opportunity_id, product_id, group_price_id, product_gift_id, ref_id, parent_id, parent_note, product_combo, warehouse_id, ordering, product_code, product_name, prod_note, prod_quantity, prod_price, prod_orig_price, prod_sale_price, prod_discount, prod_discount_amt, prod_promotion, prod_promotion_amt, prod_more_percent, prod_vat_tax, prod_vat_amount, prod_amount, prod_total_amount, prod_orig_amount, prod_profit_amount, product_unit, prod_unit_opt, prod_unit_qty, prod_unit_length, prod_unit_width, prod_unit_price, product_type, product_gift, prod_convert_kg, prod_total_kg, prod_convert_mt, prod_total_mt, product_lots, prod_lot_id, delivered_qty, missing_qty, prod_custom, prod_num_uses, prod_date_start, prod_repeat_cycle, handling_note, prod_foreign_price, prod_foreign_amount, prod_actual_price, prod_stock_price, prod_more_amount, prod_comm_amount, prod_comm_more_amt, prod_more_price, prod_comm_price, prod_more_ret_tax, prod_more_ret_amt, prod_more_receive_amt, prod_more_note, prod_01_comm_per, prod_01_comm_amt, prod_01_comm_ret_tax, prod_01_comm_ret_amt, prod_01_comm_receive_amt, prod_01_comm_note, prod_02_comm_per, prod_02_comm_amt, prod_02_comm_ret_tax, prod_02_comm_ret_amt, prod_02_comm_receive_amt, prod_02_comm_note, prod_tax_exempt, prod_per_cpps, prod_amount_cpps, prod_total_stock_price, prod_total_amount_in, have_warehoused, prod_ext_incur_amount, prod_value_vat_import, prod_vat_tax_import, prod_vat_amount_import, prod_value_vat_spec, prod_vat_tax_spec, prod_vat_amount_spec, prod_value_vat_amount, prod_int_incur_amount, product_primary, product_set, prod_orig2_price, prod_return_orig_price, prod_custom_unit_qty, prod_custom_unit_price) VALUES ("45626fa3-301c-2d32-29a1-67a420a48801", "1", "2025-02-06 02:42:15", "8528d7d7-052a-ad55-8591-6017bd1e7408", "322b9926-966f-56ff-8294-67a420af8124", "21deafd7-dfd4-948b-e9e6-5e9e9f99156c", "", "", "", "", "", "", "2e5ac341-c6b6-0c24-34f2-55530a9c12bc", "1", "BNCV40-22", "BỘT TRÉT CHOKWANG VINA NGOÀI", "", "5", "0", "82001", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "410005", "-410005", "", "0", "0", "0", "0", "0", "0", "0", "22", "110", "0", "0", "", "", "0", "5", "", "0", "", "", "", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "", "82001.0426", "82001", "0", "0");',
  17 => 'INSERT INTO opportunities_tqt_products (id, deleted, date_modified, modified_user_id, opportunity_id, product_id, group_price_id, product_gift_id, ref_id, parent_id, parent_note, product_combo, warehouse_id, ordering, product_code, product_name, prod_note, prod_quantity, prod_price, prod_orig_price, prod_sale_price, prod_discount, prod_discount_amt, prod_promotion, prod_promotion_amt, prod_more_percent, prod_vat_tax, prod_vat_amount, prod_amount, prod_total_amount, prod_orig_amount, prod_profit_amount, product_unit, prod_unit_opt, prod_unit_qty, prod_unit_length, prod_unit_width, prod_unit_price, product_type, product_gift, prod_convert_kg, prod_total_kg, prod_convert_mt, prod_total_mt, product_lots, prod_lot_id, delivered_qty, missing_qty, prod_custom, prod_num_uses, prod_date_start, prod_repeat_cycle, handling_note, prod_foreign_price, prod_foreign_amount, prod_actual_price, prod_stock_price, prod_more_amount, prod_comm_amount, prod_comm_more_amt, prod_more_price, prod_comm_price, prod_more_ret_tax, prod_more_ret_amt, prod_more_receive_amt, prod_more_note, prod_01_comm_per, prod_01_comm_amt, prod_01_comm_ret_tax, prod_01_comm_ret_amt, prod_01_comm_receive_amt, prod_01_comm_note, prod_02_comm_per, prod_02_comm_amt, prod_02_comm_ret_tax, prod_02_comm_ret_amt, prod_02_comm_receive_amt, prod_02_comm_note, prod_tax_exempt, prod_per_cpps, prod_amount_cpps, prod_total_stock_price, prod_total_amount_in, have_warehoused, prod_ext_incur_amount, prod_value_vat_import, prod_vat_tax_import, prod_vat_amount_import, prod_value_vat_spec, prod_vat_tax_spec, prod_vat_amount_spec, prod_value_vat_amount, prod_int_incur_amount, product_primary, product_set, prod_orig2_price, prod_return_orig_price, prod_custom_unit_qty, prod_custom_unit_price) VALUES ("545dd70a-506f-93c3-0c05-67a588592a2a", "1", "2025-02-07 06:28:54", "8528d7d7-052a-ad55-8591-6017bd1e7408", "4307f17c-814d-b5a0-c467-67a5880b5172", "5259a0c0-f023-d0d8-5618-67a58864264c", "", "", "", "", "", "", "2e5ac341-c6b6-0c24-34f2-55530a9c12bc", "1", "oz", "nuoc oz", "", "1", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "", "", "0", "1", "", "0", "", "", "", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "", "0", "0", "0", "0");',
  18 => 'INSERT INTO opportunities_tqt_products (id, deleted, date_modified, modified_user_id, opportunity_id, product_id, group_price_id, product_gift_id, ref_id, parent_id, parent_note, product_combo, warehouse_id, ordering, product_code, product_name, prod_note, prod_quantity, prod_price, prod_orig_price, prod_sale_price, prod_discount, prod_discount_amt, prod_promotion, prod_promotion_amt, prod_more_percent, prod_vat_tax, prod_vat_amount, prod_amount, prod_total_amount, prod_orig_amount, prod_profit_amount, product_unit, prod_unit_opt, prod_unit_qty, prod_unit_length, prod_unit_width, prod_unit_price, product_type, product_gift, prod_convert_kg, prod_total_kg, prod_convert_mt, prod_total_mt, product_lots, prod_lot_id, delivered_qty, missing_qty, prod_custom, prod_num_uses, prod_date_start, prod_repeat_cycle, handling_note, prod_foreign_price, prod_foreign_amount, prod_actual_price, prod_stock_price, prod_more_amount, prod_comm_amount, prod_comm_more_amt, prod_more_price, prod_comm_price, prod_more_ret_tax, prod_more_ret_amt, prod_more_receive_amt, prod_more_note, prod_01_comm_per, prod_01_comm_amt, prod_01_comm_ret_tax, prod_01_comm_ret_amt, prod_01_comm_receive_amt, prod_01_comm_note, prod_02_comm_per, prod_02_comm_amt, prod_02_comm_ret_tax, prod_02_comm_ret_amt, prod_02_comm_receive_amt, prod_02_comm_note, prod_tax_exempt, prod_per_cpps, prod_amount_cpps, prod_total_stock_price, prod_total_amount_in, have_warehoused, prod_ext_incur_amount, prod_value_vat_import, prod_vat_tax_import, prod_vat_amount_import, prod_value_vat_spec, prod_vat_tax_spec, prod_vat_amount_spec, prod_value_vat_amount, prod_int_incur_amount, product_primary, product_set, prod_orig2_price, prod_return_orig_price, prod_custom_unit_qty, prod_custom_unit_price) VALUES ("7d87beea-3006-dfe5-69e2-67b3f27d5a6b", "1", "2025-02-18 02:37:52", "627d3b02-1dea-cd40-ca0a-5f96342ef270", "44ac2d52-f1ac-625d-a711-67aebfd7239c", "b898031f-a3da-2378-dbf1-65db3c826bf6", "761b03bd-ee3d-85e5-3916-65f2ba1711a7", "", "", "", "", "", "2e5ac341-c6b6-0c24-34f2-55530a9c12bc", "1", "AOSMBGAI", "Áo sơ mi bé gái", "", "100", "20000", "45000", "20000", "0", "0", "0", "0", "0", "0", "0", "2000000", "2000000", "4500000", "-2500000", "Cái", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "", "", "0", "100", "", "0", "", "", "", "0", "0", "20000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "", "45000", "0", "0", "0");',
  19 => 'INSERT INTO opportunities_tqt_products (id, deleted, date_modified, modified_user_id, opportunity_id, product_id, group_price_id, product_gift_id, ref_id, parent_id, parent_note, product_combo, warehouse_id, ordering, product_code, product_name, prod_note, prod_quantity, prod_price, prod_orig_price, prod_sale_price, prod_discount, prod_discount_amt, prod_promotion, prod_promotion_amt, prod_more_percent, prod_vat_tax, prod_vat_amount, prod_amount, prod_total_amount, prod_orig_amount, prod_profit_amount, product_unit, prod_unit_opt, prod_unit_qty, prod_unit_length, prod_unit_width, prod_unit_price, product_type, product_gift, prod_convert_kg, prod_total_kg, prod_convert_mt, prod_total_mt, product_lots, prod_lot_id, delivered_qty, missing_qty, prod_custom, prod_num_uses, prod_date_start, prod_repeat_cycle, handling_note, prod_foreign_price, prod_foreign_amount, prod_actual_price, prod_stock_price, prod_more_amount, prod_comm_amount, prod_comm_more_amt, prod_more_price, prod_comm_price, prod_more_ret_tax, prod_more_ret_amt, prod_more_receive_amt, prod_more_note, prod_01_comm_per, prod_01_comm_amt, prod_01_comm_ret_tax, prod_01_comm_ret_amt, prod_01_comm_receive_amt, prod_01_comm_note, prod_02_comm_per, prod_02_comm_amt, prod_02_comm_ret_tax, prod_02_comm_ret_amt, prod_02_comm_receive_amt, prod_02_comm_note, prod_tax_exempt, prod_per_cpps, prod_amount_cpps, prod_total_stock_price, prod_total_amount_in, have_warehoused, prod_ext_incur_amount, prod_value_vat_import, prod_vat_tax_import, prod_vat_amount_import, prod_value_vat_spec, prod_vat_tax_spec, prod_vat_amount_spec, prod_value_vat_amount, prod_int_incur_amount, product_primary, product_set, prod_orig2_price, prod_return_orig_price, prod_custom_unit_qty, prod_custom_unit_price) VALUES ("15313ff6-1ee8-f522-82e6-67a5aae91ba8", "1", "2025-02-07 06:53:11", "1", "df300416-12a3-bfbe-1513-67a5aace6fc2", "33a87820-4421-5ac7-b839-62d635cb8705", "", "", "", "", "", "", "2e5ac341-c6b6-0c24-34f2-55530a9c12bc", "1", "AQUAFINA", "Nước uống AQUAFINA CHAI 335ML", "", "13", "4000", "6991.15", "4000", "0", "0", "0", "0", "0", "0", "0", "52000", "52000", "90884.95", "-38884.95", "Chai", "0", "0", "0", "0", "0", "0", "0", "24", "312", "0", "0", "", "", "0", "13", "", "0", "", "", "", "0", "0", "4000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "", "6991.1517", "0", "0", "0");',
  20 => 'INSERT INTO product_gifts (id, date_entered, date_modified, modified_user_id, created_by, deleted, name, description, branch_id, department_id, assigned_user_id, status, date_valid_from, date_valid_to, qty_buy, qty_gift, gift_type, branch_all, membership_all, account_type_all, tqt_product_id) VALUES ("6570e406-a2cc-ce84-1c81-67a440862a45", "2025-02-06 04:54:50", "2025-02-06 06:24:28", "1", "1", "1", "tặng Test", "", "2", "3", "1", "Active", "2025-02-06", "2025-02-28", "5", "1", "1", "^-1^", "", "^KL^,^CT^,^CH^,^TM^,^KQ^,^CV^,^KH^", "cd3e50a0-0972-4270-429a-65ae09578b13");',
  21 => 'INSERT INTO product_promotions (id, name, date_entered, date_modified, modified_user_id, created_by, description, deleted, assigned_user_id, branch_id, department_id, code, status, promotion_type, apply_all, date_valid_from, date_valid_to, all_discount, branch_all, department_all, membership_all) VALUES ("********-120a-83b6-2867-65b8ac367f80", "Khuyến mãi tiền mặt", "2024-01-30 07:58:58", "2025-02-06 07:30:17", "1", "1", "", "1", "1", "2", "3", "KM2401/004", "2", "2", "0", "2024-01-29", "2024-12-23", "0", "^-1^", "^-1^", "");',
  22 => 'INSERT INTO product_promotions (id, name, date_entered, date_modified, modified_user_id, created_by, description, deleted, assigned_user_id, branch_id, department_id, code, status, promotion_type, apply_all, date_valid_from, date_valid_to, all_discount, branch_all, department_all, membership_all) VALUES ("ee7a01ae-b7f8-fe32-b11d-6767dda3100d", "KHuyến mãi theo %", "2024-12-22 09:37:49", "2025-02-06 07:30:17", "1", "1", "", "1", "1", "2", "3", "KM2412/001", "2", "1", "0", "2024-12-22", "2024-12-31", "0", "^32cf802a-1420-f3d0-ab1b-5f150b79846f^", "", "");',
  23 => 'INSERT INTO product_promotions (id, name, date_entered, date_modified, modified_user_id, created_by, description, deleted, assigned_user_id, branch_id, department_id, code, status, promotion_type, apply_all, date_valid_from, date_valid_to, all_discount, branch_all, department_all, membership_all) VALUES ("c78fd6d0-bd7a-dee7-4912-6767de3201c6", "Khuyến mãi theo tiền mặt", "2024-12-22 09:40:09", "2025-02-06 07:30:17", "1", "1", "", "1", "1", "2", "3", "KM2412/002", "2", "2", "0", "2024-12-22", "2024-12-31", "0", "", "", "");',
  24 => 'INSERT INTO product_promotions_discounts (id, deleted, date_modified, product_promotion_id, tqt_product_id, discount) VALUES ("8faf8856-3163-afe9-215b-6768d01a0b1c", "1", "2025-02-06 07:30:17", "********-120a-83b6-2867-65b8ac367f80", "b50798b1-000c-1805-8edd-6321af9fe2ec", "5000");',
  25 => 'INSERT INTO product_promotions_discounts (id, deleted, date_modified, product_promotion_id, tqt_product_id, discount) VALUES ("8fa10be7-3d2a-3203-5445-6768d0739ddb", "1", "2025-02-06 07:30:17", "********-120a-83b6-2867-65b8ac367f80", "a0f97d78-5a56-8321-079c-6321afcf16ad", "7000");',
  26 => 'INSERT INTO product_promotions_discounts (id, deleted, date_modified, product_promotion_id, tqt_product_id, discount) VALUES ("8f8ac4ab-8c6e-09f1-3f26-6768d0ee1869", "1", "2025-02-06 07:30:17", "********-120a-83b6-2867-65b8ac367f80", "9c6e8781-b1d8-6579-9643-6321af2904a7", "10000");',
  27 => 'INSERT INTO product_promotions_discounts (id, deleted, date_modified, product_promotion_id, tqt_product_id, discount) VALUES ("d91e09f3-02bd-dca3-84da-6769037e0045", "1", "2025-02-06 07:30:17", "ee7a01ae-b7f8-fe32-b11d-6767dda3100d", "cf1c311a-adb8-37a1-7c66-65db3ca05d65", "10");',
  28 => 'INSERT INTO product_promotions_discounts (id, deleted, date_modified, product_promotion_id, tqt_product_id, discount) VALUES ("d9100fd9-d60b-e857-5632-6769035ea91b", "1", "2025-02-06 07:30:17", "ee7a01ae-b7f8-fe32-b11d-6767dda3100d", "cd6e34c7-fcbc-c437-6e32-65db3c5ca0a9", "5");',
  29 => 'INSERT INTO product_promotions_discounts (id, deleted, date_modified, product_promotion_id, tqt_product_id, discount) VALUES ("d8f8c846-8899-4115-3160-676903ed5634", "1", "2025-02-06 07:30:17", "ee7a01ae-b7f8-fe32-b11d-6767dda3100d", "baed16b3-0ace-c24c-2af6-65db3c4549da", "0");',
  30 => 'INSERT INTO product_promotions_discounts (id, deleted, date_modified, product_promotion_id, tqt_product_id, discount) VALUES ("c8fa737c-540c-1dbb-20ee-6767de79af07", "1", "2025-02-06 07:30:17", "c78fd6d0-bd7a-dee7-4912-6767de3201c6", "cd6e34c7-fcbc-c437-6e32-65db3c5ca0a9", "10000");',
  31 => 'INSERT INTO product_promotions_discounts (id, deleted, date_modified, product_promotion_id, tqt_product_id, discount) VALUES ("c90e9cd3-0cc0-4367-703a-6767decb10da", "1", "2025-02-06 07:30:17", "c78fd6d0-bd7a-dee7-4912-6767de3201c6", "cf1c311a-adb8-37a1-7c66-65db3ca05d65", "0");',
  32 => 'INSERT INTO product_promotions_discounts (id, deleted, date_modified, product_promotion_id, tqt_product_id, discount) VALUES ("c91e0aab-8862-7c9d-3425-6767deac5cda", "1", "2025-02-06 07:30:17", "c78fd6d0-bd7a-dee7-4912-6767de3201c6", "baed16b3-0ace-c24c-2af6-65db3c4549da", "20000");',
  33 => 'INSERT INTO sales_lines_accounts (id, deleted, date_modified, sales_line_id, account_id, line_order) VALUES ("7b2d064f-706b-d9b7-b918-67a1b235a019", "1", "2025-02-04 06:28:59", "ca1188f5-941c-bf44-9e6e-67a1b1624ec9", "b2dd214d-cdf2-e874-5fe0-5d5d363f0771", "1");',
  34 => 'INSERT INTO sales_lines_accounts (id, deleted, date_modified, sales_line_id, account_id, line_order) VALUES ("69b85bbf-6a36-4e45-fa46-67a1b2fa8eb2", "1", "2025-02-04 06:29:01", "ca1188f5-941c-bf44-9e6e-67a1b1624ec9", "41a45cef-9d92-b5b5-181e-617ce5c3ad03", "2");',
  35 => 'INSERT INTO sales_lines_accounts (id, deleted, date_modified, sales_line_id, account_id, line_order) VALUES ("29092bf7-13c8-90d0-70a7-67a1b275c9af", "1", "2025-02-04 06:28:52", "ca1188f5-941c-bf44-9e6e-67a1b1624ec9", "8ed2c911-3f69-a6fb-91ef-617a043d2e82", "3");',
  36 => 'INSERT INTO sales_lines_accounts (id, deleted, date_modified, sales_line_id, account_id, line_order) VALUES ("8106a32b-e294-692b-f16c-67a1b2e65278", "1", "2025-02-04 06:28:55", "ca1188f5-941c-bf44-9e6e-67a1b1624ec9", "6253467b-3963-3b3e-8759-67a18b8e4100", "4");',
  37 => 'INSERT INTO sales_lines_accounts (id, deleted, date_modified, sales_line_id, account_id, line_order) VALUES ("92a863a8-9825-1edf-f744-67a1b2080b45", "1", "2025-02-04 06:29:03", "ca1188f5-941c-bf44-9e6e-67a1b1624ec9", "293d3b55-df85-46f9-6347-5d5d364920e9", "5");',
  38 => 'INSERT INTO sales_lines_accounts (id, deleted, date_modified, sales_line_id, account_id, line_order) VALUES ("5711bc2f-9c43-512a-a35e-67a1b3e7f11a", "1", "2025-02-04 06:29:05", "ca1188f5-941c-bf44-9e6e-67a1b1624ec9", "21e11bf1-3b3e-0f4c-05f4-6639945c7d52", "6");',
  39 => 'INSERT INTO sales_lines_accounts (id, deleted, date_modified, sales_line_id, account_id, line_order) VALUES ("890b965c-6f95-6d30-461c-67a1b3259a3a", "1", "2025-02-04 06:29:07", "ca1188f5-941c-bf44-9e6e-67a1b1624ec9", "d4178efa-4342-91ce-b5b0-5d5d3636178d", "7");',
  40 => 'INSERT INTO sales_lines_accounts (id, deleted, date_modified, sales_line_id, account_id, line_order) VALUES ("b0f05806-1651-0057-d4ab-67a1b3102ccb", "1", "2025-02-04 06:30:15", "7c961fd8-7a65-3e52-d012-67a1b348d30a", "37f49ff9-ea8c-295f-53d0-5ed3ce914b98", "");',
  41 => 'INSERT INTO sales_lines_accounts (id, deleted, date_modified, sales_line_id, account_id, line_order) VALUES ("f2bba5a3-1b0a-b716-892a-67a1d6004004", "1", "2025-02-04 08:56:42", "5a1a3763-8b3d-5f08-5b9d-67a1d6f17ff0", "865d7e26-b3f8-a16b-03ed-5d5d36080f11", "");',
  42 => 'INSERT INTO securitygroups_records (id, securitygroup_id, record_id, module, date_modified, deleted) VALUES ("88e6092a-15cd-5321-4811-67aebf535699", "1f28b2e6-69cb-1e48-631d-57dc08f22aed", "44ac2d52-f1ac-625d-a711-67aebfd7239c", "Opportunities", "2025-02-18 02:37:52", "1");',
  43 => 'INSERT INTO securitygroups_records (id, securitygroup_id, record_id, module, date_modified, deleted) VALUES ("c4628aa0-b3ae-b738-7d5c-67aec0464eef", "1f28b2e6-69cb-1e48-631d-57dc08f22aed", "44ac2d52-f1ac-625d-a711-67aebfd7239c", "Opportunities", "2025-02-18 02:37:52", "1");',
  44 => 'INSERT INTO securitygroups_records (id, securitygroup_id, record_id, module, date_modified, deleted) VALUES ("51d94ea9-1abb-fef8-f0c9-67a4204b0199", "39b8a58c-d235-1e8c-3d39-625fc6873923", "322b9926-966f-56ff-8294-67a420af8124", "Opportunities", "2025-02-06 02:42:15", "1");',
  45 => 'INSERT INTO securitygroups_records (id, securitygroup_id, record_id, module, date_modified, deleted) VALUES ("e4538685-f8c2-11eb-8d1c-00163effd27c", "8a4340c1-b652-23be-2f80-5f2a3412c8c8", "abbcbbc4-789b-68c4-5b04-5d5d2315ec67", "Accounts", "2025-02-06 03:55:05", "1");',
  46 => 'INSERT INTO securitygroups_records (id, securitygroup_id, record_id, module, date_modified, deleted) VALUES ("8be20c93-b580-d7fb-1133-67aebfe51fd8", "9a4fcc0e-c0d7-c39b-789a-5d5cda661ed2", "44ac2d52-f1ac-625d-a711-67aebfd7239c", "Opportunities", "2025-02-18 02:37:52", "1");',
  47 => 'INSERT INTO securitygroups_records (id, securitygroup_id, record_id, module, date_modified, deleted) VALUES ("c65ce98a-d48d-5d60-06ee-67aec0d8e670", "9a4fcc0e-c0d7-c39b-789a-5d5cda661ed2", "44ac2d52-f1ac-625d-a711-67aebfd7239c", "Opportunities", "2025-02-18 02:37:52", "1");',
  48 => 'INSERT INTO securitygroups_records (id, securitygroup_id, record_id, module, date_modified, deleted) VALUES ("97df10ba-6139-11ec-9635-00163effd27c", "9f7c59a2-10ad-81e3-9d66-61bfe57b5df6", "abbcbbc4-789b-68c4-5b04-5d5d2315ec67", "Accounts", "2025-02-06 03:55:05", "1");',
  49 => 'INSERT INTO securitygroups_records (id, securitygroup_id, record_id, module, date_modified, deleted) VALUES ("52ff1549-85a9-a94b-c3df-67a420f0d7ac", "a761fc66-d781-f901-29c5-6017bd0bab8d", "322b9926-966f-56ff-8294-67a420af8124", "Opportunities", "2025-02-06 02:42:15", "1");',
  50 => 'INSERT INTO tasks_group_users (id, deleted, date_modified, task_id, user_id) VALUES ("4e214a92-ec7b-e890-c61d-67a08260b52e", "1", "2025-02-03 08:45:04", "1da773e7-f6a8-1983-d09d-67a081f63b5d", "69647d43-1925-d5c4-885e-6538d6aca0d7");',
  51 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("aa910e1b-f94c-17f9-5ffc-67a94fb73d2d", "1", "2025-02-10 00:59:04", "1", "admin", "z21232f297a57a5a743894a0e4a801fc3z9b045c31ea6aff22f11de768a37fa0e5z");',
  52 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("4e780aeb-35b5-a2d5-5ca8-67a9c29a5764", "1", "2025-02-10 09:11:04", "1", "admin", "z21232f297a57a5a743894a0e4a801fc3z9b045c31ea6aff22f11de768a37fa0e5z");',
  53 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("d23b960d-095e-7e2b-55da-67a9c4a5c1e5", "1", "2025-02-10 09:21:02", "1", "admin", "z21232f297a57a5a743894a0e4a801fc3z9b045c31ea6aff22f11de768a37fa0e5z");',
  54 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("b85dd4a0-763d-7037-7a18-67b15d388ca8", "1", "2025-02-16 03:38:06", "1", "admin", "z21232f297a57a5a743894a0e4a801fc3z9b045c31ea6aff22f11de768a37fa0e5z");',
  55 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("2e6c0708-9a4c-670f-a7cd-67b4480ed504", "1", "2025-02-18 08:46:15", "1", "admin", "z21232f297a57a5a743894a0e4a801fc3z9b045c31ea6aff22f11de768a37fa0e5z");',
  56 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("32c4b597-5ea5-8950-0852-67b947535eb5", "1", "2025-02-22 03:41:55", "1", "admin", "z21232f297a57a5a743894a0e4a801fc3z9b045c31ea6aff22f11de768a37fa0e5z");',
  57 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("b0380f6d-b96d-6e8e-fc10-67b947120b07", "1", "2025-02-22 03:42:55", "1", "admin", "z21232f297a57a5a743894a0e4a801fc3z9b045c31ea6aff22f11de768a37fa0e5z");',
  58 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("8f950387-faf6-2c40-396d-67b94a5a4c54", "1", "2025-02-22 03:55:52", "1", "admin", "z21232f297a57a5a743894a0e4a801fc3z9b045c31ea6aff22f11de768a37fa0e5z");',
  59 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("3cf8df35-9786-3dc9-1c3d-67bbe0dfa8be", "1", "2025-02-24 02:57:17", "1", "admin", "z21232f297a57a5a743894a0e4a801fc3z9b045c31ea6aff22f11de768a37fa0e5z");',
  60 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("76093e30-524f-450e-c5b9-67bed6a57044", "1", "2025-02-26 08:53:38", "1", "admin", "z21232f297a57a5a743894a0e4a801fc3z9b045c31ea6aff22f11de768a37fa0e5z");',
  61 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("5a7f6a21-4082-de07-b12a-67bedbd2d68c", "1", "2025-02-26 09:15:35", "1", "admin", "z21232f297a57a5a743894a0e4a801fc3z9b045c31ea6aff22f11de768a37fa0e5z");',
  62 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("9b5719cc-05d6-7283-89d6-67c10ca39701", "1", "2025-02-28 01:09:34", "1", "admin", "z21232f297a57a5a743894a0e4a801fc3z9b045c31ea6aff22f11de768a37fa0e5z");',
  63 => 'INSERT INTO warehouse_ins (id, name, date_entered, date_modified, modified_user_id, created_by, description, deleted, assigned_user_id, branch_id, department_id, code, status, in_type, date_perform, quantity, amount, befor_vat, currency_id, rate_usd, address, phone, document, transporter, notes, date_doc, warehouse_person, tqt_warehouse_id, account_id, product_lot_id, handle_user_id, warehouse_type_id, tqt_productgroup_id, opportunity_id, warehouse_out_id, is_locked, custom_warehouses_checked, is_vat) VALUES ("3bc086d8-cc29-5c0b-9dbc-67c16583aad1", "PNK-2502-0043", "2025-02-28 07:27:06", "2025-02-28 07:27:46", "8528d7d7-052a-ad55-8591-6017bd1e7408", "8528d7d7-052a-ad55-8591-6017bd1e7408", "", "1", "8528d7d7-052a-ad55-8591-6017bd1e7408", "2", "3", "", "6", "9", "2025-02-28", "6", "2000000", "2000000", "-99", "", "Sài Thành", "", "", "", "", "2025-02-28", "", "2f6cf5e3-7478-a8a2-91cf-6372ef7ec556", "6253467b-3963-3b3e-8759-67a18b8e4100", "", "", "2e016fc8-9381-7461-8b7e-5e9e522c7124", "", "", "", "0", "0", "0");',
);
?>
