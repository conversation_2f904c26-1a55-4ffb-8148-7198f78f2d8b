
<section id="DetailViewBody" class="view-module-{$module|strtolower}">

<form action="index.php" method="post" name="{$form_name|default:'DetailView'}" id="form" class="_view-{$instanceName}">
<table cellpadding="1" cellspacing="0" border="0" width="100%">
<tr>
<td class="buttons" align="left" valign="top" nowrap>
<input type="hidden" name="module" value="{$module}" />
<input type="hidden" name="action" value="EditView" />
<input type="hidden" name="record" value="{$fields.id.value}" />
<input type="hidden" name="return_action" />
<input type="hidden" name="return_module" />
<input type="hidden" name="return_id" />
<input type="hidden" name="module_tab" /> 
<input type="hidden" name="isDuplicate" value="false" />
<input type="hidden" name="offset" value="{$offset}" />
{if $DISPLAY_EDIT}<input title="{$APP.LBL_EDIT_BUTTON_TITLE}" accessKey="{$APP.LBL_EDIT_BUTTON_KEY}" class="button" onclick="this.form.return_module.value='{$module}'; this.form.return_action.value='DetailView'; this.form.return_id.value='{$id}'; this.form.action.value='EditView'" type="submit" name="Edit" id="edit_button" value="{$APP.LBL_EDIT_BUTTON_LABEL}" /> {/if}
{if $DISPLAY_DUPLICATE}<input title="{$APP.LBL_DUPLICATE_BUTTON_TITLE}" accessKey="{$APP.LBL_DUPLICATE_BUTTON_KEY}" class="button" onclick="this.form.return_module.value='{$module}'; this.form.return_action.value='DetailView'; this.form.return_id.value='{$id}'; this.form.isDuplicate.value=true; this.form.action.value='EditView'" type="submit" name="Duplicate" value="{$APP.LBL_DUPLICATE_BUTTON_LABEL}" id="duplicate_button" /> {/if}
</td>
<td class="buttons" align="left" valign="top" nowrap>
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=Employees", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</td>
<td align="right" width="90%">{$ADMIN_EDIT}</td>
</tr>
</table>
</form>
{incomCRM_include include=$includes}
<div id="DEFAULT" class="detailview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<div class="detail view {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="DEFAULT_GROUP" style="display:block">
<table id='detailpanel_1' cellspacing='{$gridline}'>
{$PAGINATION}
<tr>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ROWINDEX' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td colspan='3' id='_rowindex_field' >
{counter name="panelFieldCount"}

<span id='{$fields.rowindex.name}' >
{$fields.rowindex.value}
</span>
</td>
</tr>
<tr>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_USER_CODE' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_user_code_field' >
{counter name="panelFieldCount"}

<span id='{$fields.user_code.name}' >
{$fields.user_code.value}
</span>
</td>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_EMPLOYEE_STATUS' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_employee_status_field' >
{counter name="panelFieldCount"}

<span id='{$fields.employee_status.name}' >
{$fields.employee_status.value}
</span>
</td>
</tr>
<tr>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_NAME' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_full_name_field' >
{counter name="panelFieldCount"}

<span id='{$fields.full_name.name}' >
{$fields.full_name.value}
</span>
</td>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_OFFICE_PHONE' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_phone_work_field' >
{counter name="panelFieldCount"}

<span id='{$fields.phone_work.name}' >
{$fields.phone_work.value}
</span>
</td>
</tr>
<tr>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_GENDER' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_gender_field' >
{counter name="panelFieldCount"}

{ $fields.gender.options[$fields.gender.value]}
</td>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ID_CITIZEN' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_id_citizen_field' >
{counter name="panelFieldCount"}

<span id='{$fields.id_citizen.name}' >
{$fields.id_citizen.value}
</span>
</td>
</tr>
<tr>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_BIRTHDATE' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_birthdate_field' >
{counter name="panelFieldCount"}

<span id='{$fields.birthdate.name}' >
{$fields.birthdate.value}
</span>
</td>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ISSUED_DATE' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_issued_date_field' >
{counter name="panelFieldCount"}

<span id='{$fields.issued_date.name}' >
{$fields.issued_date.value}
</span>
</td>
</tr>
<tr>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ACADEMIC_LEVEL' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_academic_level_field' >
{counter name="panelFieldCount"}

<span id='{$fields.academic_level.name}' >
{$fields.academic_level.value}
</span>
</td>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ISSUED_BY' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_issued_by_field' >
{counter name="panelFieldCount"}

<span id='{$fields.issued_by.name}' >
{$fields.issued_by.value}
</span>
</td>
</tr>
<tr>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_SPECIALIZED' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_specialized_field' >
{counter name="panelFieldCount"}

<span id='{$fields.specialized.name}' >
{$fields.specialized.value}
</span>
</td>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_PRIMARY_ADDRESS' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_address_street_field' >
{counter name="panelFieldCount"}

<span id='{$fields.address_street.name}' >
{$fields.address_street.value}
</span>
</td>
</tr>
<tr>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_TITLE' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_title_field' >
{counter name="panelFieldCount"}

<span id='{$fields.title.name}' >
{$fields.title.value}
</span>
</td>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ADDRESS_PERMANENT' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_address_permanent_field' >
{counter name="panelFieldCount"}

<span id='{$fields.address_permanent.name}' >
{$fields.address_permanent.value}
</span>
</td>
</tr>
<tr>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_BRANCH_ID' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_branch_id_field' >
{counter name="panelFieldCount"}

{ $fields.branch_id.options[$fields.branch_id.value]}
</td>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ADDRESS_TEMPORARY' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_address_temporary_field' >
{counter name="panelFieldCount"}

<span id='{$fields.address_temporary.name}' >
{$fields.address_temporary.value}
</span>
</td>
</tr>
<tr>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DEPARTMENT' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_department_field' >
{counter name="panelFieldCount"}

{ $fields.department.options[$fields.department.value]}
</td>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_MOBILE_PHONE' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_phone_mobile_field' >
{counter name="panelFieldCount"}

<span id='{$fields.phone_mobile.name}' >
{$fields.phone_mobile.value}
</span>
</td>
</tr>
<tr>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DATE_START' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_work_start_date_field' >
{counter name="panelFieldCount"}

<span id='{$fields.work_start_date.name}' >
{$fields.work_start_date.value}
</span>
</td>
<td width='16.67%' scope="row">
&nbsp;
</td>
<td width='33.33%' id='__field' >
</td>
</tr>
<tr>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DATE_END' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_work_end_date_field' >
{counter name="panelFieldCount"}

<span id='{$fields.work_end_date.name}' >
{$fields.work_end_date.value}
</span>
</td>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_INSURANCE_NUMBER' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_insurance_number_field' >
{counter name="panelFieldCount"}

<span id='{$fields.insurance_number.name}' >
{$fields.insurance_number.value}
</span>
</td>
</tr>
<tr>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_SENIORITY' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_seniority_field' >
{counter name="panelFieldCount"}

<span id='{$fields.seniority.name}' >
{$fields.seniority.value}
</span>
</td>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_INSURANCE_MONTH' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_insurance_month_field' >
{counter name="panelFieldCount"}

<span id='{$fields.insurance_month.name}' >
{$fields.insurance_month.value}
</span>
</td>
</tr>
<tr>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_MATERNITY_LEAVE' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_maternity_leave_field' >
{counter name="panelFieldCount"}

<span id='{$fields.maternity_leave.name}' >
{$fields.maternity_leave.value}
</span>
</td>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_AVARTAR_IMAGE' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_avartar_image_field' >
{counter name="panelFieldCount"}
{if !empty($fields.avartar_image.value)}<a class="avatar-pic" href="javascript:;" title="Xem hình" onclick="popupDialogImage('{$fields.avartar_image.value|validDownloadFile|htmlspecialchars}', {ldelim}title:'{$fields.name.value|htmlspecialchars}'{rdelim})"><img border="0" src="{$fields.avartar_image.value|validDownloadFile}" alt="{$fields.avartar_image.value|basename}" height="50" /></a>{/if}	
</td>
</tr>
<tr>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_EMAIL' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_email1_field' >
{counter name="panelFieldCount"}

<span id='{$fields.email1.name}' >
{$fields.email1.value}
</span>
</td>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DATE_ENTERED' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_date_entered_field' >
{counter name="panelFieldCount"}
{$fields.date_entered.value} {$APP.LBL_BY} {$fields.created_by_name.value}	
</td>
</tr>
<tr>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ATTACHMENT_FILES' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_notes_field' >
{counter name="panelFieldCount"}
<div id="attachment_files">{$ATTACHMENTS}</div>	
</td>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DATE_MODIFIED' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_date_modified_field' >
{counter name="panelFieldCount"}
{$fields.date_modified.value} {$APP.LBL_BY} {$fields.modified_by_name.value}	
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("DEFAULT").style.display='none';</script>
{/if}
<div id="LBL_PROBATION_CONTRACT" class="detailview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_PROBATION_CONTRACT');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_PROBATION_CONTRACT_IMG" border="0" />
{incomCRM_translate label='LBL_PROBATION_CONTRACT' module='Employees'}
</a>
</h4>
<div class="detail view {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_PROBATION_CONTRACT_GROUP" style="display:none">
<table id='detailpanel_2' cellspacing='{$gridline}'>
<tr>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CONTRACT_NUMBER' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_contract_number_field' >
{counter name="panelFieldCount"}

<span id='{$fields.contract_number.name}' >
{$fields.contract_number.value}
</span>
</td>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_SALARY' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_salary_field' >
{counter name="panelFieldCount"}

<span id='{$fields.salary.name}' >
{$fields.salary.value}
</span>
</td>
</tr>
<tr>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CONTRACT_DATE_START' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_contract_date_start_field' >
{counter name="panelFieldCount"}

<span id='{$fields.contract_date_start.name}' >
{$fields.contract_date_start.value}
</span>
</td>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CONTRACT_DATE_END' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_contract_date_end_field' >
{counter name="panelFieldCount"}

<span id='{$fields.contract_date_end.name}' >
{$fields.contract_date_end.value}
</span>
</td>
</tr>
<tr>
<td width='16.67%' scope="row">
Ngày còn lại
</td>
<td width='33.33%' id='__field' >
{counter name="panelFieldCount"}
{$DAY_REAMINING}	
</td>
<td width='16.67%' scope="row">
&nbsp;
</td>
<td width='33.33%' id='__field' >
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_PROBATION_CONTRACT").style.display='none';</script>
{/if}
<div id="LBL_12M_CONTRACT" class="detailview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_12M_CONTRACT');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_12M_CONTRACT_IMG" border="0" />
{incomCRM_translate label='LBL_12M_CONTRACT' module='Employees'}
</a>
</h4>
<div class="detail view {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_12M_CONTRACT_GROUP" style="display:none">
<table id='detailpanel_3' cellspacing='{$gridline}'>
<tr>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CONTRACT_NUMBER' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_contract12_number_field' >
{counter name="panelFieldCount"}

<span id='{$fields.contract12_number.name}' >
{$fields.contract12_number.value}
</span>
</td>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_SALARY' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_contract12_salary_field' >
{counter name="panelFieldCount"}

<span id='{$fields.contract12_salary.name}' >
{$fields.contract12_salary.value}
</span>
</td>
</tr>
<tr>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CONTRACT_DATE_START' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_contract12_date_start_field' >
{counter name="panelFieldCount"}

<span id='{$fields.contract12_date_start.name}' >
{$fields.contract12_date_start.value}
</span>
</td>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_APPENDIX_DATE' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_appendix_date_field' >
{counter name="panelFieldCount"}

<span id='{$fields.appendix_date.name}' >
{$fields.appendix_date.value}
</span>
</td>
</tr>
<tr>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CONTRACT_DATE_END' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_contract12_date_end_field' >
{counter name="panelFieldCount"}

<span id='{$fields.contract12_date_end.name}' >
{$fields.contract12_date_end.value}
</span>
</td>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_APPENDIX_NOTE' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_description_field' >
{counter name="panelFieldCount"}

{$fields.description.value|url2html|nl2br}
</td>
</tr>
<tr>
<td width='16.67%' scope="row">
Ngày còn lại
</td>
<td width='33.33%' id='__field' >
{counter name="panelFieldCount"}
{$CT12_DAY_REAMINING}	
</td>
<td width='16.67%' scope="row">
&nbsp;
</td>
<td width='33.33%' id='__field' >
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_12M_CONTRACT").style.display='none';</script>
{/if}
<div id="LBL_UNSPECIFIED_CONTRACT" class="detailview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_UNSPECIFIED_CONTRACT');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_UNSPECIFIED_CONTRACT_IMG" border="0" />
{incomCRM_translate label='LBL_UNSPECIFIED_CONTRACT' module='Employees'}
</a>
</h4>
<div class="detail view {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_UNSPECIFIED_CONTRACT_GROUP" style="display:none">
<table id='detailpanel_4' cellspacing='{$gridline}'>
<tr>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CONTRACT_NUMBER' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_contract_unspecified_number_field' >
{counter name="panelFieldCount"}

<span id='{$fields.contract_unspecified_number.name}' >
{$fields.contract_unspecified_number.value}
</span>
</td>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_SALARY' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_contract_unspecified_salary_field' >
{counter name="panelFieldCount"}

<span id='{$fields.contract_unspecified_salary.name}' >
{$fields.contract_unspecified_salary.value}
</span>
</td>
</tr>
<tr>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CONTRACT_DATE_START' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_contract_unspecified_date_start_field' >
{counter name="panelFieldCount"}

<span id='{$fields.contract_unspecified_date_start.name}' >
{$fields.contract_unspecified_date_start.value}
</span>
</td>
<td width='16.67%' scope="row">
&nbsp;
</td>
<td width='33.33%' id='__field' >
</td>
</tr>
<tr>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_APPENDIX_DATE' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_contract_unspecified_appendix_date_field' >
{counter name="panelFieldCount"}

<span id='{$fields.contract_unspecified_appendix_date.name}' >
{$fields.contract_unspecified_appendix_date.value}
</span>
</td>
<td width='16.67%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_APPENDIX_NOTE' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='33.33%' id='_appendix_note_field' >
{counter name="panelFieldCount"}

{$fields.appendix_note.value|url2html|nl2br}
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_UNSPECIFIED_CONTRACT").style.display='none';</script>
{/if}

</section>