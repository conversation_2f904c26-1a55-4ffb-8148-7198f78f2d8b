
<div class="form-control">

<form action="index.php" method="POST" name="{$form_name}" id="{$form_id}" class="_edit-{$instanceName}" {$enctype}>
<input type="hidden" name="module" value="{$module}" />
<input type="hidden" name="action" id="action" />
{if isset($smarty.request.isDuplicate) && $smarty.request.isDuplicate eq "true"}
<input type="hidden" name="record" id="record" value="" />
<input type="hidden" name="duplicateSave" value="true" />
{else}
<input type="hidden" name="record" id="record" value="{$fields.id.value}" />
{/if}
<input type="hidden" name="return_module" value="{$smarty.request.return_module}" />
<input type="hidden" name="return_action" value="{if $smarty.request.return_action eq 'LoadTabSubpanels'}DetailView{else}{$smarty.request.return_action}{/if}" />
<input type="hidden" name="return_id" value="{$smarty.request.return_id}" />
<input type="hidden" name="isDuplicate" value="false" />
<input type="hidden" name="module_tab" /> 
<input type="hidden" name="contact_role" />
{if !empty($smarty.request.return_module)}
<input type="hidden" name="relate_to" value="{if $smarty.request.return_relationship}{$smarty.request.return_relationship}{else}{$smarty.request.return_module}{/if}" />
<input type="hidden" name="relate_id" value="{$smarty.request.return_id}" />
{/if}
<input type="hidden" name="offset" value="{$offset}" />
{if empty($fields.id.value)}
<input type="hidden" name="_notify_random" value="{php}echo create_guid();{/php}" />
{/if}
<input type="hidden" name="isFromEditView" value="{$module}" />
<input type="hidden" name="isSaveAndNew" value="false" />
<input type="hidden" name="opportunity_id" id="opportunity_id" value="{$fields.opportunity_id.value}" />
<input type="hidden" name="opportunity_name" id="opportunity_name" value="{$fields.opportunity_name.value}" />
<input type="hidden" name="name" id="name" value="{$fields.name.value}" />
<input type="hidden" name="job_id" id="job_id" value="{$fields.job_id.value}" />
<input type="hidden" name="working_time_by" id="working_time_by" value="{$fields.working_time_by.value}" />
<input type="hidden" name="task_relate_id" id="task_relate_id" value="{$fields.task_relate_id.value}" />
<input type="hidden" name="to_pdf" value="1" />
<input type="hidden" name="is_ajax_call" value="1" />
<input type="hidden" name="is_editpopup" value="1" />
<input type="hidden" name="is_editsmart" value="1" />
<input type="hidden" name="call_from_ajax" value="{$smarty.request.is_ajax_call}" />
<input type="hidden" name="return_url" value="{$smarty.request.return_url|escape:'url'}" />
<input type="hidden" name="return_selected_tab" value="{$smarty.request.return_selected_tab}" />
<table width="100%" cellpadding="0" cellspacing="0" border="0">
<tr>
<td class="buttons">
{if $bean->aclAccess("save")}<input title="{$APP.LBL_SAVE_BUTTON_TITLE}" accessKey="{$APP.LBL_SAVE_BUTTON_KEY}" class="button" onclick="removeFromValidate('{$form_name}', 'next_step'); if( this.form.status.value == 'Completed' ) addToValidate('{$form_name}', 'next_step', 'text', true, '{incomCRM_translate label='LBL_NEXT_STEP' module=$module}'); this.form.action.value='Save'; return luloSmart.save( this.form, true, true {if empty($smarty.request.is_ajax_call)},{ldelim}autoReload:false, showSmart:true, normalEdit:true{rdelim}{/if});" type="button" name="button" value="{$APP.LBL_SAVE_BUTTON_LABEL}" />{/if} 
<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="{if !empty($smarty.request.return_module) && !empty($smarty.request.return_id)}this.form.return_module.value='{$smarty.request.return_module}'; this.form.return_action.value='DetailView'; this.form.return_id.value='{$smarty.request.return_id}'; {/if}luloSmart.close(null, this.form);" type="button" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> 
{if !empty($isRecurrence)}<input type="button" class="button highlight" name="btn" value=" {$MOD.LBL_SETUP_RECURRENCE} " onclick="recurrences.init(this.form, null, null, callbackUpdateRecurrence)" /><textarea class="hidden" name="recurrence_data"></textarea><textarea class="hidden" name="recurrence_text"></textarea><div class="recurrences pb-5" id="recurrenceText">{$fields.recurrence_text.value|from_html|url2html}</div>{/if}{if !empty($isTodoList)}<div id="cycle_type_grp" class="btn-group my-5 pl-10"><span>{$MOD.LBL_CHOOSE_CYCLE_TYPE}:</span> {html_radios name="cycle_type" options=$cycle_type_dom selected="0" onclick="changeCycleType(this);"}</div>{/if}
</td>
</tr>
</table>
{incomCRM_include include=$includes}
<div id="DEFAULT" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="DEFAULT_GROUP" style="display:block">
<div class="row">
<label class="col-4 col-ss-12" id='subject_label'>
{capture name="label" assign="label}
{incomCRM_translate label='LBL_SUBJECT' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</label>
<div id='_subject_field' class="col-8 col-ss-12 ">
{counter name="panelFieldCount"}

{if strlen($fields.subject.value) <= 0}
{assign var="value" value=$fields.subject.default_value }
{else}
{assign var="value" value=$fields.subject.value }
{/if}
{if isTypeNumber($fields.subject.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.subject.name}' id='{$fields.subject.name}' size='30' maxlength='250' value='{$value}' title='' tabindex='100'  /> 
</div>
</div>
<div class="row">
<label class="col-4 col-ss-12" id='content_label'>
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTENT' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</label>
<div id='_content_field' class="col-8 col-ss-12 yui-ac-format">
{counter name="panelFieldCount"}

{if empty($fields.content.value)}
{assign var="value" value=$fields.content.default_value }
{else}
{assign var="value" value=$fields.content.value }
{/if}
<textarea id="{$fields.content.name}" name="{$fields.content.name}" rows="3" cols="20" title='' tabindex="101"   data-autoresize="true" class="sqsEnabled">{$value}</textarea>
</div>
</div>
<div class="row">
<label class="col-4 col-ss-12" id='next_step_label'>
{capture name="label" assign="label}
{incomCRM_translate label='LBL_NEXT_STEP' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id='_next_step_field' class="col-8 col-ss-12 ">
{counter name="panelFieldCount"}

{if empty($fields.next_step.value)}
{assign var="value" value=$fields.next_step.default_value }
{else}
{assign var="value" value=$fields.next_step.value }
{/if}
<textarea id="{$fields.next_step.name}" name="{$fields.next_step.name}" rows="3" cols="20" title='' tabindex="102"   data-autoresize="true">{$value}</textarea>
</div>
</div>
<div class="row">
<label class="col-4 col-ss-12" id='transactions_label'>
{capture name="label" assign="label}
{incomCRM_translate label='LBL_TRANSACTIONS' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</label>
<div id='_transactions_field' class="col-8 col-ss-12 ">
{counter name="panelFieldCount"}

<select name="{$fields.transactions.name}" id="{$fields.transactions.name}" title='' tabindex="103"  onchange="changeTransactionTask(this.form); changeTextTransactions(this.form);" >
{if isset($fields.transactions.value) && $fields.transactions.value != ''}
{html_options options=$fields.transactions.options selected=$fields.transactions.value}
{else}
{html_options options=$fields.transactions.options selected=$fields.transactions.default}
{/if}
</select>
</div>
</div>
<div class="row">
<label class="col-4 col-ss-12" id='user_err_name_label'>
{capture name="label" assign="label}
{incomCRM_translate label='LBL_USER_ERR_NAME' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id='_user_err_name_field' class="col-8 col-ss-12 yui-ac-format">
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{else}{/if}<input type="hidden" name="{$fields.user_err_name.id_name}" id="{$fields.user_err_name.id_name}" value="{$fields.user_err_id.value}" />
<input type="text" name="{$fields.user_err_name.name}" class="sqsEnabled" tabindex="104" id="{$fields.user_err_name.name}" size="16" value="{$fields.user_err_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{else}{/if}
<div class="{if !empty($isSmartView)}col-auto col-s2-12{else}grp-buttons{/if}"><input type="button" name="btn_{$fields.user_err_name.name}" id="btn_{$fields.user_err_name.name}" tabindex="104" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.user_err_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"form_EditSmart_Tasks","field_to_name_array":{"id":"user_err_id","last_name":"user_err_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.user_err_name.name}" id="btn_clr_{$fields.user_err_name.name}" tabindex="104" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.user_err_name.name}.value=""; this.form.{$fields.user_err_name.id_name}.value=""; this.form.{$fields.user_err_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
</div>{if !empty($isSmartView)}</div>{else}{/if}<script type="text/javascript">enableQS(false);</script>
</div>
</div>
<div class="row">
<label class="col-4 col-ss-12" id='description_error_label'>
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DESCRIPTION_ERROR' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id='_description_error_field' class="col-8 col-ss-12 ">
{counter name="panelFieldCount"}

{if empty($fields.description_error.value)}
{assign var="value" value=$fields.description_error.default_value }
{else}
{assign var="value" value=$fields.description_error.value }
{/if}
<textarea id="{$fields.description_error.name}" name="{$fields.description_error.name}" rows="3" cols="20" title='' tabindex="105"   data-autoresize="true">{$value}</textarea>
</div>
</div>
<div class="row">
<label class="col-4 col-ss-12" id='status_label'>
{capture name="label" assign="label}
{incomCRM_translate label='LBL_STATUS' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</label>
<div id='_status_field' class="col-8 col-ss-12 ">
{counter name="panelFieldCount"}

<select name="{$fields.status.name}" id="{$fields.status.name}" title='' tabindex="106"  >
{if isset($fields.status.value) && $fields.status.value != ''}
{html_options options=$fields.status.options selected=$fields.status.value}
{else}
{html_options options=$fields.status.options selected=$fields.status.default}
{/if}
</select>
</div>
</div>
<div class="row">
<label class="col-4 col-ss-12" id='date_start_label'>
{capture name="label" assign="label}
{incomCRM_translate label='LBL_START_DATE' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</label>
<div id='_date_start_field' class="col-8 col-ss-12 ">
{counter name="panelFieldCount"}

<table border="0" cellpadding="0" cellspacing="0" class="datetimecombo">
<tr>
<td nowrap>
<input autocomplete="off" type="text" id="{$fields.date_start.name}_date" value="{$fields[$fields.date_start.name].value}" size="11" maxlength="10" title='' tabindex="107" onblur="combo_{$fields.date_start.name}.update(); callbackCalcWorkTimeTask(-1);" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.date_start.name}_trigger" align="absmiddle" />&nbsp;
<br/>
<div id="{$fields.date_start.name}_time_section" class="datetime-section"></div>
</td>
</tr>
</table>
<input type="hidden" id="{$fields.date_start.name}" name="{$fields.date_start.name}" value="{$fields[$fields.date_start.name].value}" />
<script type="text/javascript">
var combo_{$fields.date_start.name} = new Datetimecombo("{$fields[$fields.date_start.name].value}", "{$fields.date_start.name}", "{$TIME_FORMAT}", '107', '', '{$fields[$fields.date_start.name_flag].value|default:0}', '1', '1'); 
//Render the remaining widget fields
text = combo_{$fields.date_start.name}.html('callbackCalcWorkTimeTask(-1);');
document.getElementById('{$fields.date_start.name}_time_section').innerHTML = text;

//Call eval on the update function to handle updates to calendar picker object
eval(combo_{$fields.date_start.name}.jsscript('callbackCalcWorkTimeTask(-1);'));
</script>
<script type="text/javascript">
function update_{$fields.date_start.name}_available() {ldelim}
	YAHOO.util.Event.onAvailable("{$fields.date_start.name}_date", this.handleOnAvailable, this); 
{rdelim}

update_{$fields.date_start.name}_available.prototype.handleOnAvailable = function(me) {ldelim}
		Calendar.setup ({ldelim}
		onUpdate : update_{$fields.date_start.name},
		inputField : "{$fields.date_start.name}_date",
		ifFormat : "{$CALENDAR_FORMAT}",
		daFormat : "{$CALENDAR_FORMAT}",
		button : "{$fields.date_start.name}_trigger",
		singleClick : true,
		step : 1,
		weekNumbers:false
	{rdelim});
	
	//Call update for first time to round hours and minute values
	combo_{$fields.date_start.name}.update();
{rdelim}

var obj_{$fields.date_start.name} = new update_{$fields.date_start.name}_available(); 
</script>
</div>
</div>
<div class="row">
<label class="col-4 col-ss-12" id='date_due_label'>
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DUE_DATE' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</label>
<div id='_date_due_field' class="col-8 col-ss-12 ">
{counter name="panelFieldCount"}

<table border="0" cellpadding="0" cellspacing="0" class="datetimecombo">
<tr>
<td nowrap>
<input autocomplete="off" type="text" id="{$fields.date_due.name}_date" value="{$fields[$fields.date_due.name].value}" size="11" maxlength="10" title='' tabindex="108" onblur="combo_{$fields.date_due.name}.update(); callbackCalcWorkTimeTask(true);" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.date_due.name}_trigger" align="absmiddle" />&nbsp;
<br/>
<div id="{$fields.date_due.name}_time_section" class="datetime-section"></div>
</td>
</tr>
</table>
<input type="hidden" id="{$fields.date_due.name}" name="{$fields.date_due.name}" value="{$fields[$fields.date_due.name].value}" />
<script type="text/javascript">
var combo_{$fields.date_due.name} = new Datetimecombo("{$fields[$fields.date_due.name].value}", "{$fields.date_due.name}", "{$TIME_FORMAT}", '108', '', '{$fields[$fields.date_due.name_flag].value|default:0}', '1', '1'); 
//Render the remaining widget fields
text = combo_{$fields.date_due.name}.html('callbackCalcWorkTimeTask(true);');
document.getElementById('{$fields.date_due.name}_time_section').innerHTML = text;

//Call eval on the update function to handle updates to calendar picker object
eval(combo_{$fields.date_due.name}.jsscript('callbackCalcWorkTimeTask(true);'));
</script>
<script type="text/javascript">
function update_{$fields.date_due.name}_available() {ldelim}
	YAHOO.util.Event.onAvailable("{$fields.date_due.name}_date", this.handleOnAvailable, this); 
{rdelim}

update_{$fields.date_due.name}_available.prototype.handleOnAvailable = function(me) {ldelim}
		Calendar.setup ({ldelim}
		onUpdate : update_{$fields.date_due.name},
		inputField : "{$fields.date_due.name}_date",
		ifFormat : "{$CALENDAR_FORMAT}",
		daFormat : "{$CALENDAR_FORMAT}",
		button : "{$fields.date_due.name}_trigger",
		singleClick : true,
		step : 1,
		weekNumbers:false
	{rdelim});
	
	//Call update for first time to round hours and minute values
	combo_{$fields.date_due.name}.update();
{rdelim}

var obj_{$fields.date_due.name} = new update_{$fields.date_due.name}_available(); 
</script>
</div>
</div>
<div class="row">
<label class="col-4 col-ss-12" id='date_meeting_label'>
{capture name="label" assign="label}
{incomCRM_translate label='LBL_MEETING_DATE' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id='_date_meeting_field' class="col-8 col-ss-12 ">
{counter name="panelFieldCount"}

<table border="0" cellpadding="0" cellspacing="0" class="datetimecombo">
<tr>
<td nowrap>
<input autocomplete="off" type="text" id="{$fields.date_meeting.name}_date" value="{$fields[$fields.date_meeting.name].value}" size="11" maxlength="10" title='' tabindex="109" onblur="combo_{$fields.date_meeting.name}.update(); " class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.date_meeting.name}_trigger" align="absmiddle" />&nbsp;
<br/>
<div id="{$fields.date_meeting.name}_time_section" class="datetime-section"></div>
</td>
</tr>
</table>
<input type="hidden" id="{$fields.date_meeting.name}" name="{$fields.date_meeting.name}" value="{$fields[$fields.date_meeting.name].value}" />
<script type="text/javascript">
var combo_{$fields.date_meeting.name} = new Datetimecombo("{$fields[$fields.date_meeting.name].value}", "{$fields.date_meeting.name}", "{$TIME_FORMAT}", '109', '', '{$fields[$fields.date_meeting.name_flag].value|default:0}', '1', '1'); 
//Render the remaining widget fields
text = combo_{$fields.date_meeting.name}.html('');
document.getElementById('{$fields.date_meeting.name}_time_section').innerHTML = text;

//Call eval on the update function to handle updates to calendar picker object
eval(combo_{$fields.date_meeting.name}.jsscript(''));
</script>
<script type="text/javascript">
function update_{$fields.date_meeting.name}_available() {ldelim}
	YAHOO.util.Event.onAvailable("{$fields.date_meeting.name}_date", this.handleOnAvailable, this); 
{rdelim}

update_{$fields.date_meeting.name}_available.prototype.handleOnAvailable = function(me) {ldelim}
		Calendar.setup ({ldelim}
		onUpdate : update_{$fields.date_meeting.name},
		inputField : "{$fields.date_meeting.name}_date",
		ifFormat : "{$CALENDAR_FORMAT}",
		daFormat : "{$CALENDAR_FORMAT}",
		button : "{$fields.date_meeting.name}_trigger",
		singleClick : true,
		step : 1,
		weekNumbers:false
	{rdelim});
	
	//Call update for first time to round hours and minute values
	combo_{$fields.date_meeting.name}.update();
{rdelim}

var obj_{$fields.date_meeting.name} = new update_{$fields.date_meeting.name}_available(); 
</script>
</div>
</div>
<div class="row">
<label class="col-4 col-ss-12" id='processing_time_label'>
{capture name="label" assign="label}
{incomCRM_translate label='LBL_PROCESSING_TIME' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id='_processing_time_field' class="col-8 col-ss-12 ">
{counter name="panelFieldCount"}

{if strlen($fields.processing_time.value) <= 0}
{assign var="value" value=$fields.processing_time.default_value }
{else}
{assign var="value" value=$fields.processing_time.value }
{/if}
{if isTypeNumber($fields.processing_time.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.processing_time.name}' id='{$fields.processing_time.name}' size='30' maxlength='4' value='{$value}' title='' tabindex='110'  class="input-cal" onblur="callbackCalcWorkTimeTask(false);" onkeypress="return blockNonNumbers(this, event);" /> 
{$MOD.LBL_TIME_UNIT}
</div>
</div>
<div class="row">
<label class="col-4 col-ss-12" id='parent_name_label'>
{capture name="label" assign="label}
{incomCRM_translate label='LBL_LIST_RELATED_TO' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</label>
<div id='_parent_name_field' class="col-8 col-ss-12 yui-ac-format">
{counter name="panelFieldCount"}

{if empty($fields.parent_name.options[$fields.parent_type.value])}
{assign var="keepParent" value = 0}
{else}
{assign var="keepParent value = 1}
{/if}
{if !empty($isSmartView)}<div class="row">{/if}
<div class="{if !empty($isSmartView)}col-auto col-ss-12{else}grp-parent-type{/if}" >
<select name='parent_type' tabindex="111" id='parent_type' title='' onchange='document.{$form_name}.{$fields.parent_name.name}.value="";document.{$form_name}.parent_id.value=""; changeQS(); checkParentType(document.{$form_name}.parent_type.value, document.{$form_name}.btn_{$fields.parent_name.name});' style='width:auto;'>
{html_options options=$fields.parent_name.options selected=$fields.parent_type.value}
</select>
</div>
<div class="{if !empty($isSmartView)}col- col-s2-12 px-0 pl-ss-10 pr-s2-10{else}grp-parent-type{/if}">
<input type="hidden" name="{$fields.parent_id.name}" id="{$fields.parent_id.name}" {if $keepParent}value="{$fields.parent_id.value}"{/if} />
<input type="text" name="{$fields.parent_name.name}" id="{$fields.parent_name.name}" class="sqsEnabled" tabindex="111" size="16" {if $keepParent}value="{$fields.parent_name.value}"{/if} autocomplete="off" />
{if !empty($isSmartView)}</div>{/if}
<div class="{if !empty($isSmartView)}col-auto col-s2-12{else}grp-buttons{/if}"><input type="button" name="btn_{$fields.parent_name.name}" id="btn_{$fields.parent_name.name}" tabindex="111" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup(document.{$form_name}.parent_type.value, 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"form_EditSmart_Tasks","field_to_name_array":{"id":"parent_id","name":"parent_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.parent_name.name}" id="btn_clr_{$fields.parent_name.name}" tabindex="111" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick="this.form.{$fields.parent_name.name}.value = ''; this.form.{$fields.parent_id.name}.value = ''; this.form.{$fields.parent_name.name}.focus();" value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
</div>{if empty($isSmartView)}</div>{/if}{if !empty($isSmartView)}</div>{/if}
<script type="text/javascript">
function changeQS() {ldelim}
	var form_name = "{$form_name}";
	changeParentTypeQS( form_name, "parent_name", "parent_id", document.forms[form_name].elements["parent_type"].value );
{rdelim}
YAHOO.util.Event.onDOMReady(changeQS);
</script>
{literal}
<script type="text/javascript">var disabledModules=[];</script>
{/literal}
</div>
</div>
<div class="row">
<label class="col-4 col-ss-12" id='main_name_label'>
{capture name="label" assign="label}
{incomCRM_translate label='LBL_MAIN_TASK_NAME' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id='_main_name_field' class="col-8 col-ss-12 yui-ac-format">
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{else}{/if}<input type="hidden" name="{$fields.main_name.id_name}" id="{$fields.main_name.id_name}" value="{$fields.main_id.value}" />
<input type="text" name="{$fields.main_name.name}" class="sqsEnabled" tabindex="112" id="{$fields.main_name.name}" size="16" value="{$fields.main_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{else}{/if}
<div class="{if !empty($isSmartView)}col-auto col-s2-12{else}grp-buttons{/if}"><input type="button" name="btn_{$fields.main_name.name}" id="btn_{$fields.main_name.name}" tabindex="112" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.main_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"form_EditSmart_Tasks","field_to_name_array":{"id":"main_id","subject":"main_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.main_name.name}" id="btn_clr_{$fields.main_name.name}" tabindex="112" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.main_name.name}.value=""; this.form.{$fields.main_name.id_name}.value=""; this.form.{$fields.main_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
</div>{if !empty($isSmartView)}</div>{else}{/if}<script type="text/javascript">enableQS(false);</script>
</div>
</div>
<div class="row">
<label class="col-4 col-ss-12" id='assigned_user_name_label'>
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</label>
<div id='_assigned_user_name_field' class="col-8 col-ss-12 ">
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{else}{/if}<input type="hidden" name="{$fields.assigned_user_name.id_name}" id="{$fields.assigned_user_name.id_name}" value="{$fields.assigned_user_id.value}" />
<input type="text" name="{$fields.assigned_user_name.name}" class="sqsEnabled" tabindex="113" id="{$fields.assigned_user_name.name}" size="16" value="{$fields.assigned_user_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{else}{/if}
<div class="{if !empty($isSmartView)}col-auto col-s2-12{else}grp-buttons{/if}"><input type="button" name="btn_{$fields.assigned_user_name.name}" id="btn_{$fields.assigned_user_name.name}" tabindex="113" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.assigned_user_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"form_EditSmart_Tasks","field_to_name_array":{"id":"assigned_user_id","user_name":"assigned_user_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.assigned_user_name.name}" id="btn_clr_{$fields.assigned_user_name.name}" tabindex="113" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.assigned_user_name.name}.value=""; this.form.{$fields.assigned_user_name.id_name}.value=""; this.form.{$fields.assigned_user_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
</div>{if !empty($isSmartView)}</div>{else}{/if}<script type="text/javascript">enableQS(false);</script>
</div>
</div>
<div class="row">
<label class="col-4 col-ss-12" id='group_name_label'>
{capture name="label" assign="label}
{incomCRM_translate label='LBL_GROUP_NAME' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id='_group_name_field' class="col-8 col-ss-12 ">
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{else}{/if}<input type="hidden" name="{$fields.group_name.id_name}" id="{$fields.group_name.id_name}" value="{$fields.group_id.value}" />
<input type="text" name="{$fields.group_name.name}" class="sqsEnabled" tabindex="114" id="{$fields.group_name.name}" size="16" value="{$fields.group_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{else}{/if}
<div class="{if !empty($isSmartView)}col-auto col-s2-12{else}grp-buttons{/if}"><input type="button" name="btn_{$fields.group_name.name}" id="btn_{$fields.group_name.name}" tabindex="114" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.group_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"form_EditSmart_Tasks","field_to_name_array":{"id":"group_id","name":"group_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.group_name.name}" id="btn_clr_{$fields.group_name.name}" tabindex="114" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.group_name.name}.value=""; this.form.{$fields.group_name.id_name}.value=""; this.form.{$fields.group_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
</div>{if !empty($isSmartView)}</div>{else}{/if}<script type="text/javascript">enableQS(false);</script>
</div>
</div>
<div class="row">
<label class="col-4 col-ss-12" id='approval_level_name_label'>
{capture name="label" assign="label}
{incomCRM_translate label='LBL_APPROVAL_LEVEL_NAME' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id='_approval_level_name_field' class="col-8 col-ss-12 ">
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{else}{/if}<input type="hidden" name="{$fields.approval_level_name.id_name}" id="{$fields.approval_level_name.id_name}" value="{$fields.approval_level_id.value}" />
<input type="text" name="{$fields.approval_level_name.name}" class="sqsEnabled" tabindex="115" id="{$fields.approval_level_name.name}" size="16" value="{$fields.approval_level_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{else}{/if}
<div class="{if !empty($isSmartView)}col-auto col-s2-12{else}grp-buttons{/if}"><input type="button" name="btn_{$fields.approval_level_name.name}" id="btn_{$fields.approval_level_name.name}" tabindex="115" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.approval_level_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"form_EditSmart_Tasks","field_to_name_array":{"id":"approval_level_id","name":"approval_level_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.approval_level_name.name}" id="btn_clr_{$fields.approval_level_name.name}" tabindex="115" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.approval_level_name.name}.value=""; this.form.{$fields.approval_level_name.id_name}.value=""; this.form.{$fields.approval_level_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
</div>{if !empty($isSmartView)}</div>{else}{/if}<script type="text/javascript">enableQS(false);</script>
</div>
</div>
<div class="row">
<label class="col-4 col-ss-12" id='share_users_label'>
{capture name="label" assign="label}
{incomCRM_translate label='LBL_SHARE_USERS' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id='_share_users_field' class="col-8 col-ss-12 ">
{counter name="panelFieldCount"}
<input type="hidden" id="{$fields.share_users.name}_multiselect" name="{$fields.share_users.name}_multiselect" value="true" />
{multienum_to_array string=$fields.share_users.value default=$fields.share_users.default assign="values"}
<div style="" id="{$fields.share_users.name}_multi_grp" class="multi-enum-grp multi-enum-grp-default">
{if isset($fields.share_users.groupBy) and isset($fields.share_users.groupDom)}
<label id="_grp_choose">
<select name="_{$fields.share_users.name}_grp" size="1" onchange="changeFilterSelectedGroup(this.value, '{$fields.share_users.name}_multi_grp' )">
{html_options options=$fields.share_users.groupDom}
</select>
</label>
{/if}
<label id="_grp_all_" class="_grp_all_">
<input type="checkbox" value="__all__" onclick="checkedChangeSel(this, '{$fields.share_users.name}', true)" />
-- {$APP.LBL_LINK_ALL} --
</label>
{counter name="rowCount" start=0 print=false assign="rowCount"}
{foreach from=$fields.share_users.options item=option key=value}
{if $value}
<label id="_grp_{if isset($fields.share_users.groupBy) and isset($fields.share_users.groupBy[$value])}{ $fields.share_users.groupBy[$value]}{/if}" style="white-space: nowrap">
<input type="checkbox" id="{$fields.share_users.name}_checkbox{$rowCount}" name="{$fields.share_users.name}[]" value="{$value}" {if in_array(strval($value), $values)}checked="checked"{/if}  /> {$option}
</label>
{counter name="rowCount"}
{/if}
{/foreach}
</div>
</div>
</div>
<div class="row">
<label class="col-4 pr-0" id='reminder_time_label'>
{capture name="label" assign="label}
{incomCRM_translate label='LBL_REMINDER' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id='_reminder_time_field' class="col-8  ">
{counter name="panelFieldCount"}
{if $fields.reminder_checked.value eq "1" or empty($fields.id.value)}{assign var="REMINDER_TIME_DISPLAY" value="inline"}{assign var="REMINDER_CHECKED" value="checked"}{else}{assign var="REMINDER_TIME_DISPLAY" value="none"}{assign var="REMINDER_CHECKED" value=""}{/if}<input tabindex="117"  name="reminder_checked" type="hidden" value="0" /><input tabindex="117"  name="reminder_checked" onclick='toggleDisplay("should_remind_list");' type="checkbox" class="checkbox" value="1" {$REMINDER_CHECKED} /> <div id="should_remind_list" style="display:{$REMINDER_TIME_DISPLAY}">{$fields.reminder_time.value}</div>
</div>
</div>
<div class="row">
<label class="col-4 pr-0" id='priority_label'>
{capture name="label" assign="label}
{incomCRM_translate label='LBL_PRIORITY' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id='_priority_field' class="col-8  ">
{counter name="panelFieldCount"}
<input tabindex="118"  name="priority" type="hidden" value="" /><input tabindex="118"  name="priority" id="priority" type="checkbox" class="checkbox" value="Urgent" {if $fields.priority.value eq "Urgent"}checked{/if} />
</div>
</div>
<div class="row">
<label class="col-4 col-ss-12" id='comment_label'>
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ATTACHMENT_FILES' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id='_comment_field' class="col-8 col-ss-12 ">
{counter name="panelFieldCount"}
<div id="attachment_files">{$ATTACHMENTS}</div>
</div>
</div>
</div>
</div>
{if $panelFieldCount == 0}
<script type="text/javascript">document.getElementById("DEFAULT").style.display='none';</script>
{/if}

</form>
{$set_focus_block}
<!-- Begin Meta-Data Javascript -->
<script type="text/javascript">
var _taskFormName = "{$form_name}";
{if !empty($isTodoList)}
var qsContentName = "{$form_name}_content";
{literal}
function pre_check_form(formname) {
	if( typeof(document.forms[formname]["module"]) == "undefined" || document.forms[formname]["module"].value != "Tasks" ) return true;
	// check upload file size
	if( !LULO.inputFiles.checkFileSize(formname) ) {
		alert( LULO.inputFiles.getAlertMsg() );
		return false;
	}
	return true;
}
var generateRequest_Todo = function(sQuery) {
		var sqs = this.sqs, val = document.forms[_taskFormName].elements["cycle_type"].value;
		if( val == "-1" ) sqs["additions"] = {"cycle_type":""};
		else if( val != "0" ) sqs["additions"] = {"cycle_type":val};
		var out = incomCRM.util.paramsToUrl({to_pdf:"true", module:"Home", action:"quicksearchQuery", data:encodeURIComponent(YAHOO.lang.JSON.stringify(sqs)), query:sQuery});
		return out;
	};

if(typeof sqs_objects == "undefined"){var sqs_objects = new Array;}
sqs_objects[qsContentName] = {"form":"{/literal}{$form_name}{literal}","method":"query","modules":["Todo_Lists"],"group":"or", "field_list":["name", "id", "processing_time", "group_id", "group_name", "job_type", "cycle_type", "name"], "populate_list":["content", "job_id", "processing_time", "group_id", "group_name", "job_type_x", "cycle_type_x", "name"], "conditions":[{"name":"name","op":"contains","end":"%","value":""}], "required_list":["content"], "order":"cycle_type,job_type,code","limit":30,"maxItems":30, "disable":true, "oConfigs":{"formatResult":formatResultTodoLists, generateRequest:generateRequest_Todo}, "post_onblur_function":"postSelectedTodoList", "no_match_text":incomCRM.language.get("app_strings","ERR_SQS_NO_MATCH")};

function postSelectedTodoList( collection_extended, qs_id ) {
	callbackCalcWorkTimeTask(false);
}
function changeCycleType( elem ) {
	$("div#cycle_type_grp > label").removeClass("active");
	$(elem).parent().addClass("active");
	//
	if( elem.value == "0" ) {
		sqs_objects[qsContentName]["disable"] = true;
		if( QSFieldsArray[qsContentName] ) QSFieldsArray[qsContentName].minQueryLength = -1;
		document.forms[_taskFormName].elements["name"].value = "";
		document.forms[_taskFormName].elements["job_id"].value = "";
	}
	else {
		sqs_objects[qsContentName]["disable"] = false;
		if( QSFieldsArray[qsContentName] ) QSFieldsArray[qsContentName].minQueryLength = 1;
	}
	enableQS(false);
}
{/literal}
{/if}
{if !empty($isRecurrence)}
function callbackUpdateRecurrence() {ldelim}
	callbackCalcWorkTimeTask(-1);
{rdelim}
{/if}
{literal}
function changeTransactionTask( form ) {
	var el_1, el_2, val = form.transactions.value;
	el_1 = getObjectById("_user_err_name_field");
	el_2 = getObjectById("_description_error_field");

	if( val == "kpiLoi" ) {
		el_1.parentNode.style.display = "";
		el_2.parentNode.style.display = "";
	}
	else {
		el_1.parentNode.style.display = "none";
		el_2.parentNode.style.display = "none";
	}
}

function changeTextTransactions(form)
{
	var txt = "", txt2 = "";
	var val = form.transactions.value;
	if( val == "LichCongTac" || val == "XeCongTac" )
		txt = "- Nhân viên YC: "
+ "\n- Bộ phận: "
+ "\n- Loại xe đề nghị: "+ (val == "LichCongTac"? "Honda" : "Du lịch")
+ "\n- Ngày giờ đi: "
+ "\n- Ngày giờ về: "
+ "\n- Địa đểm đến (ghi rõ tên đối tác, địa chỉ, số điện thoại): "
+ "\n- Người liên hệ: "
+ "\n- Nội dung công tác: "
+ "\n- Ghi chú: ";
	if( txt != "" ) form.content.value = txt;
	form.content.focus();
}

YAHOO.util.Event.onDOMReady(function(){
	{/literal}
	changeTransactionTask( document.{$form_name} );
{if !empty($isTodoList)}
	{literal}
	var $lbl = $("div#cycle_type_grp > label");
	$("> input", $lbl).each(function() {
		if( $(this).is(":checked") )
			$(this).parent().addClass("active");
	});
	{/literal}
{/if}
{if isMobileApp()}
	LULO.requestUserGPS();
{/if}
	{literal}
	LULO.SpeechToText.register("textarea#content, textarea#next_step");
});
{/literal}
</script>
<!-- End Meta-Data Javascript -->
</div>{literal}
<script type="text/javascript">
addToValidate('form_EditSmart_Tasks', 'id', 'id', false, '{/literal}{incomCRM_translate label='LBL_ID' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'date_entered_date', 'date', false, 'Ngày tạo' );
addToValidate('form_EditSmart_Tasks', 'date_modified_date', 'date', false, 'Ngày cập nhật' );
addToValidate('form_EditSmart_Tasks', 'modified_user_id', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_ID' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'modified_by_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_NAME' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'created_by', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_CREATED_ID' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'created_by_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_CREATED' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'deleted', 'bool', false, '{/literal}{incomCRM_translate label='LBL_DELETED' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'name', 'name', false, '{/literal}{incomCRM_translate label='LBL_NAME' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'description', 'text', false, '{/literal}{incomCRM_translate label='LBL_DESCRIPTION' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'branch_id', 'enum', false, '{/literal}{incomCRM_translate label='LBL_BRANCH_ID' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'department_id', 'enum', false, '{/literal}{incomCRM_translate label='LBL_DEPARTMENT_ID' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'assigned_user_id', 'relate', false, '{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO_ID' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'assigned_user_name', 'relate', true, '{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'parent_type', 'parent_type', false, '{/literal}{incomCRM_translate label='LBL_PARENT_NAME' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'parent_name', 'parent', true, '{/literal}{incomCRM_translate label='LBL_LIST_RELATED_TO' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'parent_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_PARENT_ID' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'status', 'enum', true, '{/literal}{incomCRM_translate label='LBL_STATUS' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'priority', 'enum', false, '{/literal}{incomCRM_translate label='LBL_PRIORITY' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'date_due_flag', 'bool', false, '{/literal}{incomCRM_translate label='LBL_DATE_DUE_FLAG' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'date_due_date', 'date', true, 'Ngày hoàn thành' );
addToValidate('form_EditSmart_Tasks', 'time_due_date', 'date', false, 'Giờ hoàn thành' );
addToValidate('form_EditSmart_Tasks', 'date_due_field_date', 'date', false, 'Ngày & giờ hoàn thành' );
addToValidate('form_EditSmart_Tasks', 'date_start_flag', 'bool', false, '{/literal}{incomCRM_translate label='LBL_DATE_START_FLAG' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'date_start_date', 'date', true, 'Ngày bắt đầu' );
addToValidate('form_EditSmart_Tasks', 'date_start_field_date', 'date', false, 'Ngày & giờ hoàn thành' );
addToValidate('form_EditSmart_Tasks', 'contact_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_ID' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'contact_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_NAME' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'contact_phone', 'phone', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_PHONE' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'contact_email', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_EMAIL_ADDRESS' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'user_err_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_USER_ERR_ID' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'user_err_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_USER_ERR_NAME' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'description_error', 'text', false, '{/literal}{incomCRM_translate label='LBL_DESCRIPTION_ERROR' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'confirm', 'bool', false, '{/literal}{incomCRM_translate label='LBL_CONFIRM' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'subject', 'name', true, '{/literal}{incomCRM_translate label='LBL_SUBJECT' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'task_relate_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_TASK_RELATE_ID' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'task_relate_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_TASK_RELATE_NAME' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'main_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_MAIN_TASK_ID' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'main_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_MAIN_TASK_NAME' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'panel_name', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_PANEL_NAME' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'date_converter', 'date', false, '{/literal}{incomCRM_translate label='LBL_DATE_CONVERTER' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'contact_info', 'text', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_INFO' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'comment', 'text', false, '{/literal}{incomCRM_translate label='LBL_ATTACHMENT_FILES' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'cmt_type', 'enum', false, '{/literal}{incomCRM_translate label='LBL_CMT_TYPE' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'cmt_user', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_CMT_USER' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'share_users[]', 'enum', false, '{/literal}{incomCRM_translate label='LBL_SHARE_USERS' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'care_result', 'enum', false, '{/literal}{incomCRM_translate label='LBL_CARE_RESULT' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'opportunity_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_OPPORTUNITY_ID' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'opportunity_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_OPPORTUNITY_NAME' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'group_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_GROUP_ID' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'group_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_GROUP_NAME' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'group_steps', 'text', false, '{/literal}{incomCRM_translate label='LBL_GROUP_STEPS' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'job_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_JOB_ID' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'job_type', 'enum', false, '{/literal}{incomCRM_translate label='LBL_JOB_TYPE' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'job_mark', 'tinyint', false, '{/literal}{incomCRM_translate label='LBL_JOB_MARK' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'processing_time', 'int', false, '{/literal}{incomCRM_translate label='LBL_PROCESSING_TIME' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'working_time_by', 'tinyint', false, '{/literal}{incomCRM_translate label='LBL_WORKING_TIME_BY' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'ref_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_REF_ID' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'table_params', 'text', false, '{/literal}{incomCRM_translate label='LBL_TABLE_PARAMS' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'recurrence_data', 'text', false, '{/literal}{incomCRM_translate label='LBL_RECURRENCE_DATA' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'recurrence_text', 'text', false, '{/literal}{incomCRM_translate label='LBL_RECURRENCE_TEXT' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'is_locked', 'bool', false, '{/literal}{incomCRM_translate label='LBL_IS_LOCKED' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'asap', 'enum', false, '{/literal}{incomCRM_translate label='LBL_ASAP' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'transactions', 'enum', true, '{/literal}{incomCRM_translate label='LBL_TRANSACTIONS' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'communication', 'enum', false, '{/literal}{incomCRM_translate label='LBL_COMMUNICATION' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'date_completion_date', 'date', false, 'Ngày đóng công việc' );
addToValidate('form_EditSmart_Tasks', 'time_completion_date', 'date', false, 'Giờ đóng công việc' );
addToValidate('form_EditSmart_Tasks', 'date_completion_flag', 'bool', false, '{/literal}{incomCRM_translate label='LBL_DATE_COMPLETION_FLAG' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'date_completion_field_date', 'date', false, 'Ngày & giờ đóng công việc' );
addToValidate('form_EditSmart_Tasks', 'date_meeting_date', 'date', false, 'Ngày báo cáo' );
addToValidate('form_EditSmart_Tasks', 'time_meeting_date', 'date', false, 'Giờ báo cáo' );
addToValidate('form_EditSmart_Tasks', 'date_meeting_flag', 'bool', false, '{/literal}{incomCRM_translate label='LBL_DATE_MEETING_FLAG' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'date_meeting_field_date', 'date', false, 'Ngày & giờ báo cáo' );
addToValidate('form_EditSmart_Tasks', 'support_by', 'bool', false, '{/literal}{incomCRM_translate label='LBL_SUPPORT_BY' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'date_meeting_from', 'date', false, '{/literal}{incomCRM_translate label='LBL_MEETING_DATE_FORM' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'date_meeting_to', 'date', false, '{/literal}{incomCRM_translate label='LBL_MEETING_DATE_TO' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'reminder_checked', 'bool', false, '{/literal}{incomCRM_translate label='LBL_REMINDER' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'reminder_time', 'int', false, '{/literal}{incomCRM_translate label='LBL_REMINDER' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'note', 'text', false, '{/literal}{incomCRM_translate label='LBL_NOTE' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'content', 'text', true, '{/literal}{incomCRM_translate label='LBL_CONTENT' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'next_step', 'text', false, '{/literal}{incomCRM_translate label='LBL_NEXT_STEP' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'location_nearby', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_LOCATION_NEARBY' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'location_address', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_LOCATION_ADDRESS' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'parent_phone', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_PARENT_PHONE' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'parent_addr', 'text', false, '{/literal}{incomCRM_translate label='LBL_PARENT_ADDR' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'pin_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_PIN_ID' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'users_all[]', 'enum', false, '{/literal}{incomCRM_translate label='LBL_USERS_ALL' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'forward_user_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_FORWARD_USER_ID' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'forward_user_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_FORWARD_USER_NAME' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'is_opp_sales', 'bool', false, '{/literal}{incomCRM_translate label='LBL_IS_OPP_SALES' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'refusal_reasons', 'text', false, '{/literal}{incomCRM_translate label='LBL_REFUSAL_REASONS' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'vehicle_work', 'enum', false, '{/literal}{incomCRM_translate label='LBL_VEHICLE_WORK' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'vehicle_reasons', 'text', false, '{/literal}{incomCRM_translate label='LBL_VEHICLE_REASONS' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'approval_level_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_APPROVAL_LEVEL_ID' module='Tasks'}{literal}' );
addToValidate('form_EditSmart_Tasks', 'approval_level_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_APPROVAL_LEVEL_NAME' module='Tasks'}{literal}' );
addToValidateBinaryDependency('form_EditSmart_Tasks', 'assigned_user_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Tasks'}{literal}{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO' module='Tasks'}{literal}', 'assigned_user_id' );
addToValidateBinaryDependency('form_EditSmart_Tasks', 'parent_name', 'alpha', true, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Tasks'}{literal}{/literal}{incomCRM_translate label='LBL_LIST_RELATED_TO' module='Tasks'}{literal}', 'parent_id' );
addToValidateBinaryDependency('form_EditSmart_Tasks', 'user_err_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Tasks'}{literal}{/literal}{incomCRM_translate label='LBL_USER_ERR_NAME' module='Tasks'}{literal}', 'user_err_id' );
addToValidateBinaryDependency('form_EditSmart_Tasks', 'main_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Tasks'}{literal}{/literal}{incomCRM_translate label='LBL_MAIN_TASK_NAME' module='Tasks'}{literal}', 'main_id' );
addToValidateBinaryDependency('form_EditSmart_Tasks', 'group_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Tasks'}{literal}{/literal}{incomCRM_translate label='LBL_GROUP_NAME' module='Tasks'}{literal}', 'group_id' );
addToValidateBinaryDependency('form_EditSmart_Tasks', 'approval_level_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Tasks'}{literal}{/literal}{incomCRM_translate label='LBL_APPROVAL_LEVEL_NAME' module='Tasks'}{literal}', 'approval_level_id' );
</script>
<script type="text/javascript">if(typeof sqs_objects == 'undefined'){var sqs_objects = new Array;}
sqs_objects['form_EditSmart_Tasks_user_err_name'] = {"form":"form_EditSmart_Tasks","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["user_err_name","user_err_id"],"required_list":["user_err_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['form_EditSmart_Tasks_parent_name'] = {"form":"form_EditSmart_Tasks","method":"query","modules":["Accounts"],"group":"or","field_list":["name","id"],"populate_list":["parent_name","parent_id"],"required_list":["parent_id"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['form_EditSmart_Tasks_main_name'] = {"form":"form_EditSmart_Tasks","method":"query","modules":["Tasks"],"group":"or","field_list":["subject","id"],"populate_list":["main_name","main_id"],"required_list":["parent_id"],"conditions":[{"name":"subject","op":"contains","end":"%","value":""}],"order":"subject","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['form_EditSmart_Tasks_assigned_user_name'] = {"form":"form_EditSmart_Tasks","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["assigned_user_name","assigned_user_id"],"required_list":["assigned_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['form_EditSmart_Tasks_group_name'] = {"form":"form_EditSmart_Tasks","method":"query","modules":["Tasks_Groups"],"group":"or","field_list":["name","id"],"populate_list":["group_name","group_id"],"required_list":["parent_id"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['form_EditSmart_Tasks_approval_level_name'] = {"form":"form_EditSmart_Tasks","method":"query","modules":["Approval_Levels"],"group":"or","field_list":["name","id"],"populate_list":["approval_level_name","approval_level_id"],"required_list":["parent_id"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
</script>{/literal}
