<?php
// created: 2025-02-26 15:50:16
$<PERSON><PERSON><PERSON><PERSON><PERSON>["dictionary"]["Map_Point"] = array (
  'table' => 'map_points',
  'audited' => false,
  'unified_search' => false,
  'duplicate_merge' => false,
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'int',
      'len' => 4,
      'acl' => true,
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'longtext',
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'map_points_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'map_points_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'branch_id' => 
    array (
      'name' => 'branch_id',
      'vname' => 'LBL_BRANCH_ID',
      'type' => 'enum',
      'options' => 'users_branch_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'BranchID of assigned user',
    ),
    'department_id' => 
    array (
      'name' => 'department_id',
      'vname' => 'LBL_DEPARTMENT_ID',
      'type' => 'enum',
      'options' => 'users_department_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'DepartmentID of assigned user',
    ),
    'assigned_user_id' => 
    array (
      'name' => 'assigned_user_id',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'vname' => 'LBL_ASSIGNED_TO_ID',
      'group' => 'assigned_user_name',
      'type' => 'relate',
      'table' => 'users',
      'module' => 'Users',
      'isnull' => 'false',
      'dbType' => 'id',
      'audited' => true,
      'reportable' => true,
      'duplicate_merge' => 'disabled',
      'comment' => 'User ID assigned to record',
    ),
    'assigned_user_name' => 
    array (
      'name' => 'assigned_user_name',
      'link' => 'assigned_user_link',
      'vname' => 'LBL_ASSIGNED_TO_NAME',
      'rname' => 'user_name',
      'type' => 'relate',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'assigned_user_id',
      'module' => 'Users',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'assigned_user_link' => 
    array (
      'name' => 'assigned_user_link',
      'type' => 'link',
      'relationship' => 'map_points_assigned_user',
      'vname' => 'LBL_ASSIGNED_TO_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
      'duplicate_merge' => 'enabled',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'table' => 'users',
    ),
    'do_act' => 
    array (
      'name' => 'do_act',
      'vname' => 'LBL_DO_ACT',
      'type' => 'enum',
      'options' => 'map_points_action_dom',
      'len' => 30,
    ),
    'latlng' => 
    array (
      'name' => 'latlng',
      'vname' => 'LatLng',
      'type' => 'varchar',
      'len' => 100,
    ),
    'parent_type' => 
    array (
      'name' => 'parent_type',
      'vname' => 'LBL_PARENT_TYPE',
      'type' => 'parent_type',
      'dbType' => 'varchar',
      'group' => 'parent_name',
      'len' => 50,
    ),
    'parent_id' => 
    array (
      'name' => 'parent_id',
      'vname' => 'LBL_PARENT_ID',
      'type' => 'id',
      'required' => false,
      'reportable' => true,
    ),
    'parent_name' => 
    array (
      'name' => 'parent_name',
      'parent_type' => 'record_type_display',
      'type_name' => 'parent_type',
      'id_name' => 'parent_id',
      'vname' => 'LBL_RELATED_TO',
      'type' => 'parent',
      'source' => 'non-db',
      'options' => 'record_type_display_notes',
    ),
    'account_id' => 
    array (
      'name' => 'account_id',
      'vname' => 'LBL_ACCOUNT_ID',
      'type' => 'id',
    ),
    'account_name' => 
    array (
      'name' => 'account_name',
      'vname' => 'LBL_ACCOUNT_NAME',
      'type' => 'varchar',
      'len' => 200,
    ),
    'location_nearby' => 
    array (
      'name' => 'location_nearby',
      'vname' => 'LBL_LOCATION_NEARBY',
      'type' => 'varchar',
      'len' => 250,
      'source' => 'non-db',
    ),
    'acc_latlng' => 
    array (
      'name' => 'acc_latlng',
      'vname' => 'Shop LatLng',
      'type' => 'varchar',
      'source' => 'non-db',
    ),
  ),
  'indices' => 
  array (
    'id' => 
    array (
      'name' => 'map_pointspk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    'idx_map_points_branch_id' => 
    array (
      'name' => 'idx_map_points_branch_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
      ),
    ),
    'idx_map_points_department_id' => 
    array (
      'name' => 'idx_map_points_department_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'department_id',
      ),
    ),
    'idx_map_points_branch_dept' => 
    array (
      'name' => 'idx_map_points_branch_dept',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
        1 => 'department_id',
      ),
    ),
    'idx_map_points_assigned' => 
    array (
      'name' => 'idx_map_points_assigned',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'assigned_user_id',
      ),
    ),
    0 => 
    array (
      'name' => 'idx_map_point_id_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'id',
        1 => 'deleted',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_map_point_del_act',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'do_act',
      ),
    ),
    2 => 
    array (
      'name' => 'idx_map_point_del_pid',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'parent_id',
      ),
    ),
    3 => 
    array (
      'name' => 'idx_map_point_del_ptype',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'parent_type',
      ),
    ),
    4 => 
    array (
      'name' => 'idx_map_point_parent',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'parent_type',
        1 => 'parent_id',
      ),
    ),
    5 => 
    array (
      'name' => 'idx_map_point_del_acc',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'account_id',
      ),
    ),
  ),
  'relationships' => 
  array (
    'map_points_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Map_Points',
      'rhs_table' => 'map_points',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'map_points_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Map_Points',
      'rhs_table' => 'map_points',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
    'map_points_assigned_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Map_Points',
      'rhs_table' => 'map_points',
      'rhs_key' => 'assigned_user_id',
      'relationship_type' => 'one-to-many',
    ),
  ),
  'optimistic_lock' => true,
  'templates' => 
  array (
    'assignable' => 'assignable',
    'groups' => 'groups',
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
