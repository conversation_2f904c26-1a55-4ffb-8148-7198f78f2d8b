<?php
// created: 2025-02-26 15:24:03
$<PERSON><PERSON><PERSON><PERSON><PERSON>["dictionary"]["Invoice"] = array (
  'table' => 'invoices',
  'audited' => true,
  'unified_search' => false,
  'duplicate_merge' => false,
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'name',
      'dbType' => 'varchar',
      'len' => '20',
      'acl' => true,
      'audited' => true,
      'required' => false,
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
      'comment' => 'Full text of the note',
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'invoices_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'invoices_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'branch_id' => 
    array (
      'name' => 'branch_id',
      'vname' => 'LBL_BRANCH_ID',
      'type' => 'enum',
      'options' => 'users_branch_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'BranchID of assigned user',
    ),
    'department_id' => 
    array (
      'name' => 'department_id',
      'vname' => 'LBL_DEPARTMENT_ID',
      'type' => 'enum',
      'options' => 'users_department_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'DepartmentID of assigned user',
    ),
    'assigned_user_id' => 
    array (
      'name' => 'assigned_user_id',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'vname' => 'LBL_ASSIGNED_TO_ID',
      'group' => 'assigned_user_name',
      'type' => 'relate',
      'table' => 'users',
      'module' => 'Users',
      'isnull' => 'false',
      'dbType' => 'id',
      'audited' => true,
      'reportable' => true,
      'duplicate_merge' => 'disabled',
      'comment' => 'User ID assigned to record',
    ),
    'assigned_user_name' => 
    array (
      'name' => 'assigned_user_name',
      'link' => 'assigned_user_link',
      'vname' => 'LBL_ASSIGNED_TO_NAME',
      'rname' => 'user_name',
      'type' => 'relate',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'assigned_user_id',
      'module' => 'Users',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'assigned_user_link' => 
    array (
      'name' => 'assigned_user_link',
      'type' => 'link',
      'relationship' => 'invoices_assigned_user',
      'vname' => 'LBL_ASSIGNED_TO_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
      'duplicate_merge' => 'enabled',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'table' => 'users',
    ),
    'status' => 
    array (
      'name' => 'status',
      'vname' => 'LBL_STATUS',
      'type' => 'enum',
      'options' => 'invoice_status_dom',
      'len' => '2',
      'audited' => true,
      'massupdate' => false,
      'display_default' => '-2',
    ),
    'date_arising' => 
    array (
      'name' => 'date_arising',
      'vname' => 'LBL_DATE_ARISING',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
      'display_default' => 'now',
    ),
    'date_issue' => 
    array (
      'name' => 'date_issue',
      'vname' => 'LBL_DATE_ISSUE',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'inv_no' => 
    array (
      'name' => 'inv_no',
      'vname' => 'LBL_INV_NO',
      'type' => 'varchar',
      'len' => '30',
      'audited' => true,
    ),
    'inv_pattern' => 
    array (
      'name' => 'inv_pattern',
      'vname' => 'LBL_INV_PATTERN',
      'type' => 'varchar',
      'len' => '30',
      'audited' => true,
      'function' => 
      array (
        'name' => 'getInvoicePatternDropDown',
        'returns' => 'html',
      ),
    ),
    'inv_serial' => 
    array (
      'name' => 'inv_serial',
      'vname' => 'LBL_INV_SERIAL',
      'type' => 'varchar',
      'len' => '30',
      'audited' => true,
      'function' => 
      array (
        'name' => 'getInvoiceSerialDropDown',
        'returns' => 'html',
      ),
    ),
    'inv_type' => 
    array (
      'name' => 'inv_type',
      'vname' => 'LBL_INV_TYPE',
      'type' => 'enum',
      'options' => 'invoice_type_dom',
      'len' => '1',
      'audited' => true,
      'massupdate' => false,
      'display_default' => '1',
    ),
    'inv_catalog' => 
    array (
      'name' => 'inv_catalog',
      'vname' => 'LBL_INV_CATALOG',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => false,
      'default' => 0,
    ),
    'vatrate' => 
    array (
      'name' => 'vatrate',
      'vname' => 'LBL_VATRATE',
      'type' => 'enum',
      'options' => 'invoice_vat_dom',
      'dbType' => 'float',
      'audited' => true,
      'massupdate' => false,
      'display_default' => '10',
    ),
    'vatamount' => 
    array (
      'name' => 'vatamount',
      'vname' => 'LBL_VATAMOUNT',
      'type' => 'double',
      'audited' => true,
    ),
    'total' => 
    array (
      'name' => 'total',
      'vname' => 'LBL_TOTAL',
      'type' => 'double',
      'audited' => true,
    ),
    'amount' => 
    array (
      'name' => 'amount',
      'vname' => 'LBL_AMOUNT',
      'type' => 'double',
      'audited' => true,
    ),
    'amountinwords' => 
    array (
      'name' => 'amountinwords',
      'vname' => 'LBL_AMOUNTINWORDS',
      'type' => 'text',
    ),
    'result_log' => 
    array (
      'name' => 'result_log',
      'vname' => 'LBL_RESULT_LOG',
      'type' => 'text',
      'audited' => true,
    ),
    'cuscode' => 
    array (
      'name' => 'cuscode',
      'vname' => 'LBL_CUSCODE',
      'type' => 'varchar',
      'len' => '50',
      'audited' => true,
    ),
    'cusname' => 
    array (
      'name' => 'cusname',
      'vname' => 'LBL_CUSNAME',
      'type' => 'varchar',
      'len' => '250',
      'audited' => true,
    ),
    'buyer' => 
    array (
      'name' => 'buyer',
      'vname' => 'LBL_BUYER',
      'type' => 'varchar',
      'len' => '150',
      'audited' => true,
    ),
    'email' => 
    array (
      'name' => 'email',
      'vname' => 'LBL_EMAIL',
      'type' => 'varchar',
      'len' => '200',
      'audited' => true,
    ),
    'cusaddress' => 
    array (
      'name' => 'cusaddress',
      'vname' => 'LBL_CUSADDRESS',
      'type' => 'varchar',
      'len' => '250',
      'audited' => true,
    ),
    'cusbankname' => 
    array (
      'name' => 'cusbankname',
      'vname' => 'LBL_CUSBANKNAME',
      'type' => 'varchar',
      'len' => '200',
      'audited' => true,
    ),
    'cusbankno' => 
    array (
      'name' => 'cusbankno',
      'vname' => 'LBL_CUSBANKNO',
      'type' => 'varchar',
      'len' => '100',
      'audited' => true,
    ),
    'cusphone' => 
    array (
      'name' => 'cusphone',
      'vname' => 'LBL_CUSPHONE',
      'type' => 'varchar',
      'len' => '50',
      'audited' => true,
    ),
    'custaxcode' => 
    array (
      'name' => 'custaxcode',
      'vname' => 'LBL_CUSTAXCODE',
      'type' => 'varchar',
      'len' => '30',
      'audited' => true,
    ),
    'paymentmethod' => 
    array (
      'name' => 'paymentmethod',
      'vname' => 'LBL_PAYMENTMETHOD',
      'type' => 'enum',
      'options' => 'invoice_payment_method_dom',
      'len' => '10',
      'audited' => true,
      'massupdate' => false,
    ),
    'paymentstatus' => 
    array (
      'name' => 'paymentstatus',
      'vname' => 'LBL_PAYMENTSTATUS',
      'type' => 'enum',
      'options' => 'invoice_payment_status_dom',
      'dbType' => 'char',
      'len' => '1',
      'audited' => true,
      'massupdate' => false,
      'display_default' => '1',
    ),
    'exchangerate' => 
    array (
      'name' => 'exchangerate',
      'vname' => 'LBL_EXCHANGERATE',
      'type' => 'double',
      'audited' => true,
    ),
    'currencyunit' => 
    array (
      'name' => 'currencyunit',
      'vname' => 'LBL_CURRENCYUNIT',
      'type' => 'varchar',
      'len' => '30',
      'audited' => true,
    ),
    'account_id' => 
    array (
      'name' => 'account_id',
      'vname' => 'LBL_ACCOUNT_ID',
      'type' => 'id',
      'audited' => true,
      'massupdate' => false,
      'required' => false,
      'reportable' => false,
    ),
    'account_name' => 
    array (
      'name' => 'account_name',
      'rname' => 'name',
      'id_name' => 'account_id',
      'vname' => 'LBL_ACCOUNT_NAME',
      'table' => 'accounts',
      'type' => 'relate',
      'link' => 'accounts',
      'join_name' => 'accounts',
      'isnull' => 'true',
      'module' => 'Accounts',
      'source' => 'non-db',
      'audited' => false,
      'massupdate' => false,
    ),
    'reference_code' => 
    array (
      'name' => 'reference_code',
      'rname' => 'reference_code',
      'id_name' => 'account_id',
      'vname' => 'LBL_REFERENCE_CODE',
      'table' => 'accounts',
      'type' => 'relate',
      'link' => 'accounts',
      'join_name' => 'accounts',
      'isnull' => 'true',
      'module' => 'Accounts',
      'source' => 'non-db',
      'audited' => false,
      'massupdate' => false,
    ),
    'accounts' => 
    array (
      'name' => 'accounts',
      'type' => 'link',
      'relationship' => 'accounts_invoices',
      'vname' => 'LBL_ACCOUNTS',
      'source' => 'non-db',
    ),
    'opportunities' => 
    array (
      'name' => 'opportunities',
      'type' => 'link',
      'relationship' => 'invoices_opportunities',
      'vname' => 'LBL_OPPORTUNITIES',
      'source' => 'non-db',
    ),
  ),
  'indices' => 
  array (
    'id' => 
    array (
      'name' => 'invoicespk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    'idx_invoices_branch_id' => 
    array (
      'name' => 'idx_invoices_branch_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
      ),
    ),
    'idx_invoices_department_id' => 
    array (
      'name' => 'idx_invoices_department_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'department_id',
      ),
    ),
    'idx_invoices_branch_dept' => 
    array (
      'name' => 'idx_invoices_branch_dept',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
        1 => 'department_id',
      ),
    ),
    'idx_invoices_assigned' => 
    array (
      'name' => 'idx_invoices_assigned',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'assigned_user_id',
      ),
    ),
    0 => 
    array (
      'name' => 'idx_invoice_id_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'id',
        1 => 'deleted',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_invoice_del_assigned',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'assigned_user_id',
      ),
    ),
    2 => 
    array (
      'name' => 'idx_invoice_del_account',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'account_id',
      ),
    ),
    3 => 
    array (
      'name' => 'idx_invoice_del_status',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'status',
      ),
    ),
    4 => 
    array (
      'name' => 'idx_invoice_del_type',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'inv_type',
      ),
    ),
    5 => 
    array (
      'name' => 'idx_invoice_del_catalog',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'inv_catalog',
      ),
    ),
    6 => 
    array (
      'name' => 'idx_invoice_del_payment',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'paymentmethod',
      ),
    ),
    7 => 
    array (
      'name' => 'idx_invoice_del_pay_status',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'paymentstatus',
      ),
    ),
  ),
  'relationships' => 
  array (
    'invoices_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Invoices',
      'rhs_table' => 'invoices',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'invoices_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Invoices',
      'rhs_table' => 'invoices',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
    'invoices_assigned_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Invoices',
      'rhs_table' => 'invoices',
      'rhs_key' => 'assigned_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'accounts_invoices' => 
    array (
      'lhs_module' => 'Accounts',
      'lhs_table' => 'accounts',
      'lhs_key' => 'id',
      'rhs_module' => 'Invoices',
      'rhs_table' => 'invoices',
      'rhs_key' => 'account_id',
      'relationship_type' => 'one-to-many',
    ),
  ),
  'optimistic_lock' => true,
  'templates' => 
  array (
    'assignable' => 'assignable',
    'groups' => 'groups',
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
