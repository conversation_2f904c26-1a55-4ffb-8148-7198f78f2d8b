

{math equation="floor(left / right)"
	left=12
		right=$templateMeta.maxColumns
			assign=colClass
	}

<section class="container2 search-form ml-0 search_form_basic">
	<div class="row">
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12">
			<div class="row">
				<label class="col-5 pr-0" for="name_basic">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_NAME' module='Contacts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.name_basic.value) <= 0}
	{assign var="value" value=$fields.name_basic.default_value }
{else}
	{assign var="value" value=$fields.name_basic.value }
{/if}
{if isTypeNumber($fields.name_basic.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.name_basic.name}' id='{$fields.name_basic.name}' size='30' maxlength='255' value='{$value}' title='' tabindex=''  /> 
									</div>
			</div>
		</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12">
			<div class="row">
				<label class="col-5 pr-0" for="phone_basic">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_ANY_PHONE' module='Contacts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.phone_basic.value) <= 0}
	{assign var="value" value=$fields.phone_basic.default_value }
{else}
	{assign var="value" value=$fields.phone_basic.value }
{/if}
{if isTypeNumber($fields.phone_basic.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.phone_basic.name}' id='{$fields.phone_basic.name}' size='30'  value='{$value}' title='' tabindex=''  /> 
									</div>
			</div>
		</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12">
			<div class="row">
				<label class="col-5 pr-0" for="email_basic">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_ANY_EMAIL' module='Contacts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.email_basic.value) <= 0}
	{assign var="value" value=$fields.email_basic.default_value }
{else}
	{assign var="value" value=$fields.email_basic.value }
{/if}
{if isTypeNumber($fields.email_basic.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.email_basic.name}' id='{$fields.email_basic.name}' size='30'  value='{$value}' title='' tabindex=''  /> 
									</div>
			</div>
		</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12">
			<div class="row">
				<label class="col-5 pr-0" for="assigned_user_id_basic">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_ASSIGNED_TO' module='Contacts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="assigned_user_id_basic[]" id="{$fields.assigned_user_id_basic.name}" size="1"   >
{html_options options=$fields.assigned_user_id_basic.options selected=$fields.assigned_user_id_basic.value}
</select>
									</div>
			</div>
		</div>
	</div>
</section>

{if $HAS_ADVANCED_SEARCH}
<table cellspacing="0" cellpadding="0" border="0" class="mt-10">
<tr>
	<td scope="row" nowrap="nowrap" class="pt-10">
	<a style="font-size:10pt; text-decoration:none;" onclick="incomCRM.searchForm.searchFormSelect('{$module}|advanced_search','{$module}|basic_search')" href="#">[ {$APP.LNK_ADVANCED_SEARCH} ]</a>
	</td>
</tr>
</table>
{/if}

{literal}<script type="text/javascript">if(typeof sqs_objects == 'undefined'){var sqs_objects = new Array;}
sqs_objects['search_form_modified_by_name_basic'] = {"form":"search_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["modified_by_name_basic","modified_user_id_basic"],"required_list":["modified_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['search_form_created_by_name_basic'] = {"form":"search_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["created_by_name_basic","created_by_basic"],"required_list":["created_by"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['search_form_assigned_user_name_basic'] = {"form":"search_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["assigned_user_name_basic","assigned_user_id_basic"],"required_list":["assigned_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['search_form_account_name_basic'] = {"form":"search_form","method":"query","modules":["Accounts"],"group":"or","field_list":["name","id"],"populate_list":["search_form_account_name_basic","account_id_basic"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"required_list":["account_id"],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['search_form_report_to_name_basic'] = {"form":"search_form","method":"get_contact_array","modules":["Contacts"],"field_list":["salutation","first_name","last_name","id"],"populate_list":["report_to_name_basic","reports_to_id_basic","reports_to_id_basic","reports_to_id_basic"],"required_list":["reports_to_id"],"group":"or","conditions":[{"name":"first_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"last_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['search_form_campaign_name_basic'] = {"form":"search_form","method":"query","modules":["Campaigns"],"group":"or","field_list":["name","id"],"populate_list":["campaign_name_basic","campaign_id_basic"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"required_list":["campaign_id"],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
</script>{/literal}