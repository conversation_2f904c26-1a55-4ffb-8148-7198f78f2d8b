incomCRM.language.setLanguage('app_strings', {"LBL_SORT":"S\u1eafp x\u1ebfp","LBL_OUTBOUND_EMAIL_ADD_SERVER":"Th\u00eam m\u00e1y ch\u1ee7...","LBL_ROUTING_ADD_RULE":"Th\u00eam lu\u1eadt","LBL_ROUTING_ALL":"T\u1ea5t c\u1ea3","LBL_ROUTING_ANY":"B\u1ea5t k\u1ef3","LBL_ROUTING_BREAK":"-","LBL_ROUTING_BUTTON_CANCEL":"H\u1ee7y","LBL_ROUTING_BUTTON_SAVE":"Ghi lu\u1eadt","LBL_ROUTING_ACTIONS_COPY_MAIL":"Sao ch\u00e9p email","LBL_ROUTING_ACTIONS_DELETE_BEAN":"X\u00f3a incomCRM Object","LBL_ROUTING_ACTIONS_DELETE_FILE":"X\u00f3a file","LBL_ROUTING_ACTIONS_DELETE_MAIL":"X\u00f3a email","LBL_ROUTING_ACTIONS_FORWARD":"Chuy\u1ec3n ti\u1ebfp email","LBL_ROUTING_ACTIONS_MARK_FLAGGED":"C\u1edd email","LBL_ROUTING_ACTIONS_MARK_READ":"\u0110\u00e1nh d\u1ea5u \u0111\u00e3 \u0111\u1ecdc","LBL_ROUTING_ACTIONS_MARK_UNREAD":"\u0110\u00e1nh d\u1ea5u ch\u01b0a \u0111\u1ecdc","LBL_ROUTING_ACTIONS_MOVE_MAIL":"Chuy\u1ec3n email","LBL_ROUTING_ACTIONS_PEFORM":"Th\u1ef1c hi\u1ec7n c\u00e1c ho\u1ea1t \u0111\u1ed9ng sau","LBL_ROUTING_ACTIONS_REPLY":"Tr\u1ea3 l\u1eddi email","LBL_ROUTING_CHECK_RULE":"M\u1ed9t l\u1ed7i \u0111\u00e3 nh\u1eadn di\u1ec7n:","LBL_ROUTING_CHECK_RULE_DESC":"H\u00e3y xem l\u1ea1i t\u1ea5t c\u1ea3 c\u00e1c tr\u01b0\u1eddng \u0111\u00e1nh d\u1ea5u.","LBL_ROUTING_CONFIRM_DELETE":"B\u1ea1n c\u00f3 ch\u1eafc l\u00e0 x\u00f3a lu\u1eadt n\u00e0y kh\u00f4ng? Lu\u1eadt kh\u00f4ng th\u1ec3 kh\u00f4i ph\u1ee5c l\u1ea1i khi x\u00f3a.","LBL_ROUTING_FLAGGED":"thi\u1ebft l\u1eadp c\u1edd","LBL_ROUTING_FORM_DESC":"Ghi lu\u1eadt v\u00e0 hi\u1ec7u l\u1ef1c ngay l\u1eadp t\u1ee9c.","LBL_ROUTING_FW":"Chuy\u1ec3n t\u1edbi: ","LBL_ROUTING_LIST_TITLE":"Lu\u1eadt","LBL_ROUTING_MATCH":"N\u1ebfu","LBL_ROUTING_MATCH_2":"c\u1ee7a c\u00e1c \u0111i\u1ec1u ki\u1ec7n sau th\u1ecfa m\u00e3n:","LBL_ROUTING_MATCH_CC_ADDR":"\u0110\u1ed3ng g\u1eedi","LBL_ROUTING_MATCH_DESCRIPTION":"N\u1ed9i dung ch\u00ednh","LBL_ROUTING_MATCH_FROM_ADDR":"T\u1eeb","LBL_ROUTING_MATCH_NAME":"Ti\u00eau \u0111\u1ec1","LBL_ROUTING_MATCH_PRIORITY_HIGH":"Quan tr\u1ecdng","LBL_ROUTING_MATCH_PRIORITY_NORMAL":"B\u00ecnh th\u01b0\u1eddng","LBL_ROUTING_MATCH_PRIORITY_LOW":"Th\u1ea5p","LBL_ROUTING_MATCH_TO_ADDR":"G\u1eedi \u0111\u1ebfn","LBL_ROUTING_MATCH_TYPE_MATCH":"Ch\u1ee9a \u0111\u1ef1ng","LBL_ROUTING_MATCH_TYPE_NOT_MATCH":"Kh\u00f4ng ch\u1ee9a \u0111\u1ef1ng","LBL_ROUTING_NAME":"T\u00ean lu\u1eadt","LBL_ROUTING_NEW_NAME":"Lu\u1eadt m\u1edbi","LBL_ROUTING_ONE_MOMENT":"\u0110\u1ee3i trong gi\u00e2y l\u00e1t...","LBL_ROUTING_ORIGINAL_MESSAGE_FOLLOWS":"V\u0103n b\u1ea3n g\u1ed1c theo sau.","LBL_ROUTING_RE":"V\/V: ","LBL_ROUTING_SAVING_RULE":"L\u01b0u lu\u1eadt","LBL_ROUTING_SUB_DESC":"Ki\u1ec3m tra lu\u1eadt \u0111ang ho\u1ea1t \u0111\u1ed9ng. Ch\u1ecdn t\u00ean \u0111\u1ec3 s\u1eeda.","LBL_ROUTING_TO":"\u0111\u1ebfn","LBL_ROUTING_TO_ADDRESS":"\u0111\u1ecba ch\u1ec9 \u0111\u1ebfn","LBL_ROUTING_WITH_TEMPLATE":"v\u1edbi m\u1eabu","NTC_OVERWRITE_ADDRESS_PHONE_CONFIRM":"Ch\u1ecdn \"OK\" \u0111\u1ec3 ghi \u0111\u00e8 gi\u00e1 tr\u1ecb 2 tr\u01b0\u1eddng S\u1ed1 \u0111i\u1ec7n tho\u1ea1i v\u00e0 \u0110\u1ecba ch\u1ec9, ng\u01b0\u1ee3c l\u1ea1i ch\u1ecdn \"H\u1ee7y b\u1ecf\" \u0111\u1ec3 gi\u1eef l\u1ea1i gi\u00e1 tr\u1ecb 2 tr\u01b0\u1eddng hi\u1ec7n t\u1ea1i.","LBL_EMAIL_ACCOUNTS_EDIT":"So\u1ea1n th\u1ea3o","LBL_EMAIL_ACCOUNTS_GMAIL_DEFAULTS":"Ch\u1ecdn m\u1eb7c \u0111\u1ecbnh Gmail","LBL_EMAIL_ACCOUNTS_NAME":"T\u00ean","LBL_EMAIL_ACCOUNTS_OUTBOUND":"Outbound Mail Server","LBL_EMAIL_ACCOUNTS_SENDTYPE":"\u0110\u1ea1i l\u00fd trung chuy\u1ec3n mail","LBL_EMAIL_ACCOUNTS_SMTPAUTH_REQ":"D\u00f9ng ch\u1ee9ng th\u1ef1c quy\u1ec1n SMTP?","LBL_EMAIL_ACCOUNTS_SMTPPASS":"M\u1eadt m\u00e3 SMTP","LBL_EMAIL_ACCOUNTS_SMTPPORT":"C\u1ed5ng SMTP","LBL_EMAIL_ACCOUNTS_SMTPSERVER":"Server SMTP","LBL_EMAIL_ACCOUNTS_SMTPSSL":"S\u1eed d\u1ee5ng SSL khi k\u1ebft n\u1ed1i","LBL_EMAIL_ACCOUNTS_SMTPUSER":"T\u00ean ng\u01b0\u1eddi d\u00f9ng SMTP","LBL_EMAIL_ACCOUNTS_TITLE":"Qu\u1ea3n l\u00fd t\u00e0i kho\u1ea3n mail","LBL_EMAIL_ADD":"Th\u00eam \u0111\u1ecba ch\u1ec9","LBL_EMAIL_ADDRESS_BOOK_ADD":"Th\u00eam","LBL_EMAIL_ADDRESS_BOOK_ADD_LIST":"T\u1ea1o danh s\u00e1ch","LBL_EMAIL_ADDRESS_BOOK_EMAIL_ADDR":"\u0110\u1ecba ch\u1ec9 email","LBL_EMAIL_ADDRESS_BOOK_ERR_NOT_CONTACT":"Vi\u1ec7c ch\u1ec9nh s\u1eeda li\u00ean h\u1ec7 kh\u00f4ng \u0111\u01b0\u1ee3c h\u1ed7 tr\u1ee3 t\u1ea1i th\u1eddi \u0111i\u1ec3m n\u00e0y.","LBL_EMAIL_ADDRESS_BOOK_FILTER":"B\u1ed9 l\u1ecdc","LBL_EMAIL_ADDRESS_BOOK_FIRST_NAME":"T\u00ean","LBL_EMAIL_ADDRESS_BOOK_LAST_NAME":"H\u1ecd","LBL_EMAIL_ADDRESS_BOOK_MY_CONTACTS":"Ng\u01b0\u1eddi LH ri\u00eang","LBL_EMAIL_ADDRESS_BOOK_MY_LISTS":"Danh s\u00e1ch th\u01b0 ri\u00eang","LBL_EMAIL_ADDRESS_BOOK_NAME":"T\u00ean","LBL_EMAIL_ADDRESS_BOOK_NOT_FOUND":"Kh\u00f4ng t\u00ecm th\u1ea5y \u0111\u1ecba ch\u1ec9","LBL_EMAIL_ADDRESS_BOOK_SAVE_AND_ADD":"Ghi & Th\u00eam v\u00e0o S\u1ed5 \u0111\u1ecba ch\u1ec9","LBL_EMAIL_ADDRESS_BOOK_SEARCH":"T\u00ecm ki\u1ebfm","LBL_EMAIL_ADDRESS_BOOK_SELECT_TITLE":"Ch\u1ecdn m\u1ee5c c\u1ee7a S\u1ed5 \u0111\u1ecba ch\u1ec9","LBL_EMAIL_ADDRESS_BOOK_TITLE":"S\u1ed5 \u0111\u1ecba ch\u1ec9","LBL_EMAIL_REPORTS_TITLE":"B\u00e1o c\u00e1o","LBL_EMAIL_ADDRESS_BOOK_TITLE_ICON":"<img src=themes\/default\/images\/icon_email_addressbook.gif align=absmiddle border=0> Address Book","LBL_EMAIL_ADDRESSES":"\u0110\u1ecba ch\u1ec9 email","LBL_EMAIL_ADDRESS_PRIMARY":"\u0110\u1ecba ch\u1ec9 email","LBL_EMAIL_ADDRESSES_TITLE":"\u0110\u1ecba ch\u1ec9 email","LBL_EMAIL_ARCHIVE_TO_incomCRM":"Nh\u1eadp t\u1eeb incomCRM","LBL_EMAIL_ASSIGNMENT":"G\u00e1n","LBL_EMAIL_ATTACH_FILE_TO_EMAIL":"File \u0111\u00ednh k\u00e8m theo email","LBL_EMAIL_ATTACHMENT":"\u0110\u00ednh k\u00e8m","LBL_EMAIL_ATTACHMENTS":"T\u1eeb m\u00e1y local","LBL_EMAIL_ATTACHMENTS2":"T\u1eeb t\u00e0i li\u1ec7u c\u1ee7a incomCRM","LBL_EMAIL_ATTACHMENTS3":"\u0110\u00ednh k\u00e8m m\u1eabu","LBL_EMAIL_ATTACHMENTS_FILE":"File","LBL_EMAIL_ATTACHMENTS_DOCUMENT":"T\u00e0i li\u1ec7u","LBL_EMAIL_ATTACHMENTS_EMBEDED":"\u0110\u00ednh k\u00e8m","LBL_EMAIL_BCC":"BCC","LBL_EMAIL_CANCEL":"H\u1ee7y","LBL_EMAIL_CC":"CC","LBL_EMAIL_CHARSET":"B\u1ea3ng m\u00e3","LBL_EMAIL_CHECK":"Ki\u1ec3m tra th\u01b0","LBL_EMAIL_CHECKING_NEW":"Ki\u1ec3m tra email m\u1edbi","LBL_EMAIL_CHECKING_DESC":"Ki\u1ec3m tra mail m\u1edbi. <br><br>N\u1ebfu \u0111\u00e2y l\u00e0 l\u1ea7n \u0111\u1ea7u th\u00ec n\u00f3 s\u1ebd m\u1ea5t m\u1ed9t kho\u1ea3ng th\u1eddi gian.","LBL_EMAIL_CLOSE":"\u0110\u00f3ng","LBL_EMAIL_COFFEE_BREAK":"Ki\u1ec3m tra mail m\u1edbi. <br><br>N\u1ebfu nhi\u1ec1u t\u00e0i kho\u1ea3n mail s\u1ebd t\u1ed1n nhi\u1ec1u th\u1eddi gian.","LBL_EMAIL_COMMON":"Th\u00f4ng th\u01b0\u1eddng","LBL_EMAIL_COMPOSE":"So\u1ea1n email","LBL_EMAIL_COMPOSE_ERR_NO_RECIPIENTS":"Nh\u1eadp ng\u01b0\u1eddi nh\u1eadn cho email n\u00e0y.","LBL_EMAIL_COMPOSE_LINK_TO":"Li\u00ean h\u1ec7 v\u1edbi","LBL_EMAIL_COMPOSE_NO_BODY":"Email ch\u01b0a c\u00f3 n\u1ed9i dung. C\u00f3 g\u1eedi kh\u00f4ng?","LBL_EMAIL_COMPOSE_NO_SUBJECT":"Email ch\u01b0a c\u00f3 ti\u00eau \u0111\u1ec1. C\u00f3 g\u1eedi kh\u00f4ng?","LBL_EMAIL_COMPOSE_NO_SUBJECT_LITERAL":"(kh\u00f4ng ti\u00eau \u0111\u1ec1)","LBL_EMAIL_COMPOSE_READ":"\u0110\u1ecdc v\u00e0 tr\u1ea3 l\u1eddi email","LBL_EMAIL_COMPOSE_SEND_FROM":"G\u1eedi t\u1eeb t\u00e0i kho\u1ea3n email","LBL_EMAIL_COMPOSE_OPTIONS":"T\u00f9y ch\u1ecdn","LBL_EMAIL_COMPOSE_INVALID_ADDRESS":"Ki\u1ec3m tra l\u1ea1i \u0111\u1ecba ch\u1ec9 email cho c\u00e1c email To, CC and BCC","LBL_EMAIL_CONFIRM_CLOSE":"H\u1ee7y email n\u00e0y?","LBL_EMAIL_CONFIRM_DELETE":"X\u00f3a nh\u1eefng m\u1ee5c n\u00e0y t\u1eeb s\u1ed5 \u0111\u1ecba ch\u1ec9 c\u1ee7a b\u1ea1n?","LBL_EMAIL_CONFIRM_DELETE_SIGNATURE":"B\u1ea1n mu\u1ed1n x\u00f3a ch\u1eef k\u00fd n\u00e0y?","LBL_EMAIL_CREATE_NEW":"--T\u1ea1o ng\u00e0y l\u01b0u--","LBL_EMAIL_DATE_SENT_BY_SENDER":"Ng\u00e0y g\u1eedi b\u1edfi ng\u01b0\u1eddi g\u1eedi","LBL_EMAIL_DATE_RECEIVED":"Ng\u00e0y nh\u1eadn","LBL_EMAIL_ASSIGNED_TO_USER":"G\u00e1n cho ng\u01b0\u1eddi d\u00f9ng","LBL_EMAIL_DATE_TODAY":"H\u00f4m nay","LBL_EMAIL_DATE_YESTERDAY":"H\u00f4m qua","LBL_EMAIL_DD_TEXT":"email \u0111\u01b0\u1ee3c ch\u1ecdn.","LBL_EMAIL_DEFAULTS":"M\u1eb7c \u0111\u1ecbnh","LBL_EMAIL_DELETE":"X\u00f3a","LBL_EMAIL_DELETE_CONFIRM":"X\u00f3a th\u00f4ng \u0111\u1ecbp v\u1eeba ch\u1ecdn?","LBL_EMAIL_DELETE_SUCCESS":"X\u00f3a Email th\u00e0nh c\u00f4ng.","LBL_EMAIL_DELETING_MESSAGE":"\u0110ang x\u00f3a email","LBL_EMAIL_DETAILS":"Chi ti\u1ebft","LBL_EMAIL_DISPLAY_MSG":"Hi\u1ec3n th\u1ecb email(s) {0} - {1} c\u1ee7a {2}","LBL_EMAIL_ADDR_DISPLAY_MSG":"Hi\u1ec3n th\u1ecb \u0111\u1ecba ch\u1ec9 email(es) {0} - {1} c\u1ee7a {2}","LBL_EMAIL_EDIT_CONTACT":"So\u1ea1n ng\u01b0\u1eddi LH","LBL_EMAIL_EDIT_CONTACT_WARN":"Ch\u1ec9 \u0111\u1ecba ch\u1ec9 ch\u00ednh m\u1edbi \u0111\u01b0\u1ee3c d\u00f9ng khi l\u00e0m vi\u1ec7c v\u1edbi li\u00ean h\u1ec7","LBL_EMAIL_EDIT_MAILING_LIST":"So\u1ea1n danh s\u00e1ch th\u01b0","LBL_EMAIL_EMPTYING_TRASH":"L\u00e0m r\u1ed7ng th\u00f9ng r\u00e1c","LBL_EMAIL_DELETING_OUTBOUND":"\u0110ang x\u00f3a outbound server","LBL_EMAIL_CLEARING_CACHE_FILES":"L\u00e0m s\u1ea1ch file cache","LBL_EMAIL_EMPTY_MSG":"Kh\u00f4ng c\u00f3 email \u0111\u01b0\u1ee3c hi\u1ec3n th\u1ecb.","LBL_EMAIL_EMPTY_ADDR_MSG":"Kh\u00f4ng c\u00f3 \u0111\u1ecba ch\u1ec9 email \u0111\u01b0\u1ee3c hi\u1ec3n th\u1ecb.","LBL_EMAIL_ERROR_ADD_GROUP_FOLDER":"T\u00ean th\u01b0 m\u1ee5c ph\u1ea3i duy nh\u1ea5t v\u00e0 kh\u00e1c r\u1ed7ng. Vui l\u00f2ng ki\u1ec3m tra l\u1ea1i.","LBL_EMAIL_ERROR_DELETE_GROUP_FOLDER":"Kh\u00f4ng th\u1ec3 x\u00f3a th\u01b0 m\u1ee5c. C\u1ea3 th\u01b0 m\u1ee5c n\u00e0y v\u00e0 th\u01b0 m\u1ee5c con \u0111\u1ec1u c\u00f3 mail li\u00ean quan.","LBL_EMAIL_ERROR_CANNOT_FIND_NODE":"Kh\u00f4ng th\u1ec3 x\u00e1c \u0111\u1ecbnh th\u01b0 m\u1ee5c t\u1eeb ng\u1eef c\u1ea3nh n\u00e0y. Th\u1eed l\u1ea1i.","LBL_EMAIL_ERROR_CHECK_IE_SETTINGS":"Vui l\u00f2ng ki\u1ec3m tra l\u1ea1i thi\u1ebft l\u1eadp c\u1ea5u h\u00ecnh c\u1ee7a b\u1ea1n.","LBL_EMAIL_ERROR_CONTACT_NAME":"Vui l\u00f2ng ki\u1ec3m tra nh\u1eadp t\u00ean","LBL_EMAIL_ERROR_DESC":"L\u1ed7i \u0111\u01b0\u1ee3c ph\u00e1t hi\u1ec7n:","LBL_EMAIL_DELETE_ERROR_DESC":"B\u1ea1n kh\u00f4ng th\u1ec3 truy c\u1eadp v\u00f9ng n\u00e0y. Li\u00ean h\u1ec7 v\u1edbi Qu\u1ea3n tr\u1ecb h\u1ec7 th\u1ed1ng \u0111\u1ec3 bi\u1ebft th\u00eam th\u00f4ng tin.","LBL_EMAIL_ERROR_DUPE_FOLDER_NAME":"T\u00ean th\u01b0 m\u1ee5c incomCRM ph\u1ea3i l\u00e0 duy nh\u1ea5t.","LBL_EMAIL_ERROR_EMPTY":"Vui l\u00f2ng nh\u1eadp m\u1ed9t s\u1ed1 ti\u00eau ch\u00ed t\u00ecm ki\u1ebfm:","LBL_EMAIL_ERROR_GENERAL_TITLE":"M\u1ed9t l\u1ed7i xu\u1ea5t hi\u1ec7n","LBL_EMAIL_ERROR_LIST_NAME":"C\u00f3 m\u1ed9t danh s\u00e1ch email c\u00f9ng t\u00ean \u0111\u00e3 t\u1ed3n t\u1ea1i.","LBL_EMAIL_ERROR_MESSAGE_DELETED":"Tin nh\u1eafn \u0111\u01b0\u1ee3c x\u00f3a t\u1eeb Server","LBL_EMAIL_ERROR_IMAP_MESSAGE_DELETED":"C\u1ea3 tin nh\u1eafn x\u00f3a t\u1eeb Server ho\u1eb7c di chuy\u1ec3n t\u1edbi th\u01b0 m\u1ee5c kh\u00e1c","LBL_EMAIL_ERROR_MAILSERVERCONNECTION":"K\u1ebft n\u1ed1i t\u1edbi mail server th\u1ea5t b\u1ea1i. Vui l\u00f2ng li\u00ean h\u1ec7 Qu\u1ea3n tr\u1ecb h\u1ec7 th\u1ed1ng.","LBL_EMAIL_ERROR_MOVE":"Di chuy\u1ec3n mail gi\u1eefa server","LBL_EMAIL_ERROR_MOVE_TITLE":"L\u1ed7i di chuy\u1ec3n","LBL_EMAIL_ERROR_NAME":"T\u00ean ph\u1ea3i c\u00f3","LBL_EMAIL_ERROR_FROM_ADDRESS":"\u0110\u1ecba ch\u1ec9 g\u1edfi ph\u1ea3i c\u00f3","LBL_EMAIL_ERROR_NO_FILE":"Vui l\u00f2ng cung c\u1ea5p m\u1ed9t file","LBL_EMAIL_ERROR_NO_IMAP_FOLDER_RENAME":"Th\u01b0 m\u1ee5c IMAP kh\u00f4ng h\u1ed7 tr\u1ee3 \u0111\u1ed5i t\u00ean l\u00fac n\u00e0y.","LBL_EMAIL_ERROR_SERVER":"\u0110\u1ecba ch\u1ec9 mail server ph\u1ea3i c\u00f3","LBL_EMAIL_ERROR_SAVE_ACCOUNT":"T\u00e0i kho\u1ea3n mail n\u00e0y kh\u00f4ng \u0111\u01b0\u1ee3c l\u01b0u.","LBL_EMAIL_ERROR_TIMEOUT":"C\u00f3 l\u1ed7i trong khi k\u1ebft n\u1ed1i t\u1edbi mail server.","LBL_EMAIL_ERROR_USER":"T\u00ean \u0111\u0103ng nh\u1eadp l\u00e0 b\u1eaft bu\u1ed9c","LBL_EMAIL_ERROR_PASSWORD":"M\u1eadt kh\u1ea9u l\u00e0 b\u1eaft bu\u1ed9c","LBL_EMAIL_ERROR_PORT":"C\u1ed5ng mail server l\u00e0 b\u1eaft bu\u1ed9c","LBL_EMAIL_ERROR_PROTOCOL":"Ph\u01b0\u01a1ng th\u1ee9c mail server l\u00e0 b\u1eaft bu\u1ed9c","LBL_EMAIL_ERROR_MONITORED_FOLDER":"Th\u01b0 m\u1ee5c theo d\u00f5i l\u00e0 b\u1eaft bu\u1ed9c","LBL_EMAIL_ERROR_TRASH_FOLDER":"Th\u01b0 m\u1ee5c r\u00e1c l\u00e0 b\u1eaft bu\u1ed9c","LBL_EMAIL_ERROR_VIEW_RAW_SOURCE":"Th\u00f4ng tin kh\u00f4ng c\u00f3 s\u1eb5n","LBL_EMAIL_FOLDERS":"<img src=themes\/default\/images\/icon_email_folder.gif align=absmiddle border=0> Th\u01b0 m\u1ee5c","LBL_EMAIL_FOLDERS_ACTIONS":"Chuy\u1ec3n t\u1edbi","LBL_EMAIL_FOLDERS_ADD":"Th\u00eam","LBL_EMAIL_FOLDERS_ADD_DIALOG_TITLE":"Th\u00eam th\u01b0 m\u1ee5c m\u1edbi","LBL_EMAIL_FOLDERS_ADD_NEW_FOLDER":"Th\u00eam nh\u00f3m th\u01b0 m\u1ee5c m\u1edbi","LBL_EMAIL_FOLDERS_ADD_THIS_TO":"Th\u00eam th\u01b0 m\u1ee5c n\u00e0y v\u00e0o","LBL_EMAIL_FOLDERS_CHANGE_HOME":"Th\u01b0 m\u1ee5c n\u00e0y kh\u00f4ng th\u1ec3 thay \u0111\u1ed5i","LBL_EMAIL_FOLDERS_DELETE_CONFIRM":"B\u1ea1n c\u00f3 ch\u1eafc mu\u1ed1n x\u00f3a th\u01b0 m\u1ee5c n\u00e0y? ","LBL_EMAIL_FOLDERS_NEW_FOLDER":"Th\u01b0 m\u1ee5c m\u1edbi","LBL_EMAIL_FOLDERS_NO_VALID_NODE":"Vui l\u00f2ng ch\u1ecdn m\u1ed9t th\u01b0 m\u1ee5c tr\u01b0\u1edbc \u0111\u00e3!","LBL_EMAIL_FOLDERS_TITLE":"Qu\u1ea3n l\u00fd th\u01b0 m\u1ee5c incomCRM","LBL_EMAIL_FOLDERS_USING_GROUP_USER":"S\u1eed d\u1ee5ng nh\u00f3m","LBL_EMAIL_FORWARD":"Chuy\u1ec3n ti\u1ebfp","LBL_EMAIL_DELIMITER":"::;::","LBL_EMAIL_DOWNLOAD_STATUS":"T\u1ea3i v\u1ec1 [[count]] c\u1ee7a [[total]] emails","LBL_EMAIL_FOUND":"T\u00ecm th\u1ea5y","LBL_EMAIL_FROM":"T\u1eeb","LBL_EMAIL_GROUP":"nh\u00f3m","LBL_EMAIL_HOME_FOLDER":"Trang ch\u00ednh","LBL_EMAIL_HTML_RTF":"G\u1edfi HTML","LBL_EMAIL_IE_DELETE":"X\u00f3a t\u00e0i kho\u1ea3n email","LBL_EMAIL_IE_DELETE_SIGNATURE":"X\u00f3a ch\u1eef k\u00ed","LBL_EMAIL_IE_DELETE_CONFIRM":"B\u1ea1n c\u00f3 ch\u1eafc ","LBL_EMAIL_IE_DELETE_SUCCESSFUL":"X\u00f3a th\u00e0nh c\u00f4ng","LBL_EMAIL_IE_SAVE":"L\u01b0u th\u00f4ng tin mail account","LBL_EMAIL_IMPORTING_EMAIL":"Nh\u1eadp email","LBL_EMAIL_IMPORT_EMAIL":"Nh\u1eadp t\u1eeb incomCRM","LBL_EMAIL_IMPORT_SETTINGS":"Nh\u1eadp thi\u1ebft l\u1eadp","LBL_EMAIL_INVALID":"Kh\u00f4ng h\u1ee3p l\u1ec7","LBL_EMAIL_LOADING":"\u0110ang t\u1ea3i...","LBL_EMAIL_MARK":"\u0110\u00e1nh d\u1ea5u","LBL_EMAIL_MARK_FLAGGED":"d\u1ef1ng c\u1edd","LBL_EMAIL_MARK_READ":"\u0111\u00e3 \u0111\u1ecdc","LBL_EMAIL_MARK_UNFLAGGED":"ch\u01b0a d\u1ef1ng c\u1edd","LBL_EMAIL_MARK_UNREAD":"ch\u01b0a \u0111\u1ecdc","LBL_EMAIL_ASSIGN_TO":"G\u00e1n cho","LBL_EMAIL_MENU_ADD_FOLDER":"T\u1ea1o th\u01b0 m\u1ee5c","LBL_EMAIL_MENU_COMPOSE":"So\u1ea1n th\u01b0 cho","LBL_EMAIL_MENU_DELETE_FOLDER":"X\u00f3a th\u01b0 m\u1ee5c","LBL_EMAIL_MENU_EDIT":"Ch\u1ec9nh s\u1eeda","LBL_EMAIL_MENU_EMPTY_TRASH":"X\u00f3a r\u1ed7ng th\u00f9ng r\u00e1c","LBL_EMAIL_MENU_SYNCHRONIZE":"\u0110\u1ed3ng b\u1ed9 h\u00f3a","LBL_EMAIL_MENU_CLEAR_CACHE":"X\u00f3a file cache","LBL_EMAIL_MENU_REMOVE":"X\u00f3a","LBL_EMAIL_MENU_RENAME":"\u0110\u1ed5i t\u00ean","LBL_EMAIL_MENU_RENAME_FOLDER":"\u0110\u1ed5i t\u00ean th\u01b0 m\u1ee5c","LBL_EMAIL_MENU_RENAMING_FOLDER":"\u0110\u1ed5i t\u00ean th\u01b0 m\u1ee5c","LBL_EMAIL_MENU_MAKE_SELECTION":"Vui l\u00f2ng ch\u1ecdn m\u1ed9t tr\u01b0\u1edbc khi th\u1ef1c hi\u1ec7n thao t\u00e1c.","LBL_EMAIL_MENU_HELP_ADD_FOLDER":"T\u1ea1o th\u01b0 m\u1ee5c (t\u1eeb xa ho\u1eb7c trong incomCRM)","LBL_EMAIL_MENU_HELP_ARCHIVE":"L\u01b0u nh\u1eefng email n\u00e0y v\u00e0o incomCRM","LBL_EMAIL_MENU_HELP_COMPOSE_TO_LIST":"G\u1edfi mail cho danh s\u00e1ch \u0111\u00e3 ch\u1ecdn","LBL_EMAIL_MENU_HELP_CONTACT_COMPOSE":"G\u1edfi mail cho li\u00ean h\u1ec7 n\u00e0y","LBL_EMAIL_MENU_HELP_CONTACT_REMOVE":"X\u00f3a li\u00ean h\u1ec7","LBL_EMAIL_MENU_HELP_DELETE":"X\u00f3a nh\u1eefng email n\u00e0y","LBL_EMAIL_MENU_HELP_DELETE_FOLDER":"X\u00f3a th\u01b0 m\u1ee5c (t\u1eeb xa ho\u1eb7c trong incomCRM)","LBL_EMAIL_MENU_HELP_EDIT_CONTACT":"Ch\u1ec9nh s\u1eeda li\u00ean h\u1ec7","LBL_EMAIL_MENU_HELP_EDIT_LIST":"Ch\u1ec9nh s\u1eeda danh s\u00e1ch nh\u1eadn mail","LBL_EMAIL_MENU_HELP_EMPTY_TRASH":"X\u00f3a tr\u1ed1ng t\u1ea5t c\u1ea3 th\u01b0 m\u1ee5c r\u00e1c trong t\u00e0i kho\u1ea3n email c\u1ee7a b\u1ea1n","LBL_EMAIL_MENU_HELP_MARK_FLAGGED":"\u0110\u00e1nh d\u1ea5u nh\u1eefng email n\u00e0y \u0111\u00e3 d\u1ef1ng c\u1edd","LBL_EMAIL_MENU_HELP_MARK_READ":"\u0110\u00e1nh d\u1ea5u nh\u1eefng email n\u00e0y \u0111\u00e3 \u0111\u1ecdc","LBL_EMAIL_MENU_HELP_MARK_UNFLAGGED":"\u0110\u00e1nh d\u1ea5u nh\u1eefng email n\u00e0y ch\u01b0a d\u1ef1ng c\u1edd","LBL_EMAIL_MENU_HELP_MARK_UNREAD":"\u0110\u00e1nh d\u1ea5u nh\u1eefng email n\u00e0y ch\u01b0a \u0111\u1ecdc ","LBL_EMAIL_MENU_HELP_REMOVE_LIST":"X\u00f3a danh s\u00e1ch nh\u1eadn mail","LBL_EMAIL_MENU_HELP_RENAME_FOLDER":"\u0110\u1ed5i t\u00ean th\u01b0 m\u1ee5c (t\u1eeb xa ho\u1eb7c trong incomCRM)","LBL_EMAIL_MENU_HELP_REPLY":"Tr\u1ea3 l\u1eddi nh\u1eefng email n\u00e0y","LBL_EMAIL_MENU_HELP_REPLY_ALL":"Tr\u1ea3 l\u1eddi t\u1ea5t c\u1ea3 nh\u1eefng ng\u01b0\u1eddi nh\u1eadn email n\u00e0y","LBL_EMAIL_MESSAGES":"tin nh\u1eafn","LBL_EMAIL_ML_NAME":"T\u00ean danh s\u00e1ch","LBL_EMAIL_ML_ADDRESSES_1":"Nh\u1eefng \u0111\u1ecba ch\u1ec9 danh s\u00e1ch \u0111\u01b0\u1ee3c ch\u1ecdn","LBL_EMAIL_ML_ADDRESSES_2":"Nh\u1eefng \u0111\u1ecba ch\u1ec9 danh s\u00e1ch c\u00f3 s\u1eb5n","LBL_EMAIL_MULTISELECT":"<b>Ctrl-Click<\/b>\u0111\u1ec3 ch\u1ecdn nhi\u1ec1u<br \/>(Ng\u01b0\u1eddi d\u00f9ng Mac  <b>CMD-Click<\/b>)","LBL_EMAIL_NO":"Kh\u00f4ng","LBL_EMAIL_OK":"OK","LBL_EMAIL_ONE_MOMENT":"Vui l\u00f2ng ch\u1edd gi\u00e2y l\u00e1t...","LBL_EMAIL_OPEN_ALL":"M\u1edf nhi\u1ec1u tin nh\u1eafn m\u1ed9t l\u00fac","LBL_EMAIL_OPTIONS":"T\u00f9y ch\u1ecdn","LBL_EMAIL_OPT_OUT":"Kh\u00f4ng ch\u1ecdn","LBL_EMAIL_PAGE_AFTER":" {0}","LBL_EMAIL_PAGE_BEFORE":"Trang","LBL_EMAIL_PERFORMING_TASK":"Th\u1ef1c hi\u1ec7n nhi\u1ec7m v\u1ee5","LBL_EMAIL_PRIMARY":"Ch\u00ednh","LBL_EMAIL_PRINT":"In","LBL_EMAIL_QC_BUGS":"L\u1ed7i","LBL_EMAIL_QC_CASES":"V\u1ee5 vi\u1ec7c","LBL_EMAIL_QC_LEADS":"\u0110\u1ea7u m\u1ed1i","LBL_EMAIL_QC_CONTACTS":"Ng\u01b0\u1eddi li\u00ean h\u1ec7","LBL_EMAIL_QC_TASKS":"T\u00e1c v\u1ee5","LBL_EMAIL_QUICK_CREATE":"T\u1ea1o nhanh","LBL_EMAIL_REBUILDING_FOLDERS":"Th\u01b0 m\u1ee5c l\u00e0m l\u1ea1i","LBL_EMAIL_RELATE_TO":"Li\u00ean quan","LBL_EMAIL_VIEW_RELATIONSHIPS":"Xem m\u1ed1i quan h\u1ec7","LBL_EMAIL_RECORD":"H\u1ed3 s\u01a1 email","LBL_EMAIL_REMOVE":"X\u00f3a","LBL_EMAIL_REPLY":"Tr\u1ea3 l\u1eddi","LBL_EMAIL_REPLY_ALL":"Tr\u1ea3 l\u1eddi t\u1ea5t c\u1ea3","LBL_EMAIL_REPLY_TO":"Tr\u1ea3 l\u1eddi t\u1edbi","LBL_EMAIL_RETRIEVING_LIST":"Danh s\u00e1ch mail nh\u1eadn","LBL_EMAIL_RETRIEVING_MESSAGE":"Tin nh\u1eafn nh\u1eadn","LBL_EMAIL_RETRIEVING_RECORD":"H\u1ed3 s\u01a1 email nh\u1eadn","LBL_EMAIL_SELECT_ONE_RECORD":"Vui l\u00f2ng ch\u1ecdn m\u1ed9t","LBL_EMAIL_RETURN_TO_VIEW":"Quay l\u1ea1i m\u00f4-\u0111un tr\u01b0\u1edbc?","LBL_EMAIL_REVERT":"Quay l\u1ea1i","LBL_EMAIL_RELATE_EMAIL":"Email li\u00ean quan","LBL_EMAIL_RULES_TITLE":"Qu\u1ea3n l\u00fd lu\u1eadt","LBL_EMAIL_SAVE":"L\u01b0u","LBL_EMAIL_SAVE_AND_REPLY":"L\u01b0u & tr\u1ea3 l\u1eddi","LBL_EMAIL_SAVE_DRAFT":"L\u01b0u b\u1ea3n th\u1ea3o","LBL_EMAIL_SEARCHING":"Ti\u1ebfn h\u00e0nh t\u00ecm ki\u1ebfm","LBL_EMAIL_SEARCH":"<img src=themes\/default\/images\/Search.gif align=absmiddle border=0>T\u00ecm ki\u1ebfm","LBL_EMAIL_SEARCH_ADVANCED":"T\u00ecm n\u00e2ng cao","LBL_EMAIL_SEARCH_DATE_FROM":"T\u1eeb ng\u00e0y","LBL_EMAIL_SEARCH_DATE_UNTIL":"\u0110\u1ebfn ng\u00e0y","LBL_EMAIL_SEARCH_FULL_TEXT":"N\u1ed9i dung","LBL_EMAIL_SEARCH_NO_RESULTS":"Kh\u00f4ng t\u00ecm th\u1ea5y k\u1ebft qu\u1ea3 ph\u00f9 h\u1ee3p v\u1edbi ti\u00eau ch\u00ed c\u1ee7a b\u1ea1n.","LBL_EMAIL_SEARCH_RESULTS_TITLE":"K\u1ebft qu\u1ea3 t\u00ecm ki\u1ebfm","LBL_EMAIL_SEARCH_TITLE":"T\u00ecm c\u01a1 b\u1ea3n","LBL_EMAIL_SEARCH__FROM_ACCOUNTS":"T\u00ecm t\u00e0i kho\u1ea3n email","LBL_EMAIL_SELECT":"Ch\u1ecdn","LBL_EMAIL_SEND":"G\u1eedi","LBL_EMAIL_SENDING_EMAIL":"\u0110ang g\u1eedi email","LBL_EMAIL_SETTINGS":"Thi\u1ebft l\u1eadp","LBL_EMAIL_SETTINGS_2_ROWS":"2 d\u00f2ng","LBL_EMAIL_SETTINGS_3_COLS":"3 c\u1ed9t","LBL_EMAIL_SETTINGS_LAYOUT":"Ki\u1ec3u Layout","LBL_EMAIL_SETTINGS_ACCOUNTS":"T\u00e0i kho\u1ea3n email","LBL_EMAIL_SETTINGS_ADD_ACCOUNT":"X\u00f3a m\u00e0n h\u00ecnh","LBL_EMAIL_SETTINGS_AUTO_IMPORT":"Nh\u1eadp email ","LBL_EMAIL_SETTINGS_CHECK_INTERVAL":"Ki\u1ec3m tra c\u00f3 mail m\u1edbi","LBL_EMAIL_SETTINGS_COMPOSE_INLINE":"S\u1eed d\u1ee5ng khung xem tr\u01b0\u1edbc","LBL_EMAIL_SETTINGS_COMPOSE_POPUP":"S\u1eed d\u1ee5ng c\u1eeda s\u1ed5 pop-up","LBL_EMAIL_SETTINGS_DISPLAY_NUM":"S\u1ed1 email tr\u00ean trang","LBL_EMAIL_SETTINGS_EDIT_ACCOUNT":"Ch\u1ec9nh s\u1eeda t\u00e0i kho\u1ea3n email ","LBL_EMAIL_SETTINGS_FOLDERS":"Th\u01b0 m\u1ee5c","LBL_EMAIL_SETTINGS_FROM_ADDR":"\u0110\u1ecba ch\u1ec9 g\u1edfi","LBL_EMAIL_SETTINGS_FROM_NAME":"T\u00ean g\u1edfi","LBL_EMAIL_SETTINGS_FULL_SCREEN":"Xem \u0111\u1ea7y \u0111\u1ee7","LBL_EMAIL_SETTINGS_FULL_SYNC":"\u0110\u1ed3ng b\u1ed9 h\u00f3a t\u1ea5t c\u1ea3 t\u00e0i kho\u1ea3n email","LBL_EMAIL_SETTINGS_FULL_SYNC_DESC":"Th\u1ef1c hi\u1ec7n h\u00e0nh \u0111\u1ed9ng n\u00e0y s\u1ebd \u0111\u1ed3ng b\u1ed9 h\u00f3a t\u00e0i kho\u1ea3n email v\u00e0 nh\u1eefng n\u1ed9i dung c\u1ee7a ch\u00fang","LBL_EMAIL_SETTINGS_FULL_SYNC_WARN":"Th\u1ef1c hi\u1ec7n \u0111\u1ea7y \u0111\u1ee7 \u0111\u1ed3ng b\u1ed9 h\u00f3a? n M\u1ed9t l\u01b0\u1ee3ng l\u1edbn t\u00e0i kho\u1ea3n email s\u1ebd t\u1ed1n nhi\u1ec1u th\u1eddi gian h\u01a1n.","LBL_EMAIL_SUBSCRIPTION_FOLDER_HELP":"Click ph\u00edm Shift ho\u1eb7c Ctrl \u0111\u1ec3 ch\u1ecdn nhi\u1ec1u th\u01b0 m\u1ee5c.","LBL_EMAIL_SETTINGS_GENERAL":"Chung","LBL_EMAIL_SETTINGS_GROUP_FOLDERS":"Nh\u1eefng nh\u00f3m th\u01b0 m\u1ee5c s\u1eb5n c\u00f3","LBL_EMAIL_SETTINGS_GROUP_FOLDERS_CREATE":"T\u1ea1o nh\u00f3m th\u01b0 m\u1ee5c","LBL_EMAIL_SETTINGS_GROUP_FOLDERS_Save":"L\u01b0u nh\u00f3m th\u01b0 m\u1ee5c","LBL_EMAIL_SETTINGS_RETRIEVING_GROUP":"Nh\u00f3m th\u01b0 m\u1ee5c nh\u1eadn \u0111\u01b0\u1ee3c","LBL_EMAIL_SETTINGS_GROUP_FOLDERS_EDIT":"Ch\u1ec9nh s\u1eeda nh\u00f3m th\u01b0 m\u1ee5c","LBL_EMAIL_SETTINGS_NAME":"T\u00ean","LBL_EMAIL_SETTINGS_REQUIRE_REFRESH":"Nh\u1eefng thi\u1ebft l\u1eadp n\u00e0y c\u00f3 th\u1ec3 c\u1ea7n ph\u1ea3i load l\u1ea1i trang.","LBL_EMAIL_SETTINGS_RETRIEVING_ACCOUNT":"T\u00e0i kho\u1ea3n email nh\u1eadn","LBL_EMAIL_SETTINGS_RULES":"Lu\u1eadt","LBL_EMAIL_SETTINGS_SAVED":"Nh\u1eefng thi\u1ebft l\u1eadp \u0111\u01b0\u1ee3c l\u01b0u. B\u1ea1n ph\u1ea3i t\u1ea3i l\u1ea1i trang \u0111\u1ec3 nh\u1eefng thi\u1ebft l\u1eadp m\u1edbi n\u00e0y c\u00f3 hi\u1ec7u l\u1ef1c.","LBL_EMAIL_SETTINGS_SAVE_OUTBOUND":"Sao ch\u00e9p t\u1edbi m\u1ee5c Email g\u1edfi ","LBL_EMAIL_SETTINGS_SEND_EMAIL_AS":"G\u1edfi email \u0111\u1ecbnh d\u1ea1ng v\u0103n b\u1ea3n thu\u1ea7n","LBL_EMAIL_SETTINGS_SHOW_IN_FOLDERS":"T\u00e0i kho\u1ea3n email ho\u1ea1t \u0111\u1ed9ng","LBL_EMAIL_SETTINGS_SHOW_NUM_IN_LIST":"S\u1ed1 email tr\u00ean trang","LBL_EMAIL_SETTINGS_TAB_POS":"\u0110\u1eb7t tab d\u01b0\u1edbi bottom","LBL_EMAIL_SETTINGS_TITLE_LAYOUT":"Thi\u1ebft l\u1eadp tr\u1ef1c quan","LBL_EMAIL_SETTINGS_TITLE_PREFERENCES":"T\u00f9y ch\u1ecdn","LBL_EMAIL_SETTINGS_TOGGLE_ADV":"Hi\u1ec3n th\u1ecb n\u00e2ng cao","LBL_EMAIL_SETTINGS_USER_FOLDERS":"Th\u01b0 m\u1ee5c ng\u01b0\u1eddi d\u00f9ng c\u00f3 s\u1eb5n ","LBL_EMAIL_SHOW_READ":"Hi\u1ec3n th\u1ecb t\u1ea5t c\u1ea3","LBL_EMAIL_SHOW_UNREAD_ONLY":"Hi\u1ec3n th\u1ecb ch\u1ec9 \u0111\u1ecdc","LBL_EMAIL_SIGNATURES":"Ch\u1eef k\u00ed","LBL_EMAIL_SIGNATURE_CREATE":"T\u1ea1o ch\u1eef k\u00ed","LBL_EMAIL_SIGNATURE_NAME":"T\u00ean ch\u1eef k\u00ed","LBL_EMAIL_SIGNATURE_TEXT":"N\u1ed9i dung ch\u1eef k\u00ed","LBL_EMAIL_SPACER_MAIL_SERVER":"[ Th\u01b0 m\u1ee5c t\u1eeb xa ]","LBL_EMAIL_SPACER_LOCAL_FOLDER":"[ Th\u01b0 m\u1ee5c incomCRM]","LBL_EMAIL_SUBJECT":"Ch\u1ee7 \u0111\u1ec1 ","LBL_EMAIL_TO":"T\u1eeb","LBL_EMAIL_SUCCESS":"Th\u00e0nh c\u00f4ng","LBL_EMAIL_incomCRM_FOLDER":"Th\u01b0 m\u1ee5c incomCRM","LBL_EMAIL_TEMPLATES":"M\u1eabu","LBL_EMAIL_TEXT_FIRST":"Trang \u0111\u1ea7u","LBL_EMAIL_TEXT_PREV":"Trang tr\u01b0\u1edbc","LBL_EMAIL_TEXT_NEXT":"Trang ti\u1ebfp","LBL_EMAIL_TEXT_LAST":"Trang cu\u1ed1i","LBL_EMAIL_TEXT_REFRESH":"C\u1eadp nh\u1eadt","LBL_EMAIL_TOGGLE_LIST":"Danh s\u00e1ch cu\u1ed9n","LBL_EMAIL_VIEW":"Xem","LBL_EMAIL_VIEWS":"Xem","LBL_EMAIL_VIEW_HEADERS":"Hi\u1ec3n th\u1ecb ph\u1ea7n \u0111\u1ea7u","LBL_EMAIL_VIEW_PRINTABLE":"Phi\u1ec3n b\u1ea3n in \u0111\u01b0\u1ee3c","LBL_EMAIL_VIEW_RAW":"Hi\u1ec3n th\u1ecb email d\u1ea1ng th\u00f4","LBL_EMAIL_VIEW_UNSUPPORTED":"T\u00ednh n\u0103ng n\u00e0y kh\u00f4ng \u0111\u01b0\u1ee3c h\u1ed7 tr\u1ee3 v\u1edbi POP3.","LBL_DEFAULT_LINK_TEXT":"M\u1eb7c \u0111\u1ecbnh li\u00ean k\u1ebft v\u0103n b\u1ea3n.","LBL_EMAIL_YES":"\u0110\u1ed1ng \u00fd","LBL_EMAIL_CHECK_INTERVAL_DOM":{"-1":"Th\u1ee7 c\u00f4ng","5":"M\u1ed7i 5 ph\u00fat","15":"M\u1ed7i 15 ph\u00fat","30":"M\u1ed7i 30 ph\u00fat","60":"M\u1ed7i gi\u1edd"},"LBL_EMAIL_SETTING_NUM_DOM":{"10":"10","20":"20","50":"50"},"LBL_EMAIL_MESSAGE_NO":"M\u00e3 tin nh\u1eafn","LBL_EMAIL_IMPORT_SUCCESS":"Nh\u1eadp th\u00e0nh c\u00f4ng","LBL_EMAIL_IMPORT_FAIL":"Nh\u1eadp th\u1ea5t b\u1ea1i v\u00ec c\u00f3 m\u1ed9t tin nh\u1eafn kh\u00e1c \u0111ang \u0111\u01b0\u1ee3c nh\u1eadp ho\u1eb7c x\u00f3a t\u1eeb server","LBL_LINK_NONE":"Kh\u00f4ng","LBL_LINK_ALL":"T\u1ea5t c\u1ea3","LBL_LINK_RECORDS":"H\u1ed3 s\u01a1","LBL_LINK_SELECT":"Ch\u1ecdn","ERR_CREATING_FIELDS":"L\u1ed7i \u0111i\u1ec1n th\u00f4ng tin th\u00eam: ","ERR_CREATING_TABLE":"L\u1ed7i x\u1ea3y ra khi t\u1ea1o b\u1ea3ng: ","ERR_DECIMAL_SEP_EQ_THOUSANDS_SEP":"Ph\u00e2n c\u00e1ch s\u1ed1 th\u1eadp ph\u00e2n kh\u00f4ng s\u1eed d\u1ee5ng c\u00f9ng m\u1ed9t k\u00ed t\u1ef1 nh\u01b0 ph\u00e2n c\u00e1ch h\u00e0ng ng\u00e0n. nn Vui l\u00f2ng thay \u0111\u1ed5i.","ERR_DELETE_RECORD":"M\u1ed9t h\u1ed3 s\u01a1 ph\u1ea3i \u0111\u01b0\u1ee3c ch\u1ecdn \u0111\u1ec3 x\u00f3a.","ERR_EXPORT_DISABLED":"Kh\u00f3a ch\u1ee9c n\u0103ng xu\u1ea5t ra.","ERR_EXPORT_TYPE":"L\u1ed7i xu\u1ea5t ra.","ERR_INVALID_AMOUNT":"Vui l\u00f2ng nh\u1eadp m\u1ed9t s\u1ed1 l\u01b0\u1ee3ng h\u1ee3p l\u1ec7","ERR_INVALID_DATE_FORMAT":"\u0110\u1ecbnh d\u1ea1ng ng\u00e0y ph\u1ea3i l\u00e0: ","ERR_INVALID_DATE":"Vui l\u00f2ng nh\u1eadp gi\u00e1 tr\u1ecb ng\u00e0y h\u1ee3p l\u1ec7:","ERR_INVALID_DAY":"Vui l\u00f2ng nh\u1eadp ng\u00e0y h\u1ee3p l\u1ec7:","ERR_INVALID_EMAIL_ADDRESS":"kh\u00f4ng l\u00e0 \u0111\u1ecba ch\u1ec9 email h\u1ee3p l\u1ec7.","ERR_INVALID_FILE_REFERENCE":"T\u1eadp tin tham kh\u1ea3o kh\u00f4ng h\u1ee3p l\u1ec7.","ERR_INVALID_HOUR":"Vui l\u00f2ng nh\u1eadp m\u1ed9t gi\u00e1 tr\u1ecb gi\u1edd h\u1ee3p l\u1ec7.","ERR_INVALID_MONTH":"Vui l\u00f2ng nh\u1eadp m\u1ed9t th\u00e1ng h\u1ee3p l\u1ec7.","ERR_INVALID_TIME":"Vui l\u00f2ng nh\u1eadp m\u1ed9t gi\u00e1 tr\u1ecb th\u1eddi gian h\u1ee3p l\u1ec7.","ERR_INVALID_YEAR":"Vui l\u00f2ng nh\u1eadp 4 ch\u1eef s\u1ed1 cho gi\u00e1 tr\u1ecb n\u0103m.","ERR_NEED_ACTIVE_SESSION":"M\u1ed9t phi\u00ean l\u00e0m vi\u1ec7c \u0111ang ho\u1ea1t \u0111\u1ed9ng c\u1ea7n xu\u1ea5t d\u1eef li\u1ec7u ra.","ERR_NO_HEADER_ID":"T\u00ednh n\u0103ng n\u00e0y hi\u1ec7n t\u1ea1i kh\u00f4ng c\u00f3 s\u1eb5n trong ki\u1ec3u giao di\u1ec7n n\u00e0y.","ERR_NOT_ADMIN":"Kh\u00f4ng ch\u1ee9ng th\u1ef1c \u0111\u01b0\u1ee3c quy\u1ec1n truy c\u1eadp v\u1edbi qu\u1ea3n tr\u1ecb h\u1ec7 th\u1ed1ng.","ERR_MISSING_REQUIRED_FIELDS":"Tr\u01b0\u1eddng c\u00f2n thi\u1ebfu:","ERR_INVALID_REQUIRED_FIELDS":"Tr\u01b0\u1eddng kh\u00f4ng h\u1ee3p l\u1ec7:","ERR_INVALID_VALUE":"Gi\u00e1 tr\u1ecb kh\u00f4ng h\u1ee3p l\u1ec7:","ERR_NO_SUCH_FILE":"File kh\u00f4ng t\u1ed3n t\u1ea1i tr\u00ean h\u1ec7 th\u1ed1ng","ERR_NO_SINGLE_QUOTE":"Kh\u00f4ng th\u1ec3 s\u1eed d\u1ee5ng d\u1ea5u nh\u00e1y \u0111\u01a1n \u0111\u1ec3 \u0111\u00e1nh d\u1ea5u","ERR_NOTHING_SELECTED":"Vui l\u00f2ng th\u1ef1c hi\u1ec7n ch\u1ecdn tr\u01b0\u1edbc khi thao t\u00e1c.","ERR_OPPORTUNITY_NAME_DUPE":"M\u1ed9t c\u01a1 h\u1ed9i v\u1edbi t\u00ean %s \u0111ang t\u1ed3n t\u1ea1i. Vui l\u00f2ng nh\u1eadp t\u00ean kh\u00e1c b\u00ean d\u01b0\u1edbi.","ERR_OPPORTUNITY_NAME_MISSING":"T\u00ean c\u01a1 h\u1ed9i ch\u01b0a \u0111\u01b0\u1ee3c nh\u1eadp.  Vui l\u00f2ng nh\u1eadp t\u00ean c\u01a1 h\u00f4i kh\u00e1c b\u00ean d\u01b0\u1edbi.","ERR_POTENTIAL_SEGFAULT":"M\u1ed9t l\u1ed7i ti\u1ec1m \u1ea9n c\u1ee7a Apache segmentation \u0111\u01b0\u1ee3c ph\u00e1t hi\u1ec7n. Vui l\u00f2ng th\u00f4ng b\u00e1o cho qu\u1ea3n tr\u1ecb h\u1ec7 th\u1ed1ng \u0111\u1ec3 x\u00e1c nh\u1eadn v\u1ea5n \u0111\u1ec1 n\u00e0y v\u00e0 nh\u1edd ai \u0111\u00f3 b\u00e1o c\u00e1o v\u1ea5n \u0111\u1ec1 n\u00e0y cho incomCRM","ERR_SELF_REPORTING":"Ng\u01b0\u1eddi d\u00f9ng kh\u00f4ng th\u1ec3 b\u00e1o c\u00e1o","ERR_SINGLE_QUOTE":"D\u00f9ng d\u1ea5u nh\u00e1y \u0111\u01a1n kh\u00f4ng \u0111\u01b0\u1ee3c h\u1ed7 tr\u1ee3 cho tr\u01b0\u1eddng n\u00e0y. Vui l\u00f2ng thay \u0111\u1ed5i gi\u00e1 tr\u1ecb.","ERR_SQS_NO_MATCH_FIELD":"Kh\u00f4ng ph\u00f9 h\u1ee3p v\u1edbi tr\u01b0\u1eddng: ","ERR_SQS_NO_MATCH":"Kh\u00f4ng ph\u00f9 h\u1ee3p","ERR_ADDRESS_KEY_NOT_SPECIFIED":"Vui l\u00f2ng x\u00e1c \u0111\u1ecbnh ch\u1ec9 m\u1ee5c \"key\" trong thu\u1ed9c t\u00ednh displayParams cho Meta-Data","ERR_EXISTING_PORTAL_USERNAME":"L\u1ed7i: T\u00ean Portal \u0111\u00e3 \u0111\u01b0\u1ee3c g\u00e1n cho m\u1ed9t li\u00ean h\u1ec7 kh\u00e1c.","ERR_COMPATIBLE_PRECISION_VALUE":"Gi\u00e1 tr\u1ecb c\u1ee7a tr\u01b0\u1eddng kh\u00f4ng t\u01b0\u01a1ng th\u00edch v\u1edbi gi\u00e1 tr\u1ecb th\u1ef1c c\u1ee7a n\u00f3.","LBL_ACCOUNT":"Kh\u00e1ch h\u00e0ng","LBL_OLD_ACCOUNT_LINK":"Kh\u00e1ch h\u00e0ng c\u0169","LBL_ACCOUNTS":"Kh\u00e1ch h\u00e0ng","LBL_ACTIVITIES_SUBPANEL_TITLE":"Ho\u1ea1t \u0111\u1ed9ng","LBL_ACCUMULATED_HISTORY_BUTTON_KEY":"H","LBL_ACCUMULATED_HISTORY_BUTTON_LABEL":"Xem t\u1ed5ng k\u1ebft","LBL_ACCUMULATED_HISTORY_BUTTON_TITLE":"Xem t\u1ed5ng k\u1ebft [Alt+H]","LBL_ADD_BUTTON_KEY":"A","LBL_ADD_BUTTON_TITLE":"Th\u00eam [Alt+A]","LBL_ADD_BUTTON":"Th\u00eam","LBL_ADD_DOCUMENT":"Th\u00eam t\u00e0i li\u1ec7u","LBL_ADD_TO_PROSPECT_LIST_BUTTON_KEY":"L","LBL_ADD_TO_PROSPECT_LIST_BUTTON_LABEL":"Th\u00eam v\u00e0o danh s\u00e1ch m\u1ee5c ti\u00eau","LBL_ADD_TO_PROSPECT_LIST_BUTTON_TITLE":"Th\u00eam v\u00e0o danh s\u00e1ch m\u1ee5c ti\u00eau","LBL_ADDITIONAL_DETAILS_CLOSE_TITLE":"\u1ea4n chu\u1ed9t \u0111\u1ec3 \u0111\u00f3ng","LBL_ADDITIONAL_DETAILS_CLOSE":"\u0110\u00f3ng","LBL_ADDITIONAL_DETAILS":"Th\u00eam chi ti\u1ebft","LBL_ADMIN":"Qu\u1ea3n tr\u1ecb","LBL_ALT_HOT_KEY":"Alt+","LBL_ARCHIVE":"L\u01b0 tr\u1eef","LBL_ASSIGNED_TO_USER":"Nv.Ql\u00fd","LBL_ASSIGNED_TO":"Nv.Ql\u00fd","LBL_BACK":"Quay l\u1ea1i","LBL_BILL_TO_ACCOUNT":"Thanh to\u00e1n cho Kh\u00e1ch h\u00e0ng","LBL_BILL_TO_CONTACT":"Thanh to\u00e1n cho Ng\u01b0\u1eddi LH","LBL_BILLING_ADDRESS":"\u0110\u1ecba ch\u1ec9 thanh to\u00e1n","LBL_BROWSER_TITLE":"incomSoft","LBL_BUGS":"L\u1ed7i","LBL_BY":"b\u1edfi","LBL_CALLS":"Cu\u1ed9c g\u1ecdi","LBL_CALL":"Cu\u1ed9c g\u1ecdi","LBL_CAMPAIGNS_SEND_QUEUED":"G\u1edfi Campaign Emails trong h\u00e0ng \u0111\u1ee3i","LBL_CANCEL_BUTTON_KEY":"X","LBL_CANCEL_BUTTON_LABEL":"Quay v\u1ec1","LBL_CANCEL_BUTTON_TITLE":"Quay v\u1ec1 [Alt+X]","LBL_SUBMIT_BUTTON_LABEL":"Ch\u1ea5p nh\u1eadn","LBL_CASE":"V\u1ee5 vi\u1ec7c","LBL_CASES":"V\u1ee5 vi\u1ec7c","LBL_CHANGE_BUTTON_KEY":"G","LBL_CHANGE_BUTTON_LABEL":"Thay \u0111\u1ed5i","LBL_CHANGE_BUTTON_TITLE":"Thay \u0111\u1ed5i [Alt+G]","LBL_CHARSET":"UTF-8","LBL_CHECKALL":"\u0110\u00e1nh d\u1ea5u t\u1ea5t c\u1ea3","LBL_CITY":"Th\u00e0nh ph\u1ed1","LBL_CLEAR_BUTTON_KEY":"C","LBL_CLEAR_BUTTON_LABEL":"X\u00f3a","LBL_CLEAR_BUTTON_TITLE":"X\u00f3a [Alt+C]","LBL_CLEARALL":"B\u1ecf ch\u1ecdn t\u1ea5t c\u1ea3","LBL_CLOSE_BUTTON_TITLE":"\u0110\u00f3ng","LBL_CLOSE_BUTTON_KEY":"Q","LBL_CLOSE_WINDOW":"\u0110\u00f3ng c\u1eeda s\u1ed5","LBL_CLOSEALL_BUTTON_KEY":"Q","LBL_CLOSEALL_BUTTON_LABEL":"\u0110\u00f3ng t\u1ea5t c\u1ea3","LBL_CLOSEALL_BUTTON_TITLE":"\u0110\u00f3ng t\u1ea5t c\u1ea3 [Alt+I]","LBL_CLOSE_AND_CREATE_BUTTON_LABEL":"L\u01b0u Ho\u00e0n t\u1ea5t & T\u1ea1o m\u1edbi","LBL_CLOSE_AND_CREATE_BUTTON_TITLE":"\u0110\u00f3ng v\u00e0 t\u1ea1o m\u1edbi","LBL_CLOSE_AND_CREATE_BUTTON_KEY":"C","LBL_COMPOSE_EMAIL_BUTTON_KEY":"L","LBL_COMPOSE_EMAIL_BUTTON_LABEL":"So\u1ea1n Email","LBL_COMPOSE_EMAIL_BUTTON_TITLE":"So\u1ea1n Email [Alt+L]","LBL_SEARCH_DROPDOWN_YES":"C\u00f3","LBL_SEARCH_DROPDOWN_NO":"Kh\u00f4ng","LBL_CONTACT_LIST":"Danh s\u00e1ch ng\u01b0\u1eddi li\u00ean h\u1ec7","LBL_CONTACT":"Ng\u01b0\u1eddi li\u00ean h\u1ec7","LBL_CONTACTS":"Ng\u01b0\u1eddi li\u00ean h\u1ec7","LBL_CONTRACTS":"H\u1ee3p \u0111\u1ed3ng","LBL_COUNTRY":"Qu\u1ed1c gia:","LBL_CREATE_BUTTON_LABEL":"T\u1ea1o","LBL_CREATED_BY_USER":"T\u1ea1o b\u1edfi ng\u01b0\u1eddi d\u00f9ng","LBL_CREATED_USER":"Ng\u01b0\u1eddi t\u1ea1o","LBL_CREATED_ID":"M\u00e3 ng\u01b0\u1eddi t\u1ea1o","LBL_CREATED":"T\u1ea1o b\u1edfi","LBL_CURRENT_USER_FILTER":"Ch\u1ec9 c\u1ee7a t\u00f4i:","LBL_CURRENCY":"Ti\u1ec1n t\u1ec7:","LBL_DOCUMENTS":"T\u00e0i li\u1ec7u","LBL_DATE_ENTERED":"Ng\u00e0y t\u1ea1o","LBL_DATE_MODIFIED":"Thay \u0111\u1ed5i cu\u1ed1i","LBL_DELETE_BUTTON_KEY":"D","LBL_DELETE_BUTTON_LABEL":"X\u00f3a","LBL_DELETE_BUTTON_TITLE":"X\u00f3a [Alt+D]","LBL_DELETE_BUTTON":"X\u00f3a","LBL_DELETE":"X\u00f3a","LBL_DELETED":"X\u00f3a","LBL_DIRECT_REPORTS":"B\u00e1o c\u00e1o tr\u1ef1c ti\u1ebfp","LBL_DONE_BUTTON_KEY":"X","LBL_DONE_BUTTON_LABEL":"L\u00e0m","LBL_DONE_BUTTON_TITLE":"L\u00e0m [Alt+X]","LBL_DST_NEEDS_FIXIN":"The application requires a Daylight Saving Time fix to be applied.  Please go to the <a href=\"index.php?module=Administration&action=DstFix\">Repair<\/a> link in the Admin console and apply the Daylight Saving Time fix.","LBL_DUPLICATE_BUTTON_KEY":"U","LBL_DUPLICATE_BUTTON_LABEL":"Nh\u00e2n \u0111\u00f4i","LBL_DUPLICATE_BUTTON_TITLE":"Nh\u00e2n \u0111\u00f4i[Alt+U]","LBL_DUPLICATE_BUTTON":"Nh\u00e2n \u0111\u00f4i","LBL_DOWNLOAD":"T\u1ea3i v\u1ec1","LBL_EDIT_BUTTON_KEY":"E","LBL_EDIT_BUTTON_LABEL":"Ch\u1ec9nh s\u1eeda","LBL_EDIT_BUTTON_TITLE":"Ch\u1ec9nh s\u1eeda [Alt+E]","LBL_EDIT_BUTTON":"Ch\u1ec9nh s\u1eeda","LBL_EDIT_AS_NEW_BUTTON_LABEL":"Th\u00eam m\u1edbi","LBL_EDIT_AS_NEW_BUTTON_TITLE":"Th\u00eam m\u1edbi","LBL_VCARD":"vCard","LBL_EMPTY_VCARD":"Please select a vCard file","LBL_IMPORT_VCARD":"Import vCard:","LBL_IMPORT_VCARD_BUTTON_KEY":"I","LBL_IMPORT_VCARD_BUTTON_LABEL":"Import vCard","LBL_IMPORT_VCARD_BUTTON_TITLE":"Import vCard [Alt+I]","LBL_VIEW_BUTTON_KEY":"V","LBL_VIEW_BUTTON_LABEL":"Xem","LBL_VIEW_BUTTON_TITLE":"Xem [Alt+V]","LBL_VIEW_BUTTON":"Xem","LBL_EMAIL_PDF_BUTTON_KEY":"M","LBL_EMAIL_PDF_BUTTON_LABEL":"Email nh\u01b0 PDF","LBL_EMAIL_PDF_BUTTON_TITLE":"Email nh\u01b0 PDF [Alt+M]","LBL_EMAILS":"Emails","LBL_EMPLOYEES":"Nh\u00e2n vi\u00ean","LBL_ENTER_DATE":"Nh\u1eadp ng\u00e0y","LBL_EXPORT_ALL":"Xu\u1ea5t t\u1ea5t c\u1ea3","LBL_EXPORT":"Xu\u1ea5t","LBL_GO_BUTTON_LABEL":"Th\u1ef1c hi\u1ec7n","LBL_HIDE":"\u1ea8n","LBL_ID":"ID","LBL_IMPORT_PROSPECTS":"Nh\u1eadp M\u1ee5c ti\u00eau","LBL_IMPORT":"Nh\u1eadp","LBL_IMPORT_STARTED":"Nh\u1eadp b\u1eaft \u0111\u1ea7u:","LBL_MISSING_CUSTOM_DELIMITER":"Ph\u1ea3i x\u00e1c \u0111inh d\u1ea5u ph\u00e2n c\u00e1ch c\u1ee7a b\u1ea1n","LBL_LAST_VIEWED":"Xem cu\u1ed1i","LBL_TODAYS_ACTIVITIES":"Ho\u1ea1t \u0111\u1ed9ng h\u00f4m nay","LBL_LEADS":"\u0110\u1ea7u m\u1ed1i","LBL_LESS":"k\u00e9m h\u01a1n","LBL_CAMPAIGN":"Chi\u1ebfn l\u01b0\u1ee3c:","LBL_CAMPAIGNS":"Chi\u1ebfn d\u1ecbch","LBL_CAMPAIGN_ID":"campaign_id","LBL_SITEMAP":"Sitemap","LBL_THEME":"Theme:","LBL_THEME_PICKER":"Page Style","LBL_THEME_PICKER_IE6COMPAT_CHECK":"Warning: Internet Explorer 6 is not supported for the selected theme. Click OK to select it anyways or Cancel to select a different theme.","LBL_FOUND_IN_RELEASE":"T\u00ecm th\u1ea5y trong Release","LBL_FIXED_IN_RELEASE":"Ch\u1ec9nh s\u1eeda trong Release","LBL_LIST_ACCOUNT_NAME":"T\u00ean Kh\u00e1ch h\u00e0ng","LBL_LIST_ASSIGNED_USER":"Nv.Ql\u00fd","LBL_LIST_CONTACT_NAME":"T\u00ean ng\u01b0\u1eddi li\u00ean h\u1ec7","LBL_LIST_CONTACT_ROLE":"Vai tr\u00f2 ng\u01b0\u1eddi li\u00ean h\u1ec7","LBL_LIST_EMAIL":"Email","LBL_LIST_NAME":"T\u00ean","LBL_LIST_OF":"c\u1ee7a","LBL_LIST_PHONE":"\u0110i\u1ec7n tho\u1ea1i","LBL_LIST_RELATED_TO":"Li\u00ean quan t\u1edbi","LBL_LIST_USER_NAME":"T\u00ean ng\u01b0\u1eddi s\u1eed d\u1ee5ng","LBL_LISTVIEW_MASS_UPDATE_CONFIRM":"B\u1ea1n c\u00f3 ch\u1eafc mu\u1ed1n c\u1eadp nh\u1eadt to\u00e0n b\u1ed9 danh s\u00e1ch?","LBL_LISTVIEW_NO_SELECTED":"Vui l\u00f2ng ch\u1ecdn \u00edt nh\u1ea5t 1.","LBL_LISTVIEW_TWO_REQUIRED":"Vui l\u00f2ng ch\u1ecdn \u00edt nh\u1ea5t 2.","LBL_LISTVIEW_LESS_THAN_TEN_SELECT":"Vui l\u00f2ng ch\u1ecdn \u00edt h\u01a1n 10.","LBL_LISTVIEW_ALL":"T\u1ea5t c\u1ea3","LBL_LISTVIEW_NONE":"Kh\u00f4ng","LBL_LISTVIEW_OPTION_CURRENT":"Trang n\u00e0y","LBL_LISTVIEW_OPTION_ENTIRE":"T\u1ea5t c\u1ea3","LBL_LISTVIEW_OPTION_SELECTED":"H\u1ed3 s\u01a1 \u0111\u01b0\u1ee3c ch\u1ecdn","LBL_LISTVIEW_SELECTED_OBJECTS":"Ch\u1ecdn:","LBL_LOCALE_NAME_EXAMPLE_FIRST":"John","LBL_LOCALE_NAME_EXAMPLE_LAST":"Doe","LBL_LOCALE_NAME_EXAMPLE_SALUTATION":"Mr.","LBL_LOCALE_NAME_EXAMPLE_TITLE":"Code Monkey Extraordinaire","LBL_LOGIN_TO_ACCESS":"Vui l\u00f2ng \u0111\u0103ng nh\u1eadp \u0111\u1ec3 truy c\u1eadp khu v\u1ef1c n\u00e0y.","LBL_LOGOUT":"Tho\u00e1t","LBL_MAILMERGE_KEY":"M","LBL_MAILMERGE":"Tr\u1ed9n mail","LBL_MASS_UPDATE":"C\u1eadp nh\u1eadt nhi\u1ec1u m\u1ed9t l\u00fac","LBL_OPT_OUT_FLAG_PRIMARY":"Kh\u00f4ng ch\u1ecdn email ch\u00ednh","LBL_MEETINGS":"Cu\u1ed9c h\u1eb9n","LBL_MEETING":"Cu\u1ed9c h\u1eb9n","LBL_MEMBERS":"Th\u00e0nh vi\u00ean","LBL_MEMBER_OF":"Th\u00e0nh vi\u00ean c\u1ee7a","LBL_MODIFIED_BY_USER":"Thay \u0111\u1ed5i b\u1edfi ng\u01b0\u1eddi d\u00f9ng","LBL_MODIFIED_USER":"Ng\u01b0\u1eddi thay \u0111\u1ed5i","LBL_MODIFIED":"Thay \u0111\u1ed5i b\u1edfi","LBL_MODIFIED_NAME":"T\u00ean ng\u01b0\u1eddi thay \u0111\u1ed5i","LBL_MODIFIED_ID":"M\u00e3 ng\u01b0\u1eddi thay \u0111\u1ed5i","LBL_MORE":"th\u00eam","LBL_MY_ACCOUNT":"T\u00e0i kho\u1ea3n","LBL_NAME":"T\u00ean","LBL_NEW_BUTTON_KEY":"N","LBL_NEW_BUTTON_LABEL":"Th\u00eam","LBL_NEW_BUTTON_TITLE":"Th\u00eam [Alt+N]","LBL_NEXT_BUTTON_LABEL":"Ti\u1ebfp","LBL_NONE":"--Kh\u00f4ng--","LBL_NOTES":"Ghi ch\u00fa","LBL_OPENALL_BUTTON_KEY":"O","LBL_OPENALL_BUTTON_LABEL":"M\u1edf t\u1ea5t c\u1ea3","LBL_OPENALL_BUTTON_TITLE":"M\u1edf t\u1ea5t c\u1ea3 [Alt+O]","LBL_OPENTO_BUTTON_KEY":"T","LBL_OPENTO_BUTTON_LABEL":"M\u1edf t\u1edbi: ","LBL_OPENTO_BUTTON_TITLE":"M\u1edf t\u1edbi: [Alt+T]","LBL_OPPORTUNITIES":"\u0110\u01a1n h\u00e0ng","LBL_OPPORTUNITY_NAME":"PO No.","LBL_OPPORTUNITY":"\u0110\u01a1n h\u00e0ng","LBL_OR":"HO\u1eb6C","LBL_LOWER_OR":"ho\u1eb7c","LBL_PARENT_TYPE":"Lo\u1ea1i cha","LBL_PERCENTAGE_SYMBOL":"%","LBL_PHASE":"Th\u00e0nh ng\u1eef","LBL_POSTAL_CODE":"M\u00e3 b\u01b0u \u0111i\u1ec7n:","LBL_PRIMARY_ADDRESS_CITY":"Th\u00e0nh ph\u1ed1 ch\u00ednh:","LBL_PRIMARY_ADDRESS_COUNTRY":"Qu\u1ed1c gia ch\u00ednh:","LBL_PRIMARY_ADDRESS_POSTALCODE":"M\u00e3 b\u01b0u \u0111i\u1ec7n ch\u00ednh:","LBL_PRIMARY_ADDRESS_STATE":"Qu\u1eadn ch\u00ednh:","LBL_PRIMARY_ADDRESS_STREET_2":"\u0110\u1ecba ch\u1ec9 \u0111\u01b0\u1eddng 2:","LBL_PRIMARY_ADDRESS_STREET_3":"\u0110\u1ecba ch\u1ec9 \u0111\u01b0\u1eddng 3:","LBL_PRIMARY_ADDRESS_STREET":"\u0110\u1ecba ch\u1ec9 \u0111\u01b0\u1eddng ch\u00ednh:","LBL_PRIMARY_ADDRESS":"\u0110\u1ecba ch\u1ec9 ch\u00ednh:","LBL_PRODUCT_BUNDLES":"G\u00f3i s\u1ea3n ph\u1ea9m","LBL_PRODUCTS":"S\u1ea3n ph\u1ea9m","LBL_PROJECT_TASKS":"T\u00e1c v\u1ee5 d\u1ef1 \u00e1n","LBL_PROJECTS":"D\u1ef1 \u00e1n","LBL_QUOTE_TO_OPPORTUNITY_KEY":"O","LBL_QUOTE_TO_OPPORTUNITY_LABEL":"T\u1ea1o \u0111\u01a1n h\u00e0ng t\u1eeb b\u00e1o gi\u00e1","LBL_QUOTE_TO_OPPORTUNITY_TITLE":"T\u1ea1o \u0111\u01a1n h\u00e0ng t\u1eeb b\u00e1o gi\u00e1 [Alt+O]","LBL_QUOTES_SHIP_TO":"Giao \u0111\u01a1n h\u00e0ng t\u1edbi","LBL_QUOTES":"B\u00e1o gi\u00e1","LBL_RELATED":"Li\u00ean quan","LBL_RELATED_INFORMATION":"Th\u00f4ng tin li\u00ean quan","LBL_RELATED_RECORDS":"D\u1eef li\u1ec7u li\u00ean quan","LBL_REMOVE":"B\u1ecf","LBL_REPORTS_TO":"B\u00e1o c\u00e1o cho","LBL_REQUIRED_SYMBOL":"*","LBL_SAVE_BUTTON_KEY":"S","LBL_SAVE_BUTTON_LABEL":"L\u01b0u","LBL_SAVE_BUTTON_TITLE":"L\u01b0u [Alt+S]","LBL_SAVE_AS_BUTTON_KEY":"A","LBL_SAVE_AS_BUTTON_LABEL":"Ghi theo ki\u1ec3u","LBL_SAVE_AS_BUTTON_TITLE":"Ghi theo ki\u1ec3u [Alt+A]","LBL_FULL_FORM_BUTTON_KEY":"F","LBL_FULL_FORM_BUTTON_LABEL":"M\u00e0n h\u00ecnh \u0111\u1ea7y \u0111\u1ee7","LBL_FULL_FORM_BUTTON_TITLE":"M\u00e0n h\u00ecnh \u0111\u1ea7y \u0111\u1ee7 [Alt+F]","LBL_SAVE_NEW_BUTTON_KEY":"V","LBL_SAVE_NEW_BUTTON_LABEL":"L\u01b0u  & T\u1ea1o m\u1edbi","LBL_SAVE_NEW_BUTTON_TITLE":"L\u01b0u & T\u1ea1o m\u1edbi [Alt+V]","LBL_SEARCH_BUTTON_KEY":"Q","LBL_SEARCH_BUTTON_LABEL":"T\u00ecm ki\u1ebfm","LBL_SEARCH_BUTTON_TITLE":"T\u00ecm ki\u1ebfm [Alt+Q]","LBL_SEARCH":"T\u00ecm","LBL_SEE_ALL":"Xem t\u1ea5t c\u1ea3","LBL_SELECT_BUTTON_KEY":"T","LBL_SELECT_BUTTON_LABEL":"Ch\u1ecdn","LBL_SELECT_BUTTON_TITLE":"Ch\u1ecdn [Alt+T]","LBL_BROWSE_DOCUMENTS_BUTTON_KEY":"B","LBL_BROWSE_DOCUMENTS_BUTTON_LABEL":"Duy\u1ec7t t\u00e0i li\u1ec7u","LBL_BROWSE_DOCUMENTS_BUTTON_TITLE":"Duy\u1ec7t t\u00e0i li\u1ec7u [Alt+B]","LBL_SELECT_CONTACT_BUTTON_KEY":"T","LBL_SELECT_CONTACT_BUTTON_LABEL":"Ch\u1ecdn ng\u01b0\u1eddi li\u00ean h\u1ec7","LBL_SELECT_CONTACT_BUTTON_TITLE":"Ch\u1ecdn ng\u01b0\u1eddi li\u00ean h\u1ec7 [Alt+T]","LBL_GRID_SELECTED_FILE":"ch\u1ecdn file","LBL_GRID_SELECTED_FILES":"ch\u1ecdn files","LBL_SELECT_REPORTS_BUTTON_LABEL":"Ch\u1ecdn t\u1eeb b\u00e1o c\u00e1o","LBL_SELECT_REPORTS_BUTTON_TITLE":"Ch\u1ecdn b\u00e1o c\u00e1o","LBL_SELECT_USER_BUTTON_KEY":"U","LBL_SELECT_USER_BUTTON_LABEL":"Ch\u1ecdn ng\u01b0\u1eddi s\u1eed d\u1ee5ng","LBL_SELECT_USER_BUTTON_TITLE":"Ch\u1ecdn ng\u01b0\u1eddi s\u1eed d\u1ee5ng [Alt+U]","LBL_SERVER_RESPONSE_RESOURCES":"C\u00e1c ngu\u1ed3n l\u1ef1c \u0111\u01b0\u1ee3c s\u1eed d\u1ee5ng \u0111\u1ec3 x\u00e2y d\u1ef1ng trang web n\u00e0y (truy v\u1ea5n, c\u00e1c t\u1eadp tin)","LBL_SERVER_RESPONSE_TIME_SECONDS":"gi\u00e2y.","LBL_SERVER_RESPONSE_TIME":"Th\u1eddi gian x\u1eed l\u00fd tr\u00ean server:","LBL_SHIP_TO_ACCOUNT":"Giao h\u00e0ng \u0111\u1ebfn Kh\u00e1ch h\u00e0ng","LBL_SHIP_TO_CONTACT":"Giao h\u00e0ng \u0111\u1ebfn Ng\u01b0\u1eddi LH","LBL_SHIPPING_ADDRESS":"\u0110\u1ecba ch\u1ec9 giao h\u00e0ng","LBL_SHORTCUTS":"B\u1ea5m nhanh","LBL_SHOW":"Hi\u1ec3n th\u1ecb","LBL_SQS_INDICATOR":"","LBL_STATE":"T\u00ecnh tr\u1ea1ng:","LBL_STATUS_UPDATED":"T\u00ecnh tr\u1ea1ng c\u1ee7a b\u1ea1n cho s\u1ef1 ki\u1ec7n n\u00e0y \u0111\u01b0\u1ee3c c\u1eadp nh\u1eadt!","LBL_STATUS":"T\u00ecnh tr\u1ea1ng:","LBL_SUBJECT":"Ti\u00eau \u0111\u1ec1","LBL_incomCRM_COPYRIGHT":"&copy; 2004-2016 PTSOFT JSC.","LBL_SYNC":"\u0110\u1ed3ng b\u1ed9 h\u00f3a","LBL_TABGROUP_ALL":"T\u1ea5t c\u1ea3","LBL_TABGROUP_ACTIVITIES":"Ho\u1ea1t \u0111\u1ed9ng","LBL_TABGROUP_COLLABORATION":"C\u1ed9ng t\u00e1c","LBL_TABGROUP_HOME":"Trang ch\u1ee7","LBL_TABGROUP_MARKETING":"Ti\u1ebfp th\u1ecb","LBL_TABGROUP_MY_PORTALS":"C\u1ed5ng th\u00f4ng tin","LBL_TABGROUP_OTHER":"Kh\u00e1c","LBL_TABGROUP_REPORTS":"B\u00e1o c\u00e1o","LBL_TABGROUP_SALES":"B\u00e1n h\u00e0ng","LBL_TABGROUP_SUPPORT":"H\u1ed7 tr\u1ee3","LBL_TABGROUP_TOOLS":"C\u00f4ng c\u1ee5","LBL_TASKS":"T\u00e1c v\u1ee5","LBL_TEAMS_LINK":"Nh\u00f3m","LBL_THOUSANDS_SYMBOL":"K","LBL_TRACK_EMAIL_BUTTON_KEY":"K","LBL_TRACK_EMAIL_BUTTON_LABEL":"L\u01b0u tr\u1eef email","LBL_TRACK_EMAIL_BUTTON_TITLE":"L\u01b0u tr\u1eef email [Alt+K]","LBL_UNAUTH_ADMIN":"Truy c\u1eadp tr\u00e1i ph\u00e9p v\u00e0o qu\u1ea3n tr\u1ecb h\u1ec7 th\u1ed1ng","LBL_UNDELETE_BUTTON_LABEL":"Kh\u00f4ng x\u00f3a","LBL_UNDELETE_BUTTON_TITLE":"Kh\u00f4ng x\u00f3a [Alt+D]","LBL_UNDELETE_BUTTON":"Kh\u00f4ng x\u00f3a","LBL_UNDELETE":"Kh\u00f4ng x\u00f3a","LBL_UNSYNC":"Kh\u00f4ng \u0111\u1ed3ng b\u1ed9","LBL_UPDATE":"C\u1eadp nh\u1eadt","LBL_USER_LIST":"Danh s\u00e1ch ng\u01b0\u1eddi d\u00f9ng","LBL_USERS_SYNC":"\u0110\u1ed3ng b\u1ed9 ng\u01b0\u1eddi d\u00f9ng","LBL_USERS":"Ng\u01b0\u1eddi d\u00f9ng","LBL_VERIFY_EMAIL_ADDRESS":"Ki\u1ec3m tra t\u1ed3n t\u1ea1i c\u1ee7a c\u00e1c m\u1ee5c email...","LBL_VERIFY_PORTAL_NAME":"Ki\u1ec3m tra t\u1ed3n t\u1ea1i c\u1ee7a t\u00ean c\u1ed5ng hi\u1ec7n t\u1ea1i...","LBL_VIEW_PDF_BUTTON_KEY":"P","LBL_VIEW_PDF_BUTTON_LABEL":"In d\u1ea1ng PDF","LBL_VIEW_PDF_BUTTON_TITLE":"In d\u1ea1ng PDF [Alt+P]","LNK_ABOUT":"Gi\u1edbi thi\u1ec7u","LNK_ADVANCED_SEARCH":"T\u00ecm ki\u1ebfm n\u00e2ng cao","LNK_BASIC_SEARCH":"T\u00ecm ki\u1ebfm c\u01a1 b\u1ea3n","LNK_SAVED_VIEWS":"Ghi t\u00ecm ki\u1ebfm & Layout","LNK_DELETE_ALL":"x\u00f3a h\u1ebft","LNK_DELETE":"x\u00f3a","LNK_EDIT":"s\u1eeda","LNK_GET_LATEST":"L\u1ea5y cu\u1ed1i c\u00f9ng","LNK_GET_LATEST_TOOLTIP":"Thay \u0111\u1ed5i v\u1edbi phi\u00ean b\u1ea3n sau c\u00f9ng","LNK_HELP":"Gi\u00fap \u0111\u1ee1","LNK_LIST_END":"Sau c\u00f9ng","LNK_LIST_NEXT":"Ti\u1ebfp","LNK_LIST_PREVIOUS":"Tr\u01b0\u1edbc","LNK_LIST_RETURN":"Tr\u1edf l\u1ea1i danh s\u00e1ch","LNK_LIST_START":"\u0110\u1ea7u ti\u00ean","LNK_LOAD_SIGNED":"K\u00fd","LNK_LOAD_SIGNED_TOOLTIP":"Thay \u0111\u1ed5i v\u1edbi t\u00e0i li\u1ec7u k\u00fd","LNK_PRINT":"In","LNK_REMOVE":"x\u00f3a","LNK_RESUME":"H\u1ed3i ph\u1ee5c","LNK_VIEW_CHANGE_LOG":"Nh\u1eadt k\u00fd thay \u0111\u1ed5i","NTC_CLICK_BACK":"Xin vui l\u00f2ng nh\u1ea5p v\u00e0o n\u00fat quay l\u1ea1i c\u1ee7a tr\u00ecnh duy\u1ec7t v\u00e0 s\u1eeda ch\u1eefa l\u1ed7i.","NTC_DATE_FORMAT":"(yyyy-mm-dd)","NTC_DATE_TIME_FORMAT":"(yyyy-mm-dd 24:00)","NTC_DELETE_CONFIRMATION_MULTIPLE":"B\u1ea1n c\u00f3 ch\u1eafc mu\u1ed1n x\u00f3a c\u00e1c h\u1ed3 s\u01a1 \u0111\u01b0\u1ee3c ch\u1ecdn?","NTC_DELETE_CONFIRMATION":"B\u1ea1n c\u00f3 ch\u1eafc mu\u1ed1n x\u00f3a c\u00e1c h\u1ed3 s\u01a1 \u0111\u01b0\u1ee3c ch\u1ecdn?","NTC_DELETE_CONFIRMATION_NUM":"B\u1ea1n c\u00f3 ch\u1eafc mu\u1ed1n x\u00f3a ","NTC_UPDATE_CONFIRMATION_NUM":"B\u1ea1n c\u00f3 ch\u1eafc mu\u1ed1n c\u1eadp nh\u1eadt ","NTC_DELETE_SELECTED_RECORDS":" h\u1ed3 s\u01a1 \u0111\u01b0\u1ee3c ch\u1ecdn?","NTC_LOGIN_MESSAGE":"Vui l\u00f2ng \u0111\u0103ng nh\u1eadp","NTC_NO_ITEMS_DISPLAY":"kh\u00f4ng","NTC_REMOVE_CONFIRMATION":"B\u1ea1n c\u00f3 ch\u1eafc mu\u1ed1n x\u00f3a nh\u1eefng m\u1ed1i quan h\u1ec7?","NTC_REQUIRED":"Cho bi\u1ebft tr\u01b0\u1eddng b\u1eaft bu\u1ed9c","NTC_SUPPORT_incomCRM":"H\u1ed7 tr\u1ee3 incomCRM b\u1eb1ng c\u00e1ch quy\u00ean g\u00f3p qua PayPal - nhanh, mi\u1ec5n ph\u00ed v\u00e0 b\u1ea3o m\u1eadt","NTC_TIME_FORMAT":"(24:00)","NTC_WELCOME":"Ch\u00e0o","NTC_YEAR_FORMAT":"(yyyy)","LOGIN_LOGO_ERROR":"Vui l\u00f2ng thay th\u1ebf nh\u1eefng logo incomCRM.","ERROR_FULLY_EXPIRED":"B\u1ea3n quy\u1ec1n c\u1ee7a c\u00f4ng ty b\u1ea1n cho incomCRM \u0111\u00e3 h\u1ebft h\u1ea1n s\u1eed d\u1ee5ng 30 ng\u00e0y v\u00e0 c\u1ea7n c\u1eadp nh\u1eadt. Ch\u1ec9 qu\u1ea3n tr\u1ecb h\u1ec7 th\u1ed1ng  m\u1edbi c\u00f3 th\u1ec3 login.","ERROR_LICENSE_EXPIRED":"B\u1ea3n quy\u1ec1n c\u1ee7a c\u00f4ng ty b\u1ea1n cho incomCRM \u0111\u00e3 h\u1ebft h\u1ea1n s\u1eed d\u1ee5ng 30 ng\u00e0y v\u00e0 c\u1ea7n c\u1eadp nh\u1eadt. Ch\u1ec9 qu\u1ea3n tr\u1ecb h\u1ec7 th\u1ed1ng  m\u1edbi c\u00f3 th\u1ec3 login.","ERROR_LICENSE_VALIDATION":"B\u1ea3n quy\u1ec1n c\u1ee7a c\u00f4ng ty b\u1ea1n cho incomCRM c\u1ea7n ph\u1ea3i ki\u1ec3m tra. Ch\u1ec9 qu\u1ea3n tr\u1ecb h\u1ec7 th\u1ed1ng m\u1edbi c\u00f3 th\u1ec3 login.","ERROR_NO_RECORD":"L\u1ed7i  trong khi nh\u1eadn h\u1ed3 s\u01a1 v\u1ec1. H\u1ed3 s\u01a1 n\u00e0y c\u00f3 th\u1ec3 b\u1ecb x\u00f3a ho\u1eb7c b\u1ea1n kh\u00f4ng c\u00f3 quy\u1ec1n xem n\u00f3.","ERROR_TYPE_NOT_VALID":"L\u1ed7i: Lo\u1ea1i kh\u00f4ng h\u1ee3p l\u1ec7","LBL_DUP_MERGE":"T\u00ecm b\u1ea3n sao","LBL_MANAGE_SUBSCRIPTIONS":"Qu\u1ea3n l\u00fd \u0111\u0103ng k\u00fd","LBL_MANAGE_SUBSCRIPTIONS_FOR":"Qu\u1ea3n l\u00fd \u0111\u0103ng k\u00fd cho","LBL_SUBSCRIBE":"\u0110\u0103ng k\u00fd","LBL_UNSUBSCRIBE":"B\u1ecf \u0111\u0103ng k\u00fd","LBL_LOADING":"\u0110ang t\u1ea3i...","LBL_SEARCHING":"\u0110ang t\u00ecm...","LBL_SAVING_LAYOUT":"\u0110ang l\u01b0u giao di\u1ec7n...","LBL_SAVED_LAYOUT":"Giao di\u1ec7n \u0111\u00e3 \u0111\u01b0\u1ee3c l\u01b0u.","LBL_SAVED":"\u0110\u00e3 l\u01b0u","LBL_SAVING":"\u0110ang l\u01b0u...","LBL_FAILED":"L\u1ed7i!","LBL_SENDING":"\u0110ang g\u1eedi...","LBL_DISPLAY_COLUMNS":"C\u1ed9t hi\u1ec3n th\u1ecb","LBL_HIDE_COLUMNS":"C\u1ed9t che d\u1ea5u","LBL_SEARCH_CRITERIA":"\u0110i\u1ec1u ki\u1ec7n t\u00ecm ki\u1ebfm","LBL_SAVED_VIEWS":"Ghi khung xem","LBL_PROCESSING_REQUEST":"\u0110ang x\u1eed l\u00fd..","LBL_REQUEST_PROCESSED":"Ho\u00e0n t\u1ea5t","LBL_MERGE_DUPLICATES":"H\u1ee3p nh\u1ea5t b\u1ea3n sao","LBL_SAVED_SEARCH_SHORTCUT":"T\u00ecm \u0111\u00e3 l\u01b0u","LBL_SEARCH_POPULATE_ONLY":"h\u1ef1c hi\u1ec7n vi\u1ec7c t\u00ecm ki\u1ebfm b\u1eb1ng c\u00e1ch s\u1eed d\u1ee5ng m\u1eabu \u0111\u01a1n t\u00ecm ki\u1ebfm \u1edf tr\u00ean","LBL_DETAILVIEW":"Xem chi ti\u1ebft","LBL_LISTVIEW":"Xem Danh s\u00e1ch","LBL_EDITVIEW":"Ch\u1ec9nh s\u1eeda xem","LBL_SEARCHFORM":"M\u00e0n h\u00ecnh t\u00ecm","LBL_SAVED_SEARCH_ERROR":"Vui l\u00f2ng cung c\u1ea5p m\u1ed9t t\u00ean cho xem n\u00e0y","LBL_DISPLAY_LOG":"Hi\u1ec3n th\u1ecb Log","ERROR_JS_ALERT_SYSTEM_CLASS":"H\u1ec7 th\u1ed1ng","ERROR_JS_ALERT_TIMEOUT_TITLE":"Th\u1eddi gian cho m\u1ed9t phi\u00ean l\u00e0m vi\u1ec7c","ERROR_JS_ALERT_TIMEOUT_MSG_1":"Phi\u00ean l\u00e0m vi\u1ec7c c\u1ee7a b\u1ea1n s\u1ebd k\u1ebft th\u00fac trong 2 ph\u00fat. Vui l\u00f2ng l\u01b0u l\u1ea1i c\u00f4ng vi\u1ec7c.","ERROR_JS_ALERT_TIMEOUT_MSG_2":"Phi\u00ean l\u00e0m vi\u1ec7c \u0111\u00e3 h\u1ebft.","MSG_JS_ALERT_MTG_REMINDER_AGENDA":"Ch\u01b0\u01a1ng tr\u00ecnh:","MSG_JS_ALERT_MTG_REMINDER_MEETING":"H\u1ed9i h\u1ecdp","MSG_JS_ALERT_MTG_REMINDER_CALL":"G\u1ecdi \u0111i\u1ec7n","MSG_JS_ALERT_MTG_REMINDER_TIME":"Th\u1eddi gian: ","MSG_JS_ALERT_MTG_REMINDER_LOC":"\u0110\u1ecba \u0111i\u1ec3m: ","MSG_JS_ALERT_MTG_REMINDER_DESC":"M\u00f4 t\u1ea3: ","MSG_JS_ALERT_MTG_REMINDER_MSG":"\nNh\u1ea5p v\u00e0o OK \u0111\u1ec3 xem chi ti\u1ebft ho\u1eb7c nh\u1ea5p v\u00e0o Cancel \u0111\u1ec3 b\u1ecf qua th\u00f4ng b\u00e1o n\u00e0y.","LBL_ADD_TO_FAVORITES":"Th\u00eam v\u00e0o s\u1edf th\u00edch ri\u00eang","LBL_MARK_AS_FAVORITES":"\u0110\u00e1nh d\u1ea5u l\u00e0 s\u1edf th\u00edch ri\u00eang","LBL_CREATE_CONTACT":"T\u1ea1o ng\u01b0\u1eddi li\u00ean h\u1ec7","LBL_CREATE_CASE":"T\u1ea1o v\u1ee5 vi\u1ec7c","LBL_CREATE_NOTE":"T\u1ea1o ghi ch\u00fa","LBL_CREATE_OPPORTUNITY":"T\u1ea1o \u0111\u01a1n h\u00e0ng","LBL_SCHEDULE_CALL":"L\u1ecbch g\u1ecdi \u0111i\u1ec7n","LBL_SCHEDULE_MEETING":"L\u1ecbch h\u1ed9i h\u1ecdp","LBL_CREATE_TASK":"T\u1ea1o t\u00e1c v\u1ee5","LBL_REMOVE_FROM_FAVORITES":"B\u1ecf kh\u1ecfi s\u1edf th\u00edch ri\u00eang","LBL_GENERATE_WEB_TO_LEAD_FORM":"T\u1ea1o bi\u1ec3u m\u1eabu","LBL_SAVE_WEB_TO_LEAD_FORM":"Save Web To Lead Form","LBL_PLEASE_SELECT":"Vui l\u00f2ng ch\u1ecdn","LBL_REDIRECT_URL":"Chuy\u1ec3n ti\u1ebfp t\u1edbi URL","LBL_RELATED_CAMPAIGN":"Chi\u1ebfn l\u01b0\u1ee3c li\u00ean quan","LBL_ADD_ALL_LEAD_FIELDS":"Th\u00eam t\u1ea5t c\u1ea3 c\u00e1c tr\u01b0\u1eddng","LBL_REMOVE_ALL_LEAD_FIELDS":"B\u1ecf t\u1ea5t c\u1ea3 c\u00e1c tr\u01b0\u1eddng","LBL_ONLY_IMAGE_ATTACHMENT":"Ch\u1ec9 c\u00f3 \u0111\u00ednh k\u00e8m d\u1ea1ng \u1ea3nh c\u00f3 th\u1ec3 \u1ea9n","LBL_TRAINING":"Hu\u1ea5n luy\u1ec7n","ERR_DATABASE_CONN_DROPPED":"L\u1ed7i th\u1ef1c thi truy v\u1ea5n. R\u1ea5t c\u00f3 th\u1ec3 c\u01a1 s\u1edf d\u1eef li\u1ec7u c\u1ee7a b\u1ea1n \u0111\u00e3 ng\u1eaft k\u1ebft n\u1ed1i. Vui l\u00f2ng l\u00e0m t\u01b0\u01a1i trang n\u00e0y, b\u1ea1n c\u00f3 th\u1ec3 c\u1ea7n ph\u1ea3i kh\u1edfi \u0111\u1ed9ng l\u1ea1i webserver c\u1ee7a b\u1ea1n.","ERR_MSSQL_DB_CONTEXT":"Thay \u0111\u1ed5i n\u1ed9i dung CSDL \u0111\u1ebfn","ERR_MISSING_VARDEF_NAME":"C\u1ea3nh b\u00e1o: tr\u01b0\u1eddng [[field]] kh\u00f4ng c\u00f3 m\u1ed9t \u00e1nh x\u1ea1 trong m\u1ee5c [moduleDir] vardefs.php ","ERR_CANNOT_CREATE_METADATA_FILE":"L\u1ed7i: File [[file]] b\u1ecb thi\u1ebfu. Kh\u00f4ng th\u1ec3 t\u1ea1o v\u00ec kh\u00f4ng c\u00f3 file HTML t\u01b0\u01a1ng \u1ee9ng.","ERR_CANNOT_FIND_MODULE":"L\u1ed7i: M\u00f4-\u0111un [module] kh\u00f4ng t\u1ed3n t\u1ea1i.","LBL_ALT_ADDRESS":"\u0110\u1ecba ch\u1ec9 kh\u00e1c:","ERR_SMARTY_UNEQUAL_RELATED_FIELD_PARAMETERS":"L\u1ed7i: C\u00f3 m\u1ed9t \u0111\u1ed1i s\u1ed1 c\u1ee7a 'key' v\u00e0 'copy' kh\u00f4ng gi\u1ed1ng nhau trong m\u1ea3ng displayParams","ERR_SMARTY_MISSING_DISPLAY_PARAMS":"Qu\u00ean ch\u1ec9 m\u1ee5c c\u1ee7a m\u1ea3ng [displayParams]:","LBL_DASHLET_CONFIGURE_GENERAL":"T\u1ed5ng quan","LBL_DASHLET_CONFIGURE_FILTERS":"B\u1ed9 l\u1ecdc","LBL_DASHLET_CONFIGURE_MY_ITEMS_ONLY":"Ch\u1ec9 c\u1ee7a t\u00f4i","LBL_DASHLET_CONFIGURE_TITLE":"Ti\u00eau \u0111\u1ec1","LBL_DASHLET_CONFIGURE_DISPLAY_ROWS":"Th\u1ec3 hi\u1ec7n d\u00f2ng","LBL_CREATING_NEW_PAGE":"T\u1ea1o trang m\u1edbi","LBL_NEW_PAGE_FEEDBACK":"B\u1ea1n kh\u00f4ng th\u1ec3 t\u1ea1o trang m\u1edbi. B\u1ea1n c\u00f3 th\u1ec3 th\u00eam n\u1ed9i dung m\u1edbi v\u1edbi menu t\u00f9y ch\u1ecdn [Th\u00eam B.C nhanh] ","LBL_DELETE_PAGE_CONFIRM":"B\u1ea1n c\u00f3 ch\u1eafc mu\u1ed1n x\u00f3a trang n\u00e0y.","LBL_SAVING_PAGE_TITLE":"\u0110ang l\u01b0u ti\u00eau \u0111\u1ec1...","LBL_RETRIEVING_PAGE":"Trang nh\u1eadn v\u1ec1 ...","LBL_MAX_DASHLETS_REACHED":"B\u1ea1n \u0111\u00e3 \u0111\u1ea1t t\u1edbi con s\u1ed1 c\u1ef1c \u0111\u1ea1i c\u1ee7a B\u00e1o c\u00e1o nhanh m\u00e0 qu\u1ea3n tr\u1ecb h\u1ec7 th\u1ed1ng c\u1ee7a b\u1ea1n thi\u1ebft l\u1eadp. Vui l\u00f2ng x\u00f3a b\u1edbt B\u00e1o c\u00e1o nhanh \u0111\u1ec3 th\u00eam m\u1edbi. ","LBL_ADDING_DASHLET":"\u0110ang th\u00eam B\u00e1o c\u00e1o nhanh...","LBL_ADDED_DASHLET":"\u0110\u00e3 th\u00eam B\u00e1o c\u00e1o nhanh","LBL_REMOVE_DASHLET_CONFIRM":"B\u1ea1n c\u00f3 ch\u1eafc mu\u1ed1n x\u00f3a B\u00e1o c\u00e1o nhanh?","LBL_REMOVING_DASHLET":"\u0110ang x\u00f3a B\u00e1o c\u00e1o nhanh ...","LBL_REMOVED_DASHLET":"\u0110\u00e3 x\u00f3a B\u00e1o c\u00e1o nhanh","LBL_ADD_PAGE":"Th\u00eam trang","LBL_DELETE_PAGE":"X\u00f3a trang","LBL_CHANGE_LAYOUT":"Thay \u0111\u1ed5i Layout","LBL_RENAME_PAGE":"\u0110\u1ed5i t\u00ean trang","LBL_LOADING_PAGE":"\u0110ang t\u1ea3i trang, vui l\u00f2ng ch\u1edd ... ","LBL_RELOAD_PAGE":"Vui l\u00f2ng <a href=\"javascript: window.location.reload()\">t\u1ea3i l\u1ea1i c\u1eeda s\u1ed5<\/a> \u0111\u1ec3 d\u00f9ng B\u00e1o c\u00e1o nhanh.","LBL_ADD_DASHLETS":"Th\u00eam B.C nhanh","LBL_CLOSE_DASHLETS":"\u0110\u00f3ng","LBL_OPTIONS":"T\u00f9y ch\u1ecdn","LBL_NUMBER_OF_COLUMNS":"Click v\u00e0o bi\u1ec3u t\u01b0\u1ee3ng \u0111\u1ec3 thi\u1ebft l\u1eadp s\u1ed1 c\u1ed9t","LBL_1_COLUMN":"1 c\u1ed9t","LBL_2_COLUMN":"2 c\u1ed9t","LBL_3_COLUMN":"3 c\u1ed9t","LBL_PAGE_NAME":"T\u00ean trang","LBL_SEARCH_RESULTS":"K\u1ebft qu\u1ea3 t\u00ecm ki\u1ebfm","LBL_SEARCH_MODULES":"Ph\u00e2n h\u1ec7","LBL_SEARCH_CHARTS":"Bi\u1ec3u \u0111\u1ed3","LBL_SEARCH_REPORT_CHARTS":"Bi\u1ec3u \u0111\u1ed3 b\u00e1o c\u00e1o","LBL_SEARCH_TOOLS":"C\u00f4ng c\u1ee5","LBL_SEARCH_HELP_TITLE":"L\u00e0m vi\u1ec7c v\u1edbi \u0111a ch\u1ecdn l\u1ef1a v\u00e0 T\u00ecm ki\u1ebfm m\u1ee5c \u0111\u00e3 l\u01b0u","LBL_SEARCH_HELP_CLOSE_TOOLTIP":"\u0110\u00f3ng","ERR_BLANK_PAGE_NAME":"Nh\u1eadp t\u00ean trang.","LBL_NO_IMAGE":"Kh\u00f4ng c\u00f3 h\u00ecnh","LBL_MODULE":"M\u00f4-\u0111un","LBL_COPY_ADDRESS_FROM_LEFT":"Copy \u0111\u1ecba ch\u1ec9 t\u1eeb b\u00ean tr\u00e1i:","LBL_SAVE_AND_CONTINUE":"Ghi v\u00e0 ti\u1ebfp t\u1ee5c","LBL_SEARCH_HELP_TEXT":"<p><br \/><strong>Multiselect controls<\/strong><\/p><ul><li>Click on the values to select an attribute.<\/li><li>Ctrl-click&nbsp;to&nbsp;select multiple. Mac users use CMD-click.<\/li><li>To select all values between two attributes,&nbsp; click first value&nbsp;and then shift-click last value.<\/li><\/ul><p><strong>Advanced Search & Layout Options<\/strong><br><br>Using the <b>Saved Search & Layout<\/b> option, you can save a set of search parameters and\/or a custom List View layout in order to quickly obtain the desired search results in the future. You can save an unlimited number of custom searches and layouts. All saved searches appear by name in the Saved Searches list, with the last loaded saved search appearing at the top of the list.<br><br>To customize the List View layout, use the Hide Columns and Display Columns boxes to select which fields to display in the search results. For example, you can view or hide details such as the record name, and assigned user, and assigned team in the search results. To add a column to List View, select the field from the Hide Columns list and use the left arrow to move it to the Display Columns list. To remove a column from List View, select it from the Display Columns list and use the right arrow to move it to the Hide Columns list.<br><br>If you save layout settings, you will be able to load them at any time to view the search results in the custom layout.<br><br>To save and update a search and\/or layout:<ol><li>Enter a name for the search results in the <b>Save this search as<\/b> field and click <b>Save<\/b>.The name now displays in the Saved Searches list adjacent to the <b>Clear<\/b> button.<\/li><li>To view a saved search, select it from the Saved Searches list. The search results are displayed in the List View.<\/li><li>To update the properties of a saved search, select the saved search from the list, enter the new search criteria and\/or layout options in the Advanced Search area, and click <b>Update<\/b> next to <b>Modify Current Search<\/b>.<\/li><li>To delete a saved search, select it in the Saved Searches list, click <b>Delete<\/b> next to <b>Modify Current Search<\/b>, and then click <b>OK<\/b> to confirm the deletion.<\/li><\/ol>","ERR_QUERY_LIMIT":"L\u1ed7i: Gi\u1edbi h\u1ea1n truy v\u1ea5n $limit \u0111\u1ea1t t\u1edbi gi\u1edbi h\u1ea1n cho  $module","ERROR_NOTIFY_OVERRIDE":"L\u1ed7i: ResourceObserver->notify() c\u1ea7n ph\u1ea3i \u0111\u01b0\u1ee3c overridden.","ERR_MONITOR_FILE_MISSING":"L\u1ed7i: Kh\u00f4ng th\u1ec3 theo d\u00f5i v\u00ec file si\u00eau d\u1eef li\u1ec7u r\u1ed7ng ho\u1eb7c file n\u00e0y kh\u00f4ng t\u1ed3n t\u1ea1i.","ERR_MONITOR_NOT_CONFIGURED":"L\u1ed7i: Kh\u00f4ng c\u00f3 b\u1ed9 theo d\u00f5i \u0111\u01b0\u1ee3c c\u1ea5u h\u00ecnh cho t\u00ean y\u00eau c\u1ea7u","ERR_UNDEFINED_METRIC":"L\u1ed7i: Kh\u00f4ng th\u1ec3 thi\u1ebft l\u1eadp gi\u00e1 tr\u1ecb cho th\u01b0\u1edbc \u0111o kh\u00f4ng \u0111\u1ecbnh ngh\u0129a","ERR_STORE_FILE_MISSING":"L\u1ed7i: Kh\u00f4ng t\u00ecm th\u1ea5y n\u01a1i l\u01b0u file th\u1ef1c thi","LBL_MONITOR_ID":"M\u00e3 ng\u01b0\u1eddi theo d\u00f5i","LBL_USER_ID":"M\u00e3 ng\u01b0\u1eddi d\u00f9ng","LBL_MODULE_NAME":"T\u00ean m\u00f4-\u0111un","LBL_ITEM_ID":"M\u00e3 m\u1ee5c","LBL_ITEM_SUMMARY":"T\u1ed5ng quan","LBL_ACTION":"Ho\u1ea1t \u0111\u1ed9ng","LBL_SESSION_ID":"M\u00e3 phi\u00ean l\u00e0m vi\u1ec7c","LBL_VISIBLE":"Th\u1ea5y \u0111\u01b0\u1ee3c h\u1ed3 s\u01a1","LBL_DATE_LAST_ACTION":"Ng\u00e0y c\u1ee7a ho\u1ea1t \u0111\u1ed9ng sau c\u00f9ng","MSG_IS_NOT_BEFORE":"kh\u00f4ng \u0111\u01b0\u1ee3c l\u1edbn h\u01a1n","LBL_PORTAL_WELCOME_TITLE":"Ch\u00e0o m\u1eebng b\u1ea1n t\u1edbi incomCRM Portal 5.1","LBL_PORTAL_WELCOME_INFO":"incomCRM Portal l\u00e0 framework \u0111\u01b0\u1ee3c cung c\u1ea5p real-time \u0111\u1ec3 xem c\u00e1c cases, bugs & newsletters ...","LBL_LIST":"Danh s\u00e1ch","LBL_CREATE_BUG":"T\u1ea1o l\u1ed7i","LBL_NO_RECORDS_FOUND":"- 0 h\u1ed3 s\u01a1 t\u00ecm th\u1ea5y -","DATA_TYPE_DUE":"L\u00ed do:","DATA_TYPE_START":"B\u1eaft \u0111\u1ea7u:","DATA_TYPE_SENT":"G\u1edfi:","DATA_TYPE_MODIFIED":"Ch\u1ec9nh s\u1eeda:","LBL_REPORT_NEWREPORT_COLUMNS_TAB_COUNT":"\u0110\u1ebfm","LBL_OBJECT_IMAGE":"object image","LBL_MASSUPDATE_DATE":"Ch\u1ecdn ng\u00e0y","LBL_VALIDATE_RANGE":"kh\u00f4ng trong mi\u1ec1n gi\u00e1 tr\u1ecb","LBL_DROPDOWN_LIST_ALL":"T\u1ea5t c\u1ea3","LBL_OPERATOR_IN_TEXT":"l\u00e0 m\u1ed9t trong nh\u1eefng c\u00e1i sau:","LBL_OPERATOR_NOT_IN_TEXT":"kh\u00f4ng ph\u1ea3i m\u1ed9t trong nh\u1eefng c\u00e1i sau: ","ERR_CONNECTOR_FILL_BEANS_SIZE_MISMATCH":"L\u1ed7i: K\u1ebft qu\u1ea3 kh\u00f4ng kh\u1edbp nhau.","ERR_MISSING_MAPPING_ENTRY_FORM_MODULE":"L\u1ed7i: Qu\u00ean thi\u1ebft l\u1eadp b\u1ea3n \u0111\u1ed3 cho m\u00f4-\u0111un","ERROR_UNABLE_TO_RETRIEVE_DATA":"L\u1ed7i: Kh\u00f4ng th\u1ec3 nh\u1eadn d\u1eef li\u1ec7u t\u1eeb b\u1ed9 k\u1ebft n\u1ed1i.","LBL_MERGE_CONNECTORS":"L\u1ea5y d\u1eef li\u1ec7u","LBL_MERGE_CONNECTORS_BUTTON_KEY":"[D]","LBL_REMOVE_MODULE_ENTRY":"B\u1ea1n c\u00f3 ch\u1eafc mu\u1ed1n v\u00f4 hi\u1ec7u h\u00f3a b\u1ed9 k\u1ebft n\u1ed1i t\u00edch h\u1ee3p c\u1ee7a m\u00f4-\u0111un n\u00e0y?","LBL_MASSUPDATE_DELETE_GLOBAL_TEAM":"Xin l\u1ed7i, b\u1ea1n kh\u00f4ng th\u1ec3 x\u00f3a nh\u00f3m to\u00e0n c\u1ee5c n\u00e0y. B\u1ecf qua.","LBL_MASSUPDATE_DELETE_PRIVATE_TEAMS":"Xin l\u1ed7i, b\u1ea1n kh\u00f4ng th\u1ec3 x\u00f3a nh\u00f3m ri\u00eang t\u01b0 n\u00e0y. B\u1ecf qua.","LBL_NO_FLASH_PLAYER":"Xin ch\u00e0o, b\u1ea1n c\u00f3 Flash ho\u1eb7c t\u1eaft ho\u1eb7c m\u1ed9t phi\u00ean b\u1ea3n c\u0169 c\u1ee7a Adobe's Flash Player. Xin vui l\u00f2ng t\u1ea3i v\u1ec1 <a href=\"http:\/\/www.adobe.com\/go\/getflashplayer\/\">phi\u00ean b\u1ea3n m\u1edbi nh\u1ea5t c\u1ee7a Fash player<\/a> ho\u1eb7c m\u1edf l\u1ea1i flash.","LBL_CREATE_KB_DOCUMENT":"T\u1ea1o b\u00e0i vi\u1ebft","LBL_LOGIN_AS":"Login as ","LBL_LOGOUT_AS":"Logout as ","LBL_SAVE_AMOUNT_BUTTON_LABEL":"C\u1eadp nh\u1eadt gi\u00e1 tr\u1ecb h\u1ee3p \u0111\u1ed3ng","LBL_SAVE_AMOUNT_PR_ONLINE_BUTTON_LABEL":"L\u01b0u x\u00e1c nh\u1eadn ng\u00e0y Goline","LBL_SIGN_OUT":"\u0110\u0103ng xu\u1ea5t","LBL_CONTACT_US":"Li\u00ean h\u1ec7","LBL_ADD_EMAIL_BUTTON":"Th\u00eam E-mail","LBL_SAVE_COMPLETE_BUTTON":"L\u01b0u Ho\u00e0n t\u1ea5t","MSG_JS_ALERT_MTG_REMINDER_TASK":"C\u00f4ng vi\u1ec7c","LNK_MORE_INFO":"Th\u00f4ng tin th\u00eam","LNK_VIEW_CHANGE_LOG2":"Nh\u1eadt k\u00fd","LNK_REMOVE_ALL":"X\u00f3a t\u1ea5t c\u1ea3","LNK_IMPORT_EXCEL":"Nh\u1eadp Excel","LNK_EXPORT_EXCEL":"Xu\u1ea5t Excel","LBL_CHART_VIEW":"Xem bi\u1ec3u \u0111\u1ed3","LBL_VOICE_INPUT":"Nh\u1eadp b\u1eb1ng gi\u1ecdng n\u00f3i","LBL_NOTIFICATIONS":"Th\u00f4ng b\u00e1o m\u1edbi","LBL_SEARCH_RESULT":"K\u1ebft qu\u1ea3 t\u00ecm ki\u1ebfm","LBL_SELECT_DATE_RANGES":"Ch\u1ecdn kho\u1ea3ng th\u1eddi gian","LBL_RPT_DATE_FROM":"T\u1eeb ng\u00e0y","LBL_RPT_DATE_TO":"\u0110\u1ebfn ng\u00e0y","LBL_RPT_FROM":"T\u1eeb","LBL_RPT_TO":"\u0110\u1ebfn","LBL_RPT_RN":"STT","LBL_RPT_TOTAL":"T\u1ed5ng c\u1ed9ng","LBL_SELECT_YEAR":"Ch\u1ecdn N\u0103m","LBL_SELECT_QUARTER":"Ch\u1ecdn Qu\u00fd","LBL_SELECT_MONTH":"Ch\u1ecdn Th\u00e1ng","LBL_SELECT_USERS":"Ch\u1ecdn Nh\u00e2n vi\u00ean","LBL_SELECT_DEPT":"Ch\u1ecdn Ph\u00f2ng ban","LBL_SELECT_CITY":"Ch\u1ecdn t\u1ec9nh\/th\u00e0nh ph\u1ed1","LBL_SELECT_DISTRICT":"Ch\u1ecdn qu\u1eadn\/huy\u1ec7n","LBL_SELECT_WARD":"Ch\u1ecdn ph\u01b0\u1eddng\/x\u00e3","LBL_RPT_WEEKDAY":"Th\u1ee9","LBL_RPT_DAY":"Ng\u00e0y","LBL_RPT_DATE":"Ng\u00e0y","LBL_RPT_WEEK":"Tu\u1ea7n","LBL_RPT_MONTH":"Th\u00e1ng","LBL_RPT_QUARTER":"Qu\u00fd","LBL_RPT_YEAR":"N\u0103m","LBL_RPT_HOUR":"Gi\u1edd","LBL_RPT_MINUTE":"Ph\u00fat","LBL_DATE_ENTERED_FROM":"Ng\u00e0y t\u1ea1o T\u1eeb","LBL_DATE_MODIFIED_FROM":"Ng\u00e0y s\u1eeda T\u1eeb","ERR_HANDLE_PROCESSING":"\u0110\u00e3 ph\u00e1t sinh l\u1ed7i trong qu\u00e1 tr\u00ecnh x\u1eed l\u00fd","LBL_PROCESS_BUTTON_LABEL":"X\u1eed l\u00fd","LBL_SHARING_USERS":"Share Users","LBL_PROJECT_TEAMS":"Project Teams","LBL_SHARED_TO_ME":"Chia s\u1ebb cho t\u00f4i?","LBL_ONLY_OF_ME":"Ch\u1ec9 CV ri\u00eang c\u1ee7a t\u00f4i?","LBL_ONLY_SHARED":"Ch\u1ec9 CV \u0111\u01b0\u1ee3c chia s\u1ebb?","LBL_LIST_DISPLAY":"Hi\u1ec3n th\u1ecb #","LBL_CONFIG_SUBPANEL_FIELDS":"Ch\u1ecdn c\u00e1c c\u1ed9t hi\u1ec3n th\u1ecb","LBL_CHANGE_SUBPANEL_FIELDS":"Hi\u1ec7n c\u00e1c c\u1ed9t","MSG_DATA_EMPTY":"Ch\u01b0a c\u00f3 th\u00f4ng tin!","LBL_ERR_DUPLICATION_FORMAT":"%s [%s] b\u1ea1n nh\u1eadp \u0111\u00e3 t\u1ed3n t\u1ea1i, vui l\u00f2ng nh\u1eadp l\u1ea1i.","LBL_WARNING_MISSING_FIELDS_CONFIRM":"Vui l\u00f2ng c\u1eadp nh\u1eadp \u0111\u1ea7y \u0111\u1ee7 d\u1eef li\u1ec7u trong tr\u01b0\u1eddng (*).\nB\u1ea5m OK \u0111\u1ec3 l\u01b0u, Cancel \u0111\u1ec3 nh\u1eadp ti\u1ebfp.","LBL_ATTACHMENTS":"Files \u0110K","LBL_ATT_VERSION":"Phi\u00ean b\u1ea3n","LBL_ATT_DATE_ADJUSTMENT":"Ng\u00e0y \u0111i\u1ec1u ch\u1ec9nh","LBL_ATT_CONTENT":"N\u1ed9i dung","LBL_ATT_REASON":"L\u00fd do","LBL_ATT_PERFORMED_BY":"Ng\u01b0\u1eddi th\u1ef1c hi\u1ec7n","LBL_ATT_NOTE":"Ghi ch\u00fa","LBL_ATT_FILE":"File","LBL_ATT_REMOVING_ATTACHMENT":"\u0110ang x\u00f3a \u0111\u00ednh k\u00e8m...","ERR_ATT_REMOVING_ATTACHMENT":"X\u00f3a \u0111\u00ednh k\u00e8m th\u1ea5t b\u1ea1i!","ERR_UPLOAD_MAXSIZE":"Dung l\u01b0\u1ee3ng t\u1ec7p tin (fileSize) \u0111\u01b0\u1ee3c \u00fap t\u1ed1i \u0111a l\u00e0: maxSize","ERR_ALERT_MAXSIZE":"Dung l\u01b0\u1ee3ng t\u1ec7p tin v\u01b0\u1ee3t gi\u1edbi h\u1ea1n. Vui l\u00f2ng ch\u1ecdn l\u1ea1i!","LBL_IMPORT_EXCEL_LABEL":"Nh\u1eadp t\u1eeb File Excel","LBL_IMPORT_EXCEL_BUTTON_LABEL":"Import","LBL_IMPORT_EXCEL_MARK":"\u0110\u00e1nh d\u1ea5u import","LBL_IMPORT_EXCEL_FILE_SELECT":"Ch\u1ecdn file (xls)","LBL_IMPORT_EXCEL_FILE_TEMPLATE":"Download file m\u1eabu Excel","LBL_IMPORT_EXCEL_RESULT":"K\u1ebft qu\u1ea3 Import","LBL_IMPORT_EXCEL_ROLLBACK":"X\u00f3a k\u1ebft qu\u1ea3 \u0111\u00e3 Import","LBL_IMPORT_EXCEL_DELETE_CONFIRM":"B\u1ea1n ch\u1eafc ch\u1eafn mu\u1ed1n x\u00f3a k\u1ebft qu\u1ea3 \u0111\u00e3 Import?","LBL_IMPORT_EXCEL_MISSING":"Thi\u1ebfu tr\u01b0\u1eddng b\u1eaft bu\u1ed9c [<b>%s<\/b>] t\u1ea1i d\u00f2ng th\u1ee9 [<b>%s<\/b>]","LBL_TABGROUP_CALENDARS":"C\u00f4ng vi\u1ec7c","LBL_TABGROUP_CRM":"CRM","LBL_TABGROUP_SELL_ORDERS":"B\u00e1n h\u00e0ng","LBL_TABGROUP_PURCHASE_ORDERS":"Mua h\u00e0ng","LBL_TABGROUP_ACCOUNTING":"K\u1ebf to\u00e1n","LBL_TABGROUP_WAREHOUSES":"Kho","LBL_TABGROUP_PROJECTS":"D\u1ef1 \u00e1n","LBL_TABGROUP_HR":"Nh\u00e2n s\u1ef1","LBL_TABGROUP_PUBLIC":"Th\u00f4ng tin chung","LBL_TABGROUP_CONTRACTS":"H\u1ee3p \u0111\u1ed3ng","LBL_TABGROUP_MAPS":"V\u00f9ng th\u1ecb tr\u01b0\u1eddng","LBL_TABGROUP_WORKFLOWS":"Quy tr\u00ecnh","LBL_TABGROUP_ORDER_DELIVERY":"X\u1eed l\u00fd giao h\u00e0ng","LBL_TABGROUP_MOBILE_APP":"App","LNK_QUICK_CREATE_TASK":"Th\u00eam C\u00f4ng vi\u1ec7c","LNK_QUICK_CREATE_CALL":"Th\u00eam Cu\u1ed9c g\u1ecdi","LNK_QUICK_CREATE_MEETING":"Th\u00eam Cu\u1ed9c h\u1ecdp","LNK_QUICK_CREATE_OPPORTUNITY":"Th\u00eam \u0110\u01a1n h\u00e0ng","LBL_COMMENT_BUTTON_LABEL":"Ghi ch\u00fa","LBL_BUTTON_VIEW_HISTORIES":"Xem l\u1ecbch s\u1eed","LBL_VIEW_NOTE_HISTORIES":"L\u1ecbch s\u1eed ghi ch\u00fa","LBL_EMPTY_NOTE_HISTORIES":"Ch\u01b0a c\u00f3 l\u1ecbch s\u1eed ghi ch\u00fa n\u00e0o!","LBL_BUTTON_HISTORIES":"L\u1ecbch s\u1eed","LBL_VIEW_CONTENT_FULL":"Xem \u0111\u1ea7y \u0111\u1ee7 n\u1ed9i dung","LBL_VIEW_CONTENT_LESS":"Thu g\u1ecdn n\u1ed9i dung","LBL_GROUPTAB11_1712200721":"Nh\u00f3m m\u1edbi","LBL_GROUPTAB12_1712200721":"D\u1ef1 \u00e1n","LBL_GROUPTAB12_1735630407":"Marketing","LBL_GROUPTAB12_1735630798":"Marketing","LBL_GROUPTAB14_1739862740":"Duy\u1ec7t","LBL_BRANCH_ID":"Chi nh\u00e1nh","LBL_DEPARTMENT_ID":"Ph\u00f2ng ban","LBL_TEAM_GROUP_ID":"B\u1ed9 ph\u1eadn","LBL_TEAMS":"B\u1ed9 ph\u1eadn","LBL_LOCATION_NEARBY":"V\u1ecb tr\u00ed GPS","LBL_VIEW_MAPS":"Xem b\u1ea3n \u0111\u1ed3","LBL_BRANCH_ALL":"Chi nh\u00e1nh xem","LBL_DEPARTMENT_ALL":"Ph\u00f2ng ban xem","LBL_USERS_ALL":"Ng\u01b0\u1eddi li\u00ean quan","LBL_CONVERT_KG":"Quy \u0111\u1ed5i Th\u00f9ng\/Kg","LBL_CONVERT_TOTAL_KG":"T\u1ed5ng Th\u00f9ng\/Kg","LBL_CONVERT_M3":"Quy \u0111\u1ed5i","LBL_CONVERT_TOTAL_MT":"T\u1ed5ng quy \u0111\u1ed5i"});
incomCRM.language.setLanguage('app_list_strings', {"language_pack_name":"Vietnamese","moduleList":{"Home":"Trang ch\u1ee7","Dashboard":"B\u1ea3ng th\u00f4ng tin","Contacts":"Ng\u01b0\u1eddi li\u00ean h\u1ec7","Accounts":"Kh\u00e1ch h\u00e0ng","Opportunities":"DS \u0110\u01a1n h\u00e0ng b\u00e1n (SO)","Cases":"V\u1ee5 vi\u1ec7c","Notes":"Ghi ch\u00fa & \u0110\u00ednh k\u00e8m (CHAT)","Calls":"Cu\u1ed9c g\u1ecdi","Emails":"Th\u01b0 \u0111i\u1ec7n t\u1eed","Meetings":"H\u1ed9i h\u1ecdp","Tasks":"Danh s\u00e1ch c\u00f4ng vi\u1ec7c","Calendar":"L\u1ecbch c\u00f4ng vi\u1ec7c","Leads":"Leads","Currencies":"Ti\u1ec1n t\u1ec7","Activities":"Ho\u1ea1t \u0111\u1ed9ng","Bugs":"L\u1ed7i","Feeds":"RSS","iFrames":"C\u1ed5ng th\u00f4ng tin","TimePeriods":"C\u00e1c giai \u0111o\u1ea1n","Project":"Danh s\u00e1ch D\u1ef1 \u00e1n","ProjectTask":"T\u00e1c v\u1ee5 trong d\u1ef1 \u00e1n","Campaigns":"Chi\u1ebfn d\u1ecbch","CampaignLog":"Nh\u1eadt k\u00fd chi\u1ebfn d\u1ecbch","Documents":"T\u00e0i li\u1ec7u","Sync":"\u0110\u1ed3ng b\u1ed9","Users":"Ng\u01b0\u1eddi d\u00f9ng","Releases":"Ph\u00e1t h\u00e0nh","Prospects":"M\u1ee5c ti\u00eau","Queues":"H\u00e0ng \u0111\u1ee3i","EmailMarketing":"Ti\u1ebfp th\u1ecb b\u1eb1ng th\u01b0 \u0111i\u1ec7n t\u1eed","EmailTemplates":"M\u1eabu th\u01b0","ProspectLists":"Danh s\u00e1ch m\u1ee5c ti\u00eau","SavedSearch":"L\u01b0u t\u00ecm ki\u1ebfm","Trackers":"C\u00f4ng c\u1ee5 theo d\u00f5i","TrackerPerfs":"N\u0103ng su\u1ea5t","TrackerSessions":"C\u00e1c phi\u00ean theo d\u00f5i","TrackerQueries":"C\u00e1c truy v\u1ea5n","FAQ":"FAQ","Newsletters":"Th\u01b0 gi\u1ea5y","incomCRMFeed":"incomCRM Feed","Employees":"Danh s\u00e1ch nh\u00e2n s\u1ef1","Library":"Library","EmailAddresses":"Email Address","KBDocuments":"Knowledge Base","Debit_Loans":"N\u1ee3 Vay","FinancialReport":"B\u00e1o c\u00e1o t\u00ecnh h\u00ecnh t\u00e0i ch\u00ednh","Find_Products_Purchase":"L\u1ecbch s\u1eed SP\/\u0110\u01a1n h\u00e0ng(Mua)","Customer_Cares":"CS Kh\u00e1ch h\u00e0ng","Track_Inventories":"Theo d\u00f5i t\u1ed3n kho","Report_Purchase":"B\u00e1o c\u00e1o Ph\u00e2n h\u1ec7 Mua h\u00e0ng","SalePlans":"K\u1ebf ho\u1ea1ch th\u00e1ng","Report_HR":"B\u00e1o c\u00e1o Ph\u00e2n h\u1ec7 Nh\u00e2n s\u1ef1","Debit_Bills":"N\u1ee3 ph\u1ea3i Thu","TQT_PurchaseProcessings":"X\u1eed l\u00fd ti\u1ebfn \u0111\u1ed9 \u0110\u01a1n h\u00e0ng mua","Contracts":"H\u1ee3p \u0111\u1ed3ng","Contracts_Locks":"Kh\u00f3a h\u1ee3p \u0111\u1ed3ng","Contracts_UnLocks":"M\u1edf kh\u00f3a h\u1ee3p \u0111\u1ed3ng","Contracts_Payments":"\u00d0\u1ee3t TT - H\u1ee3p \u0111\u1ed3ng","Contracts_Collections":"Thu ti\u1ec1n\/H\u1ee3p \u0111\u1ed3ng","Contracts_Approves":"Duy\u1ec7t h\u1ee3p \u0111\u1ed3ng","Contracts_Invoice":"H\u00f3a \u0111\u01a1n\/H\u1ee3p \u0111\u1ed3ng","Contracts_RevenueReduces":"Gi\u1ea3m thu ti\u1ec1n tr\u00ean H\u0110","Opportunities_Tasks":"Ti\u1ebfn tr\u00ecnh x\u1eed l\u00fd \u0111\u01a1n h\u00e0ng","BI_ContractTypes":"B.I Lo\u1ea1i H\u1ee3p \u0111\u1ed3ng","TQT_Product_VATs":"Qu\u1ea3n l\u00fd kho VAT","TQT_ProductSeries":"Nh\u00e3n hi\u1ec7u \/ H\u00e3ng s\u1ea3n xu\u1ea5t","Opportunities_Payments":"Xu\u1ea5t h\u00f3a \u0111\u01a1n VAT","Debit_Collections":"Thu n\u1ee3 kh\u00e1ch h\u00e0ng","TQT_Comments":"Comments","Payable_Debts":"N\u1ee3 ph\u1ea3i Tr\u1ea3","Product_Gifts":"Khuy\u1ebfn m\u00e3i t\u1eb7ng s\u1ea3n ph\u1ea9m","Design_Orders_Tasks":"Ti\u1ebfn tr\u00ecnh x\u1eed l\u00fd ti\u1ebfn \u0111\u1ed9 Mua h\u00e0ng nh\u1eadp kh\u1ea9u","Report_Warehouses":"B\u00e1o c\u00e1o Ph\u00e2n h\u1ec7 Kho","Warehouse_Ins":"Nh\u1eadp Kho","TrackWarehouse_Stocks":"T\u1ed3n kho theo kho h\u00e0ng","Telesales":"Telesales","Find_Products":"L\u1ecbch s\u1eed SP\/\u0110\u01a1n h\u00e0ng(B\u00e1n)","Sync_Data":"\u0110\u1ed3ng b\u1ed9 d\u1eef li\u1ec7u","MonthlyTargets":"Ch\u1ec9 ti\u00eau Th\u00e1ng","TQT_DesignProcessings":"X\u1eed l\u00fd ti\u1ebfn \u0111\u1ed9 Mua h\u00e0ng nh\u1eadp kh\u1ea9u","Cash_Reasons":"Lo\u1ea1i Thu\/Chi","Report_Works":"B\u00e1o c\u00e1o Ph\u00e2n h\u1ec7 Qu\u1ea3n l\u00fd c\u00f4ng vi\u1ec7c","AseanRevenues":"B\u00e1o c\u00e1o K\u1ebft qu\u1ea3 ho\u1ea1t \u0111\u1ed9ng kinh doanh","Revenue_Promotions":"Khuy\u1ebfn m\u00e3i theo doanh s\u1ed1","Regional_Markets":"V\u00f9ng th\u1ecb tr\u01b0\u1eddng","SecurityGroups":"Nh\u00f3m ng\u01b0\u1eddi d\u00f9ng","Data_Locks":"Kh\u00f3a\/M\u1edf kh\u00f3a D\u1eef li\u1ec7u","List_Expenses":"C\u00e1c kho\u1ea3n chi","Account_Discounts":"Chi\u1ebft kh\u1ea5u tr\u00ean Kh\u00e1ch h\u00e0ng","Suppliers":"Nh\u00e0 Cung C\u1ea5p","TQT_Products":"S\u1ea3n ph\u1ea9m","Checkin_Dates":"Check-in","Product_Discounts":"Chi\u1ebft kh\u1ea5u tr\u00ean \u0110\u01a1n h\u00e0ng","Monitoring_Cares":"Gi\u00e1m s\u00e1t CSKH","TQT_OrderDetails":"Chi ph\u00ed ph\u00e1t sinh","Delivery_Orders_Tasks":"Ti\u1ebfn tr\u00ecnh x\u1eed l\u00fd Phi\u1ebfu giao h\u00e0ng","BI_Filters":"L\u1ecdc KH theo D.S\u1ed1","TQT_Warehouses":"Danh s\u00e1ch Kho","Group_Prices":"Thi\u1ebft l\u1eadp gi\u00e1 b\u00e1n theo S\u1ed1 l\u01b0\u1ee3ng","Debit_Sales":"Theo d\u00f5i n\u1ee3 Nh\u00e2n vi\u00ean","Branches":"Chi nh\u00e1nh\/Khu v\u1ef1c","Report_Accountant":"B\u00e1o c\u00e1o Ph\u00e2n h\u1ec7 K\u1ebf to\u00e1n","TQT_GrowthAbilities":"Ph\u00e2n t\u00edch kh\u1ea3 n\u0103ng t\u0103ng tr\u01b0\u1edfng","Work_Sales":"B\u00e1n h\u00e0ng h\u00f4m nay","TQT_ExecContracts":"Th\u1ef1c hi\u1ec7n H\u1ee3p \u0111\u1ed3ng","Product_Units":"\u0110\u01a1n v\u1ecb t\u00ednh","Accounts_Leads":"\u0110\u1ea7u m\u1ed1i","TQT_MainTasks":"C\u00f4ng vi\u1ec7c ch\u00ednh","TQT_SubTasks":"Chi ti\u1ebft c\u00f4ng vi\u1ec7c","TQT_SummaryWeeks":"B\u00e1o c\u00e1o tu\u1ea7n","TrackReview":"Theo d\u00f5i & \u0110\u00e1nh gi\u00e1","Invoices":"H\u00f3a \u0111\u01a1n \u0111i\u1ec7n t\u1eed","TQT_ProductGroup":"Nh\u00f3m S\u1ea3n ph\u1ea9m","Time_Keepings":"Ch\u1ea5m c\u00f4ng","Cashes":"Qu\u1ea3n l\u00fd Thu\/Chi","Accounts_Targets":"Ph\u00e2n b\u1ed5 doanh thu kh\u00e1ch h\u00e0ng","PurchaseLists":"Danh s\u00e1ch \u0110\u01a1n h\u00e0ng mua (PO)","Report_Project":"B\u00e1o c\u00e1o Ph\u00e2n h\u1ec7 D\u1ef1 \u00e1n","TQT_DeliveryProcessings":"X\u1eed l\u00fd ti\u1ebfn \u0111\u1ed9 Phi\u1ebfu giao h\u00e0ng","Product_Norms":"\u0110\u1ecbnh m\u1ee9c t\u1ed3n kho Min - Max","TQT_OrderProcessings":"X\u1eed l\u00fd ti\u1ebfn \u0111\u1ed9 \u0110\u01a1n h\u00e0ng b\u00e1n","TQT_GuaranteeProcessings":"X\u1eed l\u00fd b\u1ea3o h\u00e0nh\/b\u1ea3o tr\u00ec","Purchase_Orders_Tasks":"Ti\u1ebfn tr\u00ecnh x\u1eed l\u00fd mua h\u00e0ng","Approval_Histories":"L\u1ecbch s\u1eed duy\u1ec7t","Product_Promotions":"Ch\u01b0\u01a1ng tr\u00ecnh khuy\u1ebfn m\u00e3i","Handle_Steerings":"Ch\u1ec9 \u0111\u1ea1o x\u1eed l\u00fd","Tasks_Groups":"Nh\u00f3m ng\u01b0\u1eddi th\u1ef1c hi\u1ec7n c\u00f4ng vi\u1ec7c","List_Incurred_Costs":"Lo\u1ea1i chi ph\u00ed ph\u00e1t sinh","Report_CRM":"B\u00e1o c\u00e1o Ph\u00e2n h\u1ec7 CRM","Custom_Units":"Danh m\u1ee5c \u0111\u01a1n v\u1ecb t\u00ednh","Bank_Accounts":"TK ng\u00e2n h\u00e0ng","Warehouse_Invs":"Ki\u1ec3m kho","Guarantee_Orders_Tasks":"Ti\u1ebfn tr\u00ecnh X\u1eed l\u00fd b\u1ea3o h\u00e0nh\/b\u1ea3o tr\u00ec","Debt_Norms":"\u0110\u1ecbnh m\u1ee9c c\u00f4ng n\u1ee3","Opportunities_Commissions":"Hoa h\u1ed3ng \u0111\u1ed1i t\u00e1c","TQT_BusinessFields":"L\u0129nh v\u1ef1c kinh doanh","Accounting_Codes":"T\u00e0i kho\u1ea3n k\u1ebf to\u00e1n","Product_Lots":"Qu\u1ea3n l\u00fd theo l\u00f4(LOT)","Approval_Levels":"C\u1ea5p duy\u1ec7t","PayRolls":"B\u1ea3ng l\u01b0\u01a1ng nh\u00e2n vi\u00ean","Monitoring_Sales":"Gi\u00e1m s\u00e1t b\u00e1n h\u00e0ng","TQT_Product_Stocks":"Sp. Trong kho","Account_Groups":"Nh\u00f3m kh\u00e1ch h\u00e0ng","Warehouse_Types":"Lo\u1ea1i phi\u1ebfu Nh\u1eadp\/Xu\u1ea5t","Report_Opportunities":"B\u00e1o c\u00e1o Ph\u00e2n h\u1ec7 B\u00e1n H\u00e0ng","TQT_Reports":"B\u00e1o c\u00e1o","Project_Tasks":"Ti\u1ebfn tr\u00ecnh X\u1eed l\u00fd D\u1ef1 \u00e1n","Report_Sales":"Th\u00f4ng tin \u0111\u01a1n h\u00e0ng","TQT_ProjectProcessings":"Qu\u1ea3n l\u00fd d\u1ef1 \u00e1n","TQT_Product_Names":"Qu\u1ea3n l\u00fd \u0111\u00edch danh","TQT_SharingUsers":"Chia s\u1ebb ng\u01b0\u1eddi d\u00f9ng","Debit_Lends":"Cho Vay","Purchase_Orders":"Th\u00f4ng tin mua h\u00e0ng","TQT_Announcement":"Th\u00f4ng b\u00e1o","BI_Markets":"Y\u00eau c\u1ea7u h\u1ed7 tr\u1ee3","Team_Groups":"B\u1ed9 ph\u1eadn","Departments":"Ph\u00f2ng ban\/NPP","Todo_Lists":"Danh s\u00e1ch \u0111\u1ea7u vi\u1ec7c","Update_Prices":"Thay \u0111\u1ed5i gi\u00e1 b\u00e1n SP","Report_Syncs":"Xu\u1ea5t nh\u1eadp K\u1ebf to\u00e1n","Map_Points":"\u0110\u1ecbnh v\u1ecb GPS","TQT_ProductPrices":"Thi\u1ebft l\u1eadp gi\u00e1 b\u00e1n theo Kh\u00e1ch h\u00e0ng","Approval_Lists":"Duy\u1ec7t","Warehouse_Outs":"Xu\u1ea5t Kho","BI_Inventories":"B.I Inventory","External_Orders":"Mua h\u00e0ng Nh\u1eadp kh\u1ea9u","ReferenceCode":"C\u1eadp nh\u1eadt m\u00e3 tham chi\u1ebfu","Sales_Lines":"Tuy\u1ebfn \u0111\u01b0\u1eddng","Cycle_Plans":"Ho\u1ea1t \u0111\u1ed9ng b\u00e1n h\u00e0ng trong th\u00e1ng"},"moduleListSingular":{"Home":"Trang ch\u1ee7","Dashboard":"B\u1ea3ng th\u00f4ng tin","Contacts":"Ng\u01b0\u1eddi li\u00ean h\u1ec7","Accounts":"Kh\u00e1ch h\u00e0ng","Opportunities":"\u0110\u01a1n h\u00e0ng","Cases":"V\u1ee5 vi\u1ec7c","Notes":"Ghi ch\u00fa","Calls":"Cu\u1ed9c g\u1ecdi","Emails":"Th\u01b0 \u0111i\u1ec7n t\u1eed","Meetings":"H\u1ed9i h\u1ecdp","Tasks":"T\u00e1c v\u1ee5","Calendar":"L\u1ecbch","Leads":"\u0110\u1ea7u m\u1ed1i","Activities":"Ho\u1ea1t \u0111\u1ed9ng","Bugs":"L\u1ed7i","Feeds":"RSS","iFrames":"C\u1ed5ng th\u00f4ng tin","TimePeriods":"C\u00e1c giai \u0111o\u1ea1n","Project":"D\u1ef1 \u00e1n","ProjectTask":"T\u00e1c v\u1ee5 trong d\u1ef1 \u00e1n","Campaigns":"Chi\u1ebfn d\u1ecbch","Documents":"T\u00e0i li\u1ec7u","Sync":"\u0110\u1ed3ng b\u1ed9","Users":"Ng\u01b0\u1eddi d\u00f9ng"},"checkbox_dom":["","C\u00f3","Kh\u00f4ng"],"checkbox_no_dom":{"0":"","2":"Kh\u00f4ng"},"account_type_dom":{"":"","KL":"Kh\u00e1ch l\u1ebb","CT":"C\u00f4ng ty","CH":"C\u1eeda h\u00e0ng\/\u0110.L\u00fd","TM":"Kh\u00e1ch th\u01b0\u01a1ng m\u1ea1i","KQ":"Kh\u00e1ch quen","CV":"CV Ri\u00eang","KH":"Kh\u00e1c"},"industry_dom":{"":"","Apparel":"\u0110\u1ed3 may m\u1eb7c","Banking":"Ng\u00e2n h\u00e0ng","Biotechnology":"C\u00f4ng ngh\u1ec7 sinh h\u1ecdc","Chemicals":"H\u00f3a ch\u1ea5t","Communications":"Giao ti\u1ebfp c\u1ed9ng \u0111\u1ed3ng","Construction":"X\u00e2y d\u1ef1ng","Consulting":"T\u01b0 v\u1ea5n","Education":"Gi\u00e1o d\u1ee5c","Electronics":"\u0110i\u1ec7n t\u1eed","Energy":"N\u0103ng l\u01b0\u1ee3ng","Engineering":"C\u00f4ng ngh\u1ec7","Entertainment":"Gi\u1ea3i tr\u00ed","Environmental":"M\u00f4i tr\u01b0\u1eddng","Finance":"T\u00e0i ch\u00ednh","Government":"Ch\u00ednh ph\u1ee7","Healthcare":"Ch\u0103m s\u00f3c s\u1ee9c kh\u1ecfe","Hospitality":"Nh\u00e0 h\u00e0ng","Insurance":"B\u1ea3o hi\u1ec3m","Machinery":"M\u00e1y m\u00f3c","Manufacturing":"S\u1ea3n xu\u1ea5t","Media":"Truy\u1ec1n th\u00f4ng","Not For Profit":"Phi l\u1ee3i nhu\u1eadn","Recreation":"S\u00e1ng t\u1ea1o","Retail":"B\u00e1n l\u1ebb","Shipping":"V\u1eadn t\u1ea3i \u0111\u01b0\u1eddng bi\u1ec3n","Technology":"K\u1ef9 thu\u1eadt","Telecommunications":"Vi\u1ec5n th\u00f4ng","Transportation":"V\u1eadn t\u1ea3i","Utilities":"Ti\u1ec7n \u00edch","Other":"Kh\u00e1c"},"lead_source_default_key":"Self Generated","lead_source_dom":{"":"","Facebook":"Facebook","Google":"Google","Zalo":"Zalo","Instagram":"Instagram","Twitter":"Twitter","Youtube":"Youtube","Website":"Website","Email":"Email","SMSbrand":"SMS brand","Friend":"Ng\u01b0\u1eddi th\u00e2n gi\u1edbi thi\u1ec7u","Other":"Ngu\u1ed3n kh\u00e1c"},"roi_type_dom":{"Revenue":"Doanh thu","Investment":"\u0110\u1ea7u t\u01b0","Expected_Revenue":"Doanh thu d\u1ef1 ki\u1ebfn","Budget":"Ng\u00e2n s\u00e1ch"},"opportunity_relationship_type_default_key":"Primary Decision Maker","opportunity_relationship_type_dom":{"":"","Primary Decision Maker":"Ng\u01b0\u1eddi quy\u1ebft \u0111\u1ecbnh ch\u00ednh","Business Decision Maker":"Ng\u01b0\u1eddi quy\u1ebft \u0111\u1ecbnh kinh doanh","Business Evaluator":"Ng\u01b0\u1eddi \u0111\u00e1nh gi\u00e1 kinh doanh","Technical Decision Maker":"Ng\u01b0\u1eddi quy\u1ebft \u0111\u1ecbnh k\u1ef9 thu\u1eadt","Technical Evaluator":"Ng\u01b0\u1eddi \u0111\u00e1nh gi\u00e1 k\u1ef9 thu\u1eadt","Executive Sponsor":"Nh\u00e0 t\u00e0i tr\u1ee3","Influencer":"Ng\u01b0\u1eddi th\u00f4ng th\u1ea1o","Other":"Kh\u00e1c"},"case_relationship_type_default_key":"Primary Contact","case_relationship_type_dom":{"":"","Primary Contact":"Li\u00ean l\u1ea1c ch\u00ednh","Alternate Contact":"Li\u00ean l\u1ea1c ph\u1ee5"},"payment_terms":{"":"","Net 15":"Ng\u00e0y 15 h\u00e0ng th\u00e1ng","Net 30":"Ng\u00e0y 30 h\u00e0ng th\u00e1ng"},"sales_stage_default_key":"Prospecting","in_total_group_stages":{"Draft":"Draft","Negotiation":"Negotiation","Delivered":"Delivered","On Hold":"On Hold","Confirmed":"Confirmed","Closed Accepted":"Closed Accepted","Closed Lost":"Closed Lost","Closed Dead":"Closed Dead"},"activity_dom":{"Call":"G\u1ecdi \u0111i\u1ec7n","Meeting":"H\u1ed9i h\u1ecdp","Task":"T\u00e1c v\u1ee5","Email":"Th\u01b0 \u0111i\u1ec7n t\u1eed","Note":"Ghi ch\u00fa"},"salutation_dom":{"":"","Sir":"Anh","Ms.":"Ch\u1ecb","Mr.":"\u00d4ng","Mrs.":"B\u00e0","Dr.":"TS.","Prof.":"GS."},"reminder_max_time":262800,"reminder_time_options":{"600":"Tr\u01b0\u1edbc 10 ph\u00fat","900":"Tr\u01b0\u1edbc 15 ph\u00fat","1800":"Tr\u01b0\u1edbc 30 ph\u00fat","2700":"Tr\u01b0\u1edbc 45 ph\u00fat","3600":"Tr\u01b0\u1edbc 1 gi\u1edd","7200":"Tr\u01b0\u1edbc 2 gi\u1edd","10800":"Tr\u01b0\u1edbc 3 gi\u1edd","43200":"Tr\u01b0\u1edbc 12 gi\u1edd","86400":"Tr\u01b0\u1edbc 1 ng\u00e0y","172800":"Tr\u01b0\u1edbc 2 ng\u00e0y","259200":"Tr\u01b0\u1edbc 3 ng\u00e0y"},"task_priority_default":"Medium","task_priority_dom":{"":"","Urgent":"G\u1ea5p"},"task_status_default":"Planned","task_status_dom":{"":"","Planned":"K\u1ebf ho\u1ea1ch","Completed":"Ho\u00e0n t\u1ea5t","Cancel":"H\u1ee7y b\u1ecf"},"meeting_status_default":"Planned","meeting_status_dom":{"Planned":"L\u1eadp k\u1ebf ho\u1ea1ch","Held":"T\u1ed5 ch\u1ee9c","Not Held":"Kh\u00f4ng t\u1ed5 ch\u1ee9c"},"call_status_default":"Planned","call_status_dom":{"Planned":"L\u1eadp k\u1ebf ho\u1ea1ch","Held":"T\u1ed5 ch\u1ee9c","Not Held":"Kh\u00f4ng t\u1ed5 ch\u1ee9c"},"call_direction_default":"Outbound","call_direction_dom":{"Inbound":"B\u00ean trong","Outbound":"B\u00ean ngo\u00e0i"},"lead_status_dom":{"":"","New":"M\u1edbi","Assigned":"\u0110\u01b0\u1ee3c giao","In Process":"Ti\u1ebfn h\u00e0nh","Converted":"Chuy\u1ec3n \u0111\u1ed5i","Recycled":"T\u00e1i s\u1eed d\u1ee5ng","Dead":"H\u1ee7y"},"gender_list":{"":"","male":"Nam","female":"N\u1eef"},"case_status_default_key":"New","case_status_dom":{"":"","Planning":"K\u1ebf ho\u1ea1ch","Exploring":"\u0110ang khai th\u00e1c","Suspend Explore":"Ng\u01b0ng khai th\u00e1c","Other":"Kh\u00e1c"},"case_priority_default_key":"P2","case_priority_dom":{"P1":"Cao","P2":"Trung b\u00ecnh","P3":"Th\u1ea5p"},"user_status_dom":{"Active":"Ho\u1ea1t \u0111\u1ed9ng","Inactive":"Kh\u00f4ng ho\u1ea1t \u0111\u1ed9ng"},"employee_status_dom":{"Active":"Ho\u1ea1t \u0111\u1ed9ng","Terminated":"K\u1ebft th\u00fac","Leave of Absence":"V\u1eafng m\u1eb7t"},"messenger_type_dom":{"0":"","MSN":"MSN","Yahoo!":"Yahoo!","AOL":"AOL"},"project_task_priority_options":{"High":"Cao","Medium":"Trung b\u00ecnh","Low":"Th\u1ea5p"},"project_task_priority_default":"Medium","project_task_status_options":{"Not Started":"Ch\u01b0a b\u1eaft \u0111\u1ea7u","In Progress":"\u0110ang th\u1ef1c hi\u1ec7n","Completed":"Ho\u00e0n t\u1ea5t","Pending Input":"Ch\u1edd nh\u1eadp","Deferred":"T\u1eeb ch\u1ed1i"},"project_task_utilization_options":{"0":"Kh\u00f4ng","25":"25","50":"50","75":"75","100":"100"},"project_status_dom":{"":"","1":"Thi\u1ebft k\u1ebf","2":"B\u00e1o gi\u00e1","3":"\u0110ang ch\u0103m s\u00f3c","4":"\u0110\u1ea5u th\u1ea7u","5":"K\u00fd h\u1ee3p \u0111\u1ed3ng","6":"Tri\u1ec3n khai","7":"K\u1ebft th\u00fac","8":"Kh\u00f4ng th\u00e0nh c\u00f4ng"},"project_status_default":"Draft","project_duration_units_dom":{"Days":"Ng\u00e0y","Hours":"Gi\u1edd"},"project_priority_options":{"High":"Cao","Medium":"Trung b\u00ecnh","Low":"Th\u1ea5p"},"project_priority_default":"Medium","record_type_default_key":"Accounts","record_type_display":{"":"","Accounts":"Kh\u00e1ch h\u00e0ng","Contacts":"Ng\u01b0\u1eddi li\u00ean h\u1ec7","Opportunities":"\u0110\u01a1n h\u00e0ng","Cases":"V\u1ee5 vi\u1ec7c","Bugs":"L\u1ed7i","Project":"D\u1ef1 \u00e1n","ProjectTask":"T\u00e1c v\u1ee5 trong d\u1ef1 \u00e1n","Tasks":"T\u00e1c v\u1ee5","Prospects":"M\u1ee5c ti\u00eau","Contracts":"H\u1ee3p \u0111\u1ed3ng","TQT_Comments":"Comments","TQT_SubTasks":"Chi ti\u1ebft c\u00f4ng vi\u1ec7c","TQT_MainTasks":"C\u00f4ng vi\u1ec7c ch\u00ednh","Cashes":"Thu\/Chi","PayRolls":"B\u1ea3ng l\u01b0\u01a1ng nh\u00e2n vi\u00ean"},"record_type_display_notes":{"Accounts":"Kh\u00e1ch h\u00e0ng","Contacts":"Ng\u01b0\u1eddi li\u00ean h\u1ec7","Opportunities":"\u0110\u01a1n h\u00e0ng","Tasks":"T\u00e1c v\u1ee5","Cases":"V\u1ee5 vi\u1ec7c","Bugs":"L\u1ed7i","Emails":"Th\u01b0 \u0111i\u1ec7n t\u1eed","Project":"D\u1ef1 \u00e1n","ProjectTask":"T\u00e1c v\u1ee5 trong d\u1ef1 \u00e1n","Meetings":"H\u1ed9i h\u1ecdp","Calls":"Cu\u1ed9c g\u1ecdi","Contracts":"H\u1ee3p \u0111\u1ed3ng","TQT_Comments":"Comments","TQT_SubTasks":"Chi ti\u1ebft c\u00f4ng vi\u1ec7c","TQT_MainTasks":"C\u00f4ng vi\u1ec7c ch\u00ednh","Cashes":"Thu\/Chi","PayRolls":"B\u1ea3ng l\u01b0\u01a1ng nh\u00e2n vi\u00ean","Users":"Nh\u00e2n vi\u00ean","Accounts_Leads":"\u0110\u1ea7u m\u1ed1i"},"parent_type_display":{"Accounts":"Kh\u00e1ch h\u00e0ng","Opportunities":"\u0110\u01a1n h\u00e0ng","Contacts":"Ng\u01b0\u1eddi li\u00ean h\u1ec7","Project":"D\u1ef1 \u00e1n","Users":"Nh\u00e2n vi\u00ean","Accounts_Leads":"\u0110\u1ea7u m\u1ed1i"},"quote_type_dom":{"Quotes":"Ch\u00e0o gi\u00e1","Orders":"\u0110\u1eb7t h\u00e0ng"},"default_quote_stage_key":"Draft","quote_stage_dom":{"Draft":"B\u1ea3n th\u1ea3o","Negotiation":"Th\u01b0\u01a1ng thuy\u1ebft","Delivered":"Giao h\u00e0ng","On Hold":"T\u1ea1m ho\u00e3n","Confirmed":"X\u00e1c nh\u1eadn","Closed Accepted":"K\u1ebft th\u00fac \u0111\u1ed3ng \u00fd","Closed Lost":"K\u1ebft th\u00fac th\u1ea5t b\u1ea1i","Closed Dead":"K\u1ebft th\u00fac h\u1ee7y"},"default_order_stage_key":"Pending","order_stage_dom":{"Pending":"T\u1ed3n \u0111\u1ecdng","Confirmed":"X\u00e1c nh\u1eadn","On Hold":"T\u1ea1m ho\u00e3n","Shipped":"\u0110\u00e3 v\u1eadn chuy\u1ec3n","Cancelled":"\u0110\u00e3 h\u1ee7y b\u1ecf"},"quote_relationship_type_default_key":"Primary Decision Maker","quote_relationship_type_dom":{"":"","Primary Decision Maker":"Ng\u01b0\u1eddi quy\u1ebft \u0111\u1ecbnh ch\u00ednh","Business Decision Maker":"Ng\u01b0\u1eddi quy\u1ebft \u0111\u1ecbnh kinh doanh","Business Evaluator":"Ng\u01b0\u1eddi \u0111\u00e1nh gi\u00e1 kinh doanh","Technical Decision Maker":"Ng\u01b0\u1eddi quy\u1ebft \u0111\u1ecbnh k\u1ef9 thu\u1eadt","Technical Evaluator":"Ng\u01b0\u1eddi \u0111\u00e1nh gi\u00e1 k\u1ef9 thu\u1eadt","Executive Sponsor":"Ng\u01b0\u1eddi t\u00e0i tr\u1ee3","Influencer":"Ng\u01b0\u1eddi th\u00f4ng th\u1ea1o","Other":"Kh\u00e1c"},"layouts_dom":{"Standard":"Ti\u00eau chu\u1ea9n","Invoice":"H\u00f3a \u0111\u01a1n","Terms":"\u0110i\u1ec1n ki\u1ec7n thanh to\u00e1n"},"issue_priority_default_key":"Medium","issue_priority_dom":{"Urgent":"G\u1ea5p","High":"Quan tr\u1ecdng","Medium":"Trung b\u00ecnh","Low":"Th\u1ea5p"},"issue_resolution_default_key":"","issue_resolution_dom":{"":"","Accepted":"\u0110\u1ed3ng \u00fd","Duplicate":"B\u1ea3n sao","Closed":"\u0110\u00f3ng","Out of Date":"H\u1ebft h\u1ea1n","Invalid":"Kh\u00f4ng h\u1ee3p l\u1ec7"},"issue_status_default_key":"New","issue_status_dom":{"New":"M\u1edbi","Assigned":"\u0110\u01b0\u1ee3c giao","Closed":"K\u1ebft th\u00fac","Pending":"T\u1ed3n \u0111\u1ed9ng","Rejected":"T\u1eeb ch\u1ed1i"},"bug_priority_default_key":"Medium","bug_priority_dom":{"Urgent":"G\u1ea5p","High":"Quan tr\u1ecdng","Medium":"Trung b\u00ecnh","Low":"Th\u1ea5p"},"bug_resolution_default_key":"","bug_resolution_dom":{"":"","Accepted":"\u0110\u1ed3ng \u00fd","Duplicate":"Sao hai b\u1ea3n","Fixed":"S\u1eeda l\u1ed7i","Out of Date":"H\u1ebft h\u1ea1n","Invalid":"Kh\u00f4ng h\u1ee3p l\u1ec5","Later":"Sau"},"bug_status_default_key":"New","bug_status_dom":{"New":"M\u1edbi","Assigned":"\u0110\u1ed3ng \u00fd","Closed":"K\u1ebft th\u00fac","Pending":"T\u1ed3n \u0111\u1ed9ng","Rejected":"T\u1eeb ch\u1ed1i"},"bug_type_default_key":"Defect","bug_type_dom":{"Defect":"L\u1ed7i","Feature":"\u0110\u1eb7c tr\u01b0ng"},"case_type_dom":{"":"","Isolate":"\u0110\u1ed9c l\u1eadp","Transplant":"Gh\u00e9p","Other":"Kh\u00e1c"},"source_default_key":"","source_dom":{"":"","Internal":"N\u1ed9i b\u1ed9","Forum":"Di\u1ec5n \u0111\u00e0n","Web":"Web","InboundEmail":"Email"},"product_category_default_key":"","product_category_dom":{"":"","Accounts":"Kh\u00e1ch h\u00e0ng","Activities":"Ho\u1ea1t \u0111\u1ed9ng","Bug Tracker":"Theo d\u00f5i l\u1ed7i","Calendar":"L\u1ecbch","Calls":"G\u1ecdi \u0111i\u1ec7n","Campaigns":"Chi\u1ebfn d\u1ecbch","Cases":"V\u1ee5 vi\u1ec7c","Contacts":"Ng\u01b0\u1eddi li\u00ean h\u1ec7","Currencies":"Ti\u1ec1n t\u1ec7","Dashboard":"B\u1ea3ng th\u00f4ng tin","Documents":"T\u00e0i li\u1ec7u","Emails":"Emails","Feeds":"Feeds","Forecasts":"D\u1ef1 b\u00e1o","Help":"Gi\u00fap \u0111\u1ee1","Home":"Trang ch\u1ee7","Leads":"\u0110\u1ea7u m\u1ed1i","Meetings":"H\u1ed9i h\u1ecdp","Notes":"Ghi ch\u00fa","Opportunities":"\u0110\u01a1n h\u00e0ng","Outlook Plugin":"K\u1ebft n\u1ed1i v\u1edbi Outlook","Product Catalog":"Lo\u1ea1i s\u1ea3n ph\u1ea9m","Products":"S\u1ea3n ph\u1ea9m","Projects":"D\u1ef1 \u00e1n","Quotes":"B\u00e1o gi\u00e1","Releases":"Xu\u1ea5t b\u1ea3n","RSS":"RSS","Studio":"Ph\u00f2ng thu","Upgrade":"N\u00e2ng c\u1ea5p","Users":"Ng\u01b0\u1eddi s\u1eed d\u1ee5ng"},"campaign_status_dom":{"0":"","Planning":"\u0110ang k\u1ebf ho\u1ea1ch","Active":"Ho\u1ea1t \u0111\u1ed9ng","Inactive":"Kh\u00f4ng ho\u1ea1t \u0111\u1ed9ng","Complete":"Ho\u00e0n th\u00e0nh","In Queue":"H\u00e0ng \u0111\u1ee3i","Sending":"\u0110ang g\u1eedi"},"campaign_type_dom":{"":"","workshop":"H\u1ed9i th\u1ea3o","conference":"H\u1ed9i ngh\u1ecb","news":"\u0110\u0103ng b\u00e1o","radio":"\u0110\u00e0i","mail":"G\u1edfi th\u01b0 tay","call":"G\u1ecdi \u0111i\u1ec7n","email":"Th\u01b0 \u0111i\u1ec7n t\u1eed","eventinvest":"T\u00e0i tr\u1ee3 s\u1ef1 ki\u1ec7n"},"newsletter_frequency_dom":{"0":"","Weekly":"H\u00e0ng tu\u1ea7n","Monthly":"H\u00e0ng th\u00e1ng","Quarterly":"H\u00e0ng qu\u00ed","Annually":"N\u1eeda n\u0103m"},"notifymail_sendtype":{"sendmail":"G\u1eedi email","SMTP":"SMTP"},"dom_timezones":{"-12":"(GMT - 12) International Date Line West","-11":"(GMT - 11) Midway Island, Samoa","-10":"(GMT - 10) Hawaii","-9":"(GMT - 9) Alaska","-8":"(GMT - 8) San Francisco","-7":"(GMT - 7) Phoenix","-6":"(GMT - 6) Saskatchewan","-5":"(GMT - 5) New York","-4":"(GMT - 4) Santiago","-3":"(GMT - 3) Buenos Aires","-2":"(GMT - 2) Mid-Atlantic","-1":"(GMT - 1) Azores","0":"(GMT)","1":"(GMT + 1) Madrid","2":"(GMT + 2) Athens","3":"(GMT + 3) Moscow","4":"(GMT + 4) Kabul","5":"(GMT + 5) Ekaterinburg","6":"(GMT + 6) Astana","7":"(GMT + 7) Bangkok","8":"(GMT + 8) Perth","9":"(GMT + 9) Seol","10":"(GMT + 10) Brisbane","11":"(GMT + 11) Solomone Is.","12":"(GMT + 12) Auckland"},"dom_cal_month_long":["","Th\u00e1ng M\u1ed9t","Th\u00e1ng Hai","Th\u00e1ng Ba","Th\u00e1ng T\u01b0","Th\u00e1ng N\u0103m","Th\u00e1ng S\u00e1u","Th\u00e1ng B\u1ea3y","Th\u00e1ng T\u00e1m","Th\u00e1ng Ch\u00edn","Th\u00e1ng M\u01b0\u1eddi","Th\u00e1ng M\u01b0\u1eddi M\u1ed9t","Th\u00e1ng M\u01b0\u1eddi Hai"],"dom_cal_month_short":["","Th\u00e1ng 1","Th\u00e1ng 2","Th\u00e1ng 3","Th\u00e1ng 4","Th\u00e1ng 5","Th\u00e1ng 6","Th\u00e1ng 7","Th\u00e1ng 8","Th\u00e1ng 9","Th\u00e1ng 10","Th\u00e1ng 11","Th\u00e1ng 12"],"dom_cal_day_long":["","Ch\u1ee7 Nh\u1eadt","Th\u1ee9 Hai","Th\u1ee9 Ba","Th\u1ee9 T\u01b0","Th\u1ee9 N\u0103m","Th\u1ee9 S\u00e1u","Th\u1ee9 B\u1ea3y"],"dom_cal_day_short":["","CN","T.Hai","T.Ba","T.T\u01b0","T.N\u0103m","T.S\u00e1u","T.B\u1ea3y"],"dom_meridiem_lowercase":{"am":"s\u00e1ng","pm":"chi\u1ec1u"},"dom_meridiem_uppercase":{"AM":"S\u00e1ng","PM":"Chi\u1ec1u"},"dom_report_types":{"tabular":"d\u00f2ng v\u00e0 h\u00e0ng","summary":"t\u1ed5ng c\u1ed9ng","detailed_summary":"t\u1ed5ng c\u1ed9ng v\u00e0 chi ti\u1ebft","Matrix":"Ma tr\u1eadn"},"dom_email_types":{"out":"G\u1eedi","archived":"Sao l\u01b0u","draft":"B\u1ea3n th\u1ea3o","inbound":"B\u00ean trong","campaign":"Chi\u1ebfn d\u1ecbch"},"dom_email_status":{"archived":"Sao l\u01b0u","closed":"\u0110\u00f3ng","draft":"B\u1ea3n th\u1ea3o","read":"\u0110\u1ecdc","replied":"Tr\u1ea3 l\u1eddi","sent":"\u0110\u00e3 g\u1eedi","send_error":"G\u1eedi b\u1ecb l\u1ed7i","unread":"Ch\u01b0a \u0111\u1ecdc"},"dom_email_archived_status":{"archived":"Sao l\u01b0u"},"dom_email_server_type":{"0":"","imap":"IMAP","pop3":"POP3"},"dom_mailbox_type":{"pick":"--Kh\u00f4ng--","createcase":"T\u1ea1o v\u1ee5 vi\u1ec7c","bounce":"Th\u00f4ng b\u00e1o v\u1ee5 vi\u1ec7c"},"dom_email_distribution":{"0":"","direct":"G\u00e1n tr\u1ef1c ti\u1ebfp","roundRobin":"Round-Robin","leastBusy":"Least-Busy"},"dom_email_distribution_for_auto_create":{"roundRobin":"Round-Robin","leastBusy":"Least-Busy"},"dom_email_errors":{"1":"Ch\u1ec9 ch\u1ecdn m\u1ed9t ng\u01b0\u1eddi d\u00f9ng khi g\u00e1n tr\u1ef1c ti\u1ebfp c\u00e1c m\u1ee5c","2":"B\u1ea1n ph\u1ea3i g\u00e1n m\u1ee5c Ch\u1ec9 nh\u1eefng m\u1ee5c ki\u1ec3m tra khi g\u00e1n nh\u1eefng m\u1ee5c"},"dom_email_bool":{"bool_true":"\u0110\u00fang","bool_false":"Sai"},"dom_int_bool":{"1":"C\u00f3","0":"Kh\u00f4ng"},"dom_switch_bool":{"on":"B\u1eadt","off":"T\u1eaft","":"T\u1eaft"},"dom_email_link_type":{"":"","incomCRM":"Tr\u00ecnh g\u1edfi mail c\u1ee7a incomCRM","mailto":"Tr\u00ecnh g\u1edfi mail kh\u00e1c"},"dom_email_editor_option":{"":"","html":"\u0110\u1ecbnh d\u1ea1ng HTML","plain":"\u0110\u1ecbnh d\u1ea1ng v\u0103n b\u1ea3n thu\u1ea7n"},"schedulers_times_dom":{"not run":"Kh\u00f4ng th\u1ef1c thi","ready":"S\u1eb5n s\u00e0ng","in progress":"\u0110ang th\u1ef1c thi","failed":"Th\u1ea5t b\u1ea1i","completed":"Ho\u00e0n t\u1ea5t","no curl":"Kh\u00f4ng ch\u1ea1y: cURL kh\u00f4ng c\u00f3 s\u1eb5n"},"scheduler_status_dom":{"Active":"Ho\u1ea1t \u0111\u1ed9ng","Inactive":"Kh\u00f4ng ho\u1ea1t \u0111\u1ed9ng"},"scheduler_period_dom":{"min":"Ph\u00fat","hour":"Gi\u1edd"},"forecast_schedule_status_dom":{"Active":"Ho\u1ea1t \u0111\u1ed9ng","Inactive":"Kh\u00f4ng ho\u1ea1t \u0111\u1ed9ng"},"forecast_type_dom":{"Direct":"Tr\u1ef1c ti\u1ebfp","Rollup":"Rollup"},"document_category_dom":{"0":"","Marketing":"Ti\u1ebfp th\u1ecb","Knowledege Base":"Ki\u1ebfn th\u1ee9c c\u1edf b\u1ea3n","Sales":"B\u00e1n h\u00e0ng"},"document_subcategory_dom":{"0":"","Marketing Collateral":"Ti\u1ebfp th\u1ecb Collateral","Product Brochures":"Gi\u1edbi thi\u1ec7u s\u1ea3n ph\u1ea9m","FAQ":"FAQ"},"document_status_dom":{"Active":"Ho\u1ea1t \u0111\u1ed9ng","Draft":"B\u1ea3n th\u1ea3o","FAQ":"H\u1ecfi \u0111\u00e1p","Expired":"H\u1ebft h\u1ea1n","Under Review":"\u0110ang xem x\u00e9t","Pending":"T\u1ed3n \u0111\u1ed9ng"},"document_template_type_dom":{"":"","Contracts":"H\u1ee3p \u0111\u1ed3ng","Proposals":"\u0110\u1ec1 xu\u1ea5t","Quotes":"B\u00e1o gi\u00e1","Reports":"B\u00e1o c\u00e1o","Consult Experience":"Kinh nghi\u1ec7m tham kh\u1ea3o","User Guide":"T\u00e0i li\u1ec7u HDSD","BA":"T\u00e0i li\u1ec7u R&D_BA S\u1ea3n ph\u1ea9m","Other":"Kh\u00e1c"},"dom_meeting_accept_options":{"accept":"\u0110\u1ed3ng \u00fd","decline":"T\u1eeb ch\u1ed1i","tentative":"Ch\u01b0a quy\u1ebft"},"dom_meeting_accept_status":{"accept":"\u0110\u1ed3ng \u00fd","decline":"T\u1eeb ch\u1ed1i","tentative":"Ch\u01b0a quy\u1ebft","none":"Kh\u00f4ng"},"duration_intervals":{"0":"00","15":"15","30":"30","45":"45"},"prospect_list_type_dom":{"default":"M\u1eb7c \u0111\u1ecbnh","seed":"H\u1ea1t gi\u1ed1ng","exempt_domain":"Danh s\u00e1ch ch\u1eb7n b\u1edfi domain","exempt_address":"Danh s\u00e1ch ch\u1eb7n b\u1edfi \u0111\u1ecba ch\u1ec9 email","exempt":"Danh s\u00e1ch ch\u1eb7n b\u1edfi ID","test":"Th\u1eed"},"email_marketing_status_dom":{"0":"","active":"Ho\u1ea1t \u0111\u1ed9ng","inactive":"Kh\u00f4ng ho\u1ea1t \u0111\u1ed9ng"},"campainglog_activity_type_dom":{"0":"","targeted":"Tin nh\u1eafn g\u1edfi","send error":"Tin nh\u1eafn b\u1ecb tr\u1ea3 v\u1ec1\/ kh\u00e1c","invalid email":"Tin nh\u1eafn b\u1ecb tr\u1ea3 v\u1ec1\/ Sai email","link":"Click-thru Link","viewed":"Tin nh\u1eafn \u0111\u00e3 xem","removed":"Opted Out","lead":"Kh\u00e1ch h\u00e0ng ti\u1ec1m n\u0103ng \u0111\u00e3 t\u1ea1o","contact":"Li\u00ean h\u1ec7 \u0111\u00e3 t\u1ea1o","blocked":"H\u1ec7 th\u1ed1ng t\u1eeb g\u1eedi email \u0111i"},"campainglog_target_type_dom":{"Contacts":"Ng\u01b0\u1eddi li\u00ean h\u1ec7","Users":"Ng\u01b0\u1eddi d\u00f9ng","Prospects":"M\u1ee5c ti\u00eau","Leads":"\u0110\u1ea7u m\u1ed1i"},"merge_operators_dom":{"like":"C\u00f3 ch\u1ee9a","exact":"Ch\u00ednh x\u00e1c","start":"B\u1eaft \u0111\u1ea7u v\u1edbi"},"custom_fields_importable_dom":{"true":"\u0110\u00fang ","false":"Sai","required":"Y\u00eau c\u1ea7u"},"custom_fields_merge_dup_dom":["B\u1ea5t hi\u1ec7u l\u1ef1c","C\u00f3 hi\u1ec7n l\u1ef1c"],"navigation_paradigms":{"m":"Ph\u00e2n h\u1ec7","gm":"Nh\u00f3m ph\u00e2n h\u1ec7"},"projects_priority_options":{"high":"Cao","medium":"Trung b\u00ecnh","low":"Th\u1ea5p"},"projects_status_options":{"notstarted":"Ch\u01b0a b\u1eaft \u0111\u1ea7u","inprogress":"Trong ti\u1ebfn tr\u00ecnh","completed":"Ho\u00e0n t\u1ea5t"},"chart_strings":{"expandlegend":"M\u1edf r\u1ed9ng","collapselegend":"Thu g\u1ecdn","clickfordrilldown":"Click for Drilldown","drilldownoptions":"Drilldown Options","detailview":"More Details...","piechart":"Pie Chart","groupchart":"Group Chart","stackedchart":"Stacked Chart","barchart":"Bar Chart","horizontalbarchart":"Horizontal Bar Chart","linechart":"Line Chart","noData":"Kh\u00f4ng c\u00f3 d\u1eef li\u1ec7u","print":"Print","pieWedgeName":"sections"},"release_status_dom":{"Active":"Ho\u1ea1t \u0111\u1ed9ng","Inactive":"Kh\u00f4ng ho\u1ea1t \u0111\u1ed9ng"},"library_type":{"Books":"S\u00e1ch","Music":"Nh\u1ea1c","DVD":"DVD","Magazines":"T\u1ea1p ch\u00ed"},"kbdocument_status_dom":{"Draft":"B\u1ea3n th\u1ea3o","Expired":"H\u1ebft h\u1ea1n","In Review":"\u0110ang duy\u1ec7t","Published":"C\u00f4ng b\u1ed1"},"kbadmin_actions_dom":{"0":"","Create New Tag":"T\u1ea1o tag m\u1edbi","Delete Tag":"X\u00f3a tag","Rename Tag":"\u0110\u1ed5i t\u00ean tag","Move Selected Articles":"Di chuy\u1ec3n ch\u1ecdn","Apply Tags On Articles":"\u00c1p d\u1ee5ng ch\u1ecdn","Delete Selected Articles":"X\u00f3a ch\u1ecdn"},"kbdocument_attachment_option_dom":{"0":"","some":"C\u00f3 \u0111\u00ednh k\u00e8m","none":"C\u00f3 m\u1ed9t","mime":"Lo\u1ea1i ","name":"T\u00ean"},"kbdocument_viewing_frequency_dom":{"0":"","Top_5":"Top 5","Top_10":"Top 10","Top_20":"Top 20","Bot_5":"Bottom 5","Bot_10":"Bottom 10","Bot_20":"Bottom 20"},"kbdocument_canned_search":{"all":"T\u1ea5t c\u1ea3","added":"\u0110\u01b0\u1ee3c th\u00eam v\u00e0o 30 ng\u00e0y g\u1ea7n \u0111\u00e2y","pending":"T\u1ed3n \u0111\u1ecdng ","updated":"C\u1eadp nh\u1eadt 30 g\u1ea7n \u0111\u00e2y","faqs":"FAQs"},"kbdocument_date_filter_options":{"0":"","on":"Tr\u00ean","before":"Tr\u01b0\u1edbc","after":"Sau","between_dates":"Gi\u1eefa","last_7_days":"7 ng\u00e0y qua","next_7_days":"7 ng\u00e0y ti\u1ebfp","last_month":"Th\u00e1ng qua","this_month":"Th\u00e1ng n\u00e0y","next_month":"Th\u00e1ng t\u1edbi","last_30_days":"30 ng\u00e0y g\u1ea7n \u0111\u00e2y","next_30_days":"30 ng\u00e0y t\u1edbi","last_year":"N\u0103m v\u1eeba qua","this_year":"N\u0103m n\u00e0y","next_year":"N\u0103m t\u1edbi","isnull":"R\u1ed7ng"},"countries_dom":{"0":"","ABU DHABI":"ABU DHABI","ADEN":"ADEN","AFGHANISTAN":"AFGHANISTAN","ALBANIA":"ALBANIA","ALGERIA":"ALGERIA","AMERICAN SAMOA":"AMERICAN SAMOA","ANDORRA":"ANDORRA","ANGOLA":"ANGOLA","ANTARCTICA":"ANTARCTICA","ANTIGUA":"ANTIGUA","ARGENTINA":"ARGENTINA","ARMENIA":"ARMENIA","ARUBA":"ARUBA","AUSTRALIA":"AUSTRALIA","AUSTRIA":"AUSTRIA","AZERBAIJAN":"AZERBAIJAN","BAHAMAS":"BAHAMAS","BAHRAIN":"BAHRAIN","BANGLADESH":"BANGLADESH","BARBADOS":"BARBADOS","BELARUS":"BELARUS","BELGIUM":"BELGIUM","BELIZE":"BELIZE","BENIN":"BENIN","BERMUDA":"BERMUDA","BHUTAN":"BHUTAN","BOLIVIA":"BOLIVIA","BOSNIA":"BOSNIA","BOTSWANA":"BOTSWANA","BOUVET ISLAND":"BOUVET ISLAND","BRAZIL":"BRAZIL","BRITISH ANTARCTICA TERRITORY":"BRITISH ANTARCTICA TERRITORY","BRITISH INDIAN OCEAN TERRITORY":"BRITISH INDIAN OCEAN TERRITORY","BRITISH VIRGIN ISLANDS":"BRITISH VIRGIN ISLANDS","BRITISH WEST INDIES":"BRITISH WEST INDIES","BRUNEI":"BRUNEI","BULGARIA":"BULGARIA","BURKINA FASO":"BURKINA FASO","BURUNDI":"BURUNDI","CAMBODIA":"CAMBODIA","CAMEROON":"CAMEROON","CANADA":"CANADA","CANAL ZONE":"CANAL ZONE","CANARY ISLAND":"CANARY ISLAND","CAPE VERDI ISLANDS":"CAPE VERDI ISLANDS","CAYMAN ISLANDS":"CAYMAN ISLANDS","CEVLON":"CEVLON","CHAD":"CHAD","CHANNEL ISLAND UK":"CHANNEL ISLAND UK","CHILE":"CHILE","CHINA":"CHINA","CHRISTMAS ISLAND":"CHRISTMAS ISLAND","COCOS (KEELING) ISLAND":"COCOS (KEELING) ISLAND","COLOMBIA":"COLOMBIA","COMORO ISLANDS":"COMORO ISLANDS","CONGO":"CONGO","CONGO KINSHASA":"CONGO KINSHASA","COOK ISLANDS":"COOK ISLANDS","COSTA RICA":"COSTA RICA","CROATIA":"CROATIA","CUBA":"CUBA","CURACAO":"CURACAO","CYPRUS":"CYPRUS","CZECH REPUBLIC":"CZECH REPUBLIC","DAHOMEY":"DAHOMEY","DENMARK":"DENMARK","DJIBOUTI":"DJIBOUTI","DOMINICA":"DOMINICA","DOMINICAN REPUBLIC":"DOMINICAN REPUBLIC","DUBAI":"DUBAI","ECUADOR":"ECUADOR","EGYPT":"EGYPT","EL SALVADOR":"EL SALVADOR","EQUATORIAL GUINEA":"EQUATORIAL GUINEA","ESTONIA":"ESTONIA","ETHIOPIA":"ETHIOPIA","FAEROE ISLANDS":"FAEROE ISLANDS","FALKLAND ISLANDS":"FALKLAND ISLANDS","FIJI":"FIJI","FINLAND":"FINLAND","FRANCE":"FRANCE","FRENCH GUIANA":"FRENCH GUIANA","FRENCH POLYNESIA":"FRENCH POLYNESIA","GABON":"GABON","GAMBIA":"GAMBIA","GEORGIA":"GEORGIA","GERMANY":"GERMANY","GHANA":"GHANA","GIBRALTAR":"GIBRALTAR","GREECE":"GREECE","GREENLAND":"GREENLAND","GUADELOUPE":"GUADELOUPE","GUAM":"GUAM","GUATEMALA":"GUATEMALA","GUINEA":"GUINEA","GUYANA":"GUYANA","HAITI":"HAITI","HONDURAS":"HONDURAS","HONG KONG":"HONG KONG","HUNGARY":"HUNGARY","ICELAND":"ICELAND","IFNI":"IFNI","INDIA":"INDIA","INDONESIA":"INDONESIA","IRAN":"IRAN","IRAQ":"IRAQ","IRELAND":"IRELAND","ISRAEL":"ISRAEL","ITALY":"ITALY","IVORY COAST":"IVORY COAST","JAMAICA":"JAMAICA","JAPAN":"JAPAN","JORDAN":"JORDAN","KAZAKHSTAN":"KAZAKHSTAN","KENYA":"KENYA","KOREA":"KOREA","KOREA, SOUTH":"KOREA, SOUTH","KUWAIT":"KUWAIT","KYRGYZSTAN":"KYRGYZSTAN","LAOS":"LAOS","LATVIA":"LATVIA","LEBANON":"LEBANON","LEEWARD ISLANDS":"LEEWARD ISLANDS","LESOTHO":"LESOTHO","LIBYA":"LIBYA","LIECHTENSTEIN":"LIECHTENSTEIN","LITHUANIA":"LITHUANIA","LUXEMBOURG":"LUXEMBOURG","MACAO":"MACAO","MACEDONIA":"MACEDONIA","MADAGASCAR":"MADAGASCAR","MALAWI":"MALAWI","MALAYSIA":"MALAYSIA","MALDIVES":"MALDIVES","MALI":"MALI","MALTA":"MALTA","MARTINIQUE":"MARTINIQUE","MAURITANIA":"MAURITANIA","MAURITIUS":"MAURITIUS","MELANESIA":"MELANESIA","MEXICO":"MEXICO","MOLDOVIA":"MOLDOVIA","MONACO":"MONACO","MONGOLIA":"MONGOLIA","MOROCCO":"MOROCCO","MOZAMBIQUE":"MOZAMBIQUE","MYANAMAR":"MYANAMAR","NAMIBIA":"NAMIBIA","NEPAL":"NEPAL","NETHERLANDS":"NETHERLANDS","NETHERLANDS ANTILLES":"NETHERLANDS ANTILLES","NETHERLANDS ANTILLES NEUTRAL ZONE":"NETHERLANDS ANTILLES NEUTRAL ZONE","NEW CALADONIA":"NEW CALADONIA","NEW HEBRIDES":"NEW HEBRIDES","NEW ZEALAND":"NEW ZEALAND","NICARAGUA":"NICARAGUA","NIGER":"NIGER","NIGERIA":"NIGERIA","NORFOLK ISLAND":"NORFOLK ISLAND","NORWAY":"NORWAY","OMAN":"OMAN","OTHER":"OTHER","PACIFIC ISLAND":"PACIFIC ISLAND","PAKISTAN":"PAKISTAN","PANAMA":"PANAMA","PAPUA NEW GUINEA":"PAPUA NEW GUINEA","PARAGUAY":"PARAGUAY","PERU":"PERU","PHILIPPINES":"PHILIPPINES","POLAND":"POLAND","PORTUGAL":"PORTUGAL","PORTUGUESE TIMOR":"PORTUGUESE TIMOR","PUERTO RICO":"PUERTO RICO","QATAR":"QATAR","REPUBLIC OF BELARUS":"REPUBLIC OF BELARUS","REPUBLIC OF SOUTH AFRICA":"REPUBLIC OF SOUTH AFRICA","REUNION":"REUNION","ROMANIA":"ROMANIA","RUSSIA":"RUSSIA","RWANDA":"RWANDA","RYUKYU ISLANDS":"RYUKYU ISLANDS","SABAH":"SABAH","SAN MARINO":"SAN MARINO","SAUDI ARABIA":"SAUDI ARABIA","SENEGAL":"SENEGAL","SERBIA":"SERBIA","SEYCHELLES":"SEYCHELLES","SIERRA LEONE":"SIERRA LEONE","SINGAPORE":"SINGAPORE","SLOVAKIA":"SLOVAKIA","SLOVENIA":"SLOVENIA","SOMALILIAND":"SOMALILIAND","SOUTH AFRICA":"SOUTH AFRICA","SOUTH YEMEN":"SOUTH YEMEN","SPAIN":"SPAIN","SPANISH SAHARA":"SPANISH SAHARA","SRI LANKA":"SRI LANKA","ST. KITTS AND NEVIS":"ST. KITTS AND NEVIS","ST. LUCIA":"ST. LUCIA","SUDAN":"SUDAN","SURINAM":"SURINAM","SW AFRICA":"SW AFRICA","SWAZILAND":"SWAZILAND","SWEDEN":"SWEDEN","SWITZERLAND":"SWITZERLAND","SYRIA":"SYRIA","TAIWAN":"TAIWAN","TAJIKISTAN":"TAJIKISTAN","TANZANIA":"TANZANIA","THAILAND":"THAILAND","TONGA":"TONGA","TRINIDAD":"TRINIDAD","TUNISIA":"TUNISIA","TURKEY":"TURKEY","UGANDA":"UGANDA","UKRAINE":"UKRAINE","UNITED ARAB EMIRATES":"UNITED ARAB EMIRATES","UNITED KINGDOM":"UNITED KINGDOM","UPPER VOLTA":"UPPER VOLTA","URUGUAY":"URUGUAY","US PACIFIC ISLAND":"US PACIFIC ISLAND","US VIRGIN ISLANDS":"US VIRGIN ISLANDS","USA":"USA","UZBEKISTAN":"UZBEKISTAN","VANUATU":"VANUATU","VATICAN CITY":"VATICAN CITY","VENEZUELA":"VENEZUELA","VIETNAM":"VIETNAM","WAKE ISLAND":"WAKE ISLAND","WEST INDIES":"WEST INDIES","WESTERN SAHARA":"WESTERN SAHARA","YEMEN":"YEMEN","ZAIRE":"ZAIRE","ZAMBIA":"ZAMBIA","ZIMBABWE":"ZIMBABWE"},"charset_dom":{"BIG-5":"BIG-5 (Taiwan and Hong Kong)","CP1251":"CP1251 (MS Cyrillic)","CP1252":"CP1252 (MS Western European & US)","EUC-CN":"EUC-CN (Simplified Chinese GB2312)","EUC-JP":"EUC-JP (Unix Japanese)","EUC-KR":"EUC-KR (Korean)","EUC-TW":"EUC-TW (Taiwanese)","ISO-2022-JP":"ISO-2022-JP (Japanese)","ISO-2022-KR":"ISO-2022-KR (Korean)","ISO-8859-1":"ISO-8859-1 (Western European and US)","ISO-8859-2":"ISO-8859-2 (Central and Eastern European)","ISO-8859-3":"ISO-8859-3 (Latin 3)","ISO-8859-4":"ISO-8859-4 (Latin 4)","ISO-8859-5":"ISO-8859-5 (Cyrillic)","ISO-8859-6":"ISO-8859-6 (Arabic)","ISO-8859-7":"ISO-8859-7 (Greek)","ISO-8859-8":"ISO-8859-8 (Hebrew)","ISO-8859-9":"ISO-8859-9 (Latin 5)","ISO-8859-10":"ISO-8859-10 (Latin 6)","ISO-8859-13":"ISO-8859-13 (Latin 7)","ISO-8859-14":"ISO-8859-14 (Latin 8)","ISO-8859-15":"ISO-8859-15 (Latin 9)","KOI8-R":"KOI8-R (Cyrillic Russian)","KOI8-U":"KOI8-U (Cyrillic Ukranian)","SJIS":"SJIS (MS Japanese)","UTF-8":"UTF-8"},"timezone_dom":{"Africa\/Algiers":"Africa\/Algiers","Africa\/Luanda":"Africa\/Luanda","Africa\/Porto-Novo":"Africa\/Porto-Novo","Africa\/Gaborone":"Africa\/Gaborone","Africa\/Ouagadougou":"Africa\/Ouagadougou","Africa\/Bujumbura":"Africa\/Bujumbura","Africa\/Douala":"Africa\/Douala","Atlantic\/Cape_Verde":"Atlantic\/Cape_Verde","Africa\/Bangui":"Africa\/Bangui","Africa\/Ndjamena":"Africa\/Ndjamena","Indian\/Comoro":"Indian\/Comoro","Africa\/Kinshasa":"Africa\/Kinshasa","Africa\/Lubumbashi":"Africa\/Lubumbashi","Africa\/Brazzaville":"Africa\/Brazzaville","Africa\/Abidjan":"Africa\/Abidjan","Africa\/Djibouti":"Africa\/Djibouti","Africa\/Cairo":"Africa\/Cairo","Africa\/Malabo":"Africa\/Malabo","Africa\/Asmera":"Africa\/Asmera","Africa\/Addis_Ababa":"Africa\/Addis_Ababa","Africa\/Libreville":"Africa\/Libreville","Africa\/Banjul":"Africa\/Banjul","Africa\/Accra":"Africa\/Accra","Africa\/Conakry":"Africa\/Conakry","Africa\/Bissau":"Africa\/Bissau","Africa\/Nairobi":"Africa\/Nairobi","Africa\/Maseru":"Africa\/Maseru","Africa\/Monrovia":"Africa\/Monrovia","Africa\/Tripoli":"Africa\/Tripoli","Indian\/Antananarivo":"Indian\/Antananarivo","Africa\/Blantyre":"Africa\/Blantyre","Africa\/Bamako":"Africa\/Bamako","Africa\/Nouakchott":"Africa\/Nouakchott","Indian\/Mauritius":"Indian\/Mauritius","Indian\/Mayotte":"Indian\/Mayotte","Africa\/Casablanca":"Africa\/Casablanca","Africa\/El_Aaiun":"Africa\/El_Aaiun","Africa\/Maputo":"Africa\/Maputo","Africa\/Windhoek":"Africa\/Windhoek","Africa\/Niamey":"Africa\/Niamey","Africa\/Lagos":"Africa\/Lagos","Indian\/Reunion":"Indian\/Reunion","Africa\/Kigali":"Africa\/Kigali","Atlantic\/St_Helena":"Atlantic\/St_Helena","Africa\/Sao_Tome":"Africa\/Sao_Tome","Africa\/Dakar":"Africa\/Dakar","Indian\/Mahe":"Indian\/Mahe","Africa\/Freetown":"Africa\/Freetown","Africa\/Mogadishu":"Africa\/Mogadishu","Africa\/Johannesburg":"Africa\/Johannesburg","Africa\/Khartoum":"Africa\/Khartoum","Africa\/Mbabane":"Africa\/Mbabane","Africa\/Dar_es_Salaam":"Africa\/Dar_es_Salaam","Africa\/Lome":"Africa\/Lome","Africa\/Tunis":"Africa\/Tunis","Africa\/Kampala":"Africa\/Kampala","Africa\/Lusaka":"Africa\/Lusaka","Africa\/Harare":"Africa\/Harare","Antarctica\/Casey":"Antarctica\/Casey","Antarctica\/Davis":"Antarctica\/Davis","Antarctica\/Mawson":"Antarctica\/Mawson","Indian\/Kerguelen":"Indian\/Kerguelen","Antarctica\/DumontDUrville":"Antarctica\/DumontDUrville","Antarctica\/Syowa":"Antarctica\/Syowa","Antarctica\/Vostok":"Antarctica\/Vostok","Antarctica\/Rothera":"Antarctica\/Rothera","Antarctica\/Palmer":"Antarctica\/Palmer","Antarctica\/McMurdo":"Antarctica\/McMurdo","Asia\/Kabul":"Asia\/Kabul","Asia\/Yerevan":"Asia\/Yerevan","Asia\/Baku":"Asia\/Baku","Asia\/Bahrain":"Asia\/Bahrain","Asia\/Dhaka":"Asia\/Dhaka","Asia\/Thimphu":"Asia\/Thimphu","Indian\/Chagos":"Indian\/Chagos","Asia\/Brunei":"Asia\/Brunei","Asia\/Rangoon":"Asia\/Rangoon","Asia\/Phnom_Penh":"Asia\/Phnom_Penh","Asia\/Harbin":"Asia\/Harbin","Asia\/Shanghai":"Asia\/Shanghai","Asia\/Chongqing":"Asia\/Chongqing","Asia\/Urumqi":"Asia\/Urumqi","Asia\/Kashgar":"Asia\/Kashgar","Asia\/Hong_Kong":"Asia\/Hong_Kong","Asia\/Taipei":"Asia\/Taipei","Asia\/Macau":"Asia\/Macau","Asia\/Nicosia":"Asia\/Nicosia","Asia\/Tbilisi":"Asia\/Tbilisi","Asia\/Dili":"Asia\/Dili","Asia\/Calcutta":"Asia\/Calcutta","Asia\/Jakarta":"Asia\/Jakarta","Asia\/Pontianak":"Asia\/Pontianak","Asia\/Makassar":"Asia\/Makassar","Asia\/Jayapura":"Asia\/Jayapura","Asia\/Tehran":"Asia\/Tehran","Asia\/Baghdad":"Asia\/Baghdad","Asia\/Jerusalem":"Asia\/Jerusalem","Asia\/Tokyo":"Asia\/Tokyo","Asia\/Amman":"Asia\/Amman","Asia\/Almaty":"Asia\/Almaty","Asia\/Qyzylorda":"Asia\/Qyzylorda","Asia\/Aqtobe":"Asia\/Aqtobe","Asia\/Aqtau":"Asia\/Aqtau","Asia\/Oral":"Asia\/Oral","Asia\/Bishkek":"Asia\/Bishkek","Asia\/Seoul":"Asia\/Seoul","Asia\/Pyongyang":"Asia\/Pyongyang","Asia\/Kuwait":"Asia\/Kuwait","Asia\/Vientiane":"Asia\/Vientiane","Asia\/Beirut":"Asia\/Beirut","Asia\/Kuala_Lumpur":"Asia\/Kuala_Lumpur","Asia\/Kuching":"Asia\/Kuching","Indian\/Maldives":"Indian\/Maldives","Asia\/Hovd":"Asia\/Hovd","Asia\/Ulaanbaatar":"Asia\/Ulaanbaatar","Asia\/Choibalsan":"Asia\/Choibalsan","Asia\/Katmandu":"Asia\/Katmandu","Asia\/Muscat":"Asia\/Muscat","Asia\/Karachi":"Asia\/Karachi","Asia\/Gaza":"Asia\/Gaza","Asia\/Manila":"Asia\/Manila","Asia\/Qatar":"Asia\/Qatar","Asia\/Riyadh":"Asia\/Riyadh","Asia\/Singapore":"Asia\/Singapore","Asia\/Colombo":"Asia\/Colombo","Asia\/Damascus":"Asia\/Damascus","Asia\/Dushanbe":"Asia\/Dushanbe","Asia\/Bangkok":"Asia\/Bangkok","Asia\/Ashgabat":"Asia\/Ashgabat","Asia\/Dubai":"Asia\/Dubai","Asia\/Samarkand":"Asia\/Samarkand","Asia\/Tashkent":"Asia\/Tashkent","Asia\/Saigon":"Asia\/Saigon","Asia\/Aden":"Asia\/Aden","Australia\/Darwin":"Australia\/Darwin","Australia\/Perth":"Australia\/Perth","Australia\/Brisbane":"Australia\/Brisbane","Australia\/Lindeman":"Australia\/Lindeman","Australia\/Adelaide":"Australia\/Adelaide","Australia\/Hobart":"Australia\/Hobart","Australia\/Currie":"Australia\/Currie","Australia\/Melbourne":"Australia\/Melbourne","Australia\/Sydney":"Australia\/Sydney","Australia\/Broken_Hill":"Australia\/Broken_Hill","Indian\/Christmas":"Indian\/Christmas","Pacific\/Rarotonga":"Pacific\/Rarotonga","Indian\/Cocos":"Indian\/Cocos","Pacific\/Fiji":"Pacific\/Fiji","Pacific\/Gambier":"Pacific\/Gambier","Pacific\/Marquesas":"Pacific\/Marquesas","Pacific\/Tahiti":"Pacific\/Tahiti","Pacific\/Guam":"Pacific\/Guam","Pacific\/Tarawa":"Pacific\/Tarawa","Pacific\/Enderbury":"Pacific\/Enderbury","Pacific\/Kiritimati":"Pacific\/Kiritimati","Pacific\/Saipan":"Pacific\/Saipan","Pacific\/Majuro":"Pacific\/Majuro","Pacific\/Kwajalein":"Pacific\/Kwajalein","Pacific\/Truk":"Pacific\/Truk","Pacific\/Ponape":"Pacific\/Ponape","Pacific\/Kosrae":"Pacific\/Kosrae","Pacific\/Nauru":"Pacific\/Nauru","Pacific\/Noumea":"Pacific\/Noumea","Pacific\/Auckland":"Pacific\/Auckland","Pacific\/Chatham":"Pacific\/Chatham","Pacific\/Niue":"Pacific\/Niue","Pacific\/Norfolk":"Pacific\/Norfolk","Pacific\/Palau":"Pacific\/Palau","Pacific\/Port_Moresby":"Pacific\/Port_Moresby","Pacific\/Pitcairn":"Pacific\/Pitcairn","Pacific\/Pago_Pago":"Pacific\/Pago_Pago","Pacific\/Apia":"Pacific\/Apia","Pacific\/Guadalcanal":"Pacific\/Guadalcanal","Pacific\/Fakaofo":"Pacific\/Fakaofo","Pacific\/Tongatapu":"Pacific\/Tongatapu","Pacific\/Funafuti":"Pacific\/Funafuti","Pacific\/Johnston":"Pacific\/Johnston","Pacific\/Midway":"Pacific\/Midway","Pacific\/Wake":"Pacific\/Wake","Pacific\/Efate":"Pacific\/Efate","Pacific\/Wallis":"Pacific\/Wallis","Europe\/London":"Europe\/London","Europe\/Dublin":"Europe\/Dublin","WET":"WET","CET":"CET","MET":"MET","EET":"EET","Europe\/Tirane":"Europe\/Tirane","Europe\/Andorra":"Europe\/Andorra","Europe\/Vienna":"Europe\/Vienna","Europe\/Minsk":"Europe\/Minsk","Europe\/Brussels":"Europe\/Brussels","Europe\/Sofia":"Europe\/Sofia","Europe\/Prague":"Europe\/Prague","Europe\/Copenhagen":"Europe\/Copenhagen","Atlantic\/Faeroe":"Atlantic\/Faeroe","America\/Danmarkshavn":"America\/Danmarkshavn","America\/Scoresbysund":"America\/Scoresbysund","America\/Godthab":"America\/Godthab","America\/Thule":"America\/Thule","Europe\/Tallinn":"Europe\/Tallinn","Europe\/Helsinki":"Europe\/Helsinki","Europe\/Paris":"Europe\/Paris","Europe\/Berlin":"Europe\/Berlin","Europe\/Gibraltar":"Europe\/Gibraltar","Europe\/Athens":"Europe\/Athens","Europe\/Budapest":"Europe\/Budapest","Atlantic\/Reykjavik":"Atlantic\/Reykjavik","Europe\/Rome":"Europe\/Rome","Europe\/Riga":"Europe\/Riga","Europe\/Vaduz":"Europe\/Vaduz","Europe\/Vilnius":"Europe\/Vilnius","Europe\/Luxembourg":"Europe\/Luxembourg","Europe\/Malta":"Europe\/Malta","Europe\/Chisinau":"Europe\/Chisinau","Europe\/Monaco":"Europe\/Monaco","Europe\/Amsterdam":"Europe\/Amsterdam","Europe\/Oslo":"Europe\/Oslo","Europe\/Warsaw":"Europe\/Warsaw","Europe\/Lisbon":"Europe\/Lisbon","Atlantic\/Azores":"Atlantic\/Azores","Atlantic\/Madeira":"Atlantic\/Madeira","Europe\/Bucharest":"Europe\/Bucharest","Europe\/Kaliningrad":"Europe\/Kaliningrad","Europe\/Moscow":"Europe\/Moscow","Europe\/Samara":"Europe\/Samara","Asia\/Yekaterinburg":"Asia\/Yekaterinburg","Asia\/Omsk":"Asia\/Omsk","Asia\/Novosibirsk":"Asia\/Novosibirsk","Asia\/Krasnoyarsk":"Asia\/Krasnoyarsk","Asia\/Irkutsk":"Asia\/Irkutsk","Asia\/Yakutsk":"Asia\/Yakutsk","Asia\/Vladivostok":"Asia\/Vladivostok","Asia\/Sakhalin":"Asia\/Sakhalin","Asia\/Magadan":"Asia\/Magadan","Asia\/Kamchatka":"Asia\/Kamchatka","Asia\/Anadyr":"Asia\/Anadyr","Europe\/Belgrade":"Europe\/Belgrade","Europe\/Madrid":"Europe\/Madrid","Africa\/Ceuta":"Africa\/Ceuta","Atlantic\/Canary":"Atlantic\/Canary","Europe\/Stockholm":"Europe\/Stockholm","Europe\/Zurich":"Europe\/Zurich","Europe\/Istanbul":"Europe\/Istanbul","Europe\/Kiev":"Europe\/Kiev","Europe\/Uzhgorod":"Europe\/Uzhgorod","Europe\/Zaporozhye":"Europe\/Zaporozhye","Europe\/Simferopol":"Europe\/Simferopol","America\/New_York":"America\/New_York","America\/Chicago":"America\/Chicago","America\/North_Dakota\/Center":"America\/North_Dakota\/Center","America\/Denver":"America\/Denver","America\/Los_Angeles":"America\/Los_Angeles","America\/Juneau":"America\/Juneau","America\/Yakutat":"America\/Yakutat","America\/Anchorage":"America\/Anchorage","America\/Nome":"America\/Nome","America\/Adak":"America\/Adak","Pacific\/Honolulu":"Pacific\/Honolulu","America\/Phoenix":"America\/Phoenix","America\/Boise":"America\/Boise","America\/Indiana\/Indianapolis":"America\/Indiana\/Indianapolis","America\/Indiana\/Marengo":"America\/Indiana\/Marengo","America\/Indiana\/Knox":"America\/Indiana\/Knox","America\/Indiana\/Vevay":"America\/Indiana\/Vevay","America\/Kentucky\/Louisville":"America\/Kentucky\/Louisville","America\/Kentucky\/Monticello":"America\/Kentucky\/Monticello","America\/Detroit":"America\/Detroit","America\/Menominee":"America\/Menominee","America\/St_Johns":"America\/St_Johns","America\/Goose_Bay":"America\/Goose_Bay","America\/Halifax":"America\/Halifax","America\/Glace_Bay":"America\/Glace_Bay","America\/Montreal":"America\/Montreal","America\/Toronto":"America\/Toronto","America\/Thunder_Bay":"America\/Thunder_Bay","America\/Nipigon":"America\/Nipigon","America\/Rainy_River":"America\/Rainy_River","America\/Winnipeg":"America\/Winnipeg","America\/Regina":"America\/Regina","America\/Swift_Current":"America\/Swift_Current","America\/Edmonton":"America\/Edmonton","America\/Vancouver":"America\/Vancouver","America\/Dawson_Creek":"America\/Dawson_Creek","America\/Pangnirtung":"America\/Pangnirtung","America\/Iqaluit":"America\/Iqaluit","America\/Coral_Harbour":"America\/Coral_Harbour","America\/Rankin_Inlet":"America\/Rankin_Inlet","America\/Cambridge_Bay":"America\/Cambridge_Bay","America\/Yellowknife":"America\/Yellowknife","America\/Inuvik":"America\/Inuvik","America\/Whitehorse":"America\/Whitehorse","America\/Dawson":"America\/Dawson","America\/Cancun":"America\/Cancun","America\/Merida":"America\/Merida","America\/Monterrey":"America\/Monterrey","America\/Mexico_City":"America\/Mexico_City","America\/Chihuahua":"America\/Chihuahua","America\/Hermosillo":"America\/Hermosillo","America\/Mazatlan":"America\/Mazatlan","America\/Tijuana":"America\/Tijuana","America\/Anguilla":"America\/Anguilla","America\/Antigua":"America\/Antigua","America\/Nassau":"America\/Nassau","America\/Barbados":"America\/Barbados","America\/Belize":"America\/Belize","Atlantic\/Bermuda":"Atlantic\/Bermuda","America\/Cayman":"America\/Cayman","America\/Costa_Rica":"America\/Costa_Rica","America\/Havana":"America\/Havana","America\/Dominica":"America\/Dominica","America\/Santo_Domingo":"America\/Santo_Domingo","America\/El_Salvador":"America\/El_Salvador","America\/Grenada":"America\/Grenada","America\/Guadeloupe":"America\/Guadeloupe","America\/Guatemala":"America\/Guatemala","America\/Port-au-Prince":"America\/Port-au-Prince","America\/Tegucigalpa":"America\/Tegucigalpa","America\/Jamaica":"America\/Jamaica","America\/Martinique":"America\/Martinique","America\/Montserrat":"America\/Montserrat","America\/Managua":"America\/Managua","America\/Panama":"America\/Panama","America\/Puerto_Rico":"America\/Puerto_Rico","America\/St_Kitts":"America\/St_Kitts","America\/St_Lucia":"America\/St_Lucia","America\/Miquelon":"America\/Miquelon","America\/St_Vincent":"America\/St_Vincent","America\/Grand_Turk":"America\/Grand_Turk","America\/Tortola":"America\/Tortola","America\/St_Thomas":"America\/St_Thomas","America\/Argentina\/Buenos_Aires":"America\/Argentina\/Buenos_Aires","America\/Argentina\/Cordoba":"America\/Argentina\/Cordoba","America\/Argentina\/Tucuman":"America\/Argentina\/Tucuman","America\/Argentina\/La_Rioja":"America\/Argentina\/La_Rioja","America\/Argentina\/San_Juan":"America\/Argentina\/San_Juan","America\/Argentina\/Jujuy":"America\/Argentina\/Jujuy","America\/Argentina\/Catamarca":"America\/Argentina\/Catamarca","America\/Argentina\/Mendoza":"America\/Argentina\/Mendoza","America\/Argentina\/Rio_Gallegos":"America\/Argentina\/Rio_Gallegos","America\/Argentina\/Ushuaia":"America\/Argentina\/Ushuaia","America\/Aruba":"America\/Aruba","America\/La_Paz":"America\/La_Paz","America\/Noronha":"America\/Noronha","America\/Belem":"America\/Belem","America\/Fortaleza":"America\/Fortaleza","America\/Recife":"America\/Recife","America\/Araguaina":"America\/Araguaina","America\/Maceio":"America\/Maceio","America\/Bahia":"America\/Bahia","America\/Sao_Paulo":"America\/Sao_Paulo","America\/Campo_Grande":"America\/Campo_Grande","America\/Cuiaba":"America\/Cuiaba","America\/Porto_Velho":"America\/Porto_Velho","America\/Boa_Vista":"America\/Boa_Vista","America\/Manaus":"America\/Manaus","America\/Eirunepe":"America\/Eirunepe","America\/Rio_Branco":"America\/Rio_Branco","America\/Santiago":"America\/Santiago","Pacific\/Easter":"Pacific\/Easter","America\/Bogota":"America\/Bogota","America\/Curacao":"America\/Curacao","America\/Guayaquil":"America\/Guayaquil","Pacific\/Galapagos":"Pacific\/Galapagos","Atlantic\/Stanley":"Atlantic\/Stanley","America\/Cayenne":"America\/Cayenne","America\/Guyana":"America\/Guyana","America\/Asuncion":"America\/Asuncion","America\/Lima":"America\/Lima","Atlantic\/South_Georgia":"Atlantic\/South_Georgia","America\/Paramaribo":"America\/Paramaribo","America\/Port_of_Spain":"America\/Port_of_Spain","America\/Montevideo":"America\/Montevideo","America\/Caracas":"America\/Caracas"},"email_settings_for_ssl":["","SSL","TLS"],"announcement_status_list":{"":"","Applicable":"\u00c1p d\u1ee5ng","Not Applicable":"Kh\u00f4ng \u00c1p d\u1ee5ng","Other":"Kh\u00e1c"},"trackreview_type_display":{"Tasks":"T\u00e1c v\u1ee5 & Giao d\u1ecbch","Accounts":"Kh\u00e1ch h\u00e0ng"},"satisfaction_level_dom":{"":"","No comments":"Kh\u00f4ng c\u00f3 \u00fd ki\u1ebfn","Acceptable":"T\u1ea1m ch\u1ea5p nh\u1eadn \u0111\u01b0\u1ee3c","Satisfied":"H\u00e0i l\u00f2ng","Very satisfied":"R\u1ea5t h\u00e0i l\u00f2ng","Dissatisfied":"Kh\u00f4ng h\u00e0i l\u00f2ng","Criticism":"C\u00f3 ph\u00ea b\u00ecnh","Unacceptable":"Kh\u00f4ng th\u1ec3 ch\u1ea5p nh\u1eadn"},"completed_level_dom":{"":"","Not completed":"Ch\u01b0a ho\u00e0n th\u00e0nh","Temporary":"T\u1ea1m \u0111\u01b0\u1ee3c","Fulfill":"Ho\u00e0n th\u00e0nh t\u1ed1t","Poor - not accepted":"K\u00e9m - kh\u00f4ng ch\u1ea5p nh\u1eadn"},"execution_time_dom":{"":"","Timely":"\u0110\u00fang h\u1ea1n","Demurrage + Non positive":"Tr\u1ec5 h\u1ea1n + Kh\u00f4ng t\u00edch c\u1ef1c gi\u1ea3i quy\u1ebft d\u1ee9t \u0111i\u1ec3m c\u00f4ng vi\u1ec7c","Demurrage Positive":"Tr\u1ec5 h\u1ea1n + C\u00f3 n\u1ed5 l\u1ef1c ho\u00e0n th\u00e0nh c\u00f4ng vi\u1ec7c","Completed earlier planned":"Ho\u00e0n th\u00e0nh tr\u01b0\u01a1c k\u1ebf ho\u1ea1ch"},"cashes_type_dom":{"":"","1":"Phi\u1ebfu Thu","2":"Phi\u1ebfu Chi"},"cashes_status_dom":{"":"","1":"L\u01b0u t\u1ea1m","4":"Ch\u1edd duy\u1ec7t","5":"Kh\u00f4ng duy\u1ec7t","6":"\u0110\u00e3 duy\u1ec7t","2":"\u0110\u00e3 th\u1ef1c hi\u1ec7n","3":"H\u1ee7y"},"cashes_status2_dom":{"":"","1":"L\u01b0u t\u1ea1m","2":"\u0110\u00e3 th\u1ef1c hi\u1ec7n","3":"\u0110\u00e3 h\u1ee7y"},"cashes_status3_dom":{"":"","1":"L\u01b0u t\u1ea1m","4":"Ch\u1edd duy\u1ec7t","3":"H\u1ee7y"},"cashes_record_status_dom":{"":"","1":"Ch\u01b0a ghi s\u1ed5","2":"\u0110\u00e3 ghi s\u1ed5"},"cashes_accounting_status_dom":{"":"","1":"Ch\u01b0a h\u1ea1ch to\u00e1n","2":"H\u1ea1ch to\u00e1n 1 ph\u1ea7n","3":"H\u1ea1ch to\u00e1n 100%"},"cashes_branches_dom":{"":"","1":"Chi nh\u00e1nh 1","2":"Chi nh\u00e1nh 2"},"cashes_payments_dom":{"":"","TM":"Ti\u1ec1n m\u1eb7t","CK":"Chuy\u1ec3n kho\u1ea3n","AD":"ATM\/Debit Card","VS":"Visa","MC":"MasterCard","AM":"Amex","VC":"Voucher","GN":"Ghi n\u1ee3","DC":"\u0110\u1eb7t c\u1ecdc","KH":"H\u00ecnh th\u1ee9c kh\u00e1c"},"cashes_accounting_type_dom":{"":"","TM":"111","CK":"112"},"cashes_payment_status_dom":{"":"","1":"Ch\u01b0a thanh to\u00e1n","2":"Thanh to\u00e1n 1 ph\u1ea7n","3":"Thanh to\u00e1n 100%"},"warehouse_outs_status_dom":{"":"","1":"L\u01b0u t\u1ea1m","4":"Ch\u1edd xu\u1ea5t","5":"\u0110\u00e3 xu\u1ea5t kho","6":"\u0110\u00e3 h\u1ee7y"},"warehouse_outs_type_dom":{"":"","9":"Xu\u1ea5t kho","1":"Xu\u1ea5t b\u00e1n","2":"Xu\u1ea5t cho thu\u00ea","3":"Xu\u1ea5t h\u00e0ng m\u1eabu\/K\u00fd g\u1edfi","4":"Xu\u1ea5t tr\u1ea3 b\u1ea3o h\u00e0nh","5":"Xu\u1ea5t tr\u1ea3 h\u00e0ng thu\u00ea","6":"Xu\u1ea5t chuy\u1ec3n kho n\u1ed9i b\u1ed9","7":"Xu\u1ea5t chuy\u1ec3n kho kh\u00e1ch h\u00e0ng","8":"Kh\u00e1c"},"warehouse_outs_code_dom":{"9":"","1":"XB","2":"CT","3":"KG","4":"BH","5":"TH","6":"CK","7":"KA","8":"KH"},"payment_status_dom":{"":"","1":"Ch\u01b0a thu","2":"\u0110\u00e3 thu 1 ph\u1ea7n","3":"\u0110\u00e3 thu 100%"},"map_points_objects_dom":{"Tasks":"T\u00e1c v\u1ee5 & Giao d\u1ecbch","Opportunities":"\u0110\u01a1n h\u00e0ng","Accounts":"Kh\u00e1ch h\u00e0ng","Contacts":"Ng\u01b0\u1eddi li\u00ean h\u1ec7","Documents":"T\u00e0i li\u1ec7u"},"map_points_action_dom":{"":"","Add":"Th\u00eam","Edit":"S\u1eeda","Delete":"X\u00f3a","View":"Xem"},"account_discounts_dom":["Kh\u00f4ng s\u1eed d\u1ee5ng","Theo %","Theo ti\u1ec1n m\u1eb7t"],"warehouse_types_in_out_dom":{"":"","1":"Phi\u1ebfu Nh\u1eadp","2":"Phi\u1ebfu Xu\u1ea5t"},"opportunity_processing_stage_dom":{"":"","1":"\u0110H Ch\u1edd giao","2":"\u0110H Giao 1 ph\u1ea7n","3":"\u0110H Giao xong"},"processing_stage_config_dom":{"default":"1","completed":"4","cancel":"5"},"cash_reasons_type_dom":{"":"","1":"Thu","2":"Chi"},"cash_accounting_type_dom":{"":"","131":"C.N\u1ee3 ph\u1ea3i thu (131)","331":"C.N\u1ee3 ph\u1ea3i tr\u1ea3 (331)","CPT":"Chi ph\u00ed thu\u1ea7n","TDN":"CP Thu\u1ebf doanh nghi\u1ec7p","THK":"Thu kh\u00e1c","CHK":"Chi kh\u00e1c","KHT":"Kh\u00f4ng h\u1ea1ch to\u00e1n"},"sales_lines_status_dom":{"":"","Plan":"K\u1ebf ho\u1ea1ch","Active":"K\u00edch ho\u1ea1t","Cancel":"H\u1ee7y b\u1ecf"},"sales_lines_type_dom":{"":"","Sales":"B\u00e1n h\u00e0ng","Cares":"Ch\u0103m s\u00f3c KH"},"sales_lines_cycle_dom":{"month":"Th\u00e1ng","week":"Tu\u1ea7n"},"product_names_status_dom":{"":"","1":"\u0110\u00e3 v\u1ec1 c\u00f4ng ty","2":"\u0110ang \u1edf kh\u00e1ch h\u00e0ng","3":"Tr\u1ea3 cho kh\u00e1ch h\u00e0ng"},"product_names_in_type_dom":{"":"","1":"Nh\u1eadp cho thu\u00ea l\u1ea5y v\u1ec1","2":"Nh\u1eadp k\u00fd g\u1edfi tr\u1ea3 v\u1ec1","3":"Nh\u1eadp m\u1edbi - \u0110i thu\u00ea v\u1ec1"},"product_names_out_type_dom":{"":"","1":"Xu\u1ea5t cho thu\u00ea","2":"Xu\u1ea5t k\u00fd g\u1edfi","3":"Xu\u1ea5t tr\u1ea3 \u0111\u1ed1i t\u00e1c"},"product_names_origins_dom":{"":"","1":"C\u00f4ng ty","2":"\u0110i thu\u00ea"},"chart_trans_level_dom":{"pie":"Tr\u1ea1ng th\u00e1i sau c\u00f9ng","funnel":"L\u1ecbch s\u1eed kh\u1ea3 n\u0103ng b\u00e1n h\u00e0ng\/Ch\u1ed1t"},"invoice_type_dom":{"":"","1":"T\u1eeb \u0111\u01a1n h\u00e0ng","2":"T\u1ea1o th\u1ee7 c\u00f4ng"},"invoice_status_dom":{"":"","-2":"L\u01b0u t\u1ea1m","0":"M\u1edbi t\u1ea1o l\u1eadp (server)","1":"C\u00f3 ch\u1eef k\u00fd s\u1ed1","2":"\u0110\u00e3 khai b\u00e1o thu\u1ebf","3":"B\u1ecb thay th\u1ebf","4":"B\u1ecb \u0111i\u1ec1u ch\u1ec9nh","5":"B\u1ecb h\u1ee7y","6":"\u0110\u00e3 duy\u1ec7t","-1":"Ch\u1edd k\u00fd"},"invoice_vat_dom":{"":"","-1":"Kh\u00f4ng t\u00ednh thu\u1ebf","0":"0%","5":"5%","8":"8%","10":"10%"},"invoice_payment_method_dom":{"":"","T\/M":"Ti\u1ec1n m\u1eb7t","C\/K":"Chuy\u1ec3n kho\u1ea3n","TM\/CK":"TM ho\u1eb7c CK","TT\/D":"Th\u1ebb t\u00edn d\u1ee5ng"},"invoice_payment_status_dom":{"":"","0":"Ch\u01b0a thanh to\u00e1n","1":"\u0110\u00e3 thanh to\u00e1n"},"product_units_packing_dom":{"":"","Bao":"Bao","Lon":"Lon","Phuy":"Phuy","Thung":"Th\u00f9ng","Khac":"Kh\u00e1c"},"products_units_dom":{"":"","Bag":"Bao","B\u1ecbch":"B\u1ecbch","B\u1ed9":"B\u1ed9","C\u00e1i":"C\u00e1i","C\u1eb7p":"C\u1eb7p","C\u00e2y":"C\u00e2y","Chai":"Chai","Chi\u1ebfc":"Chi\u1ebfc","Chuy\u1ebfn":"Chuy\u1ebfn","G\u00f3i":"G\u00f3i","H\u1ed9p":"H\u1ed9p","Kg":"Kg","L\u1ea7n":"L\u1ea7n","Lit":"Lit","L\u1ed1c":"L\u1ed1c","Lon":"Lon","M":"M","ml":"ml","S\u1ee3i":"S\u1ee3i","THUNG":"Th\u00f9ng","Ti\u1ebfng Anh":"Ti\u1ebfng Vi\u1ec7t"},"products_made_in_dom":{"":"","CH":"CH","DL":"\u0110L","Duc":"\u0110\u1ee9c","HQ":"HQ","HK":"H\u1ed3ng K\u00f4ng","Nhat":"Nh\u1eadt","TQ":"TQ","USA":"USA","VN":"VN"},"products_type_dom":{"":"","1":"1","2":"2"},"incurred_fee_type_dom":{"":"","1":"Trong n\u01b0\u1edbc","2":"N\u01b0\u1edbc ngo\u00e0i"},"opportunity_processing_design_dom":{"":"","1":"C\u01a1 h\u1ed9i m\u1edbi","2":"B\u00e1o gi\u00e1","3":"\u0110\u00e3 g\u1eedi h\u1ee3p \u0111\u1ed3ng","4":"\u0110\u00e3 k\u00fd h\u1ee3p \u0111\u1ed3ng","5":"H\u1ee7y b\u1ecf"},"processing_design_config_dom":{"":"","1":"H\u0110 \u0110\u00e3 \u0111\u1eb7t mua NCC","2":"H\u0110 \u0110\u00e3 mua BH & m\u1edf LC","3":"\u0110H Theo d\u00f5i ch\u1ee9ng t\u1eeb","4":"H\u0110 Th\u00f4ng b\u00e1o t\u00e0u v\u1ec1 & mua BHBS","5":"H\u0110 Th\u00f4ng b\u00e1o h\u00e0ng v\u1ec1","6":"H\u0110 Thanh to\u00e1n LC","7":"H\u0110 Khai h\u1ea3i quang","8":"H\u0110 H\u00e0ng \u0111\u00e3 nh\u1eadp kho"},"revenue_promotion_period_type_dom":{"":"","monthly":"Th\u00e1ng","quarterly":"Qu\u00fd","yearly":"N\u0103m"},"todo_list_cycle_type_dom":{"":"","1":"Ng\u00e0y","2":"Tu\u1ea7n","3":"Th\u00e1ng","4":"Qu\u00fd","5":"N\u0103m"},"todo_list_job_type_dom":{"":"","1":"\u0110\u01a1n","2":"Ph\u1ee9c","3":"Quy tr\u00ecnh"},"productprices_status_dom":{"":"","Active":"\u0110ang \u00e1p d\u1ee5ng","Inactive":"Kh\u00f3a & Kh\u00f4ng s\u1eed d\u1ee5ng"},"time_keepings_status_dom":{"":"","3":"C\u00f3 m\u1eb7t","2":"Ngh\u1ec9 ph\u00e9p n\u0103m","9":"Ngh\u1ec9 ph\u00e9p theo quy \u0111\u1ecbnh NN","1":"V\u1eafng-C\u00f3 ph\u00e9p","-1":"V\u1eafng-Kh\u00f4ng ph\u00e9p"},"maintask_status_dom":{"":"","Drafting":"So\u1ea1n th\u1ea3o","Received":"Ti\u1ebfp nh\u1eadn-Plan","In Progress":"\u0110ang ti\u1ebfn h\u00e0nh","Completed":"Ho\u00e0n t\u1ea5t","Refusal":"T\u1eeb ch\u1ed1i","ReWork":"Y\u00eau c\u1ea7u x\u1eed l\u00fd l\u1ea1i"},"maintask_eval_confirm_dom":{"":"","Yes":"\u0110\u1ed3ng \u00fd","No":"Kh\u00f4ng \u0111\u1ed3ng \u00fd"},"record_type_display_sales":{"Opportunities":"C\u01a1 h\u1ed9i KD","Contracts":"H\u1ee3p \u0111\u1ed3ng"},"record_type_display_subtasks":{"Opportunities":"C\u01a1 h\u1ed9i KD","Contracts":"H\u1ee3p \u0111\u1ed3ng","TQT_MainTasks":"Presales\/PostSales","TQT_WorkOthers":"Cases"},"subtask_eval_confirm_dom":{"":"","Appreciated":"\u0110\u00e1nh gi\u00e1 cao","Not Convinced":"Ch\u01b0a thuy\u1ebft ph\u1ee5c","ReWork":"Y\u00eau c\u1ea7u l\u00e0m l\u1ea1i","Refusal":"T\u1eeb ch\u1ed1i"},"opportunities_line_profile_dom":{"":"","Public Profile":"H\u1ed3 s\u01a1 chung","Private Profile":"H\u1ed3 s\u01a1 ri\u00eang","Others":"C\u00e1c y\u00eau c\u1ea7u kh\u00e1c"},"opportunities_group_profile_dom":{"":"","Legal Documents":"H\u1ed3 s\u01a1 ph\u00e1p l\u00fd","Company Profile":"H\u1ed3 s\u01a1 c\u00f4ng ty","Capacity-Experience Profile":"H\u1ed3 s\u01a1 n\u0103ng l\u1ef1c v\u00e0 kinh nghi\u1ec7m","Bid Information":"Th\u00f4ng tin th\u1ea7u","Deployment Plan":"K\u1ebf ho\u1ea1ch tri\u1ec3n khai","Commitments":"C\u00e1c cam k\u1ebft","Quotation":"B\u00e1o gi\u00e1","Technical Documents":"H\u1ed3 s\u01a1 k\u1ef9 thu\u1eadt","Others":"H\u1ed3 s\u01a1 kh\u00e1c"},"opportunities_type_profile_dom":{"":"","Green":"Xanh","Red":"\u0110\u1ecf","Yellow":"V\u00e0ng"},"contracts_line_profile_dom":{"":"","a1":"D\u00f2ng 1","a2":"D\u00f2ng 2","a3":"D\u00f2ng 3","Others":"Kh\u00e1c"},"contracts_group_profile_dom":{"":"","a1":"Nh\u00f3m 1","a2":"Nh\u00f3m 2","a3":"Nh\u00f3m 3","Others":"Kh\u00e1c"},"contracts_type_profile_dom":{"":"","a1":"Lo\u1ea1i 1","a2":"Lo\u1ea1i 2","a3":"Lo\u1ea1i 3"},"record_type_display_summaryweeks":{"TQT_MainTasks":"Presale\/PostSale","Contracts":"H\u1ee3p \u0111\u1ed3ng","TQT_WorkOthers":"Cases"},"subtask_line_profile_dom":{"":"","Public Profile":"H\u1ed3 s\u01a1 chung","Private Profile":"H\u1ed3 s\u01a1 ri\u00eang","Others":"Kh\u00e1c","a1":"D\u00f2ng 1","a2":"D\u00f2ng 2","a3":"D\u00f2ng 3"},"subtask_group_profile_dom":{"":"","Legal Documents":"H\u1ed3 s\u01a1 ph\u00e1p l\u00fd","Company Profile":"H\u1ed3 s\u01a1 c\u00f4ng ty","Capacity-Experience Profile":"H\u1ed3 s\u01a1 n\u0103ng l\u1ef1c v\u00e0 kinh nghi\u1ec7m","Bid Information":"Th\u00f4ng tin th\u1ea7u","Deployment Plan":"K\u1ebf ho\u1ea1ch tri\u1ec3n khai","Commitments":"C\u00e1c cam k\u1ebft","Quotation":"B\u00e1o gi\u00e1","Technical Documents":"H\u1ed3 s\u01a1 k\u1ef9 thu\u1eadt","Others":"Kh\u00e1c","a1":"Nh\u00f3m 1","a2":"Nh\u00f3m 2","a3":"Nh\u00f3m 3"},"subtask_type_profile_dom":{"":"","Green":"Xanh","Red":"\u0110\u1ecf","Yellow":"V\u00e0ng","a1":"Lo\u1ea1i 1","a2":"Lo\u1ea1i 2","a3":"Lo\u1ea1i 3"},"guarantee_processing_stage_dom":{"":"","1":"Ti\u1ebfp nh\u1eadn B\u1ea3o h\u00e0nh\/B\u1ea3o tr\u00ec","2":"Ki\u1ec3m tra l\u1ed7i","3":"Th\u1ef1c hi\u1ec7n b\u1ea3o h\u00e0nh t\u1ea1i Cty","4":"\u0110\u00e3 chuy\u1ec3n NCC b\u1ea3o h\u00e0nh","5":"B\u1ea3o h\u00e0nh xong ch\u1edd tr\u1ea3","6":"\u0110\u00e3 tr\u1ea3 kh\u00e1ch h\u00e0ng"},"vat_status_dom":{"":"","1":"Ch\u01b0a K\u00edch ho\u1ea1t","2":"K\u00edch ho\u1ea1t","3":"\u0110\u00e3 h\u1ee7y"},"product_promotion_status_dom":{"":"","1":"\u0110\u1ec1 xu\u1ea5t","2":"K\u00edch ho\u1ea1t","3":"\u0110\u00e3 h\u1ee7y"},"product_promotion_type_dom":{"":"","1":"Theo %","2":"Theo ti\u1ec1n m\u1eb7t","3":"\u0110\u1ed3ng gi\u00e1"},"product_promotion_branches_dom":{"":"","all":"T\u1ea5t c\u1ea3"},"sales_plan_display_dom":{"amount":"Doanh thu","output":"S\u1ea3n l\u01b0\u1ee3ng"},"checkin_type_dom":{"":"","1":"Online","2":"M\u00e1y ch\u1ea5m c\u00f4ng"},"checkin_dates_category_dom":{"":"","1":"Thi\u1ebft b\u1ecb \u0111i\u1ec7n","2":"Thi\u1ebft b\u1ecb c\u00f4ng nghi\u1ec7p","3":"\u0110i\u1ec7n tho\u1ea1i & \u0110i\u1ec7n m\u00e1y","4":"\u0110i\u1ec7n, \u0110i\u1ec7n t\u1eed","5":"Thi\u1ebft b\u1ecb y t\u1ebf","6":"S\u1eaft th\u00e9p\/ Inox","7":"V\u1eadt li\u1ec7u x\u00e2y d\u1ef1ng","8":"Kho\u00e1n s\u1ea3n","9":"Thi\u1ebft b\u1ecb v\u1ec7 sinh","10":"Trang tr\u00ed n\u1ed9i th\u1ea5t","11":"Thang m\u00e1y","12":"S\u01a1n","13":"Camera","14":"PCCC","15":"M\u1ef9 ph\u1ea9m","16":"H\u00f3a ch\u1ea5t\/ Ph\u1ee5 gia\/ H\u01b0\u01a1ng li\u1ec7u","17":"Ph\u00e2n b\u00f3n","18":"Bao b\u00ec","19":"B\u1ea3o h\u1ed9 lao \u0111\u1ed9ng","20":"Th\u1ef1c ph\u1ea9m","21":"FMCG (N\u01b0\u1edbc, S\u1eeda, c\u00e1, \u2026)","22":"Nh\u00e0 thu\u1ed1c","23":"Spa\/ Gym\/ L\u00e0m \u0111\u1eb9p","24":"Du l\u1ecbch\/ D\u1ecbch v\u1ee5","25":"Th\u1eddi trang","26":"V\u1ea3i","27":"Bal\u00f4\/Vali","28":"N\u00f3n B\u1ea3o Hi\u1ec3m","29":"N\u1ec7m Ch\u0103n Drap G\u1ed1i","30":"M\u1eaft K\u00ednh","31":"Gi\u1ea7y\/D\u00e9p","32":"Hoa\/ Q\u00f9a t\u1eb7ng","33":"G\u1ed5\/ \u0110\u1ed3 g\u1ed5","34":"Xe\/ Linh ki\u1ec7n","35":"K\u1ebft s\u1eaft","36":"B\u1ea5t \u0111\u1ed9ng s\u1ea3n","37":"C\u01a1 kh\u00ed ch\u1ebf t\u1ea1o","38":"Kh\u00e1c"},"opportunity_object_dom":{"":"","Proposal":"Proposal","External":"Mua h\u00e0ng nh\u1eadp kh\u1ea9u"},"sales_stage_dom":{"":"","Estimation":"D\u1ef1 \u0111o\u00e1n","Analyze":"Ph\u00e2n t\u00edch nhu c\u1ea7u","Quotes":"B\u00e1o gi\u00e1","Holding":"T\u1ea1m gi\u1eef h\u00e0ng","Pending":"Ch\u1edd duy\u1ec7t","Closed Won":"Th\u00e0nh c\u00f4ng","Closed Lost":"Th\u1ea5t b\u1ea1i","Cancel":"H\u1ee7y b\u1ecf"},"sales_stage2_dom":{"":"","Estimation":"D\u1ef1 \u0111o\u00e1n","Pending":"Ch\u1edd duy\u1ec7t","Closed Won":"\u0110\u00e3 duy\u1ec7t","Cancel":"H\u1ee7y b\u1ecf"},"sales_probability_dom":{"":"","Estimation":"5","Analyze":"25","Quotes":"50","Pending":"75","Closed Won":"100","Closed Lost":"0","Cancel":"0"},"opportunities_priority_dom":{"":"Kh\u00f4ng","1":"C\u00f3"},"opportunities_scope_dom":{"":"","0-10":"< 10 tri\u1ec7u","10-25":"10-25 tri\u1ec7u","25-50":"25-50 tri\u1ec7u","50-100":"50-100 tri\u1ec7u","100-250":"100-250 tri\u1ec7u","250-500":"250-500 tri\u1ec7u","500-1000":"500-1 t\u1ef7","1000-0":"> 1 t\u1ef7"},"opportunities_revenue_dom":{"amount":"Doanh s\u1ed1"},"opportunity_type_dom":{"":"","1":"SO (\u0110\u01a1n H\u00e0ng B\u00e1n)","2":"Phi\u1ebfu Giao H\u00e0ng","3":"Phi\u1ebfu Tr\u1ea3 H\u00e0ng B\u00e1n","6":"PO (\u0110\u01a1n H\u00e0ng Mua)","4":"Phi\u1ebfu Nh\u1eadp H\u00e0ng","5":"Phi\u1ebfu Tr\u1ea3 H\u00e0ng Mua NCC"},"opportunity_type2_dom":{"":"","1":"SO (\u0110\u01a1n H\u00e0ng B\u00e1n)","2":"Phi\u1ebfu Giao H\u00e0ng","3":"Phi\u1ebfu Tr\u1ea3 H\u00e0ng B\u00e1n"},"opportunity_type3_dom":{"":"","6":"PO (\u0110\u01a1n H\u00e0ng Mua)","4":"Phi\u1ebfu Nh\u1eadp H\u00e0ng","5":"Phi\u1ebfu Tr\u1ea3 H\u00e0ng Mua NCC"},"opportunity_type4_dom":{"":"","1":"H\u1ee3p \u0111\u1ed3ng","2":"\u0110\u1ee3t thanh to\u00e1n","3":"Ph\u1ea1t h\u1ee3p \u0111\u1ed3ng"},"opportunity_type5_dom":{"":"","2":"\u0110H Giao\/N\u1ee3 ph\u1ea3i thu","4":"\u0110H Mua\/N\u1ee3 ph\u1ea3i tr\u1ea3"},"opportunity_type6_dom":{"":"","1":"\u0110\u01a1n h\u00e0ng"},"opportunity_type_contracts_dom":{"":"","1":"H\u1ee3p \u0111\u1ed3ng","2":"\u0110\u1ee3t thanh to\u00e1n","3":"Ph\u1ea1t h\u1ee3p \u0111\u1ed3ng","6":"PO (\u0110\u01a1n H\u00e0ng Mua)","4":"Phi\u1ebfu Nh\u1eadp H\u00e0ng","5":"Phi\u1ebfu Tr\u1ea3 H\u00e0ng Mua NCC"},"opportunity_type_orders_dom":{"":"","1":"\u0110\u01a1n h\u00e0ng","6":"PO (\u0110\u01a1n H\u00e0ng Mua)","4":"Phi\u1ebfu Nh\u1eadp H\u00e0ng","5":"Phi\u1ebfu Tr\u1ea3 H\u00e0ng Mua NCC"},"opportunity_order_type2_dom":{"":"","External":"Mua n\u01b0\u1edbc ngo\u00e0i","Internal":"Mua trong n\u01b0\u1edbc"},"payment_type_dom":{"":"","TM":"Ti\u1ec1n m\u1eb7t","CK":"Chuy\u1ec3n kho\u1ea3n","AD":"ATM\/Debit Card","VS":"Visa","MC":"MasterCard","AM":"Amex","VC":"Voucher","GN":"Ghi n\u1ee3","DC":"\u0110\u1eb7t c\u1ecdc","KH":"Kh\u00e1c"},"opportunity_failures_dom":{"":"","1":"Nh\u1eadn \u0111\u1ecbnh sai, thi\u1ebfu th\u00f4ng tin","2":"Gi\u00e1 kh\u00f4ng h\u1ee3p l\u00fd","3":"Ch\u1ea5t l\u01b0\u1ee3ng SP + Sai s\u00f3t trong Demo","4":"Th\u1eddi gian giao h\u00e0ng","5":"D\u1ecbch v\u1ee5 sau b\u00e1n h\u00e0ng","6":"Kh\u00e1c"},"opportunity_order_type_dom":{"":"","Packages":"B\u00e1n m\u1edbi","ForRent":"Cho thu\u00ea","Maintenance":"B\u1ea3o h\u00e0nh","OrderVAT":"\u0110\u01a1n h\u00e0ng VAT","Consignment":"K\u00fd g\u1edfi\/M\u1eabu","MM":"M\u01b0\u1ee3n m\u1eabu","Other":"Kh\u00e1c"},"opportunity_repeat_cycle_dom":{"":"","1 day":"1 ng\u00e0y","2 days":"2 Ng\u00e0y","3 days":"3 ng\u00e0y","4 days":"4 ng\u00e0y","5 days":"5 ng\u00e0y","6 days":"6 ng\u00e0y","1 week":"1 tu\u1ea7n","2 weeks":"2 tu\u1ea7n","1 months":"1 th\u00e1ng"},"holding_time_dom":{"":"","1":"1 tu\u1ea7n","2":"2 tu\u1ea7n","3":"3 tu\u1ea7n","4":"4 tu\u1ea7n"},"opportunity_type_default":"1","opportunity_target_default":"1","warehouse_ins_status_dom":{"":"","1":"L\u01b0u t\u1ea1m","4":"Ch\u1edd nh\u1eadp","5":"\u0110\u00e3 nh\u1eadp kho","6":"\u0110\u00e3 h\u1ee7y"},"warehouse_ins_type_dom":{"":"","9":"Nh\u1eadp kho","1":"Nh\u1eadp mua - N\u01b0\u1edbc ngo\u00e0i","2":"Nh\u1eadp mua - Trong n\u01b0\u1edbc","3":"Nh\u1eadp h\u00e0ng \u0111i thu\u00ea","4":"Nh\u1eadp b\u1ea3o h\u00e0nh","5":"Nh\u1eadp h\u00e0ng thu\u00ea tr\u1ea3 v\u1ec1","6":"Nh\u1eadp h\u00e0ng k\u00fd g\u1edfi tr\u1ea3 v\u1ec1","7":"Kh\u00e1c"},"warehouse_ins_code_dom":{"9":"","1":"NN","2":"TN","3":"DT","4":"BH","5":"TV","6":"KG","7":"KH"},"debit_bills_status_dom":{"":"","1":"Ch\u01b0a thanh to\u00e1n","2":"Thanh to\u00e1n 1 ph\u1ea7n","3":"Thanh to\u00e1n 100%"},"debit_bills_payment_type_dom":{"":"","CK":"CK","TM":"TM","CK\/TM":"CK\/TM"},"debit_bills_status_bill_dom":{"":"","1":"S\u1eed d\u1ee5ng","2":"H\u1ee7y"},"debit_bills_type_bill_dom":{"":"","1":"B\u00e1n h\u00e0ng","2":"D\u1ecbch v\u1ee5"},"debited_status_dom":{"":"","1":"Ch\u01b0a ghi n\u1ee3 h\u1ebft"},"contact_position_dom":{"":"","primary":"Ch\u00ednh 1","secondary":"Ch\u00ednh 2"},"products_status_dom":{"":"","Active":"K\u00edch ho\u1ea1t","Unactive":"Kh\u00f4ng k\u00edch ho\u1ea1t"},"priority_type_dom":{"":"","Urgent":"Kh\u1ea9n c\u1ea5p","High":"Cao","Medium":"Trung b\u00ecnh","Low":"Th\u1ea5p"},"select_process_dom":{"":"","Select":"Ch\u1ecdn th\u1ef1c hi\u1ec7n","NotSelect":"Kh\u00f4ng ch\u1ecdn","NeedReview":"C\u1ea7n xem x\u00e9t","StaffAdvice":"NV \u0111\u1ec1 xu\u1ea5t"},"management_object_dom":{"ZingAds":"ZingAds","Campaigns":"Campaigns","Others":"Kh\u00e1c"},"currency_type_dom":{"USD":"USD","VND":"VND"},"quaters_dom":{"Q1":"Qu\u00fd 1","Q2":"Qu\u00fd 2","Q3":"Qu\u00fd 3","Q4":"Qu\u00fd 4"},"contract_tax_dom":{"-1":"Kh\u00f4ng VAT","0":"0%","5":"5%","7":"7%","8":"8%","10":"10%","12":"12%","15":"15%","18":"18%","20":"20%","22":"22%","25":"25%"},"contract_stage_dom":{"":"","2":"\u0110ang th\u1ef1c thi","3":"Ho\u00e0n t\u1ea5t (c\u00f3 h\u00f3a \u0111\u01a1n)","1":"Thanh l\u00fd"},"contract_status_dom":{"":"","1":"Ph\u00e1t th\u1ea3o","2":"Tr\u00ecnh duy\u1ec7t","3":"Kh\u00f4ng duy\u1ec7t","4":"Duy\u1ec7t","5":"K\u00fd k\u1ebft","6":"H\u1ee7y b\u1ecf"},"contract_type_dom":{"":"","1":"H\u0110 Qu\u1ea3ng c\u00e1o","2":"H\u0110 NT CKDS","3":"PO","4":"B\u1ea3ng k\u00ea PO","6":"B\u1ea3ng k\u00ea CPM\/CPC","5":"H\u0110 3 B\u00ean","7":"H\u0110 Campaign"},"contract_payment_dom":{"":"","1":"Chuy\u1ec3n kho\u1ea3n","2":"Ti\u1ec1n m\u1eb7t","3":"Th\u1ebb t\u00edn d\u1ee5ng","4":"Kh\u00e1c"},"contract_approves_status_dom":{"":"","1":"Duy\u1ec7t","2":"Tr\u1ea3 v\u1ec1"},"contract_amount_summary":{"1000":"&lt; 1 t\u1ef7","3000":"1 - 3 t\u1ef7","7000":"3 - 7 t\u1ef7","15000":"7 - 15 t\u1ef7","30000":"15 - 30 t\u1ef7","60000":"30 - 60 t\u1ef7","100000":"60 - 100 t\u1ef7","-1":"&gt;= 100 T\u1ef7"},"contract_cnt_type_dom":{"":"","Packages":"B\u00e1n tr\u1ecdn g\u00f3i","ForRent":"Cho thu\u00ea","Consignment":"K\u00fd g\u1edfi\/M\u1eabu","Other":"Kh\u00e1c"},"contract_market_dom":{"":"","Service":"D\u1ecbch v\u1ee5","Marketing":"Th\u01b0\u01a1ng m\u1ea1i","Production":"S\u1ea3n xu\u1ea5t","Other":"Kh\u00e1c"},"payments_status_dom":["Ch\u01b0a thanh to\u00e1n","\u0110\u00e3 TT 1 ph\u1ea7n","\u0110\u00e3 TT 100%"],"collections_status_dom":["Ch\u01b0a h\u1ea1ch to\u00e1n","\u0110\u00e3 h\u1ea1ch to\u00e1n 1 ph\u1ea7n","\u0110\u00e3 h\u1ea1ch to\u00e1n 100%"],"invoice_payment_type_dom":{"":"","90":"90 ng\u00e0y","75":"75 ng\u00e0y","60":"60 ng\u00e0y","45":"45 ng\u00e0y","30":"30 ng\u00e0y","15":"15 ng\u00e0y","10":"10 ng\u00e0y","07":"07 ng\u00e0y","05":"05 ng\u00e0y","03":"03 ng\u00e0y","00":"Thanh to\u00e1n ngay"},"revenue_reduced_type_dom":{"":"","Late-termContract":"Tr\u1ec5 h\u1ea1n h\u1ee3p \u0111\u1ed3ng","ContractPenalty":"Ph\u1ea1t h\u1ee3p \u0111\u1ed3ng","Other":"Kh\u00e1c"},"opportunities_task_status_dom":{"":"","Processing":"\u0110ang th\u1ef1c hi\u1ec7n","Arising":"C\u00f3 ph\u00e1t sinh","Completed":"Ho\u00e0n t\u1ea5t"},"data_locks_status_dom":{"Lock":"Kh\u00f3a d\u1eef li\u1ec7u","Unlock":"M\u1edf kh\u00f3a d\u1eef li\u1ec7u"},"data_locks_objects_dom":{"":"","Opportunities":"\u0110\u01a1n h\u00e0ng","Cashes":"Phi\u1ebfu Thu\/Chi","TQT_OrderDetails":"Chi ph\u00ed ph\u00e1t sinh","Warehouse_Ins":"Nh\u1eadp kho","Warehouse_Outs":"Xu\u1ea5t kho","Tasks":"T\u00e1c v\u1ee5 & Giao d\u1ecbch","Accounts":"Kh\u00e1ch h\u00e0ng","Contacts":"Ng\u01b0\u1eddi li\u00ean h\u1ec7"},"data_locks_fields_dom":{"Opportunities":"date_closed","Cashes":"date_perform","TQT_OrderDetails":"date_arise","Warehouse_Ins":"date_perform","Warehouse_Outs":"date_perform","Tasks":"date_start","Accounts":"date_entered","Contacts":"date_entered"},"cycle_plan_display_dom":{"amount":"Doanh Thu","output":"S\u1ea3n l\u01b0\u1ee3ng"},"cycle_method_calculate_dom":{"auto":"T\u1ef1 \u0111\u1ed9ng","manual":"C\u1ea5u h\u00ecnh b\u1eb1ng tay"},"cashes_bank_dom":{"":"","TKTM":"TK Ti\u1ec1n m\u1eb7t","VCB":"Vietcombank","ACB":"ACB","DongABank":"Dong A Bank","Sacombank":"Sacombank","BIDV":"BIDV","Vietinbank":"Vietinbank","Agribank":"Agribank","VPBank":"VP Bank","HDBank":"HD Bank","MBBank":"MB Bank","NamABank":"Nam A Bank","VIB":"VIB","SCB":"SCB","SHB":"SHB","OCB":"OCB","VDB":"VDB","ANZ":"ANZ","HSBC":"HSBC","CITI":"CITI Bank","PGBank":"PG Bank","ABBank":"AB Bank","GPBank":"GP Bank","Eximbank":"Eximbank","Seabank":"Seabank","MaritimeBank":"Maritime Bank","Techcombank":"Techcombank","Other":"Kh\u00e1c"},"product_stocks_expired_dom":{"":"","expired":"H\u1ebft h\u1ea1n","15 days":"< 15 ng\u00e0y","1 month":"< 1 th\u00e1ng","2 month":"< 2 th\u00e1ng","3 month":"< 3 th\u00e1ng","6 month":"< 6 th\u00e1ng","12 month":"< 12 th\u00e1ng","18 month":"< 18 th\u00e1ng"},"warehouse_inv_status_dom":{"":"","1":"L\u01b0u t\u1ea1m","2":"\u0110\u00e3 ki\u1ec3m","3":"H\u1ee7y b\u1ecf"},"purchase_processing_stage_dom":{"":"","1":"\u0110H \u0110\u00e3 \u0111\u1eb7t mua NCC","2":"\u0110ang s\u1ea3n xu\u1ea5t","3":"\u0110H NCC giao 1 ph\u1ea7n","4":"\u0110H NCC \u0111\u00e3 giao xong","5":"\u0110H H\u1ee7y"},"product_gift_type_dom":{"":"","1":"SL Gi\u1ea3m d\u1ea7n","2":"Ch\u1ea1y song song"},"department_dom":{"":"","5555f6c1-7823-e998-81b1-59226c1efbf0":"Kinh Doanh CN","89e7dabb-3b51-50de-7295-637445b435bb":"K\u1ef9 Thu\u1eadt","3bb9a289-d3eb-568a-4969-59226c7a1025":"K\u1ebf To\u00e1n","432a58f6-4a05-542e-a9b3-577dd1762854":"Kinh doanh","c9fe9288-5bc7-b90e-37b3-59226cd46535":"K\u1ef9 Thu\u1eadt","1b86389e-607b-1b2d-1a81-59226dabeac9":"Marketing","3":"Qu\u1ea3n Tr\u1ecb"},"teams_dom":{"":"","6793db78-de96-fb6f-88c1-606fc369c528":"B\u1ed9 ph\u1eadn QT"},"hierarchical_dom":{"":"","ACN":"Admin Chi nh\u00e1nh","TGD":"T\u1ed5ng Gi\u00e1m \u0110\u1ed1c","GDK":"Gi\u00e1m \u0110\u1ed1c","QLY":"Qu\u1ea3n l\u00fd","TPK":"Tr\u01b0\u1edfng Ph\u00f2ng","TNK":"Tr\u01b0\u1edfng Nh\u00f3m","KDO":"Nv.Kinh doanh","KTO":"Nv.K\u1ebf to\u00e1n","BHA":"Nv.B\u00e1n h\u00e0ng","MHA":"Nv.Mua h\u00e0ng","KHO":"Nv.Kho","CSK":"Nv.CSKH","KTH":"Nv.K\u1ef9 thu\u1eadt","GHA":"Nv.Giao nh\u1eadn","MKT":"Nv.Marketing","NVK":"Kh\u00e1c"},"user_area_dom":{"":"","HCM":"H\u1ed3 Ch\u00ed Minh","HN":"H\u00e0 N\u1ed9i"},"sync_data_status_dom":{"":"","1":"\u0110ang x\u1eed l\u00fd","2":"Ho\u00e0n t\u1ea5t","3":"L\u1ed7i","4":"H\u1ee7y"},"months_dom":{"":"","01":"Th\u00e1ng 1","02":"Th\u00e1ng 2","03":"Th\u00e1ng 3","04":"Th\u00e1ng 4","05":"Th\u00e1ng 5","06":"Th\u00e1ng 6","07":"Th\u00e1ng 7","08":"Th\u00e1ng 8","09":"Th\u00e1ng 9","10":"Th\u00e1ng 10","11":"Th\u00e1ng 11","12":"Th\u00e1ng 12"},"quarter_dom":{"":"","1":"Qu\u00fd 1","2":"Qu\u00fd 2","3":"Qu\u00fd 3","4":"Qu\u00fd 4"},"quarters_dom":{"":"","01-03":"Qu\u00fd 1","04-06":"Qu\u00fd 2","07-09":"Qu\u00fd 3","10-12":"Qu\u00fd 4"},"undefined_dom":{"":"","Y":"C\u00f3","N":"Kh\u00f4ng","U":"Ch\u01b0a x\u00e1c \u0111\u1ecbnh"},"unit_management_dom":{"":"","Accounts":"Kh\u00e1ch h\u00e0ng","Suppliers":"Nh\u00e0 cung c\u1ea5p","Accounts_Leads":"\u0110\u1ea7u m\u1ed1i"},"account_status_dom":{"":"","Active":"\u0110ang ho\u1ea1t \u0111\u1ed9ng","Suspended":"Ng\u01b0ng ho\u1ea1t \u0111\u1ed9ng","Other":"Kh\u00e1c"},"department_manager_dom":{"":"","KHH":"Kh\u00e1ch h\u00e0ng","DCC":"\u0110\u1ed1i th\u1ee7 c\u1ea1nh tranh","NCC":"Nh\u00e0 cung c\u1ea5p","DTA":"\u0110\u1ed1i t\u00e1c","LEA":"\u0110\u1ea7u m\u1ed1i","KHA":"Kh\u00e1c"},"business_field_dom":{"":"","Edu":"EDU","Fsi":"FSI","Enterprise":"Enterprise","Telco":"Telco","Oil_Gas":"Oil & Gas","Government":"Government","Healthcare":"Healthcare","Other":"Kh\u00e1c"},"transaction_level_dom":{"":"","Prospects":"Ti\u1ec1m n\u0103ng","Approaching":"\u0110ang ti\u1ebfp c\u1eadn","Needs":"X\u00e1c nh\u1eadn c\u00f3 nhu c\u1ea7u","NewCooperation":"C\u00f3 1-3 \u0111\u01a1n h\u00e0ng","CooperationGood":"H\u1ee3p t\u00e1c < 3 th\u00e1ng","OrdersRegularly":"\u0110\u1eb7t h\u00e0ng \u0111\u1ec1u","OrdersProblems":"\u0110\u1eb7t h\u00e0ng kh\u00f4ng \u0111\u1ec1u","CooperationStopped":"\u0110\u00e3 ng\u01b0ng h\u1ee3p t\u00e1c","Other":"Kh\u00e1c"},"acc_leads_transaction_level_dom":{"":"","Prospects":"Ti\u1ec1m n\u0103ng","Approaching":"\u0110ang ti\u1ebfp c\u1eadn","Needs":"X\u00e1c nh\u1eadn c\u00f3 nhu c\u1ea7u"},"transaction_levelxxx_dom":{"":"","1":"Ti\u1ec1m n\u0103ng","2":"\u0110ang ti\u1ebfp c\u1eadn","3":"C\u00f3 nhu c\u1ea7u","4":"K\u00fd thu\u00ea l\u1ea7n 1","6":"K\u00fd thu\u00ea l\u1ea1i","7":"Kh\u00f4ng thu\u00ea l\u1ea1i","5":"K\u00fd tr\u1ecdn g\u00f3i","9":"K\u00fd d\u1ef1 \u00e1n","8":"Kh\u00e1c"},"location_area_dom":{"":"","North":"Mi\u1ec1n B\u1eafc","Central":"Mi\u1ec1n Trung","South":"Mi\u1ec1n Nam","Oversea":"N\u01b0\u1edbc ngo\u00e0i","Other":"Kh\u00e1c"},"sector_vertical_dom":{"":"","c1":"C\u1ea5p 1","c2":"C\u1ea5p 2","c3":"C\u1ea5p 3","c0":"Th\u01b0\u1eddng"},"account_scope_dom":{"":"","Large":"L\u1edbn","Medium":"Trung b\u00ecnh","Small":"Nh\u1ecf","Other":"Kh\u00e1c"},"account_rate_dom":{"":"","High":"Cao c\u1ea5p","Pretty":"Kh\u00e1","Medium":"Trung b\u00ecnh","Low":"Th\u1ea5p"},"decentralization_dom":{"":"","1":"Nh\u00e0 PP","2":"K.H\u00e0ng"},"task_communication_dom":{"":"","MobilePhone":"Mobile Phone","WorkPhone":"Work Phone","HomePhone":"Home Phone","Message":"Nh\u1eafn Tin","Email":"Email","Mail":"Th\u01b0 T\u00edn","Fax":"Fax","AtCustomer":"G\u1eb7p t\u1ea1i VP KH","AtCompany":"G\u1eb7p t\u1ea1i VP Cty","AtHome":"G\u1eb7p T\u1ea1i Nh\u00e0","Other":"Kh\u00e1c"},"task_transactions_dom":{"":"","kpiLoi":"X\u1eed l\u00fd l\u1ed7i","LenLichHen":"\u0110ang l\u00ean l\u1ecbch h\u1eb9n","DaGapMat":"\u0110\u00e3 g\u1eb7p m\u1eb7t kh\u00e1ch h\u00e0ng","XinNghiPhep":"Xin ngh\u1ec9 ph\u00e9p","NhacThanhToan":"Nh\u1eafc Thanh To\u00e1n","HenThanhToan":"H\u1eb9n Thanh To\u00e1n","ThuTien":"Thu Ti\u1ec1n","TraTien":"Tr\u1ea3 Ti\u1ec1n","BaoGia":"B\u00e1o Gi\u00e1","HoiHang":"H\u1ecfi H\u00e0ng","ChaoMau":"Ch\u00e0o m\u1eabu","DatHang":"\u0110\u1eb7t H\u00e0ng","ChinhSach":"Ch\u00ednh S\u00e1ch","ThanPhien":"Than Phi\u1ec1n","GiaoNhanGiayTo":"Giao Nh\u1eadn Gi\u1ea5y T\u1edd","ThongTinKhMoi":"Th\u00f4ng tin KH M\u1edbi","PhanHoiKhachHang":"Ph\u1ea3n h\u1ed3i kh\u00e1ch h\u00e0ng","NamBatThongTin":"N\u1eafm b\u1eaft th\u00f4ng tin","ThongTinDoiThu":"Th\u00f4ng tin \u0111\u1ed1i th\u1ee7","KiemKeHangTon":"Ki\u1ec3m k\u00ea h\u00e0ng t\u1ed3n","XuLyYC":"X\u1eed l\u00fd Y.C","OrderProcess":"X\u1eed l\u00fd \u0110\u01a1n h\u00e0ng","VisitOrder":"Vi\u1ebfng th\u0103m B\u00e1n h\u00e0ng","Care_Email":"CS b\u1eb1ng emails","Care_Call":"CS b\u1eb1ng \u0111i\u1ec7n tho\u1ea1i","Maintenance":"B\u1ea3o h\u00e0nh\/B\u1ea3o tr\u00ec","DCP":"Duy\u1ec7t chi ph\u00ed","ThucHienMau":"Th\u1ef1c hi\u1ec7n m\u1eabu","PhanTichThietKe":"Ph\u00e2n t\u00edch thi\u1ebft k\u1ebf","Assigned":"Giao vi\u1ec7c","Other":"Kh\u00e1c"},"task_care_result_dom":{"":"","10":"H\u00e0i l\u00f2ng","20":"B\u00ecnh th\u01b0\u1eddng","30":"Kh\u00f4ng h\u00e0i l\u00f2ng","40":"Y\/C h\u1ed7 tr\u1ee3"},"task_product_result_dom":{"":"","10":"Ch\u01b0a s\u1eed d\u1ee5ng","20":"\u0110ang s\u1eed d\u1ee5ng","30":"T\u1ea1m ng\u01b0ng","40":"Kh\u00f4ng s\u1eed d\u1ee5ng","50":"Kh\u00e1c"},"care_mail_result_dom":{"GoiThanhCong":"G\u1eedi th\u00e0nh c\u00f4ng","GoiThatBai":"G\u1eedi th\u1ea5t b\u1ea1i","SaiEmails":"Sai emails"},"care_call_result_dom":{"Goi-BatMay":"G\u1ecdi - B\u1eaft m\u00e1y","Goi-KoBatMay":"G\u1ecdi - Ko b\u1eaft m\u00e1y","KoLienLacDuoc":"Ko li\u00ean l\u1ea1c \u0111\u01b0\u1ee3c","NhamNguoi-LonSo":"Nh\u1ea7m ng\u01b0\u1eddi\/ L\u1ed9n s\u1ed1"},"contact_trans_level_dom":{"":"","None Approach":"Ch\u01b0a ti\u1ebfp c\u1eadn","Approaching":"\u0110ang ti\u1ebfp c\u1eadn","Good Cooperation":"C\u00f3 tinh th\u1ea7n h\u1ee3p t\u00e1c","Friendly":"Th\u00e2n thi\u1ebft","None Cooperation":"Kh\u00f4ng h\u1ee3p t\u00e1c","Cooperation opponents":"H\u1ee3p t\u00e1c \u0111\u1ed1i th\u1ee7","Arising":"C\u00f3 ph\u00e1t sinh","Other":"Kh\u00e1c"},"ship_role_dom":{"":"","Senior Representative":"\u0110\u1ea1i di\u1ec7n cao c\u1ea5p","Chief Representative":"Tr\u01b0\u1edfng \u0111\u1ea1i di\u1ec7n","Other":"Kh\u00e1c"},"priority_level_dom":{"":"","1":"1","2":"2","3":"3","4":"4","5":"5"},"number_level_dom":{"":"","1":"1","2":"2","3":"3","4":"4","5":"5"},"send_mail_status_dom":{"":"","viewed":"Nh\u1eadn v\u00e0 \u0111\u00e3 M\u1edf email","link":"\u0110\u00e3 nh\u1eadn & \u0111\u1ecdc\u2013Click link b\u00ean trong emails","invalid email":"Email kh\u00f4ng g\u1edfi \u0111\u01b0\u1ee3c\/ Sai \u0111\u1ecba ch\u1ec9 email","send error":"Email kh\u00f4ng g\u1edfi \u0111\u01b0\u1ee3c\/ L\u1ed7i kh\u00e1c","removed":"X\u00e1c nh\u1eadn kh\u00f4ng mu\u1ed1n nh\u1eadn email n\u1eefa","other":"Kh\u00e1c"},"comment_types_dom":{"Commenting":"Cho \u00fd ki\u1ebfn","Deadline Late":"Tr\u1ec5 h\u1ea1n","Review":"Duy\u1ec7t l\u1ea1i","Other":"Kh\u00e1c"},"charter_capital_dom":{"":"","100tr":"D\u01b0\u1edbi 100 tri\u1ec7u","100-500tr":"T\u1eeb 100-500 tri\u1ec7u","500-1ty":"T\u1eeb 500tr \u0111\u1ebfn 1 t\u1ef7","1-3ty":"T\u1eeb 1-3 t\u1ef7","3-5ty":"T\u1eeb 3-5 t\u1ef7","5-10ty":"T\u1eeb 5-10 t\u1ef7","10-30ty":"T\u1eeb 10-30 t\u1ef7","30-50ty":"T\u1eeb 30-50 t\u1ef7","50-100ty":"T\u1eeb 50-100 t\u1ef7","100-500ty":"T\u1eeb 100-500 t\u1ef7","500-1000ty":"T\u1eeb 500-1.000 t\u1ef7","1000ty":"Tr\u00ean 1.000 t\u1ef7"},"product_categories_dom":{"":"","CS":"C\u00f4ng S\u1edf","DV":"D\u1ecbch v\u1ee5","BHLD":"B\u1ea3o h\u1ed9 Lao \u0111\u1ed9ng","YT":"Y T\u1ebf","TH":"Tr\u01b0\u1eddng H\u1ecdc","PK":"Ph\u1ee5 ki\u1ec7n","AT":"\u00c1o thun","ToH":"T\u1ed5ng h\u1ee3p"},"account_care_level_dom":{"":"","month":"C\u00f3 ch\u0103m s\u00f3c trong th\u00e1ng","1 month":"2 th\u00e1ng ch\u01b0a ch\u0103m s\u00f3c","2 months":"3 th\u00e1ng ch\u01b0a ch\u0103m s\u00f3c","3 months":"3-6 th\u00e1ng ch\u01b0a ch\u0103m s\u00f3c","6 months":"6-12 th\u00e1ng ch\u01b0a ch\u0103m s\u00f3c","1 year":"> 12 th\u00e1ng ch\u01b0a ch\u0103m s\u00f3c","not":"Ch\u01b0a ch\u0103m s\u00f3c"},"telesales_status_dom":{"":"","10":"KH M\u1edbi","20":"Ki\u1ec3m tra Ok","30":"C\u1ea7n ki\u1ec3m tra l\u1ea1i","40":"\u0110\u00e3 TV\/C\u1ea7n C.S\u00f3c","50":"C\u01a1 h\u1ed9i l\u1ea5y ti\u1ec1n","60":"\u0110ang x\u1eed l\u00fd","70":"K\u00fd H\u1ee3p \u0111\u1ed3ng","80":"Kh\u00f4ng k\u00fd H\u0110","90":"H\u1ee7y b\u1ecf"},"project_type_dom":{"":"","Marketing":"Marketing"},"task_asap_dom":{"":"","0":"Ch\u01b0a duy\u1ec7t","1":"Ch\u1edd duy\u1ec7t c\u1ea5p b\u1ed9 ph\u1eadn","2":"Ch\u1edd duy\u1ec7t c\u1ea5p tr\u01b0\u1edfng ph\u00f2ng","3":"Ch\u1edd duy\u1ec7t c\u1ea5p kh\u1ed1i","4":"Ch\u1edd duy\u1ec7t c\u1ea5p ban gi\u00e1m \u0111\u1ed1c","5":"\u0110\u00e3 duy\u1ec7t","6":"Kh\u00f4ng duy\u1ec7t"},"task_approve_dom":{"":"","0":"Ch\u01b0a duy\u1ec7t","1":"Ch\u1edd duy\u1ec7t","2":"\u0110\u00e3 duy\u1ec7t","6":"Kh\u00f4ng duy\u1ec7t"},"notes_approved_dom":{"":"","1":"Duy\u1ec7t","2":"Kh\u00f4ng duy\u1ec7t","3":"B\u1ecf duy\u1ec7t"},"cash_group_dom":{"":"","6":"Thu ti\u1ec1n ho\u1ea1t \u0111\u1ed9ng t\u00e0i ch\u00ednh (6)","7":"Chi ph\u00ed t\u00e0i ch\u00ednh (7)","8":"Chi ph\u00ed b\u00e1n h\u00e0ng (8)","9":"Chi ph\u00ed qu\u1ea3n l\u00fd doanh nghi\u1ec7p (9)","11":"Thu nh\u1eadp kh\u00e1c (11)","12":"Chi ph\u00ed kh\u00e1c (12)","15":"Chi ph\u00ed thu\u1ebf thu nh\u1eadp doanh nghi\u1ec7p hi\u1ec7n h\u00e0nh (15)","16":"Chi ph\u00ed thu\u1ebf thu nh\u1eadp doanh nghi\u1ec7p ho\u00e0n l\u1ea1i (16)"},"cash_project_group_dom":{"":"","1":"Chi ph\u00ed b\u00e1n h\u00e0ng\/ S\u1ea3n xu\u1ea5t","2":"Chi ph\u00ed nh\u00e2n c\u00f4ng","3":"Chi ph\u00ed kh\u00e1c"},"account_warn_dom":{"":"","N":"Kh\u00e1ch h\u00e0ng m\u1edbi","O":"Kh\u00e1ch h\u00e0ng c\u0169"},"debt_type_dom":{"":"","131":"Ph\u1ea3i thu 131","331":"Ph\u1ea3i tr\u1ea3 331"},"account_use_feature_dom":{"":"","10":"Kh\u00f4ng x\u00e1c \u0111\u1ecbnh","20":"Ch\u1eafc ch\u1eafn s\u1eed d\u1ee5ng","30":"S\u1ebd ng\u01b0ng s\u1eed d\u1ee5ng","40":"\u0110\u00e3 ng\u01b0ng s\u1eed d\u1ee5ng"},"account_reason_feature_dom":{"":"","N01":"1. Gi\u00e1 cao","N02":" &nbsp; Gi\u00e1 cao","N03":"2. DV kh\u00f4ng t\u1ed1t","N04":" &nbsp; Giao h\u00e0ng kh\u00f4ng \u0111\u00fang gi\u1edd","N05":" &nbsp; Th\u1ea5t tho\u00e1t h\u00e0ng h\u00f3a","N06":" &nbsp; Th\u1ea5t tho\u00e1t ch\u1ee9ng t\u1eeb","N07":" &nbsp; X\u1eed l\u00fd khi\u1ebfu n\u1ea1i ch\u1eadm","N08":" &nbsp; Nhi\u1ec7t \u0111\u1ed9 kh\u00f4ng \u0111\u1ea1t","N09":" &nbsp; Th\u00e1i \u0111\u1ed9 NV kh\u00f4ng t\u1ed1t","N10":"3. K\u1ebft th\u00fac th\u1ea7u","N11":" &nbsp; K\u1ebft th\u00fac th\u1ea7u","N12":"4. Kh\u00e1c","N13":" &nbsp; Kh\u00e1c"},"check_meets2_dom":{"":"","1":"\u0110\u1ea7u m\u1ed1i \u0111\u1ed3ng \u00fd g\u1eb7p +\/- 2h","2":"Ch\u01b0a d\u00f9ng PM","3":"PKD >4 ng\u01b0\u1eddi, d\u00f9ng Excel","4":"Mu\u1ed1n c\u1ea3i thi\u1ec7n ph\u00f2ng KD","5":"Mu\u1ed1n thay PM c\u0169"},"check_meets_dom":{"":"","1":"\u0110\u1ea7u m\u1ed1i \u0111\u1ed3ng \u00fd g\u1eb7p +\/- 2h","2":"Ch\u01b0a d\u00f9ng PM","3":"PKD >4 ng\u01b0\u1eddi, d\u00f9ng Excel","4":"Mu\u1ed1n c\u1ea3i thi\u1ec7n ph\u00f2ng KD","5":"Mu\u1ed1n thay PM c\u0169","-":"--SPA--","11":"C\u00f3 l\u1ec5 t\u00e2n","12":"C\u00f3 t\u01b0 v\u1ea5n","13":"Tr\u00ean 3 KTV","14":"\u0110ang d\u00f9ng PM BH nh\u1ecf","15":"\u0110ang d\u00f9ng Excel","16":"\u0110ang x\u00e0i s\u1ed5 s\u00e1ch"},"emp_contract_type_dom":{"":"","1":"H\u1ee3p \u0111\u1ed3ng th\u1eed vi\u1ec7c","2":"H\u1ee3p \u0111\u1ed3ng 12 th\u00e1ng","3":"H\u1ee3p \u0111\u1ed3ng kh\u00f4ng x\u00e1c \u0111\u1ecbnh th\u1eddi h\u1ea1n (KX\u0110TH)"},"product_set_dom":{"":"","01":"B\u1ed9 01","02":"B\u1ed9 02","03":"B\u1ed9 03","04":"B\u1ed9 04","05":"B\u1ed9 05","06":"B\u1ed9 06","07":"B\u1ed9 07","08":"B\u1ed9 08","09":"B\u1ed9 09","10":"B\u1ed9 10"},"task_vehicle_work_dom":{"":"","0":"Ch\u1edd x\u1ebfp xe","1":"Kho-V\u1eadn \u0111\u00e3 x\u1ebfp xe","2":"Kho-V\u1eadn kh\u00f4ng x\u1ebfp \u0111\u01b0\u1ee3c xe"},"location_cities_dom":{"":"","SGN":"H\u1ed3 Ch\u00ed Minh","HAN":"H\u00e0 N\u1ed9i","DNA":"\u0110\u1ed3ng Nai","BDD":"B\u00ecnh D\u01b0\u01a1ng","CTH":"C\u1ea7n Th\u01a1","DAN":"\u0110\u00e0 N\u1eb5ng","AGG":"An Giang","BVT":"B\u00e0 R\u1ecba - V\u0169ng T\u00e0u","BGG":"B\u1eafc Giang","BAK":"B\u1eafc K\u1ea1n","BAL":"B\u1ea1c Li\u00eau","BNI":"B\u1eafc Ninh","BTR":"B\u1ebfn Tre","BID":"B\u00ecnh \u0110\u1ecbnh","BPP":"B\u00ecnh Ph\u01b0\u1edbc","BTH":"B\u00ecnh Thu\u1eadn","CAM":"C\u00e0 Mau","CAB":"Cao B\u1eb1ng","DKL":"\u0110\u1eafk L\u1eafk","DKN":"\u0110\u1eafk N\u00f4ng","DIB":"\u0110i\u1ec7n Bi\u00ean","DTP":"\u0110\u1ed3ng Th\u00e1p","GLA":"Gia Lai","HAG":"H\u00e0 Giang","HNM":"H\u00e0 Nam","HAT":"H\u00e0 T\u0129nh","HDG":"H\u1ea3i D\u01b0\u01a1ng","HPP":"H\u1ea3i Ph\u00f2ng","HGG":"H\u1eadu Giang","HOB":"H\u00f2a B\u00ecnh","HYN":"H\u01b0ng Y\u00ean","KHH":"Kh\u00e1nh H\u00f2a","KIG":"Ki\u00ean Giang","KTU":"Kon Tum","LAB":"Lai Ch\u00e2u","LDG":"L\u00e2m \u0110\u1ed3ng","LAS":"L\u1ea1ng S\u01a1n","LAC":"L\u00e0o Cai","LAN":"Long An","NAD":"Nam \u0110\u1ecbnh","NGA":"Ngh\u1ec7 An","NIB":"Ninh B\u00ecnh","NIT":"Ninh Thu\u1eadn","PTH":"Ph\u00fa Th\u1ecd","PYE":"Ph\u00fa Y\u00ean","QUB":"Qu\u1ea3ng B\u00ecnh","QUN":"Qu\u1ea3ng Nam","QNG":"Qu\u1ea3ng Ng\u00e3i","QNI":"Qu\u1ea3ng Ninh","QUT":"Qu\u1ea3ng Tr\u1ecb","STR":"S\u00f3c Tr\u0103ng","SLA":"S\u01a1n La","TNI":"T\u00e2y Ninh","THB":"Th\u00e1i B\u00ecnh","THN":"Th\u00e1i Nguy\u00ean","THH":"Thanh H\u00f3a","TTH":"Th\u1eeba Thi\u00ean Hu\u1ebf","TIG":"Ti\u1ec1n Giang","TRV":"Tr\u00e0 Vinh","TQU":"Tuy\u00ean Quang","VLG":"V\u0129nh Long","VPH":"V\u0129nh Ph\u00fac","YEB":"Y\u00ean B\u00e1i"},"location_district_dom":{"":"","AGG_LongXuyen":"Tp.Long Xuy\u00ean","AGG_ChauDoc":"Tp.Ch\u00e2u \u0110\u1ed1c","AGG_TanChau":"TX.T\u00e2n Ch\u00e2u","AGG_AnPhu":"An Ph\u00fa","AGG_ChauPhu":"Ch\u00e2u Ph\u00fa","AGG_ChauThanh":"Ch\u00e2u Th\u00e0nh","AGG_ChoMoi":"Ch\u1ee3 M\u1edbi","AGG_PhuTan":"Ph\u00fa T\u00e2n","AGG_ThoaiSon":"Tho\u1ea1i S\u01a1n","AGG_TinhBien":"T\u1ecbnh Bi\u00ean","AGG_TriTon":"Tri T\u00f4n","BVT_BaRia":"TP.B\u00e0 R\u1ecba","BVT_VungTau":"TP.V\u0169ng T\u00e0u","BVT_PhuMy":"TX. Ph\u00fa M\u1ef9","BVT_ChauDuc":"Ch\u00e2u \u0110\u1ee9c","BVT_ConDao":"C\u00f4n \u0110\u1ea3o","BVT_DatDo":"\u0110\u1ea5t \u0110\u1ecf","BVT_LongDien":"Long \u0110i\u1ec1n","BVT_XuyenMoc":"Xuy\u00ean M\u1ed9c","BAK_BacKan":"TP.B\u1eafc K\u1ea1n","BAK_BaBe":"Ba B\u1ec3","BAK_BachThong":"B\u1ea1ch Th\u00f4ng","BAK_ChoDon":"Ch\u1ee3 \u0110\u1ed3n","BAK_ChoMoi":"Ch\u1ee3 M\u1edbi","BAK_NaRi":"Na R\u00ec","BAK_NganSon":"Ng\u00e2n S\u01a1n","BAK_PacNam":"P\u00e1c N\u1eb7m","BGG_HiepHoa":"Hi\u1ec7p H\u00f2a","BGG_LangGiang":"L\u1ea1ng Giang","BGG_LucNam":"L\u1ee5c Nam","BGG_LucNgan":"L\u1ee5c Ng\u1ea1n","BGG_SonDong":"S\u01a1n \u0110\u1ed9ng","BGG_BacGiang":"TP. B\u1eafc Giang","BGG_TanYen":"T\u00e2n Y\u00ean","BGG_VietYen":"Vi\u1ec7t Y\u00ean","BGG_YenDung":"Y\u00ean D\u0169ng","BGG_YenThe":"Y\u00ean Th\u1ebf","BAL_BacLieu":"TP.B\u1ea1c Li\u00eau","BAL_GiaRai":"TX.Gi\u00e1 Rai","BAL_PhuocLong":"Ph\u01b0\u1edbc Long","BAL_HongDan":"H\u1ed3ng D\u00e2n","BAL_VinhLoi":"V\u0129nh L\u1ee3i","BAL_DongHai":"\u0110\u00f4ng H\u1ea3i","BAL_HoaBinh":"H\u00f2a B\u00ecnh","BNI_TuSon":"TP.T\u1eeb S\u01a1n","BNI_GiaBinh":"Gia B\u00ecnh","BNI_LuongTai":"L\u01b0\u01a1ng T\u00e0i","BNI_QueVo":"Qu\u1ebf V\u00f5","BNI_BacNinh":"TP. B\u1eafc Ninh","BNI_ThuanThanh":"Thu\u1eadn Th\u00e0nh","BNI_TienDu":"Ti\u00ean Du","BNI_YenPhong":"Y\u00ean Phong","BTR_BenTre":"Tp.B\u1ebfn Tre","BTR_ChauThanh":"Ch\u00e2u Th\u00e0nh","BTR_BinhDai":"B\u00ecnh \u0110\u1ea1i","BTR_ChoLach":"Ch\u1ee3 L\u00e1ch","BTR_BaTri":"Ba Tri","BTR_GiongTrom":"Gi\u1ed3ng Tr\u00f4m","BTR_MoCayBac":"M\u1ecf C\u00e0y B\u1eafc","BTR_MoCayNam":"M\u1ecf C\u00e0y Nam","BTR_ThanhPhu":"Th\u1ea1nh Ph\u00fa","BID_QuyNhon":"TP.Quy Nh\u01a1n","BID_AnNhon":"TX.An Nh\u01a1n","BID_HoaiNhon":"TX.Ho\u00e0i Nh\u01a1n","BID_AnLao":"An L\u00e3o","BID_HoaiAn":"Ho\u00e0i \u00c2n","BID_PhuCat":"Ph\u00f9 C\u00e1t","BID_PhuMy":"Ph\u00f9 M\u1ef9","BID_TaySon":"T\u00e2y S\u01a1n","BID_TuyPhuoc":"Tuy Ph\u01b0\u1edbc","BID_VanCanh":"V\u00e2n Canh","BID_VinhThanh":"V\u0129nh Th\u1ea1nh","BDD_DiAn":"TP.D\u0129 An","BDD_ThuanAn":"TP.Thu\u1eadn An","BDD_ThuDauMot":"TP.Th\u1ee7 D\u1ea7u M\u1ed9t","BDD_BenCat":"TX.B\u1ebfn C\u00e1t","BDD_TanUyen":"TX.T\u00e2n Uy\u00ean","BDD_BauBang":"B\u00e0u B\u00e0ng","BDD_BacTanUyen":"B\u1eafc T\u00e2n Uy\u00ean","BDD_DauTieng":"D\u1ea7u Ti\u1ebfng","BDD_PhuGiao":"Ph\u00fa Gi\u00e1o","BPP_DongXoai":"TP.\u0110\u1ed3ng Xo\u00e0i","BPP_BinhLong":"TX.B\u00ecnh Long","BPP_PhuocLong":"TX.Ph\u01b0\u1edbc Long","BPP_BuDang":"B\u00f9 \u0110\u0103ng","BPP_BuDop":"B\u00f9 \u0110\u1ed1p","BPP_BuGiaMap":"B\u00f9 Gia M\u1eadp","BPP_ChonThanh":"Ch\u01a1n Th\u00e0nh","BPP_DongPhu":"\u0110\u1ed3ng Ph\u00fa","BPP_HonQuan":"H\u1edbn Qu\u1ea3n","BPP_LocNinh":"L\u1ed9c Ninh","BPP_PhuRieng":"Ph\u00fa Ri\u1ec1ng","BTH_BacBinh":"B\u1eafc B\u00ecnh","BTH_DucLinh":"\u0110\u1ee9c Linh","BTH_HamTan":"H\u00e0m T\u00e2n","BTH_HamThuanBac":"H\u00e0m Thu\u1eadn B\u1eafc","BTH_HamThuanNam":"H\u00e0m Thu\u1eadn Nam","BTH_PhuQuy":"Ph\u00fa Qu\u00fd","BTH_TanhLinh":"T\u00e1nh Linh","BTH_TuyPhong":"Tuy Phong","BTH_LaGi":"TX.La Gi","BTH_PhanThiet":"Tp.Phan Thi\u1ebft","CAM_CaMau":"Tp.C\u00e0 Mau","CAM_CaiNuoc":"C\u00e1i N\u01b0\u1edbc","CAM_DamDoi":"\u0110\u1ea7m D\u01a1i","CAM_NamCan":"N\u0103m C\u0103n","CAM_NgocHien":"Ng\u1ecdc Hi\u1ec3n","CAM_PhuTan":"Ph\u00fa T\u00e2n","CAM_ThoiBinh":"Th\u1edbi B\u00ecnh","CAM_TranVanThoi":"Tr\u1ea7n V\u0103n Th\u1eddi","CAM_UMinh":"U Minh","CTH_BinhThuy":"B\u00ecnh Th\u1ee7y","CTH_CaiRang":"C\u00e1i R\u0103ng","CTH_NinhKieu":"Ninh Ki\u1ec1u","CTH_OMon":"\u00d4 M\u00f4n","CTH_ThotNot":"Th\u1ed1t N\u1ed1t","CTH_CoDo":"C\u1edd \u0110\u1ecf","CTH_PhongDien":"Phong \u0110i\u1ec1n","CTH_ThoiLai":"Th\u1edbi Lai","CTH_VinhThanh":"V\u0129nh Th\u1ea1nh","CAB_CaoBang":"Tp.Cao B\u1eb1ng","CAB_BaoLac":"B\u1ea3o L\u1ea1c","CAB_BaoLam":"B\u1ea3o L\u00e2m","CAB_HaLang":"H\u1ea1 Lang","CAB_HaQuang":"H\u00e0 Qu\u1ea3ng","CAB_HoaAn":"H\u00f2a An","CAB_NguyenBinh":"Nguy\u00ean B\u00ecnh","CAB_PhucHoa":"Ph\u1ee5c H\u00f2a","CAB_QuangUyen":"Qu\u1ea3ng Uy\u00ean","CAB_QuangHoa":"Qu\u1ea3ng H\u00f2a","CAB_ThachAn":"Th\u1ea1ch An","CAB_ThongNong":"Th\u00f4ng N\u00f4ng","CAB_TraLinh":"Tr\u00e0 L\u0129nh","CAB_TrungKhanh":"Tr\u00f9ng Kh\u00e1nh","DAN_CamLe":"C\u1ea9m L\u1ec7","DAN_HaiChau":"H\u1ea3i Ch\u00e2u","DAN_LienChieu":"Li\u00ean Chi\u1ec3u","DAN_NguHanhSon":"Ng\u0169 H\u00e0nh S\u01a1n","DAN_SonTra":"S\u01a1n Tr\u00e0","DAN_ThanhKhe":"Thanh Kh\u00ea","DAN_HoaVang":"H\u00f2a Vang","DAN_HoangSa":"Ho\u00e0ng Sa","DKL_BuonMaThuot":"Tp.Bu\u00f4n Ma Thu\u1ed9t","DKL_TXBuonHo":"TX.Bu\u00f4n H\u1ed3","DKL_BuonDon":"Bu\u00f4n \u0110\u00f4n","DKL_CuKuin":"C\u01b0 Kuin","DKL_CuMgar":"C\u01b0 M'gar","DKL_Drak":"M'\u0110r\u1eafk","DKL_EaHleo":"Ea H'leo","DKL_EaKar":"Ea Kar","DKL_EaSup":"Ea S\u00fap","DKL_KrongAna":"Kr\u00f4ng Ana","DKL_KrongBong":"Kr\u00f4ng B\u00f4ng","DKL_KrongBuk":"Kr\u00f4ng B\u00fak","DKL_KrongNang":"Kr\u00f4ng N\u0103ng","DKL_KrongPak":"Kr\u00f4ng Pak","DKL_Lak":"L\u1eafk","DKN_GiaNghia":"TP.Gia Ngh\u0129a","DKN_DakRLap":"\u0110\u1eafk RL\u1ea5p","DKN_DakMil":"\u0110\u1eafk Mil","DKN_CuJut":"C\u01b0 J\u00fat","DKN_DakSong":"\u0110\u1eafk Song","DKN_KrongNo":"Kr\u00f4ng N\u00f4","DKN_DakGLong":"\u0110\u1eafk GLong","DKN_TuyDuc":"Tuy \u0110\u1ee9c","DIB_DIBPhu":"Tp.\u0110i\u1ec7n Bi\u00ean Ph\u1ee7","DIB_MuongLay":"TX.M\u01b0\u1eddng Lay","DIB_DienBien":"\u0110i\u1ec7n Bi\u00ean","DIB_DIBDong":"\u0110i\u1ec7n Bi\u00ean \u0110\u00f4ng","DIB_MuongAng":"M\u01b0\u1eddng \u1ea2ng","DIB_MuongCha":"M\u01b0\u1eddng Ch\u00e0","DIB_MuongNhe":"M\u01b0\u1eddng Nh\u00e9","DIB_NamPo":"N\u1eadm P\u1ed3","DIB_TuaChua":"T\u1ee7a Ch\u00f9a","DIB_TuanGiao":"Tu\u1ea7n Gi\u00e1o","DNA_BienHoa":"TP.Bi\u00ean H\u00f2a","DNA_LongKhanh":"TP.Long Kh\u00e1nh","DNA_CamMy":"C\u1ea9m M\u1ef9","DNA_DinhQuan":"\u0110\u1ecbnh Qu\u00e1n","DNA_LongThanh":"Long Th\u00e0nh","DNA_NhonTrach":"Nh\u01a1n Tr\u1ea1ch","DNA_TanPhu":"T\u00e2n Ph\u00fa","DNA_ThongNhat":"Th\u1ed1ng Nh\u1ea5t","DNA_TrangBom":"Tr\u1ea3ng Bom","DNA_VinhCuu":"V\u0129nh C\u1eedu","DNA_XuanLoc":"Xu\u00e2n L\u1ed9c","DTP_CaoLanh":"TP.Cao L\u00e3nh","DTP_SaDec":"TP.Sa \u0110\u00e9c","DTP_HongNgu":"TP.H\u1ed3ng Ng\u1ef1","DTP_ChauThanh":"Ch\u00e2u Th\u00e0nh","DTP_LaiVung":"Lai Vung","DTP_LapVo":"L\u1ea5p V\u00f2","DTP_TamNong":"Tam N\u00f4ng","DTP_TanHong":"T\u00e2n H\u1ed3ng","DTP_ThanhBinh":"Thanh B\u00ecnh","DTP_ThapMuoi":"Th\u00e1p M\u01b0\u1eddi","GLA_Pleiku":"Tp.Pleiku","GLA_AnKhe":"TX.An Kh\u00ea","GLA_AyunPa":"TX.Ayun Pa","GLA_ChuPah":"Ch\u01b0 P\u0103h","GLA_ChuProng":"Ch\u01b0 Pr\u00f4ng","GLA_ChuPuh":"Ch\u01b0 P\u01b0h","GLA_ChuSe":"Ch\u01b0 S\u00ea","GLA_DakDoa":"\u0110\u1eafk \u0110oa","GLA_DakPo":"\u0110ak P\u01a1","GLA_DucCo":"\u0110\u1ee9c C\u01a1","GLA_IaGrai":"Ia Grai","GLA_IaPa":"Ia Pa","GLA_KBang":"K'Bang","GLA_KongChro":"K\u00f4ng Chro","GLA_KrongPa":"Kr\u00f4ng Pa","GLA_MangYang":"Mang Yang","GLA_PhuThien":"Ph\u00fa Thi\u1ec7n","SGN_Q1":"Q1","SGN_Q2":"Q2","SGN_Q3":"Q3","SGN_Q4":"Q4","SGN_Q5":"Q5","SGN_Q6":"Q6","SGN_Q7":"Q7","SGN_Q8":"Q8","SGN_Q9":"Q9","SGN_Q10":"Q10","SGN_Q11":"Q11","SGN_Q12":"Q12","SGN_BinhTan":"B\u00ecnh T\u00e2n","SGN_BinhThanh":"B\u00ecnh Th\u1ea1nh","SGN_GoVap":"G\u00f2 V\u1ea5p","SGN_PhuNhuan":"Ph\u00fa Nhu\u1eadn","SGN_TanBinh":"T\u00e2n B\u00ecnh","SGN_TanPhu":"T\u00e2n Ph\u00fa","SGN_ThuDuc":"Th\u1ee7 \u0110\u1ee9c","SGN_BinhChanh":"B\u00ecnh Ch\u00e1nh","SGN_CanGio":"C\u1ea7n Gi\u1edd","SGN_CuChi":"C\u1ee7 Chi","SGN_HocMon":"H\u00f3c M\u00f4n","SGN_NhaBe":"Nh\u00e0 B\u00e8","HAG_HaGiang":"Tp.H\u00e0 Giang","HAG_BacMe":"B\u1eafc M\u00ea","HAG_BacQuang":"B\u1eafc Quang","HAG_DongVan":"\u0110\u1ed3ng V\u0103n","HAG_HoangSuPhi":"Ho\u00e0ng Su Ph\u00ec","HAG_MeoVac":"M\u00e8o V\u1ea1c","HAG_QuanBa":"Qu\u1ea3n B\u1ea1","HAG_QuangBinh":"Quang B\u00ecnh","HAG_ViXuyen":"V\u1ecb Xuy\u00ean","HAG_XinMan":"X\u00edn M\u1ea7n","HAG_YenMinh":"Y\u00ean Minh","HNM_PhuLy":"TP.Ph\u1ee7 L\u00fd","HNM_DuyTien":"TX.Duy Ti\u00ean","HNM_BinhLuc":"B\u00ecnh L\u1ee5c","HNM_KimBang":"Kim B\u1ea3ng","HNM_LyNhan":"L\u00fd Nh\u00e2n","HNM_ThanhLiem":"Thanh Li\u00eam","HAN_BaDinh":"Ba \u0110\u00ecnh","HAN_CauGiay":"C\u1ea7u Gi\u1ea5y","HAN_DongDa":"\u0110\u1ed1ng \u0110a","HAN_HaDong":"H\u00e0 \u0110\u00f4ng","HAN_HaiBaTrung":"Hai B\u00e0 Tr\u01b0ng","HAN_HoanKiem":"Ho\u00e0n Ki\u1ebfm","HAN_HoangMai":"Ho\u00e0ng Mai","HAN_LongBien":"Long Bi\u00ean","HAN_TayHo":"T\u00e2y H\u1ed3","HAN_ThanhXuan":"Thanh Xu\u00e2n","HAN_SonTay":"TX.S\u01a1n T\u00e2y","HAN_BaVi":"Ba V\u00ec","HAN_ChuongMy":"Ch\u01b0\u01a1ng M\u1ef9","HAN_DanPhuong":"\u0110an Ph\u01b0\u1ee3ng","HAN_DongAnh":"\u0110\u00f4ng Anh","HAN_GiaLam":"Gia L\u00e2m","HAN_HoaiDuc":"Ho\u00e0i \u0110\u1ee9c","HAN_MeLinh":"M\u00ea Linh","HAN_MyDuc":"M\u1ef9 \u0110\u1ee9c","HAN_PhuXuyen":"Ph\u00fa Xuy\u00ean","HAN_PhucTho":"Ph\u00fac Th\u1ecd","HAN_QuocOai":"Qu\u1ed1c Oai","HAN_SocSon":"S\u00f3c S\u01a1n","HAN_ThachThat":"Th\u1ea1ch Th\u1ea5t","HAN_ThanhOai":"Thanh Oai","HAN_ThanhTri":"Thanh Tr\u00ec","HAN_ThuongTin":"Th\u01b0\u1eddng T\u00edn","HAN_TuLiem":"T\u1eeb Li\u00eam","HAN_UngHoa":"\u1ee8ng H\u00f2a","HAT_HaTinh":"TP.H\u00e0 T\u0129nh","HAT_HongLinh":"TX.H\u1ed3ng L\u0129nh","HAT_KyAnh":"TX.K\u1ef3 Anh","HAT_NghiXuan":"Nghi Xu\u00e2n","HAT_DucTho":"\u0110\u1ee9c Th\u1ecd","HAT_HuongSon":"H\u01b0\u01a1ng S\u01a1n","HAT_HuongKhe":"H\u01b0\u01a1ng Kh\u00ea","HAT_VuQuang":"V\u0169 Quang","HAT_CanLoc":"Can L\u1ed9c","HAT_LocHa":"L\u1ed9c H\u00e0","HAT_ThachHa":"Th\u1ea1ch H\u1ea1","HAT_CamXuyen":"C\u1ea9m Xuy\u00ean","HDG_HaiDuong":"TP.H\u1ea3i D\u01b0\u01a1ng","HDG_ChiLinh":"TP.Ch\u00ed Linh","HDG_KinhMon":"TX.Kinh M\u00f4n","HDG_BinhGiang":"B\u00ecnh Giang","HDG_CamGiang":"C\u1ea9m Gi\u00e0ng","HDG_GiaLoc":"Gia L\u1ed9c","HDG_KimThanh":"Kim Th\u00e0nh","HDG_NamSach":"Nam S\u00e1ch","HDG_NinhGiang":"Ninh Giang","HDG_ThanhHa":"Thanh H\u00e0","HDG_ThanhMien":"Thanh Mi\u1ec7n","HDG_TuKy":"T\u1ee9 K\u1ef3","HPP_AnDuong":"An D\u01b0\u01a1ng","HPP_AnLao":"An L\u00e3o","HPP_BachLongVi":"B\u1ea1ch Long V\u0129","HPP_CatHai":"C\u00e1t H\u1ea3i","HPP_KienThuy":"Ki\u1ebfn Th\u1ee5y","HPP_ThuyNguyen":"Th\u1ee7y Nguy\u00ean","HPP_TienLang":"Ti\u00ean L\u00e3ng","HPP_VinhBao":"V\u0129nh B\u1ea3o","HPP_DoSon":"\u0110\u1ed3 S\u01a1n","HPP_DuongKinh":"D\u01b0\u01a1ng Kinh","HPP_HaiAn":"H\u1ea3i An","HPP_HongBang":"H\u1ed3ng B\u00e0ng","HPP_KienAn":"Ki\u1ebfn An","HPP_LeChan":"L\u00ea Ch\u00e2n","HPP_NgoQuyen":"Ng\u00f4 Quy\u1ec1n","HGG_NgaBay":"TP.Ng\u00e3 B\u1ea3y","HGG_ViThanh":"TP.V\u1ecb Thanh","HGG_TX.LongMy":"TX.Long M\u1ef9","HGG_ChauThanh":"Ch\u00e2u Th\u00e0nh","HGG_ChauThanhA":"Ch\u00e2u Th\u00e0nh A","HGG_LongMy":"Long M\u1ef9","HGG_PhungHiep":"Ph\u1ee5ng Hi\u1ec7p","HGG_ViThuy":"V\u1ecb Th\u1ee7y","HOB_HoaBinh":"Tp.H\u00f2a B\u00ecnh","HOB_CaoPhong":"Cao Phong","HOB_DaBac":"\u0110\u00e0 B\u1eafc","HOB_KimBoi":"Kim B\u00f4i","HOB_KySon":"K\u1ef3 S\u01a1n","HOB_LacSon":"L\u1ea1c S\u01a1n","HOB_LacThuy":"L\u1ea1c Th\u1ee7y","HOB_LuongSon":"L\u01b0\u01a1ng S\u01a1n","HOB_MaiChau":"Mai Ch\u00e2u","HOB_TanLac":"T\u00e2n L\u1ea1c","HOB_YenThuy":"Y\u00ean Th\u1ee7y","HYN_HungYen":"TP.H\u01b0ng Y\u00ean","HYN_MyHao":"TX.M\u1ef9 H\u00e0o","HYN_AnThi":"\u00c2n Thi","HYN_KhoaiChau":"Kho\u00e1i Ch\u00e2u","HYN_KimDong":"Kim \u0110\u1ed9ng","HYN_PhuCu":"Ph\u00f9 C\u1eeb","HYN_TienLu":"Ti\u00ean L\u1eef","HYN_VanGiang":"V\u0103n Giang","HYN_VanLam":"V\u0103n L\u00e2m","HYN_YenMy":"Y\u00ean M\u1ef9","KHH_NhaTrang":"Tp.Nha Trang","KHH_CamRanh":"Tp.Cam Ranh","KHH_NinhHoa":"TX.Ninh H\u00f2a","KHH_CamLam":"Cam L\u00e2m","KHH_DienKhanh":"Di\u00ean Kh\u00e1nh","KHH_KhanhSon":"Kh\u00e1nh S\u01a1n","KHH_KhanhVinh":"Kh\u00e1nh V\u0129nh","KHH_TruongSa":"Tr\u01b0\u1eddng Sa","KHH_VanNinh":"V\u1ea1n Ninh","KIG_HaTien":"TP.H\u00e0 Ti\u00ean","KIG_PhuQuoc":"TP.Ph\u00fa Qu\u1ed1c","KIG_RachGia":"TP.R\u1ea1ch Gi\u00e1","KIG_AnBien":"An Bi\u00ean","KIG_AnMinh":"An Minh","KIG_ChauThanh":"Ch\u00e2u Th\u00e0nh","KIG_GiangThanh":"Giang Th\u00e0nh","KIG_GiongRieng":"Gi\u1ed3ng Ri\u1ec1ng","KIG_GoQuao":"G\u00f2 Quao","KIG_HonDat":"H\u00f2n \u0110\u1ea5t","KIG_KienHai":"Ki\u00ean H\u1ea3i","KIG_KienLuong":"Ki\u00ean L\u01b0\u01a1ng","KIG_TanHiep":"T\u00e2n Hi\u1ec7p","KIG_VinhThuan":"V\u0129nh Thu\u1eadn","KIG_UMinhThuong":"U Minh Th\u01b0\u1ee3ng","KTU_KonTum":"TP.Kon Tum","KTU_DakGlei":"\u0110\u1eafk Glei","KTU_DakHa":"\u0110\u1eafk H\u00e0","KTU_DakTo":"\u0110\u1eafk T\u00f4","KTU_IaHDrai":"Ia H' Drai","KTU_KonPlong":"Kon Pl\u00f4ng","KTU_KonRay":"Kon R\u1eaby","KTU_NgocHoi":"Ng\u1ecdc H\u1ed3i","KTU_SaThay":"Sa Th\u1ea7y","KTU_TuMoRong":"Tu M\u01a1 R\u00f4ng","LAB_LaiChau":"Tp.Lai Ch\u00e2u","LAB_MuongTe":"M\u01b0\u1eddng T\u00e8","LAB_NamNhun":"N\u1eadm Nh\u00f9n","LAB_PhongTho":"Phong Th\u1ed5","LAB_SinHo":"S\u00ecn H\u1ed3","LAB_TamDuong":"Tam \u0110\u01b0\u1eddng","LAB_TanUyen":"T\u00e2n Uy\u00ean","LAB_ThanUyen":"Than Uy\u00ean","LDG_BaoLam":"B\u1ea3o L\u00e2m","LDG_CatTien":"C\u00e1t Ti\u00ean","LDG_DaHuoai":"\u0110\u1ea1 Huoai","LDG_DaTeh":"\u0110\u1ea1 T\u1ebbh","LDG_DamRong":"\u0110am R\u00f4ng","LDG_DiLinh":"Di Linh","LDG_DonDuong":"\u0110\u01a1n D\u01b0\u01a1ng","LDG_DucTrong":"\u0110\u1ee9c Tr\u1ecdng","LDG_LacDuong":"L\u1ea1c D\u01b0\u01a1ng","LDG_LamHa":"L\u00e2m H\u00e0","LDG_BaoLoc":"Tp.B\u1ea3o L\u1ed9c","LDG_DaLat":"Tp.\u0110\u00e0 L\u1ea1t","LAS_BacSon":"B\u1eafc S\u01a1n","LAS_BinhGia":"B\u00ecnh Gia","LAS_CaoLoc":"Cao L\u1ed9c","LAS_ChiLang":"Chi L\u0103ng","LAS_DinhLap":"\u0110\u00ecnh L\u1eadp","LAS_HuuLung":"H\u1eefu L\u0169ng","LAS_LocBinh":"L\u1ed9c B\u00ecnh","LAS_LangSon":"TP. L\u1ea1ng S\u01a1n","LAS_TrangDinh":"Tr\u00e0ng \u0110\u1ecbnh","LAS_VanLang":"V\u0103n L\u00e3ng","LAS_VanQuan":"V\u0103n Quan","LAC_LaoCai":"Tp.L\u00e0o Cai","LAC_SaPa":"TX.Sa Pa","LAC_BaoThang":"B\u1ea3o Th\u1eafng","LAC_BaoYen":"B\u1ea3o Y\u00ean","LAC_BatXat":"B\u00e1t X\u00e1t","LAC_BacHa":"B\u1eafc H\u00e0","LAC_MuongKhuong":"M\u01b0\u1eddng Kh\u01b0\u01a1ng","LAC_SiMaCai":"Si Ma Cai","LAC_VanBan":"V\u0103n B\u00e0n","LAN_TanAn":"TP.T\u00e2n An","LAN_KienTuong":"TX.Ki\u1ebfn T\u01b0\u1eddng","LAN_BenLuc":"B\u1ebfn L\u1ee9c","LAN_CanDuoc":"C\u1ea7n \u0110\u01b0\u1edbc","LAN_CanGiuoc":"C\u1ea7n Giu\u1ed9c","LAN_ChauThanh":"Ch\u00e2u Th\u00e0nh","LAN_DucHoa":"\u0110\u1ee9c H\u00f2a","LAN_DucHue":"\u0110\u1ee9c Hu\u1ec7","LAN_MocHoa":"M\u1ed9c H\u00f3a","LAN_TanHuong":"T\u00e2n H\u01b0\u01a1ng","LAN_TanThanh":"T\u00e2n Th\u1ea1nh","LAN_TanTru":"T\u00e2n Tr\u1ee5","LAN_ThanhHoa":"Th\u1ea1nh H\u00f3a","LAN_ThuThua":"Th\u1ee7 Th\u1eeba","LAN_VinhHung":"V\u0129nh H\u01b0ng","NAD_NamDinh":"Tp.Nam \u0110\u1ecbnh","NAD_NghiaHung":"Ngh\u0129a H\u01b0ng","NAD_HaiHau":"H\u1ea3i H\u1eadu","NAD_GiaoThuy":"Giao Th\u1ee7y","NAD_VuBan":"V\u1ee5 B\u1ea3n","NAD_YYen":"\u00dd Y\u00ean","NAD_TrucNinh":"Tr\u1ef1c Ninh","NAD_XuanTruong":"Xu\u00e2n Tr\u01b0\u1eddng","NAD_NamTruc":"Nam Tr\u1ef1c","NAD_MyLoc":"M\u1ef9 L\u1ed9c","NGA_Vinh":"Tp.Vinh","NGA_CuaLo":"TX.C\u1eeda L\u00f2","NGA_ThaiHoa":"TX.Th\u00e1i H\u00f2a","NGA_HoangMai":"TX.Ho\u00e0ng Mai","NGA_AnhSon":"Anh S\u01a1n","NGA_ConCuong":"Con Cu\u00f4ng","NGA_DienChau":"Di\u1ec5n Ch\u00e2u","NGA_DoLuong":"\u0110\u00f4 L\u01b0\u01a1ng","NGA_HungNguyen":"H\u01b0ng Nguy\u00ean","NGA_QuyChau":"Qu\u1ef3 Ch\u00e2u","NGA_KySon":"K\u1ef3 S\u01a1n","NGA_NamDan":"Nam \u0110\u00e0n","NGA_NghiLoc":"Nghi L\u1ed9c","NGA_NghiaDan":"Ngh\u0129a \u0110\u00e0n","NGA_QuePhong":"Qu\u1ebf Phong","NGA_QuyHop":"Qu\u1ef3 H\u1ee3p","NGA_QuynhLuu":"Qu\u1ef3nh L\u01b0u","NGA_TanKy":"T\u00e2n K\u1ef3","NGA_ThanhChuong":"Thanh Ch\u01b0\u01a1ng","NGA_TuongDuong":"T\u01b0\u01a1ng D\u01b0\u01a1ng","NGA_YenThanh":"Y\u00ean Th\u00e0nh","NIB_NinhBinh":"TP.Ninh B\u00ecnh","NIB_TamDiep":"TP.Tam \u0110i\u1ec7p","NIB_GiaVien":"Gia Vi\u1ec5n","NIB_HoaLu":"Hoa L\u01b0","NIB_KimSon":"Kim S\u01a1n","NIB_NhoQuan":"Nho Quan","NIB_YenKhanh":"Y\u00ean Kh\u00e1nh","NIB_YenMo":"Y\u00ean M\u00f4","NIT_BacAi":"B\u00e1c \u00c1i","NIT_NinhHai":"Ninh H\u1ea3i","NIT_NinhPhuoc":"Ninh Ph\u01b0\u1edbc","NIT_NinhSon":"Ninh S\u01a1n","NIT_ThuanBac":"Thu\u1eadn B\u1eafc","NIT_ThuanNam":"Thu\u1eadn Nam","NIT_P.Rang-T.Cham":"Tp. P.Rang-T.Ch\u00e0m","PTH_VietTri":"Tp.Vi\u1ec7t Tr\u00ec","PTH_PhuTho":"TX.Ph\u00fa Th\u1ecd","PTH_CamKhe":"C\u1ea9m Kh\u00ea","PTH_DoanHung":"\u0110oan H\u00f9ng","PTH_HaHoa":"H\u1ea1 H\u00f2a","PTH_LamThao":"L\u00e2m Thao","PTH_PhuNinh":"Ph\u00f9 Ninh","PTH_TamNong":"Tam N\u00f4ng","PTH_TanSon":"T\u00e2n S\u01a1n","PTH_ThanhBa":"Thanh Ba","PTH_ThanhSon":"Thanh S\u01a1n","PTH_ThanhThuy":"Thanh Th\u1ee7y","PTH_YenLap":"Y\u00ean L\u1eadp","PYE_TuyHoa":"TP.Tuy Ho\u00e0","PYE_DongHoa":"TX.\u0110\u00f4ng Ho\u00e0","PYE_SongCau":"TX.S\u00f4ng C\u1ea7u","PYE_DongXuan":"\u0110\u1ed3ng Xu\u00e2n","PYE_PhuHoa":"Ph\u00fa Ho\u00e0","PYE_SonHoa":"S\u01a1n Ho\u00e0","PYE_SongHinh":"S\u00f4ng Hinh","PYE_TayHoa":"T\u00e2y Ho\u00e0","PYE_TuyAn":"Tuy An","QUB_DongHoi":"TP.\u0110\u1ed3ng H\u1edbi","QUB_BaDon":"TX.Ba \u0110\u1ed3n","QUB_BoTrach":"B\u1ed1 Tr\u1ea1ch","QUB_LeThuy":"L\u1ec7 Th\u1ee7y","QUB_MinhHoa":"Minh H\u00f3a","QUB_QuangNinh":"Qu\u1ea3ng Ninh","QUB_QuangTrach":"Qu\u1ea3ng Tr\u1ea1ch","QUB_TuyenHoa":"Tuy\u00ean H\u00f3a","QUN_TamKy":"TP.Tam K\u1ef3","QUN_HoiAn":"TP.H\u1ed9i An","QUN_DienBan":"TX.\u0110i\u1ec7n B\u00e0n","QUN_BacTraMy":"B\u1eafc Tr\u00e0 My","QUN_DuyXuyen":"Duy Xuy\u00ean","QUN_DaiLoc":"\u0110\u1ea1i L\u1ed9c","QUN_DongGiang":"\u0110\u00f4ng Giang","QUN_HiepDuc":"Hi\u1ec7p \u0110\u1ee9c","QUN_NamGiang":"Nam Giang","QUN_NamTraMy":"Nam Tr\u00e0 My","QUN_NongSon":"N\u00f4ng S\u01a1n","QUN_NuiThanh":"N\u00fai Th\u00e0nh","QUN_PhuNinh":"Ph\u00fa Ninh","QUN_PhuocSon":"Ph\u01b0\u1edbc S\u01a1n","QUN_QueSon":"Qu\u1ebf S\u01a1n","QUN_TayGiang":"T\u00e2y Giang","QUN_ThangBinh":"Th\u0103ng B\u00ecnh","QUN_TienPhuoc":"Ti\u00ean Ph\u01b0\u1edbc","QNG_Tp.QNG":"TP.Qu\u1ea3ng Ng\u00e3i","QNG_DucPho":"TX.\u0110\u1ee9c Ph\u1ed5","QNG_QuangNgai":"Qu\u1ea3ng Ng\u00e3i","QNG_BaTo":"Ba T\u01a1","QNG_BinhSon":"B\u00ecnh S\u01a1n","QNG_LySon":"L\u00fd S\u01a1n","QNG_MinhLong":"Minh Long","QNG_MoDuc":"M\u1ed9 \u0110\u1ee9c","QNG_NghiaHanh":"Ngh\u0129a H\u00e0nh","QNG_SonHa":"S\u01a1n H\u00e0","QNG_SonTay":"S\u01a1n T\u00e2y","QNG_SonTinh":"S\u01a1n T\u1ecbnh","QNG_TayTra":"T\u00e2y Tr\u00e0","QNG_TraBong":"Tr\u00e0 B\u1ed3ng","QNG_TuNghia":"T\u01b0 Ngh\u0129a","QNI_TXCamPha":"TP.C\u1ea9m Ph\u1ea3","QNI_TXUongBi":"TP.U\u00f4ng B\u00ed","QNI_Tp.HaLong":"TP.H\u1ea1 Long","QNI_Tp.MongCai":"TP.M\u00f3ng C\u00e1i","QNI_DongTrieu":"TX.\u0110\u00f4ng Tri\u1ec1u","QNI_QuangYen":"TX.Qu\u1ea3ng Y\u00ean","QNI_BaChe":"Ba Ch\u1ebd","QNI_BinhLieu":"B\u00ecnh Li\u00eau","QNI_CoTo":"C\u00f4 T\u00f4","QNI_DamHa":"\u0110\u1ea7m H\u00e0","QNI_HaiHa":"H\u1ea3i H\u00e0","QNI_HoangBo":"Ho\u00e0ng B\u1ed3","QNI_TienYen":"Ti\u00ean Y\u00ean","QNI_VanDon":"V\u00e2n \u0110\u1ed3n","QNI_YenHung":"Y\u00ean H\u01b0ng","QUT_DongHa":"Tp.\u0110\u00f4ng H\u00e0","QUT_QuangTri":"TX.Qu\u1ea3ng Tr\u1ecb","QUT_CamLo":"Cam L\u1ed9","QUT_ConCo":"C\u1ed3n C\u1ecf","QUT_DaKrong":"\u0110a Kr\u00f4ng","QUT_GioLinh":"Gio Linh","QUT_HaiLang":"H\u1ea3i L\u0103ng","QUT_HuongHoa":"H\u01b0\u1edbng H\u00f3a","QUT_TrieuPhong":"Tri\u1ec7u Phong","QUT_VinhLinh":"V\u0129nh Linh","STR_SocTrang":"TP.S\u00f3c Tr\u0103ng","STR_NgaNam":"TX.Ng\u00e3 N\u0103m","STR_VinhChau":"TX.V\u0129nh Ch\u00e2u","STR_ChauThanh":"Ch\u00e2u Th\u00e0nh","STR_CuLaoDung":"C\u00f9 Lao Dung","STR_KeSach":"K\u1ebf S\u00e1ch","STR_LongPhu":"Long Ph\u00fa","STR_MyTu":"M\u1ef9 T\u00fa","STR_MyXuyen":"M\u1ef9 Xuy\u00ean","STR_ThanhTri":"Th\u1ea1nh Tr\u1ecb","STR_TranDe":"Tr\u1ea7n \u0110\u1ec1","SLA_SonLa":"Tp.S\u01a1n La","SLA_BacYen":"B\u1eafc Y\u00ean","SLA_MaiSon":"Mai S\u01a1n","SLA_MocChau":"M\u1ed9c Ch\u00e2u","SLA_MuongLa":"M\u01b0\u1eddng La","SLA_PhuYen":"Ph\u00f9 Y\u00ean","SLA_QuynhNhai":"Qu\u1ef3nh Nhai","SLA_SongMa":"S\u00f4ng M\u00e3","SLA_SopCop":"S\u1ed1p C\u1ed9p","SLA_ThuanChau":"Thu\u1eadn Ch\u00e2u","SLA_VanHo":"V\u00e2n H\u1ed3","SLA_YenChau":"Y\u00ean Ch\u00e2u","TNI_HoaThanh":"TX.H\u00f2a Th\u00e0nh","TNI_TayNinh":"TX.T\u00e2y Ninh","TNI_TrangBang":"TX.Tr\u1ea3ng B\u00e0ng","TNI_BenCau":"B\u1ebfn C\u1ea7u","TNI_ChauThanh":"Ch\u00e2u Th\u00e0nh","TNI_DuongMinhChau":"D\u01b0\u01a1ng Minh Ch\u00e2u","TNI_GoDau":"G\u00f2 D\u1ea7u","TNI_TanBien":"T\u00e2n Bi\u00ean","TNI_TanChau":"T\u00e2n Ch\u00e2u","THB_ThaiBinh":"Tp.Th\u00e1i B\u00ecnh","THB_DongHung":"\u0110\u00f4ng H\u01b0ng","THB_HungHa":"H\u01b0ng H\u00e0","THB_KienXuong":"Ki\u1ebfn X\u01b0\u01a1ng","THB_QuynhPhu":"Qu\u1ef3nh Ph\u1ee5","THB_ThaiThuy":"Th\u00e1i Th\u1ee5y","THB_TienHai":"Ti\u1ec1n H\u1ea3i","THB_VuThu":"V\u0169 Th\u01b0","THN_ThaiNguyen":"Tp.Th\u00e1i Nguy\u00ean","THN_PhoYen":"TP.Ph\u1ed5 Y\u00ean","THN_SongCong":"TP.S\u00f4ng C\u00f4ng","THN_DaiTu":"\u0110\u1ea1i T\u1eeb","THN_DinhHoa":"\u0110\u1ecbnh H\u00f3a","THN_DongHy":"\u0110\u1ed3ng H\u1ef7","THN_PhuBinh":"Ph\u00fa B\u00ecnh","THN_PhuLuong":"Ph\u00fa L\u01b0\u01a1ng","THN_VoNhai":"V\u00f5 Nhai","THH_ThanhHoa":"TP.Thanh H\u00f3a","THH_SamSon":"TP.S\u1ea7m S\u01a1n","THH_BimSon":"TX.B\u1ec9m S\u01a1n","THH_NghiSon":"TX.Nghi S\u01a1n","THH_BaThuoc":"B\u00e1 Th\u01b0\u1edbc","THH_CamThuy":"C\u1ea9m Th\u1ee7y","THH_DongSon":"\u0110\u00f4ng S\u01a1n","THH_HaTrung":"H\u00e0 Trung","THH_HauLoc":"H\u1eadu L\u1ed9c","THH_HoangHoa":"Ho\u1eb1ng H\u00f3a","THH_LangChanh":"Lang Ch\u00e1nh","THH_MuongLat":"M\u01b0\u1eddng L\u00e1t","THH_NgaSon":"Nga S\u01a1n","THH_NgocLac":"Ng\u1ecdc L\u1eb7c","THH_NhuThanh":"Nh\u01b0 Thanh","THH_NhuXuan":"Nh\u01b0 Xu\u00e2n","THH_NongCong":"N\u00f4ng C\u1ed1ng","THH_QuanHoa":"Quan H\u00f3a","THH_QuanSon":"Quan S\u01a1n","THH_QuangXuong":"Qu\u1ea3ng X\u01b0\u01a1ng","THH_ThachThanh":"Th\u1ea1ch Th\u00e0nh","THH_ThieuHoa":"Thi\u1ec7u H\u00f3a","THH_ThoXuan":"Th\u1ecd Xu\u00e2n","THH_ThuongXuan":"Th\u01b0\u1eddng Xu\u00e2n","THH_TinhGia":"T\u0129nh Gia","THH_TrieuSon":"Tri\u1ec7u S\u01a1n","THH_VinhLoc":"V\u0129nh L\u1ed9c","THH_YenDinh":"Y\u00ean \u0110\u1ecbnh","TTH_Hue":"TP. Hu\u1ebf","TTH_TXHuongThuy":"TX H\u01b0\u01a1ng Th\u1ee7y","TTH_HuongTra":"TX.H\u01b0\u01a1ng Tr\u00e0","TTH_ALuoi":"A L\u01b0\u1edbi","TTH_NamDong":"Nam \u0110\u00f4ng","TTH_PhongDien":"Phong \u0110i\u1ec1n","TTH_PhuLoc":"Ph\u00fa L\u1ed9c","TTH_PhuVang":"Ph\u00fa Vang","TTH_QuangDien":"Qu\u1ea3ng \u0110i\u1ec1n","TIG_MyTho":"TP.M\u1ef9 Tho","TIG_TX.CaiLay":"TX.Cai L\u1eady","TIG_GoCong":"TX.G\u00f2 C\u00f4ng","TIG_CaiLay":"Cai L\u1eady","TIG_CaiBe":"C\u00e1i B\u00e8","TIG_ChauThanh":"Ch\u00e2u Th\u00e0nh","TIG_ChoGao":"Ch\u1ee3 G\u1ea1o","TIG_GoCongDong":"G\u00f2 C\u00f4ng \u0110\u00f4ng","TIG_GoCongTay":"G\u00f2 C\u00f4ng T\u00e2y","TIG_TanPhuoc":"T\u00e2n Ph\u01b0\u1edbc","TIG_TanPhuDong":"T\u00e2n Ph\u00fa \u0110\u00f4ng","TRV_TraVinh":"TP.Tr\u00e0 Vinh","TRV_TX.DuyenHai":"TX.Duy\u00ean H\u1ea3i","TRV_CangLong":"C\u00e0ng Long","TRV_CauKe":"C\u1ea7u K\u00e8","TRV_CauNgang":"C\u1ea7u Ngang","TRV_ChauThanh":"Ch\u00e2u Th\u00e0nh","TRV_DuyenHai":"Duy\u00ean H\u1ea3i","TRV_TieuCan":"Ti\u1ec3u C\u1ea7n","TRV_TraCu":"Tr\u00e0 C\u00fa","TQU_TuyenQuang":"Tp.Tuy\u00ean Quang","TQU_ChiemHoa":"Chi\u00eam H\u00f3a","TQU_HamYen":"H\u00e0m Y\u00ean","TQU_LamBinh":"L\u00e2m B\u00ecnh","TQU_NaHang":"Na Hang","TQU_SonDuong":"S\u01a1n D\u01b0\u01a1ng","TQU_YenSon":"Y\u00ean S\u01a1n","VLG_VinhLong":"TP.V\u0129nh Long","VLG_BinhMinh":"TX.B\u00ecnh Minh","VLG_BinhTan":"B\u00ecnh T\u00e2n","VLG_LongHo":"Long H\u1ed3","VLG_MangThit":"Mang Th\u00edt","VLG_TamBinh":"Tam B\u00ecnh","VLG_TraOn":"Tr\u00e0 \u00d4n","VLG_VungLiem":"V\u0169ng Li\u00eam","VPH_VinhYen":"TP.V\u0129nh Y\u00ean","VPH_PhucYen":"TP.Ph\u00fac Y\u00ean","VPH_BinhXuyen":"B\u00ecnh Xuy\u00ean","VPH_SongLo":"S\u00f4ng L\u00f4","VPH_LapThach":"L\u1eadp Th\u1ea1ch","VPH_TamDuong":"Tam D\u01b0\u01a1ng","VPH_TamDao":"Tam \u0110\u1ea3o","VPH_VinhTuong":"V\u0129nh T\u01b0\u1eddng","VPH_YenLac":"Y\u00ean L\u1ea1c","YEB_YenBai":"Tp.Y\u00ean B\u00e1i","YEB_NghiaLo":"TX.Ngh\u0129a L\u1ed9","YEB_LucYen":"L\u1ee5c Y\u00ean","YEB_YenBinh":"Y\u00ean B\u00ecnh","YEB_VanYen":"V\u0103n Y\u00ean","YEB_VanChan":"V\u0103n Ch\u1ea5n","YEB_MuCangChai":"M\u00f9 C\u0103ng Ch\u1ea3i","YEB_TramTau":"Tr\u1ea1m T\u1ea5u","YEB_TranYen":"Tr\u1ea5n Y\u00ean"},"periodic_purchase_dom":{"":"","1 week":"1 Tu\u1ea7n","2 weeks":"2 Tu\u1ea7n","3 weeks":"3 Tu\u1ea7n","1 month":"1 Th\u00e1ng","2 months":"2 Th\u00e1ng","3 months":"3 Th\u00e1ng","4 months":"4 Th\u00e1ng","5 months":"5 Th\u00e1ng","6 months":"6 Th\u00e1ng","9 months":"9 Th\u00e1ng","1 year":"1 N\u0103m","2 years":"2 N\u0103m","3 years":"3 N\u0103m","4 years":"4 N\u0103m","5 years":"5 N\u0103m","6 years":"6 N\u0103m"},"ts_customer_need_dom":{"":"","no_need":"Kh\u00f4ng c\u00f3 nhu c\u1ea7u","were_supplier":"\u0110\u00e3 c\u00f3 NCC","send_profiles":"C\u1ea7n g\u1edfi mail","needs":"Cho cu\u1ed9c h\u1eb9n","field_18":"KH Ti\u1ec1m n\u0103ng","field_19":"Xem x\u00e9t g\u1ecdi l\u1ea1i"},"ts_products_need_dom":{"":"","field_01":"BAO B\u00cc","field_02":"B\u1ed9t Tr\u00e9t","field_03":"CT\u0110T Ph\u00e1t Tri\u1ec3n DN","field_04":"E-mail Marketing Tools","field_05":"Hi\u1ec7u h\u00e3ng Test","field_06":"incomSoft CRM","field_07":"incomSoft DMS","field_08":"incomSoft ERP","field_09":"incomSoft SMEs (Cloud)","field_10":"KHO T\u01af\u01a0NG HOA SEN","field_11":"pfizer","field_12":"S\u01a1n N\u01b0\u1edbc","field_13":"TEST-HIEU-CC","field_14":"Th\u1ee9c u\u1ed1ng","field_15":"TRUNG QU\u1ed0C 1","field_16":"TV T\u00e1i C\u1ea5u Tr\u00fac DN","field_17":"www.phattrienvn.com"},"task_status_short":{"Planned":"K.Ho\u1ea1ch","InProgress":"T.H\u00e0nh","Completed":"H.T\u1ea5t","Cancel":"H\u1ee7y","MoveNext":"D\u1eddi","Due":"\u0110.H\u1ea1n","Expired":"Q.H\u1ea1n","Mission":"L\u1ecbch CT","Respone":"P.H\u1ed3i","Approve":"Duy\u1ec7t","Total":"T\u1ed5ng","Status":"T.Tr\u1ea1ng","Tasks":"C.Vi\u1ec7c","Project":"CT.MKT","AddTask":"C.Vi\u1ec7c","AddProj":"CT.MKT","AddAccount":"T\u1ea1o KH","Approve2":"S\u1ebfp duy\u1ec7t"},"task_status_long":{"Planned":"K\u1ebf ho\u1ea1ch","InProgress":"Ti\u1ebfn h\u00e0nh","Completed":"Ho\u00e0n t\u1ea5t","Cancel":"H\u1ee7y b\u1ecf","MoveNext":"D\u1eddi l\u1ecbch","Due":"\u0110\u1ebfn h\u1ea1n","Expired":"Qu\u00e1 h\u1ea1n","Mission":"L\u1ecbch c\u00f4ng t\u00e1c","Respone":"Ph\u1ea3n h\u1ed3i","Approve":"S\u1ebfp duy\u1ec7t","Total":"T\u1ed5ng","Status":"T\u00ecnh tr\u1ea1ng","Tasks":"C\u00f4ng vi\u1ec7c","Project":"CT.Marketing","AddTask":"C.Vi\u1ec7c","AddProj":"CT.MKT","AddAccount":"T\u1ea1o KH","Approve2":"S\u1ebfp duy\u1ec7t"},"reminder_time_default":1800,"years_dom":{"":"","2028":2028,"2027":2027,"2026":2026,"2025":2025,"2024":2024,"2023":2023,"2022":2022,"2021":2021,"2020":2020,"2019":2019,"2018":2018,"2017":2017,"2016":2016},"years_curr_dom":{"":"","2025":2025,"2024":2024,"2023":2023,"2022":2022,"2021":2021,"2020":2020,"2019":2019,"2018":2018,"2017":2017,"2016":2016},"hours_dom":{"00":"00","01":"01","02":"02","03":"03","04":"04","05":"05","06":"06","07":"07","08":"08","09":"09","10":"10","11":"11","12":"12","13":"13","14":"14","15":"15","16":"16","17":"17","18":"18","19":"19","20":"20","21":"21","22":"22","23":"23"},"minutes_dom":{"00":"00","15":"15","30":"30","45":"45"},"minutes2_dom":{"00":"00","15":"15","30":"30","45":"45"},"limit_option_dom":{"5":5,"10":10,"15":15,"20":20,"30":30,"50":50},"delivery_processing_stage_dom":{"":"","1":"B1","2":"B2","3":"B3","4":"B4"},"membership_card_dom":{"":"","c1":"Th\u1ebb \u0110\u1ed3ng","c2":"Th\u1ebb B\u1ea1c","c3":"Th\u1ebb V\u00e0ng","c4":"Th\u1ebb B\u1ea1ch Kim"},"percent_classification_dom":{"":"","LA":">100%","AL":"90% - 100%","BL":"70% - <90%","LB":"50% - < 70%","LC":" \u0110\u1ea1t <50%"},"user_logged_on_dom":{"":"","computer":"Laptop\/Desktop","tablet":"Tablet\/Mobile","all":"All"},"users_branch_dom":{"":"","32cf802a-1420-f3d0-ab1b-5f150b79846f":"CN SaiGon","2":"incomSoft"},"short_branch_dom":{"":"","32cf802a-1420-f3d0-ab1b-5f150b79846f":"CN SaiGon","2":"incomSoft"},"users_department_dom":{"":"","5555f6c1-7823-e998-81b1-59226c1efbf0":"CN SaiGon\/Kinh Doanh CN","89e7dabb-3b51-50de-7295-637445b435bb":"CN SaiGon\/K\u1ef9 Thu\u1eadt","3bb9a289-d3eb-568a-4969-59226c7a1025":"PTSoft JSC\/K\u1ebf To\u00e1n","432a58f6-4a05-542e-a9b3-577dd1762854":"PTSoft JSC\/Kinh doanh","c9fe9288-5bc7-b90e-37b3-59226cd46535":"PTSoft JSC\/K\u1ef9 Thu\u1eadt","1b86389e-607b-1b2d-1a81-59226dabeac9":"PTSoft JSC\/Marketing","3":"PTSoft JSC\/Qu\u1ea3n Tr\u1ecb"},"short_department_dom":{"":"","5555f6c1-7823-e998-81b1-59226c1efbf0":"Kinh Doanh CN","89e7dabb-3b51-50de-7295-637445b435bb":"K\u1ef9 Thu\u1eadt","3bb9a289-d3eb-568a-4969-59226c7a1025":"K\u1ebf To\u00e1n","432a58f6-4a05-542e-a9b3-577dd1762854":"Kinh doanh","c9fe9288-5bc7-b90e-37b3-59226cd46535":"K\u1ef9 Thu\u1eadt","1b86389e-607b-1b2d-1a81-59226dabeac9":"Marketing","3":"Qu\u1ea3n Tr\u1ecb"},"all_branch_dom":{"":"","32cf802a-1420-f3d0-ab1b-5f150b79846f":"CN SaiGon","2":"incomSoft"},"all_department_dom":{"":"","5555f6c1-7823-e998-81b1-59226c1efbf0":"Kinh Doanh CN","89e7dabb-3b51-50de-7295-637445b435bb":"K\u1ef9 Thu\u1eadt","3bb9a289-d3eb-568a-4969-59226c7a1025":"K\u1ebf To\u00e1n","432a58f6-4a05-542e-a9b3-577dd1762854":"Kinh doanh","c9fe9288-5bc7-b90e-37b3-59226cd46535":"K\u1ef9 Thu\u1eadt","1b86389e-607b-1b2d-1a81-59226dabeac9":"Marketing","3":"Qu\u1ea3n Tr\u1ecb"},"users_teams_dom":{"":"","6793db78-de96-fb6f-88c1-606fc369c528":"QT\/B\u1ed9 ph\u1eadn QT"},"branch_all_dom":{"-1":"T\u1ea5t c\u1ea3","32cf802a-1420-f3d0-ab1b-5f150b79846f":"CN SaiGon","2":"incomSoft"},"department_all_dom":{"-1":"T\u1ea5t c\u1ea3","5555f6c1-7823-e998-81b1-59226c1efbf0":"CN SaiGon\/Kinh Doanh CN","89e7dabb-3b51-50de-7295-637445b435bb":"CN SaiGon\/K\u1ef9 Thu\u1eadt","3bb9a289-d3eb-568a-4969-59226c7a1025":"PTSoft JSC\/K\u1ebf To\u00e1n","432a58f6-4a05-542e-a9b3-577dd1762854":"PTSoft JSC\/Kinh doanh","c9fe9288-5bc7-b90e-37b3-59226cd46535":"PTSoft JSC\/K\u1ef9 Thu\u1eadt","1b86389e-607b-1b2d-1a81-59226dabeac9":"PTSoft JSC\/Marketing","3":"PTSoft JSC\/Qu\u1ea3n Tr\u1ecb"},"teams_all_dom":{"-1":"T\u1ea5t c\u1ea3","6793db78-de96-fb6f-88c1-606fc369c528":"QT\/B\u1ed9 ph\u1eadn QT"},"monthlytargets_dom":{"NewLead":"\u0110\u1ea7u m\u1ed1i m\u1edbi( \u0110\u00e3 nh\u1eadp v\u00e0o ph\u1ea7n m\u1ec1m )","SoHen":"S\u1ed1 cu\u1ed9c h\u1eb9n","SHD":"S\u1ed1 h\u1ee3p \u0111\u1ed3ng","DoSo":"Doanh s\u1ed1","DoTh":"Doanh thu","BOD":"C\u00e1c v\u1ea5n \u0111\u1ec1 c\u1ea7n b\u00e1o l\u00ean Qu\u1ea3n L\u00fd\/ BG\u0110","Zalo":"No. K\u1ebft b\u1ea1n Zalo","FB":"No. K\u1ebft b\u1ea1n Facebook","LinkedIn":"No. K\u1ebft b\u1ea1n LinkedIn","Telegram":"No. K\u1ebft b\u1ea1n Telegram","Phone":"No. Nh\u1eadp danh b\u1ea1 \u0111i\u1ec7n tho\u1ea1i (Theo Nh\u00f3m)","ZaloCEO":"No.Group Zalo tham gia (CEO\/Q.L\u00fd)","NoDN":"No. Hi\u1ec7p h\u1ed9i doanh nghi\u1ec7p\/Doanh nh\u00e2n tham gia","SMS":"No. Tin nh\u1eafn\/B\u00e0i vi\u1ebft (T\u1ed5ng SMS, ZALO, FACEBOOK, Ch\u00fac M\u1eebng Sinh Nh\u1eadt, Ch\u00fac anh s\u1ee9c kh\u1ecfe, h\u1ea1nh ph\u00fac, th\u00e0nh \u0111\u1ea1t, Telegram, ...)","Deploy":"No. D\u1ef1 \u00e1n tri\u1ec3n khai","Won":"No. D\u1ef1 \u00e1n nghi\u1ec7m thu"},"list_module_dom":{"":"","Home":"I. Trang ch\u1ee7","QLCV":"II. Qu\u1ea3n l\u00fd c\u00f4ng vi\u1ec7c","CRM":"III. CRM","Sale":"IV. B\u00e1n h\u00e0ng","Purchase":"V. Mua h\u00e0ng","Accountant":"VI. K\u1ebf to\u00e1n","Warehouse":"VII. Kho","Project":"VIII. D\u1ef1 \u00e1n","HR":"IX. Nh\u00e2n s\u1ef1","Product":"X. Th\u00f4ng tin chung","Report":"XI. B\u00e1o c\u00e1o"},"list_tab_dom":{"":"","QLCV_1":"1. L\u1ecbch c\u00f4ng vi\u1ec7c","QLCV_2":"2. Danh s\u00e1ch c\u00f4ng vi\u1ec7c","QLCV_3":"3. Danh s\u00e1ch \u0111\u1ea7u c\u00f4ng vi\u1ec7c","QLCV_4":"4. Nh\u00f3m ng\u01b0\u1eddi th\u1ef1c hi\u1ec7n c\u00f4ng vi\u1ec7c","QLCV_5":"5. Th\u00f4ng b\u00e1o","QLCV_6":"6. T\u00e0i li\u1ec7u","CRM_1":"1. \u0110\u1ea7u m\u1ed1i","CRM_2":"2. Telesale","CRM_3":"3. Kh\u00e1ch h\u00e0ng","CRM_4":"4. Ng\u01b0\u1eddi li\u00ean h\u1ec7","CRM_5":"5. Nh\u00f3m kh\u00e1ch h\u00e0ng","CRM_6":"6. Ch\u1ec9 ti\u00eau th\u00e1ng","CRM_7":"7. Ho\u1ea1t \u0111\u1ed9ng b\u00e1n h\u00e0ng trong th\u00e1ng","CRM_8":"8. B\u00e1n h\u00e0ng h\u00f4m nay","CRM_9":"9. Gi\u00e1m s\u00e1t b\u00e1n h\u00e0ng","CRM_10":"10. Ch\u0103m s\u00f3c kh\u00e1ch h\u00e0ng","CRM_11":"11. Gi\u00e1m s\u00e1t CSKH","Sale_1":"1. Th\u00f4ng tin \u0111\u01a1n h\u00e0ng","Sale_2":"2. Danh s\u00e1ch \u0111\u01a1n h\u00e0ng b\u00e1n (SO)","Sale_3":"3. L\u1ecbch s\u1eed SP\/\u0110\u01a1n h\u00e0ng b\u00e1n","Sale_4":"4. X\u1eed l\u00fd ti\u1ebfn \u0111\u1ed9 \u0111\u01a1n h\u00e0ng b\u00e1n","Sale_5":"5. X\u1eed l\u00fd B\u1ea3o tr\u00ec\/B\u1ea3o h\u00e0nh","Sale_6":"6. Chi\u1ebft kh\u1ea5u tr\u00ean \u0111\u01a1n h\u00e0ng","Sale_7":"7. Chi\u1ebft kh\u1ea5u tr\u00ean kh\u00e1ch h\u00e0ng","Sale_8":"8. Thi\u1ebft l\u1eadp gi\u00e1 b\u00e1n theo s\u1ed1 l\u01b0\u1ee3ng","Sale_9":"9. Thi\u1ebft l\u1eadp gi\u00e1 b\u00e1n theo kh\u00e1ch h\u00e0ng","Sale_10":"10. Ch\u01b0\u01a1ng tr\u00ecnh khuy\u1ebfn m\u00e3i","Purchase_1":"1. Nh\u00e0 cung c\u1ea5p","Purchase_2":"2. Th\u00f4ng tin mua h\u00e0ng","Purchase_3":"3. Danh s\u00e1ch \u0111\u01a1n h\u00e0ng mua (PO)","Purchase_4":"4. L\u1ecbch s\u1eed SP\/\u0110\u01a1n h\u00e0ng mua","Purchase_5":"5. X\u1eed l\u00fd ti\u1ebfn \u0111\u1ed9 \u0111\u01a1n h\u00e0ng mua","Purchase_6":"6. Mua h\u00e0ng nh\u1eadp kh\u1ea9u","Purchase_7":"7. X\u1eed l\u00fd ti\u1ebfn \u0111\u1ed9 mua h\u00e0ng nh\u1eadp kh\u1ea9u","Accountant_1":"1. Qu\u1ea3n l\u00fd thu\/chi","Accountant_2":"2. N\u1ee3 ph\u1ea3i thu","Accountant_3":"3. N\u1ee3 ph\u1ea3i tr\u1ea3","Accountant_4":"4. N\u1ee3 vay","Accountant_5":"5. Cho vay","Accountant_6":"6. Theo d\u00f5i n\u1ee3 nh\u00e2n vi\u00ean","Accountant_7":"7. \u0110\u1ecbnh m\u1ee9c c\u00f4ng n\u1ee3","Accountant_8":"8. TK ng\u00e2n h\u00e0ng","Accountant_9":"9. Lo\u1ea1i Thu\/Chi","Accountant_10":"10. Lo\u1ea1i chi ph\u00ed ph\u00e1t sinh","Accountant_11":"11. Chi ph\u00ed ph\u00e1t sinh","Accountant_12":"12. H\u00f3a \u0111\u01a1n \u0111i\u1ec7n t\u1eed","Accountant_13":"13. B\u00e1o c\u00e1o k\u1ebft qu\u1ea3 ho\u1ea1t \u0111\u1ed9ng kinh doanh","Accountant_14":"14. B\u00e1o c\u00e1o t\u00ecnh h\u00ecnh t\u00e0i ch\u00ednh","Warehouse_1":"1. Nh\u1eadp kho","Warehouse_2":"2. Xu\u1ea5t kho","Warehouse_3":"3. SP Trong kho","Warehouse_4":"4. Theo d\u00f5i t\u1ed3n kho","Warehouse_5":"5. T\u1ed3n kho theo kho h\u00e0ng","Warehouse_6":"6. Danh s\u00e1ch kho","Warehouse_7":"7. \u0110\u1ecbnh m\u1ee9c t\u1ed3n kho","Warehouse_8":"8. Qu\u1ea3n l\u00fd theo lot","Warehouse_9":"9. Lo\u1ea1i phi\u1ebfu Nh\u1eadp\/Xu\u1ea5t","Warehouse_10":"10. Qu\u1ea3n l\u00fd VAT","Project_1":"1. Danh s\u00e1ch d\u1ef1 \u00e1n","Project_2":"2. Qu\u1ea3n l\u00fd d\u1ef1 \u00e1n","HR_1":"1. Danh s\u00e1ch nh\u00e2n s\u1ef1","HR_2":"2. Check-in","HR_3":"3. Ch\u1ea5m c\u00f4ng","Product_1":"1. S\u1ea3n ph\u1ea9m","Product_2":"2. Nh\u00f3m s\u1ea3n ph\u1ea9m","Product_3":"3. Nh\u00e3n hi\u1ec7u\/ H\u00e3ng s\u1ea3n xu\u1ea5t","Product_4":"4. \u0110\u01a1n v\u1ecb t\u00ednh","Product_5":"5. Thay \u0111\u1ed5i gi\u00e1 b\u00e1n SP","Product_6":"6. T\u1ea1o \u0111\u01a1n v\u1ecb t\u00ednh","Product_7":"7. Ti\u1ec1n t\u1ec7"},"notification_title_dom":{"":"","alert":"Th\u00f4ng b\u00e1o","warning":"C\u1ea3nh b\u00e1o","error":"L\u1ed7i","critical":"L\u1ed7i nghi\u00eam tr\u1ecdng"},"app_mobile_menus":{"":"","Checkin_Dates":"Check-in","Work_Sales":"B\u00e1n h\u00e0ng","Technical_Works":"K\u1ef9 thu\u1eadt"},"app_mobile_bottom_menu":{"Tasks":{"module":"Tasks","LABEL":"C\u00f4ng vi\u1ec7c","URL":"index.php?module=Tasks&action=index","iClass":"fa-tasks"},"Report_Sales":{"module":"Report_Sales","LABEL":"B\u00e1n h\u00e0ng","URL":"index.php?module=Report_Sales&action=index","iClass":"fa-shopping-cart"},"TQT_Product_Stocks":{"module":"TQT_Product_Stocks","LABEL":"T\u1ed3n kho","URL":"index.php?module=TQT_Product_Stocks&action=index","iClass":"fa-sitemap"},"Dashboard":{"module":"Dashboard","LABEL":"Ch\u1ee9c n\u0103ng","URL":"index.php?module=Home&action=Dashboards&showSmart=true","iClass":"fa-bars"},"Opportunities":{"module":"Opportunities","LABEL":"\u0110\u01a1n h\u00e0ng","URL":"index.php?module=Opportunities&action=index","iClass":"fa-wpforms"},"TQT_Products":{"module":"TQT_Products","LABEL":"S\u1ea3n ph\u1ea9m","URL":"index.php?module=TQT_Products&action=index","iClass":"fa-cubes"},"Accounts":{"module":"Accounts","LABEL":"Kh\u00e1ch h\u00e0ng","URL":"index.php?module=Accounts&action=index","iClass":"fa-folder-open"},"Calendar":{"module":"Calendar","LABEL":"L\u1ecbch CV","URL":"index.php?module=Calendar&action=index","iClass":"fa-calendar"},"Project":{"module":"Project","LABEL":"D\u1ef1 \u00e1n","URL":"index.php?module=Project&action=index","iClass":"fa-list-alt"},"TQT_Reports":{"module":"TQT_Reports","LABEL":"B\u00e1o c\u00e1o","URL":"index.php?module=TQT_Reports&action=index&showSmart=true","iClass":"fa-th-list"},"Home":{"module":"Home","LABEL":"BC Nhanh","URL":"index.php?module=Home&action=index&showSmart=true","iClass":"fa-bar-chart"},"TQT_SubTasks":{"module":"TQT_SubTasks","LABEL":"Tasks","URL":"index.php?module=TQT_SubTasks&action=index","iClass":"fa-bug"},"Work_Sales":{"module":"Work_Sales","LABEL":"Vi\u1ebfng th\u0103m","URL":"index.php?module=Work_Sales&action=index","iClass":"fa-map-marker"}}});