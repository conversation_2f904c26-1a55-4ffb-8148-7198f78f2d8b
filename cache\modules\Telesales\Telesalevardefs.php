<?php
// created: 2025-02-26 15:24:03
$G<PERSON><PERSON><PERSON><PERSON>["dictionary"]["Telesale"] = array (
  'table' => 'telesales',
  'audited' => true,
  'unified_search' => false,
  'duplicate_merge' => false,
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'varchar',
      'len' => '250',
      'source' => 'non-db',
      'comment' => 'Do not use',
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'source' => 'non-db',
      'comment' => 'Do not use',
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'telesales_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'telesales_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'branch_id' => 
    array (
      'name' => 'branch_id',
      'vname' => 'LBL_BRANCH_ID',
      'type' => 'enum',
      'options' => 'users_branch_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'BranchID of assigned user',
    ),
    'department_id' => 
    array (
      'name' => 'department_id',
      'vname' => 'LBL_DEPARTMENT_ID',
      'type' => 'enum',
      'options' => 'users_department_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'DepartmentID of assigned user',
    ),
    'assigned_user_id' => 
    array (
      'name' => 'assigned_user_id',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'vname' => 'LBL_ASSIGNED_TO_ID',
      'group' => 'assigned_user_name',
      'type' => 'relate',
      'table' => 'users',
      'module' => 'Users',
      'isnull' => 'false',
      'dbType' => 'id',
      'audited' => true,
      'reportable' => true,
      'duplicate_merge' => 'disabled',
      'comment' => 'User ID assigned to record',
      'massupdate' => false,
    ),
    'assigned_user_name' => 
    array (
      'name' => 'assigned_user_name',
      'link' => 'assigned_user_link',
      'vname' => 'LBL_ASSIGNED_TO_NAME',
      'rname' => 'user_name',
      'type' => 'relate',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'assigned_user_id',
      'module' => 'Users',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
      'massupdate' => false,
    ),
    'assigned_user_link' => 
    array (
      'name' => 'assigned_user_link',
      'type' => 'link',
      'relationship' => 'telesales_assigned_user',
      'vname' => 'LBL_ASSIGNED_TO_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
      'duplicate_merge' => 'enabled',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'table' => 'users',
    ),
    'no_need' => 
    array (
      'name' => 'no_need',
      'vname' => 'LBL_NO_NEED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'massupdate' => false,
      'audited' => true,
    ),
    'were_supplier' => 
    array (
      'name' => 'were_supplier',
      'vname' => 'LBL_WERE_SUPPLIER',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'massupdate' => false,
      'audited' => true,
    ),
    'send_profiles' => 
    array (
      'name' => 'send_profiles',
      'vname' => 'LBL_SEND_PROFILES',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'massupdate' => false,
      'audited' => true,
    ),
    'needs' => 
    array (
      'name' => 'needs',
      'vname' => 'LBL_NEEDS',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'massupdate' => false,
      'audited' => true,
    ),
    'need_month' => 
    array (
      'name' => 'need_month',
      'vname' => 'LBL_NEED_MONTH',
      'type' => 'enum',
      'dbType' => 'varchar',
      'len' => '200',
      'options' => 'months_dom',
      'isMultiSelect' => true,
      'massupdate' => false,
      'audited' => true,
    ),
    'note_other' => 
    array (
      'name' => 'note_other',
      'vname' => 'LBL_NOTE_OTHER',
      'type' => 'text',
      'audited' => true,
    ),
    'field_01' => 
    array (
      'name' => 'field_01',
      'vname' => 'LBL_FIELD_01',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'massupdate' => false,
      'audited' => true,
    ),
    'field_02' => 
    array (
      'name' => 'field_02',
      'vname' => 'LBL_FIELD_02',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'massupdate' => false,
      'audited' => true,
    ),
    'field_03' => 
    array (
      'name' => 'field_03',
      'vname' => 'LBL_FIELD_03',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'massupdate' => false,
      'audited' => true,
    ),
    'field_04' => 
    array (
      'name' => 'field_04',
      'vname' => 'LBL_FIELD_04',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'massupdate' => false,
      'audited' => true,
    ),
    'field_05' => 
    array (
      'name' => 'field_05',
      'vname' => 'LBL_FIELD_05',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'massupdate' => false,
      'audited' => true,
    ),
    'field_06' => 
    array (
      'name' => 'field_06',
      'vname' => 'LBL_FIELD_06',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'massupdate' => false,
      'audited' => true,
    ),
    'field_07' => 
    array (
      'name' => 'field_07',
      'vname' => 'LBL_FIELD_07',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'massupdate' => false,
      'audited' => true,
    ),
    'field_08' => 
    array (
      'name' => 'field_08',
      'vname' => 'LBL_FIELD_08',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'massupdate' => false,
      'audited' => true,
    ),
    'field_09' => 
    array (
      'name' => 'field_09',
      'vname' => 'LBL_FIELD_09',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'massupdate' => false,
      'audited' => true,
    ),
    'field_10' => 
    array (
      'name' => 'field_10',
      'vname' => 'LBL_FIELD_10',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'massupdate' => false,
      'audited' => true,
    ),
    'field_11' => 
    array (
      'name' => 'field_11',
      'vname' => 'LBL_FIELD_11',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'massupdate' => false,
      'audited' => true,
    ),
    'field_12' => 
    array (
      'name' => 'field_12',
      'vname' => 'LBL_FIELD_12',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'massupdate' => false,
      'audited' => true,
    ),
    'field_13' => 
    array (
      'name' => 'field_13',
      'vname' => 'LBL_FIELD_13',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'massupdate' => false,
      'audited' => true,
    ),
    'field_14' => 
    array (
      'name' => 'field_14',
      'vname' => 'LBL_FIELD_14',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'massupdate' => false,
      'audited' => true,
    ),
    'field_15' => 
    array (
      'name' => 'field_15',
      'vname' => 'LBL_FIELD_15',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'massupdate' => false,
      'audited' => true,
    ),
    'field_16' => 
    array (
      'name' => 'field_16',
      'vname' => 'LBL_FIELD_16',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'massupdate' => false,
      'audited' => true,
    ),
    'field_17' => 
    array (
      'name' => 'field_17',
      'vname' => 'LBL_FIELD_17',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'massupdate' => false,
      'audited' => true,
    ),
    'field_18' => 
    array (
      'name' => 'field_18',
      'vname' => 'LBL_FIELD_18',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'massupdate' => false,
      'audited' => true,
    ),
    'field_19' => 
    array (
      'name' => 'field_19',
      'vname' => 'LBL_FIELD_19',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'massupdate' => false,
      'audited' => true,
    ),
    'text_01' => 
    array (
      'name' => 'text_01',
      'vname' => 'LBL_TEXT_01',
      'type' => 'text',
      'audited' => true,
    ),
    'text_02' => 
    array (
      'name' => 'text_02',
      'vname' => 'LBL_TEXT_02',
      'type' => 'text',
      'audited' => true,
    ),
    'text_03' => 
    array (
      'name' => 'text_03',
      'vname' => 'LBL_TEXT_03',
      'type' => 'text',
      'audited' => true,
    ),
    'text_04' => 
    array (
      'name' => 'text_04',
      'vname' => 'LBL_TEXT_04',
      'type' => 'text',
      'audited' => true,
    ),
    'text_05' => 
    array (
      'name' => 'text_05',
      'vname' => 'LBL_TEXT_05',
      'type' => 'text',
      'audited' => true,
    ),
    'notes' => 
    array (
      'name' => 'notes',
      'vname' => 'LBL_NOTES',
      'type' => 'text',
      'audited' => true,
    ),
    'checked' => 
    array (
      'name' => 'checked',
      'vname' => 'LBL_CHECKED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'massupdate' => false,
      'audited' => true,
    ),
    'date_needs' => 
    array (
      'name' => 'date_needs',
      'vname' => 'LBL_DATE_NEEDS',
      'type' => 'datetime',
      'massupdate' => false,
      'audited' => true,
    ),
    'marker_user_id' => 
    array (
      'name' => 'marker_user_id',
      'vname' => 'LBL_MARKER_USER_ID',
      'type' => 'id',
      'audited' => true,
    ),
    'marker_user_name' => 
    array (
      'name' => 'marker_user_name',
      'vname' => 'LBL_MARKER_USER_ID',
      'type' => 'varchar',
      'source' => 'non-db',
    ),
    'account_id' => 
    array (
      'name' => 'account_id',
      'vname' => 'LBL_ACCOUNT_ID',
      'type' => 'id',
      'required' => false,
      'reportable' => false,
      'massupdate' => false,
      'comment' => 'Account ID note is associated with',
    ),
    'account_name' => 
    array (
      'name' => 'account_name',
      'rname' => 'name',
      'id_name' => 'account_id',
      'vname' => 'LBL_ACCOUNT_NAME',
      'table' => 'accounts',
      'type' => 'relate',
      'link' => 'accounts',
      'join_name' => 'accounts',
      'isnull' => 'true',
      'module' => 'Accounts',
      'source' => 'non-db',
      'massupdate' => false,
      'additionalFields' => 
      array (
        'transaction_level' => 'account_transaction',
      ),
    ),
    'accounts' => 
    array (
      'name' => 'accounts',
      'type' => 'link',
      'relationship' => 'accounts_telesales',
      'vname' => 'LBL_ACCOUNTS',
      'source' => 'non-db',
    ),
    'account_transaction' => 
    array (
      'name' => 'account_transaction',
      'vname' => 'LBL_ACCOUNT_TRANSACTION',
      'type' => 'enum',
      'source' => 'non-db',
      'options' => 'transaction_level_dom',
      'len' => 200,
      'massupdate' => false,
    ),
  ),
  'indices' => 
  array (
    'id' => 
    array (
      'name' => 'telesalespk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    'idx_telesales_branch_id' => 
    array (
      'name' => 'idx_telesales_branch_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
      ),
    ),
    'idx_telesales_department_id' => 
    array (
      'name' => 'idx_telesales_department_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'department_id',
      ),
    ),
    'idx_telesales_branch_dept' => 
    array (
      'name' => 'idx_telesales_branch_dept',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
        1 => 'department_id',
      ),
    ),
    'idx_telesales_assigned' => 
    array (
      'name' => 'idx_telesales_assigned',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'assigned_user_id',
      ),
    ),
    0 => 
    array (
      'name' => 'idx_tels_deleted',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_tels_del_no_need',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'no_need',
      ),
    ),
    2 => 
    array (
      'name' => 'idx_tels_del_supplier',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'were_supplier',
      ),
    ),
    3 => 
    array (
      'name' => 'idx_tels_del_profiles',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'send_profiles',
      ),
    ),
    4 => 
    array (
      'name' => 'idx_tels_del_needs',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'needs',
      ),
    ),
    5 => 
    array (
      'name' => 'idx_tels_account_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'account_id',
      ),
    ),
  ),
  'relationships' => 
  array (
    'telesales_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Telesales',
      'rhs_table' => 'telesales',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'telesales_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Telesales',
      'rhs_table' => 'telesales',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
    'telesales_assigned_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Telesales',
      'rhs_table' => 'telesales',
      'rhs_key' => 'assigned_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'accounts_telesales' => 
    array (
      'lhs_module' => 'Accounts',
      'lhs_table' => 'accounts',
      'lhs_key' => 'id',
      'rhs_module' => 'Telesales',
      'rhs_table' => 'telesales',
      'rhs_key' => 'account_id',
      'relationship_type' => 'one-to-one',
    ),
  ),
  'optimistic_locking' => true,
  'templates' => 
  array (
    'assignable' => 'assignable',
    'groups' => 'groups',
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
