incomCRM.language.setLanguage('Cash_Reasons', {"LBL_ID":"ID","LBL_DATE_ENTERED":"Ng\u00e0y t\u1ea1o","LBL_DATE_MODIFIED":"Ng\u00e0y c\u1eadp nh\u1eadt","LBL_MODIFIED":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_MODIFIED_ID":"ID ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_MODIFIED_NAME":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_CREATED":"Ng\u01b0\u1eddi t\u1ea1o","LBL_CREATED_ID":"ID ng\u01b0\u1eddi t\u1ea1o","LBL_DESCRIPTION":"Di\u1ec5n gi\u1ea3i","LBL_DELETED":"\u0110\u00e3 x\u00f3a?","LBL_NAME":"Ti\u00eau \u0111\u1ec1","LBL_CREATED_USER":"Ng\u01b0\u1eddi t\u1ea1o","LBL_MODIFIED_USER":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_MODULE_NAME":"Lo\u1ea1i Thu\/Chi","LBL_MODULE_TITLE":"Lo\u1ea1i Thu\/Chi: Trang ch\u1ee7","LNK_NEW_RECORD":"Th\u00eam m\u1edbi","LNK_LIST":"Danh s\u00e1ch","LBL_NEW_FORM_TITLE":"Th\u00eam m\u1edbi","LBL_LIST_FORM_TITLE":"Danh s\u00e1ch lo\u1ea1i Thu\/Chi","LBL_SEARCH_FORM_TITLE":"T\u00ecm ki\u1ebfm","LBL_REASON_TYPE":"Ph\u00e2n lo\u1ea1i","LBL_ACCOUNTING_TYPE":"Lo\u1ea1i h\u1ea1ch to\u00e1n","LBL_LIST_EXPENSE":"C\u00e1c kho\u1ea3n chi?","LBL_IGNORE_SUMMARY":"Kh\u00f4ng t\u00ednh t\u1ed5ng Thu\/Chi?","LBL_IGNORE_BANKS":"Kh\u00f4ng c\u1ed9ng v\u00e0o TKNH?","LBL_REASON_OPP_TYPE":"H\u1ea1ch to\u00e1n \u0111\u01a1n h\u00e0ng","LBL_LIST_IGNORE_SUMMARY":"Kh\u00f4ng t\u00ednh t\u1ed5ng?","LBL_LIST_IGNORE_BANKS":"Kh\u00f4ng c\u1ed9ng TKNH?","LBL_CASH_GROUP":"Nh\u00f3m Thu\/Chi","LBL_DEBT_TYPE":"Hi\u1ec3n th\u1ecb \u1edf m\u00e0n h\u00ecnh c\u00f4ng n\u1ee3","LBL_IS_NET_COST":"L\u00e0 chi ph\u00ed thu\u1ea7n","LBL_CASH_PROJECT_GROUP":"Nh\u00f3m chi ph\u00ed d\u1ef1 \u00e1n"});