<?php
// created: 2025-02-26 16:10:47
$<PERSON><PERSON><PERSON><PERSON><PERSON>["dictionary"]["TQT_DesignProcessing"] = array (
  'table' => 'opportunities',
  'audited' => true,
  'unified_search' => false,
  'duplicate_merge' => false,
  'comment' => 'An opportunity is the target of selling activities',
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'name',
      'dbType' => 'varchar',
      'len' => '30',
      'audited' => true,
      'unified_search' => true,
      'comment' => 'Name of the opportunity',
      'merge_filter' => 'selected',
      'importable' => 'required',
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
      'comment' => 'Full text of the note',
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'opportunities_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'opportunities_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'branch_id' => 
    array (
      'name' => 'branch_id',
      'vname' => 'LBL_BRANCH_ID',
      'type' => 'enum',
      'options' => 'users_branch_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'BranchID of assigned user',
    ),
    'department_id' => 
    array (
      'name' => 'department_id',
      'vname' => 'LBL_DEPARTMENT_ID',
      'type' => 'enum',
      'options' => 'users_department_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'DepartmentID of assigned user',
    ),
    'assigned_user_id' => 
    array (
      'name' => 'assigned_user_id',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'vname' => 'LBL_ASSIGNED_TO_ID',
      'group' => 'assigned_user_name',
      'type' => 'relate',
      'table' => 'users',
      'module' => 'Users',
      'isnull' => 'false',
      'dbType' => 'id',
      'audited' => true,
      'reportable' => true,
      'duplicate_merge' => 'disabled',
      'comment' => 'User ID assigned to record',
      'massupdate' => false,
    ),
    'assigned_user_name' => 
    array (
      'name' => 'assigned_user_name',
      'link' => 'assigned_user_link',
      'vname' => 'LBL_ASSIGNED_TO_NAME',
      'rname' => 'user_name',
      'type' => 'relate',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'assigned_user_id',
      'module' => 'Users',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
      'massupdate' => false,
    ),
    'assigned_user_link' => 
    array (
      'name' => 'assigned_user_link',
      'type' => 'link',
      'relationship' => 'opportunities_assigned_user',
      'vname' => 'LBL_ASSIGNED_TO_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
      'duplicate_merge' => 'enabled',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'table' => 'users',
    ),
    'overdue_debts' => 
    array (
      'name' => 'overdue_debts',
      'vname' => 'LBL_OVERDUE_DEBTS',
      'type' => 'float',
      'source' => 'non-db',
    ),
    'order_purchase' => 
    array (
      'name' => 'order_purchase',
      'vname' => 'LBL_ORDER_PURCHASE',
      'type' => 'enum',
      'options' => 'opportunity_order_type2_dom',
      'len' => '20',
      'audited' => true,
      'massupdate' => false,
    ),
    'opportunity_type' => 
    array (
      'name' => 'opportunity_type',
      'vname' => 'LBL_TYPE',
      'type' => 'enum',
      'options' => 'opportunity_type_dom',
      'len' => '5',
      'audited' => true,
      'massupdate' => false,
      'comment' => 'Type of opportunity (ex: Existing, New)',
      'merge_filter' => 'enabled',
    ),
    'account_id' => 
    array (
      'name' => 'account_id',
      'vname' => 'LBL_ACCOUNT_ID',
      'type' => 'id',
      'source' => 'non-db',
      'audited' => true,
      'massupdate' => false,
    ),
    'account_name' => 
    array (
      'name' => 'account_name',
      'rname' => 'name',
      'id_name' => 'account_id',
      'vname' => 'LBL_ACCOUNT_NAME',
      'type' => 'relate',
      'table' => 'accounts',
      'join_name' => 'accounts',
      'isnull' => 'true',
      'module' => 'Accounts',
      'dbType' => 'varchar',
      'link' => 'accounts',
      'len' => '255',
      'source' => 'non-db',
      'massupdate' => false,
      'unified_search' => true,
      'importable' => 'required',
    ),
    'campaign_id' => 
    array (
      'name' => 'campaign_id',
      'comment' => 'Campaign that generated lead',
      'vname' => 'LBL_CAMPAIGN_ID',
      'rname' => 'id',
      'type' => 'id',
      'dbType' => 'id',
      'table' => 'campaigns',
      'isnull' => 'true',
      'module' => 'Campaigns',
      'reportable' => false,
      'massupdate' => false,
      'duplicate_merge' => 'disabled',
    ),
    'campaign_name' => 
    array (
      'name' => 'campaign_name',
      'rname' => 'name',
      'id_name' => 'campaign_id',
      'vname' => 'LBL_CAMPAIGN',
      'type' => 'relate',
      'link' => 'campaign_opportunities',
      'isnull' => 'true',
      'table' => 'campaigns',
      'module' => 'Campaigns',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'campaign_opportunities' => 
    array (
      'name' => 'campaign_opportunities',
      'type' => 'link',
      'vname' => 'LBL_CAMPAIGN_OPPORTUNITY',
      'relationship' => 'campaign_opportunities',
      'source' => 'non-db',
    ),
    'lead_source' => 
    array (
      'name' => 'lead_source',
      'vname' => 'LBL_LEAD_SOURCE',
      'type' => 'enum',
      'options' => 'lead_source_dom',
      'len' => '50',
      'comment' => 'Source of the opportunity',
      'merge_filter' => 'enabled',
      'massupdate' => false,
    ),
    'amount' => 
    array (
      'name' => 'amount',
      'vname' => 'LBL_AMOUNT',
      'type' => 'currency',
      'dbType' => 'double',
      'comment' => 'Unconverted amount of the opportunity',
      'duplicate_merge' => 'disabled',
      'importable' => 'required',
    ),
    'amount_usdollar' => 
    array (
      'name' => 'amount_usdollar',
      'vname' => 'LBL_AMOUNT_USDOLLAR',
      'type' => 'currency',
      'group' => 'amount',
      'dbType' => 'double',
      'audited' => true,
      'comment' => 'Formatted amount of the opportunity',
    ),
    'currency_id' => 
    array (
      'name' => 'currency_id',
      'type' => 'id',
      'group' => 'currency_id',
      'vname' => 'LBL_CURRENCY_ID',
      'function' => 
      array (
        'name' => 'getCurrencyDropDown',
        'returns' => 'html',
      ),
      'reportable' => false,
      'comment' => 'Currency used for display purposes',
    ),
    'currency_name' => 
    array (
      'name' => 'currency_name',
      'rname' => 'name',
      'id_name' => 'currency_id',
      'vname' => 'LBL_CURRENCY_NAME',
      'type' => 'relate',
      'isnull' => 'true',
      'table' => 'currencies',
      'module' => 'Currencies',
      'source' => 'non-db',
      'function' => 
      array (
        'name' => 'getCurrencyNameDropDown',
        'returns' => 'html',
      ),
      'studio' => 'false',
    ),
    'currency_symbol' => 
    array (
      'name' => 'currency_symbol',
      'rname' => 'symbol',
      'id_name' => 'currency_id',
      'vname' => 'LBL_CURRENCY_SYMBOL',
      'type' => 'relate',
      'isnull' => 'true',
      'table' => 'currencies',
      'module' => 'Currencies',
      'source' => 'non-db',
      'function' => 
      array (
        'name' => 'getCurrencySymbolDropDown',
        'returns' => 'html',
      ),
    ),
    'date_closed' => 
    array (
      'name' => 'date_closed',
      'vname' => 'LBL_DATE_CLOSED',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
      'comment' => 'Expected or actual date the oppportunity will close',
      'importable' => 'required',
      'display_default' => 'now',
    ),
    'sales_stage' => 
    array (
      'name' => 'sales_stage',
      'vname' => 'LBL_SALES_STAGE',
      'type' => 'enum',
      'options' => 'sales_stage_dom',
      'len' => '25',
      'audited' => true,
      'massupdate' => false,
      'comment' => 'Indication of progression towards closure',
      'merge_filter' => 'enabled',
      'importable' => 'required',
      'display_default' => 'Estimation',
    ),
    'probability' => 
    array (
      'name' => 'probability',
      'vname' => 'LBL_PROBABILITY',
      'type' => 'int',
      'dbType' => 'double',
      'audited' => true,
      'comment' => 'The probability of closure',
      'validation' => 
      array (
        'type' => 'range',
        'min' => 0,
        'max' => 100,
      ),
      'merge_filter' => 'enabled',
    ),
    'accounts' => 
    array (
      'name' => 'accounts',
      'type' => 'link',
      'relationship' => 'accounts_opportunities',
      'source' => 'non-db',
      'link_type' => 'one',
      'module' => 'Accounts',
      'bean_name' => 'Account',
      'vname' => 'LBL_ACCOUNTS',
    ),
    'contacts' => 
    array (
      'name' => 'contacts',
      'type' => 'link',
      'relationship' => 'opportunities_contacts',
      'source' => 'non-db',
      'module' => 'Contacts',
      'bean_name' => 'Contact',
      'rel_fields' => 
      array (
        'contact_role' => 
        array (
          'type' => 'enum',
          'options' => 'opportunity_relationship_type_dom',
        ),
      ),
      'vname' => 'LBL_CONTACTS',
    ),
    'tasks' => 
    array (
      'name' => 'tasks',
      'type' => 'link',
      'relationship' => 'opportunity_tasks',
      'source' => 'non-db',
      'vname' => 'LBL_TASKS',
    ),
    'notes' => 
    array (
      'name' => 'notes',
      'type' => 'link',
      'relationship' => 'opportunity_notes',
      'source' => 'non-db',
      'module' => 'Notes',
      'bean_name' => 'Note',
      'vname' => 'LBL_NOTES',
    ),
    'meetings' => 
    array (
      'name' => 'meetings',
      'type' => 'link',
      'relationship' => 'opportunity_meetings',
      'source' => 'non-db',
      'vname' => 'LBL_MEETINGS',
    ),
    'calls' => 
    array (
      'name' => 'calls',
      'type' => 'link',
      'relationship' => 'opportunity_calls',
      'source' => 'non-db',
      'vname' => 'LBL_CALLS',
    ),
    'emails' => 
    array (
      'name' => 'emails',
      'type' => 'link',
      'relationship' => 'emails_opportunities_rel',
      'source' => 'non-db',
      'vname' => 'LBL_EMAILS',
    ),
    'project' => 
    array (
      'name' => 'project',
      'type' => 'link',
      'relationship' => 'project_opportunity_related',
      'source' => 'non-db',
      'vname' => 'LBL_PROJECTS',
    ),
    'leads' => 
    array (
      'name' => 'leads',
      'type' => 'link',
      'relationship' => 'opportunity_leads',
      'source' => 'non-db',
      'vname' => 'LBL_LEADS',
    ),
    'campaigns' => 
    array (
      'name' => 'campaigns',
      'type' => 'link',
      'relationship' => 'opportunities_campaign',
      'module' => 'CampaignLog',
      'bean_name' => 'CampaignLog',
      'source' => 'non-db',
      'vname' => 'LBL_CAMPAIGNS',
    ),
    'campaign_link' => 
    array (
      'name' => 'campaign_link',
      'type' => 'link',
      'relationship' => 'opportunities_campaign',
      'vname' => 'LBL_CAMPAIGNS',
      'link_type' => 'one',
      'module' => 'Campaigns',
      'bean_name' => 'Campaign',
      'source' => 'non-db',
    ),
    'currencies' => 
    array (
      'name' => 'currencies',
      'type' => 'link',
      'relationship' => 'opportunity_currencies',
      'source' => 'non-db',
      'vname' => 'LBL_CURRENCIES',
    ),
    'documents' => 
    array (
      'name' => 'documents',
      'type' => 'link',
      'relationship' => 'opportunities_documents',
      'module' => 'Documents',
      'bean_name' => 'Document',
      'source' => 'non-db',
      'vname' => 'LBL_DOCUMENTS',
    ),
    'tasks_direct' => 
    array (
      'name' => 'tasks_direct',
      'type' => 'link',
      'relationship' => 'opportunities_tasks_direct',
      'module' => 'Tasks',
      'bean_name' => 'Task',
      'source' => 'non-db',
      'vname' => 'LBL_TASKS',
    ),
    'rate_id' => 
    array (
      'name' => 'rate_id',
      'type' => 'id',
      'group' => 'rate_id',
      'vname' => 'LBL_RATE_ID',
      'function' => 
      array (
        'name' => 'getCurrencyForeignDropDown',
        'returns' => 'html',
      ),
      'reportable' => false,
    ),
    'rate_foreign' => 
    array (
      'name' => 'rate_foreign',
      'vname' => 'LBL_RATE_FOREIGN',
      'type' => 'double',
      'duplicate_merge' => 'disabled',
    ),
    'product_name' => 
    array (
      'name' => 'product_name',
      'vname' => 'LBL_TQT_PRODUCT_FULL_NAME',
      'type' => 'varchar',
      'len' => '150',
      'source' => 'non-db',
      'unified_search' => true,
    ),
    'is_auto_cpps' => 
    array (
      'name' => 'is_auto_cpps',
      'vname' => 'LBL_IS_AUTO_CPPS',
      'type' => 'bool',
      'len' => '1',
      'default' => '0',
      'audited' => true,
      'massupdate' => false,
    ),
    'quotes_note' => 
    array (
      'name' => 'quotes_note',
      'vname' => 'LBL_QUOTES_NOTE',
      'type' => 'text',
      'unified_search' => true,
    ),
    'shipping_time' => 
    array (
      'name' => 'shipping_time',
      'vname' => 'LBL_SHIPPING_TIME',
      'type' => 'text',
      'unified_search' => true,
    ),
    'payment_method' => 
    array (
      'name' => 'payment_method',
      'vname' => 'LBL_PAYMENT_METHOD',
      'type' => 'text',
      'unified_search' => true,
    ),
    'shipping_note' => 
    array (
      'name' => 'shipping_note',
      'vname' => 'LBL_SHIPPING_NOTE',
      'type' => 'text',
      'unified_search' => true,
    ),
    'failure_cause' => 
    array (
      'name' => 'failure_cause',
      'vname' => 'LBL_FAILURE_CAUSE',
      'type' => 'text',
      'unified_search' => true,
      'audited' => false,
      'massupdate' => false,
    ),
    'accounting_category_type' => 
    array (
      'name' => 'accounting_category_type',
      'vname' => 'LBL_ACCOUNTING_CATEGORY_TYPE',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => false,
      'default' => 0,
    ),
    'purchase_orders_tasks' => 
    array (
      'name' => 'purchase_orders_tasks',
      'type' => 'link',
      'relationship' => 'purchase_orders_tasks_related',
      'source' => 'non-db',
      'vname' => 'LBL_PURCHASE_ORDERS_TASKS',
    ),
    'invoices' => 
    array (
      'name' => 'invoices',
      'type' => 'link',
      'relationship' => 'invoices_opportunities',
      'source' => 'non-db',
      'vname' => 'LBL_INVOICES',
    ),
    'tqt_comments' => 
    array (
      'name' => 'tqt_comments',
      'type' => 'link',
      'relationship' => 'opportunities_tqt_comments',
      'source' => 'non-db',
      'vname' => 'LBL_TQT_COMMENTS',
    ),
    'SecurityGroups' => 
    array (
      'name' => 'SecurityGroups',
      'type' => 'link',
      'relationship' => 'securitygroups_opportunities',
      'module' => 'SecurityGroups',
      'bean_name' => 'SecurityGroup',
      'source' => 'non-db',
      'vname' => 'LBL_SECURITYGROUPS',
    ),
    'opportunities_tasks' => 
    array (
      'name' => 'opportunities_tasks',
      'type' => 'link',
      'relationship' => 'opportunities_tasks_related',
      'source' => 'non-db',
      'vname' => 'LBL_OPPORTUNITIES_TASKS',
    ),
    'guarantee_orders_tasks' => 
    array (
      'name' => 'guarantee_orders_tasks',
      'type' => 'link',
      'relationship' => 'opportunities_guarantee_related',
      'source' => 'non-db',
      'vname' => 'LBL_GUARANTEE_ORDERS_TASKS',
    ),
    'tqt_orderdetails' => 
    array (
      'name' => 'tqt_orderdetails',
      'type' => 'link',
      'relationship' => 'opportunities_tqt_orderdetails',
      'source' => 'non-db',
      'vname' => 'LBL_TQT_ORDER_DETAILS',
    ),
    'incurred_other' => 
    array (
      'name' => 'incurred_other',
      'vname' => 'LBL_INCURRED_OTHER',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'incurred_domestic' => 
    array (
      'name' => 'incurred_domestic',
      'vname' => 'LBL_INCURRED_DOMESTIC',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'incurred_foreign' => 
    array (
      'name' => 'incurred_foreign',
      'vname' => 'LBL_INCURRED_FOREIGN',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'processing_stage' => 
    array (
      'name' => 'processing_stage',
      'vname' => 'LBL_PROCESSING_STAGE',
      'type' => 'enum',
      'options' => 'opportunity_processing_stage_dom',
      'len' => '20',
      'audited' => true,
      'massupdate' => false,
    ),
    'processing_content' => 
    array (
      'name' => 'processing_content',
      'vname' => 'LBL_PROCESSING_CONTENT',
      'type' => 'text',
      'audited' => true,
    ),
    'amount_collected' => 
    array (
      'name' => 'amount_collected',
      'vname' => 'LBL_AMOUNT_COLLECTED',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
    ),
    'amount_owed' => 
    array (
      'name' => 'amount_owed',
      'vname' => 'LBL_AMOUNT_OWED',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
    ),
    'receivable_debts' => 
    array (
      'name' => 'receivable_debts',
      'vname' => 'LBL_RECEIVABLE_DEBTS',
      'type' => 'float',
      'source' => 'non-db',
    ),
    'debt_norm_min' => 
    array (
      'name' => 'debt_norm_min',
      'vname' => 'LBL_DEBT_NORM_MIN',
      'type' => 'double',
      'source' => 'non-db',
      'audited' => true,
    ),
    'debt_norm_max' => 
    array (
      'name' => 'debt_norm_max',
      'vname' => 'LBL_DEBT_NORM_MAX',
      'type' => 'double',
      'source' => 'non-db',
      'audited' => true,
    ),
    'debt_delay_day' => 
    array (
      'name' => 'debt_delay_day',
      'vname' => 'LBL_DEBT_DELAY_DAY',
      'type' => 'int',
      'len' => 4,
      'source' => 'non-db',
      'audited' => true,
    ),
    'handle_user_id' => 
    array (
      'name' => 'handle_user_id',
      'vname' => 'LBL_HANDLE_USER_ID',
      'type' => 'id',
      'audited' => true,
      'massupdate' => false,
    ),
    'handle_user_name' => 
    array (
      'name' => 'handle_user_name',
      'rname' => 'user_name',
      'id_name' => 'handle_user_id',
      'vname' => 'LBL_HANDLE_USER_NAME',
      'source' => 'non-db',
      'type' => 'relate',
      'link' => 'handle_users',
      'table' => 'users',
      'join_name' => 'handler',
      'module' => 'Users',
      'massupdate' => false,
    ),
    'handle_users' => 
    array (
      'name' => 'handle_users',
      'vname' => 'LBL_HANDLE_USERS',
      'type' => 'link',
      'source' => 'non-db',
      'relationship' => 'handle_users_opportunities',
    ),
    'parent_id' => 
    array (
      'name' => 'parent_id',
      'vname' => 'LBL_PARENT_ID',
      'type' => 'id',
      'audited' => true,
      'massupdate' => false,
    ),
    'parent_code' => 
    array (
      'name' => 'parent_code',
      'rname' => 'order_number',
      'id_name' => 'parent_id',
      'vname' => 'LBL_PARENT_CODE',
      'type' => 'relate',
      'table' => 'opportunities',
      'module' => 'Opportunities',
      'source' => 'non-db',
      'link' => 'member_of',
      'importable' => 'false',
      'massupdate' => false,
    ),
    'parent_name' => 
    array (
      'name' => 'parent_name',
      'rname' => 'name',
      'id_name' => 'parent_id',
      'vname' => 'LBL_PARENT_NAME',
      'type' => 'relate',
      'table' => 'opportunities',
      'module' => 'Opportunities',
      'massupdate' => false,
      'source' => 'non-db',
      'link' => 'member_of',
      'importable' => 'false',
      'additionalFields' => 
      array (
        'project_name' => 'parent_project_name',
      ),
    ),
    'parent_project_name' => 
    array (
      'name' => 'parent_project_name',
      'vname' => 'LBL_PROJECT_NAME',
      'type' => 'varchar',
      'len' => '250',
      'source' => 'non-db',
    ),
    'members' => 
    array (
      'name' => 'members',
      'type' => 'link',
      'relationship' => 'member_opportunities',
      'module' => 'Opportunities',
      'bean_name' => 'Opportunity',
      'source' => 'non-db',
      'vname' => 'LBL_MEMBERS',
    ),
    'member_of' => 
    array (
      'name' => 'member_of',
      'type' => 'link',
      'relationship' => 'member_opportunities',
      'module' => 'Opportunities',
      'bean_name' => 'Opportunity',
      'link_type' => 'one',
      'source' => 'non-db',
      'vname' => 'LBL_MEMBER_OF',
      'side' => 'left',
    ),
    'warranty_period' => 
    array (
      'name' => 'warranty_period',
      'vname' => 'LBL_WARRANTY_PERIOD',
      'type' => 'enum',
      'options' => 'periodic_purchase_dom',
      'len' => 20,
      'audited' => true,
      'massupdate' => false,
    ),
    'bank_account_id' => 
    array (
      'name' => 'bank_account_id',
      'vname' => 'LBL_BANK_ACCOUNT_ID',
      'type' => 'id',
      'function' => 
      array (
        'name' => 'getBankAccountDropDown',
        'returns' => 'html',
      ),
      'required' => false,
      'audited' => true,
      'massupdate' => false,
      'reportable' => false,
    ),
    'bank_account_name' => 
    array (
      'name' => 'bank_account_name',
      'rname' => 'bank_cash',
      'db_concat_fields' => 
      array (
        0 => 'bank_number',
        1 => 'bank_name',
      ),
      'id_name' => 'bank_account_id',
      'vname' => 'LBL_BANK_ACCOUNT_NAME',
      'table' => 'bank_accounts',
      'type' => 'relate',
      'link' => 'bank_accounts',
      'join_name' => 'bank_accounts',
      'isnull' => 'true',
      'module' => 'Bank_Accounts',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'bank_accounts' => 
    array (
      'name' => 'bank_accounts',
      'type' => 'link',
      'relationship' => 'bank_accounts_opportunities',
      'vname' => 'LBL_BANK_ACCOUNTS',
      'source' => 'non-db',
    ),
    'revenue_promotions' => 
    array (
      'name' => 'revenue_promotions',
      'type' => 'link',
      'relationship' => 'opportunities_revenue_promotions',
      'source' => 'non-db',
      'vname' => 'LBL_REVENUE_PROMOTIONS',
    ),
    'contract_number' => 
    array (
      'name' => 'contract_number',
      'vname' => 'LBL_CONTRACT_NUMBER',
      'type' => 'varchar',
      'len' => '50',
      'audited' => true,
    ),
    'contract_date' => 
    array (
      'name' => 'contract_date',
      'vname' => 'LBL_CONTRACT_DATE',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'order_number' => 
    array (
      'name' => 'order_number',
      'vname' => 'LBL_ORDER_NUMBER',
      'type' => 'varchar',
      'len' => '50',
      'audited' => true,
    ),
    'order_date' => 
    array (
      'name' => 'order_date',
      'vname' => 'LBL_ORDER_DATE',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'quote_number' => 
    array (
      'name' => 'quote_number',
      'vname' => 'LBL_QUOTE_NUMBER',
      'type' => 'varchar',
      'len' => '50',
      'audited' => true,
    ),
    'estimated_revenue' => 
    array (
      'name' => 'estimated_revenue',
      'vname' => 'LBL_ESTIMATED_REVENUE',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'priority' => 
    array (
      'name' => 'priority',
      'vname' => 'LBL_PRIORITY',
      'type' => 'enum',
      'options' => 'opportunities_priority_dom',
      'len' => '30',
      'audited' => true,
      'massupdate' => false,
    ),
    'opportunity_scope' => 
    array (
      'name' => 'opportunity_scope',
      'vname' => 'LBL_OPPORTUNITY_SCOPE',
      'type' => 'enum',
      'options' => 'opportunities_scope_dom',
      'len' => '30',
      'audited' => true,
      'massupdate' => false,
    ),
    'object' => 
    array (
      'name' => 'object',
      'vname' => 'LBL_OBJECT',
      'type' => 'enum',
      'options' => 'opportunity_object_dom',
      'len' => '20',
      'audited' => true,
      'massupdate' => false,
      'default' => 'Proposal',
    ),
    'date_start_process' => 
    array (
      'name' => 'date_start_process',
      'vname' => 'LBL_DATE_START_PROCESS',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
      'display_default' => 'now',
    ),
    'date_converter' => 
    array (
      'name' => 'date_converter',
      'vname' => 'LBL_DATE_CONVERTER',
      'type' => 'date',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'is_target' => 
    array (
      'name' => 'is_target',
      'vname' => 'LBL_IS_TARGET',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => false,
      'default' => '0',
    ),
    'account_targets' => 
    array (
      'name' => 'account_targets',
      'vname' => 'LBL_ACCOUNT_TARGETS',
      'type' => 'bool',
      'massupdate' => false,
      'default' => '0',
    ),
    'causes_failure' => 
    array (
      'name' => 'causes_failure',
      'vname' => 'LBL_CAUSES_FAILURE',
      'type' => 'enum',
      'options' => 'opportunity_failures_dom',
      'len' => 2,
      'audited' => true,
      'massupdate' => false,
    ),
    'failure_notes' => 
    array (
      'name' => 'failure_notes',
      'vname' => 'LBL_FAILURE_NOTES',
      'type' => 'text',
      'audited' => true,
    ),
    'debt_order' => 
    array (
      'name' => 'debt_order',
      'vname' => 'LBL_DEBT_ORDER',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => false,
      'default' => 0,
    ),
    'vat_stock' => 
    array (
      'name' => 'vat_stock',
      'vname' => 'LBL_VAT_STOCK',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => false,
      'default' => 0,
    ),
    'project_id' => 
    array (
      'name' => 'project_id',
      'vname' => 'LBL_PROJECT_ID',
      'type' => 'id',
      'audited' => true,
      'massupdate' => true,
    ),
    'project_name' => 
    array (
      'name' => 'project_name',
      'rname' => 'name',
      'id_name' => 'project_id',
      'vname' => 'LBL_PROJECT_NAME',
      'len' => 200,
      'type' => 'relate',
      'link' => 'project',
      'join_name' => 'project',
      'table' => 'project',
      'module' => 'Project',
      'audited' => true,
      'massupdate' => true,
    ),
    'quotation_date' => 
    array (
      'name' => 'quotation_date',
      'vname' => 'LBL_QUOTATION_DATE',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'quotation_version' => 
    array (
      'name' => 'quotation_version',
      'vname' => 'LBL_QUOTATION_VERSION',
      'type' => 'tinyint',
      'len' => 3,
      'audited' => true,
    ),
    'ttt_date_shipment' => 
    array (
      'name' => 'ttt_date_shipment',
      'vname' => 'LBL_TTT_DATE_SHIPMENT',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'ttt_date_contract' => 
    array (
      'name' => 'ttt_date_contract',
      'vname' => 'LBL_TTT_DATE_CONTRACT',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'ttt_date_declaration' => 
    array (
      'name' => 'ttt_date_declaration',
      'vname' => 'LBL_TTT_DATE_DECLARATION',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'ttt_date_storage' => 
    array (
      'name' => 'ttt_date_storage',
      'vname' => 'LBL_TTT_DATE_STORAGE',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'ttt_date_arrival' => 
    array (
      'name' => 'ttt_date_arrival',
      'vname' => 'LBL_TTT_DATE_ARRIVAL',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'ttt_date_clearance' => 
    array (
      'name' => 'ttt_date_clearance',
      'vname' => 'LBL_TTT_DATE_CLEARANCE',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'ttt_fee_amount' => 
    array (
      'name' => 'ttt_fee_amount',
      'vname' => 'LBL_TTT_FEE_AMOUNT',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'ttt_insurance_number' => 
    array (
      'name' => 'ttt_insurance_number',
      'vname' => 'LBL_TTT_INSURANCE_NUMBER',
      'type' => 'varchar',
      'len' => '100',
      'audited' => true,
    ),
    'ttt_insurance_date' => 
    array (
      'name' => 'ttt_insurance_date',
      'vname' => 'LBL_TTT_INSURANCE_DATE',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'ttt_insurance_amount' => 
    array (
      'name' => 'ttt_insurance_amount',
      'vname' => 'LBL_TTT_INSURANCE_AMOUNT',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'ttt_insurance_extension' => 
    array (
      'name' => 'ttt_insurance_extension',
      'vname' => 'LBL_TTT_INSURANCE_EXTENSION',
      'type' => 'varchar',
      'len' => '100',
      'audited' => true,
    ),
    'ttt_insurance_coop' => 
    array (
      'name' => 'ttt_insurance_coop',
      'vname' => 'LBL_TTT_INSURANCE_COOP',
      'type' => 'varchar',
      'len' => '250',
      'audited' => true,
    ),
    'ttt_date_lc_payment' => 
    array (
      'name' => 'ttt_date_lc_payment',
      'vname' => 'LBL_TTT_DATE_LC_PAYMENT',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'ttt_lc_number' => 
    array (
      'name' => 'ttt_lc_number',
      'vname' => 'LBL_TTT_LC_NUMBER',
      'type' => 'varchar',
      'len' => '100',
      'audited' => true,
    ),
    'ttt_date_lc_expired' => 
    array (
      'name' => 'ttt_date_lc_expired',
      'vname' => 'LBL_TTT_DATE_LC_EXPIRED',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'ttt_lc_amount' => 
    array (
      'name' => 'ttt_lc_amount',
      'vname' => 'LBL_TTT_LC_AMOUNT',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'ttt_lc_reiss_amt' => 
    array (
      'name' => 'ttt_lc_reiss_amt',
      'vname' => 'LBL_TTT_LC_REISS_AMT',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'ttt_lc_repair_amt' => 
    array (
      'name' => 'ttt_lc_repair_amt',
      'vname' => 'LBL_TTT_LC_REPAIR_AMT',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'ttt_lc_payment_amt' => 
    array (
      'name' => 'ttt_lc_payment_amt',
      'vname' => 'LBL_TTT_LC_PAYMENT_AMT',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'ttt_lc_bank_name' => 
    array (
      'name' => 'ttt_lc_bank_name',
      'vname' => 'LBL_TTT_LC_BANK_NAME',
      'type' => 'varchar',
      'len' => '100',
      'audited' => true,
    ),
    'ttt_invoice_number' => 
    array (
      'name' => 'ttt_invoice_number',
      'vname' => 'LBL_TTT_INVOICE_NUMBER',
      'type' => 'varchar',
      'len' => '100',
      'audited' => true,
    ),
    'ttt_invoice_date' => 
    array (
      'name' => 'ttt_invoice_date',
      'vname' => 'LBL_TTT_INVOICE_DATE',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'ttt_bol' => 
    array (
      'name' => 'ttt_bol',
      'vname' => 'LBL_TTT_BOL',
      'type' => 'varchar',
      'len' => '100',
      'audited' => true,
    ),
    'ttt_bol_date' => 
    array (
      'name' => 'ttt_bol_date',
      'vname' => 'LBL_TTT_BOL_DATE',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'ttt_packinglist' => 
    array (
      'name' => 'ttt_packinglist',
      'vname' => 'LBL_TTT_PACKINGLIST',
      'type' => 'varchar',
      'len' => '100',
      'audited' => true,
    ),
    'ttt_co' => 
    array (
      'name' => 'ttt_co',
      'vname' => 'LBL_TTT_CO',
      'type' => 'varchar',
      'len' => '100',
      'audited' => true,
    ),
    'ttt_milltest' => 
    array (
      'name' => 'ttt_milltest',
      'vname' => 'LBL_TTT_MILLTEST',
      'type' => 'varchar',
      'len' => '100',
      'audited' => true,
    ),
    'ttt_dost' => 
    array (
      'name' => 'ttt_dost',
      'vname' => 'LBL_TTT_DOST',
      'type' => 'varchar',
      'len' => '100',
      'audited' => true,
    ),
    'ttt_condi_trading' => 
    array (
      'name' => 'ttt_condi_trading',
      'vname' => 'LBL_TTT_CONDI_TRADING',
      'type' => 'varchar',
      'len' => '100',
      'audited' => true,
    ),
    'ttt_declaration' => 
    array (
      'name' => 'ttt_declaration',
      'vname' => 'LBL_TTT_DECLARATION',
      'type' => 'varchar',
      'len' => '100',
      'audited' => true,
    ),
    'ttt_declaration_number' => 
    array (
      'name' => 'ttt_declaration_number',
      'vname' => 'LBL_TTT_DECLARATION_NUMBER',
      'type' => 'varchar',
      'len' => '100',
      'audited' => true,
    ),
    'ttt_declaration_amount' => 
    array (
      'name' => 'ttt_declaration_amount',
      'vname' => 'LBL_TTT_DECLARATION_AMOUNT',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'ttt_vat_amount' => 
    array (
      'name' => 'ttt_vat_amount',
      'vname' => 'LBL_TTT_VAT_AMOUNT',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'ttt_verification' => 
    array (
      'name' => 'ttt_verification',
      'vname' => 'LBL_TTT_VERIFICATION',
      'type' => 'varchar',
      'len' => '100',
      'audited' => true,
    ),
    'ttt_verification_amount' => 
    array (
      'name' => 'ttt_verification_amount',
      'vname' => 'LBL_TTT_VERIFICATION_AMOUNT',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'ttt_vat_import' => 
    array (
      'name' => 'ttt_vat_import',
      'vname' => 'LBL_TTT_VAT_IMPORT',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'ttt_customs_fee' => 
    array (
      'name' => 'ttt_customs_fee',
      'vname' => 'LBL_TTT_CUSTOMS_FEE',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'ttt_clearance' => 
    array (
      'name' => 'ttt_clearance',
      'vname' => 'LBL_TTT_CLEARANCE',
      'type' => 'text',
      'audited' => true,
    ),
    'ttt_arrival_notice' => 
    array (
      'name' => 'ttt_arrival_notice',
      'vname' => 'LBL_TTT_ARRIVAL_NOTICE',
      'type' => 'varchar',
      'len' => '100',
      'audited' => true,
    ),
    'ttt_discharging_port' => 
    array (
      'name' => 'ttt_discharging_port',
      'vname' => 'LBL_TTT_DISCHARGING_PORT',
      'type' => 'varchar',
      'len' => '100',
      'audited' => true,
    ),
    'ttt_vessel' => 
    array (
      'name' => 'ttt_vessel',
      'vname' => 'LBL_TTT_VESSEL',
      'type' => 'varchar',
      'len' => '100',
      'audited' => true,
    ),
    'ttt_vessel_vn' => 
    array (
      'name' => 'ttt_vessel_vn',
      'vname' => 'LBL_TTT_VESSEL_VN',
      'type' => 'varchar',
      'len' => '100',
      'audited' => true,
    ),
    'ttt_owed_contract' => 
    array (
      'name' => 'ttt_owed_contract',
      'vname' => 'LBL_TTT_OWED_CONTRACT',
      'type' => 'varchar',
      'len' => '100',
      'audited' => true,
    ),
    'ttt_storage_fee' => 
    array (
      'name' => 'ttt_storage_fee',
      'vname' => 'LBL_TTT_STORAGE_FEE',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'ttt_vessel_fee' => 
    array (
      'name' => 'ttt_vessel_fee',
      'vname' => 'LBL_TTT_VESSEL_FEE',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'ttt_repair_cont_fee' => 
    array (
      'name' => 'ttt_repair_cont_fee',
      'vname' => 'LBL_TTT_REPAIR_CONT_FEE',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'ttt_deposit_cont_fee' => 
    array (
      'name' => 'ttt_deposit_cont_fee',
      'vname' => 'LBL_TTT_DEPOSIT_CONT_FEE',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'ttt_expected_output' => 
    array (
      'name' => 'ttt_expected_output',
      'vname' => 'LBL_TTT_EXPECTED_OUTPUT',
      'type' => 'float',
      'dbType' => 'double',
      'len' => '10',
      'audited' => true,
    ),
    'ttt_cnt_amount' => 
    array (
      'name' => 'ttt_cnt_amount',
      'vname' => 'LBL_TTT_CNT_AMOUNT',
      'type' => 'float',
      'dbType' => 'double',
      'len' => '10',
      'audited' => true,
    ),
    'ttt_foreign_amount' => 
    array (
      'name' => 'ttt_foreign_amount',
      'vname' => 'LBL_TTT_FOREIGN_AMOUNT',
      'type' => 'float',
      'dbType' => 'double',
      'len' => '10',
      'audited' => true,
    ),
    'ttt_actual_output' => 
    array (
      'name' => 'ttt_actual_output',
      'vname' => 'LBL_TTT_ACTUAL_OUTPUT',
      'type' => 'float',
      'dbType' => 'double',
      'precision' => 2,
      'audited' => true,
    ),
    'ttt_commodity' => 
    array (
      'name' => 'ttt_commodity',
      'vname' => 'LBL_TTT_COMMODITY',
      'type' => 'text',
      'audited' => true,
    ),
    'ttt_tolerance' => 
    array (
      'name' => 'ttt_tolerance',
      'vname' => 'LBL_TTT_TOLERANCE',
      'type' => 'text',
      'audited' => true,
    ),
    'ttt_shipment_method' => 
    array (
      'name' => 'ttt_shipment_method',
      'vname' => 'LBL_TTT_SHIPMENT_METHOD',
      'type' => 'text',
      'audited' => true,
    ),
    'ttt_partial_shipment' => 
    array (
      'name' => 'ttt_partial_shipment',
      'vname' => 'LBL_TTT_PARTIAL_SHIPMENT',
      'type' => 'text',
      'audited' => true,
    ),
    'ttt_transhipment' => 
    array (
      'name' => 'ttt_transhipment',
      'vname' => 'LBL_TTT_TRANSHIPMENT',
      'type' => 'text',
      'audited' => true,
    ),
    'ttt_packing' => 
    array (
      'name' => 'ttt_packing',
      'vname' => 'LBL_TTT_PACKING',
      'type' => 'text',
      'audited' => true,
    ),
    'ttt_quality' => 
    array (
      'name' => 'ttt_quality',
      'vname' => 'LBL_TTT_QUALITY',
      'type' => 'text',
      'audited' => true,
    ),
    'ttt_order_condi' => 
    array (
      'name' => 'ttt_order_condi',
      'vname' => 'LBL_TTT_ORDER_CONDI',
      'type' => 'text',
      'audited' => true,
    ),
    'holding_time' => 
    array (
      'name' => 'holding_time',
      'vname' => 'LBL_HOLDING_TIME',
      'type' => 'enum',
      'options' => 'holding_time_dom',
      'len' => '1',
      'audited' => true,
      'massupdate' => true,
    ),
    'holding_start' => 
    array (
      'name' => 'holding_start',
      'vname' => 'LBL_HOLDING_START',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'holding_end' => 
    array (
      'name' => 'holding_end',
      'vname' => 'LBL_HOLDING_END',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'cashes' => 
    array (
      'name' => 'cashes',
      'type' => 'link',
      'relationship' => 'cashes_collect_details',
      'source' => 'non-db',
      'vname' => 'LBL_CASHES',
    ),
    'payment_type' => 
    array (
      'name' => 'payment_type',
      'vname' => 'LBL_PAYMENT_TYPE',
      'type' => 'enum',
      'options' => 'payment_type_dom',
      'len' => 10,
      'audited' => true,
      'massupdate' => false,
      'display_default' => 'TM',
    ),
    'payment_status' => 
    array (
      'name' => 'payment_status',
      'vname' => 'LBL_PAYMENT_STATUS',
      'type' => 'enum',
      'options' => 'cashes_payment_status_dom',
      'len' => 2,
      'audited' => true,
      'massupdate' => false,
      'display_default' => '1',
    ),
    'cashes_link' => 
    array (
      'name' => 'cashes_link',
      'vname' => 'LBL_CASHES_LINK',
      'type' => 'text',
      'source' => 'non-db',
    ),
    'ignore_payment' => 
    array (
      'name' => 'ignore_payment',
      'vname' => 'LBL_IGNORE_PAYMENT',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => false,
      'default' => 0,
    ),
    'opportunities_commissions' => 
    array (
      'name' => 'opportunities_commissions',
      'type' => 'link',
      'relationship' => 'opportunities_commissions_related',
      'source' => 'non-db',
      'vname' => 'LBL_OPPORTUNITIES_COMMISSIONS',
    ),
    'opportunities_payments' => 
    array (
      'name' => 'opportunities_payments',
      'type' => 'link',
      'relationship' => 'opportunities_related_payments',
      'source' => 'non-db',
      'vname' => 'LBL_OPPORTUNITIES_PAYMENTS',
    ),
    'is_locked' => 
    array (
      'name' => 'is_locked',
      'vname' => 'LBL_IS_LOCKED',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => false,
      'default' => 0,
    ),
    'delivery_orders_tasks' => 
    array (
      'name' => 'delivery_orders_tasks',
      'type' => 'link',
      'relationship' => 'opportunities_delivery_related',
      'source' => 'non-db',
      'vname' => 'LBL_DELIVERY_ORDERS_TASKS',
    ),
    'warehouse_ins' => 
    array (
      'name' => 'warehouse_ins',
      'type' => 'link',
      'relationship' => 'opportunities_warehouse_ins',
      'source' => 'non-db',
      'vname' => 'LBL_WAREHOUSE_INS',
    ),
    'warehouse_link' => 
    array (
      'name' => 'warehouse_link',
      'vname' => 'LBL_WAREHOUSE_LINK',
      'type' => 'text',
      'source' => 'non-db',
    ),
    'order_discount' => 
    array (
      'name' => 'order_discount',
      'vname' => 'LBL_ORDER_DISCOUNT',
      'type' => 'double',
      'audited' => true,
    ),
    'contracts' => 
    array (
      'name' => 'contracts',
      'type' => 'link',
      'relationship' => 'opportunities_contracts',
      'source' => 'non-db',
      'vname' => 'LBL_CONTRACTS',
    ),
    'warehouse_outs' => 
    array (
      'name' => 'warehouse_outs',
      'type' => 'link',
      'relationship' => 'opportunities_warehouse_outs',
      'source' => 'non-db',
      'vname' => 'LBL_WAREHOUSE_OUTS',
    ),
    'delivered_all' => 
    array (
      'name' => 'delivered_all',
      'vname' => 'LBL_DELIVERED_ALL',
      'type' => 'bool',
      'default' => '0',
      'audited' => true,
      'massupdate' => false,
    ),
    'expected_output' => 
    array (
      'name' => 'expected_output',
      'vname' => 'LBL_EXPECTED_OUTPUT',
      'type' => 'float',
      'dbType' => 'double',
      'len' => '10',
      'audited' => true,
    ),
    'actual_output' => 
    array (
      'name' => 'actual_output',
      'vname' => 'LBL_ACTUAL_OUTPUT',
      'type' => 'float',
      'dbType' => 'double',
      'len' => '10',
      'audited' => true,
    ),
    'contract_revenue' => 
    array (
      'name' => 'contract_revenue',
      'vname' => 'LBL_CONTRACT_REVENUE',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'actual_receipts' => 
    array (
      'name' => 'actual_receipts',
      'vname' => 'LBL_ACTUAL_RECEIPTS',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'total_amount_in' => 
    array (
      'name' => 'total_amount_in',
      'vname' => 'LBL_TOTAL_AMOUNT_IN',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'prod_lock' => 
    array (
      'name' => 'prod_lock',
      'vname' => 'LBL_PROD_LOCK',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => false,
    ),
    'contact_id' => 
    array (
      'name' => 'contact_id',
      'vname' => 'LBL_CONTACT_ID',
      'type' => 'id',
      'audited' => true,
      'massupdate' => false,
      'source' => 'non-db',
    ),
    'contact_name' => 
    array (
      'name' => 'contact_name',
      'rname' => 'last_name',
      'db_concat_fields' => 
      array (
        0 => 'first_name',
        1 => 'last_name',
      ),
      'id_name' => 'contact_id',
      'source' => 'non-db',
      'len' => '250',
      'group' => 'contact_name',
      'vname' => 'LBL_CONTACT_NAME',
      'join_name' => 'contacts',
      'type' => 'relate',
      'module' => 'Contacts',
      'link' => 'contacts',
      'table' => 'contacts',
      'massupdate' => false,
      'reportable' => false,
      'relationship_fields' => 
      array (
        'phone_mobile' => 'contact_phone',
      ),
    ),
    'contact_phone' => 
    array (
      'name' => 'contact_phone',
      'vname' => 'LBL_CONTACT_PHONE',
      'type' => 'varchar',
      'len' => 50,
      'audited' => true,
      'source' => 'non-db',
    ),
    'vat_tax' => 
    array (
      'name' => 'vat_tax',
      'vname' => 'LBL_VAT_TAX',
      'type' => 'enum',
      'options' => 'contract_tax_dom',
      'dbType' => 'float',
      'audited' => true,
      'massupdate' => false,
      'display_default' => '0',
    ),
    'vat_amount' => 
    array (
      'name' => 'vat_amount',
      'vname' => 'LBL_VAT_AMOUNT',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'vat_more_amt' => 
    array (
      'name' => 'vat_more_amt',
      'vname' => 'LBL_VAT_MORE_AMT',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'vat_other_amt' => 
    array (
      'name' => 'vat_other_amt',
      'vname' => 'LBL_VAT_OTHER_AMT',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'discount_amount' => 
    array (
      'name' => 'discount_amount',
      'vname' => 'LBL_DISCOUNT_AMOUNT',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'cost_amount' => 
    array (
      'name' => 'cost_amount',
      'vname' => 'LBL_COST_AMOUNT',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'comm_more_amount' => 
    array (
      'name' => 'comm_more_amount',
      'vname' => 'LBL_PROD_COMM_MORE_AMT',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'incurred_amount' => 
    array (
      'name' => 'incurred_amount',
      'vname' => 'LBL_INCURRED_AMOUNT',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'commission_amount' => 
    array (
      'name' => 'commission_amount',
      'vname' => 'LBL_COMMISSION_AMOUNT',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'arise_cost_amount' => 
    array (
      'name' => 'arise_cost_amount',
      'vname' => 'LBL_ARISE_COST_AMOUNT',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'profit_amount' => 
    array (
      'name' => 'profit_amount',
      'vname' => 'LBL_PROFIT_AMOUNT',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'rate_usd' => 
    array (
      'name' => 'rate_usd',
      'vname' => 'LBL_RATE_USD',
      'type' => 'float',
      'dbType' => 'double',
      'audited' => true,
    ),
    'date_payment' => 
    array (
      'name' => 'date_payment',
      'vname' => 'LBL_DATE_PAYMENT',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'payment_period' => 
    array (
      'name' => 'payment_period',
      'vname' => 'LBL_PAYMENT_PERIOD',
      'type' => 'enum',
      'options' => 'invoice_payment_type_dom',
      'len' => '3',
      'audited' => true,
      'massupdate' => false,
    ),
    'order_type' => 
    array (
      'name' => 'order_type',
      'vname' => 'LBL_ORDER_TYPE',
      'type' => 'enum',
      'options' => 'opportunity_order_type_dom',
      'len' => '20',
      'audited' => true,
      'massupdate' => false,
      'display_default' => 'Packages',
    ),
    'acc_reference_code' => 
    array (
      'name' => 'acc_reference_code',
      'rname' => 'reference_code',
      'id_name' => 'account_id',
      'vname' => 'LBL_ACC_REFERENCE_CODE',
      'type' => 'relate',
      'table' => 'accounts',
      'join_name' => 'accounts',
      'isnull' => 'true',
      'module' => 'Accounts',
      'dbType' => 'varchar',
      'link' => 'accounts',
      'len' => '50',
      'source' => 'non-db',
      'quicksearch' => true,
      'massupdate' => false,
    ),
    'account_email' => 
    array (
      'name' => 'account_email',
      'vname' => 'LBL_ACCOUNT_EMAIL',
      'type' => 'varchar',
      'len' => 200,
      'audited' => true,
    ),
    'account_phone' => 
    array (
      'name' => 'account_phone',
      'vname' => 'LBL_ACCOUNT_PHONE',
      'type' => 'varchar',
      'len' => 100,
      'audited' => true,
    ),
    'account_address' => 
    array (
      'name' => 'account_address',
      'vname' => 'LBL_ACCOUNT_ADDRESS',
      'type' => 'text',
      'audited' => true,
    ),
    'account_tax_code' => 
    array (
      'name' => 'account_tax_code',
      'vname' => 'LBL_ACCOUNT_TAX_CODE',
      'type' => 'varchar',
      'len' => 50,
      'audited' => true,
    ),
    'sector_vertical' => 
    array (
      'name' => 'sector_vertical',
      'vname' => 'LBL_SECTOR_VERTICAL',
      'type' => 'varchar',
      'len' => 2,
      'source' => 'non-db',
    ),
    'shipping_fee' => 
    array (
      'name' => 'shipping_fee',
      'vname' => 'LBL_SHIPPING_FEE',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
    ),
    'no_currency' => 
    array (
      'name' => 'no_currency',
      'vname' => 'LBL_NO_CURRENCY',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => false,
      'default' => '0',
    ),
    'disable_auto_price' => 
    array (
      'name' => 'disable_auto_price',
      'vname' => 'LBL_DISABLE_AUTO_PRICE',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => false,
      'default' => '0',
    ),
    'ordered_merge' => 
    array (
      'name' => 'ordered_merge',
      'vname' => 'LBL_ORDERED_MERGE',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => false,
      'default' => '0',
    ),
    'total_foreign_amount' => 
    array (
      'name' => 'total_foreign_amount',
      'vname' => 'LBL_TOTAL_FOREIGN_AMOUNT',
      'type' => 'double',
    ),
    'vat_spec_amt' => 
    array (
      'name' => 'vat_spec_amt',
      'vname' => 'LBL_VAT_SPEC_AMT',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'vat_imp_amt' => 
    array (
      'name' => 'vat_imp_amt',
      'vname' => 'LBL_VAT_IMP_AMT',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'vat_total_amount' => 
    array (
      'name' => 'vat_total_amount',
      'vname' => 'LBL_VAT_TOTAL_AMOUNT',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'ext_incurred_amount' => 
    array (
      'name' => 'ext_incurred_amount',
      'vname' => 'LBL_EXT_INCURRED_AMOUNT',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'int_incurred_amount' => 
    array (
      'name' => 'int_incurred_amount',
      'vname' => 'LBL_INT_INCURRED_AMOUNT',
      'type' => 'currency',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => 'disabled',
    ),
    'is_import' => 
    array (
      'name' => 'is_import',
      'vname' => 'LBL_IS_IMPORT',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => false,
      'default' => '0',
    ),
    'money_payable' => 
    array (
      'name' => 'money_payable',
      'vname' => 'LBL_MONEY_PAYABLE',
      'type' => 'double',
      'source' => 'non-db',
    ),
    'money_deposit' => 
    array (
      'name' => 'money_deposit',
      'vname' => 'LBL_MONEY_DEPOSIT',
      'type' => 'currency',
      'dbType' => 'double',
    ),
    'money_refund' => 
    array (
      'name' => 'money_refund',
      'vname' => 'LBL_MONEY_REFUND',
      'type' => 'currency',
      'dbType' => 'double',
    ),
    'location_nearby' => 
    array (
      'name' => 'location_nearby',
      'vname' => 'LBL_LOCATION_NEARBY',
      'type' => 'varchar',
      'len' => 250,
    ),
    'service_total' => 
    array (
      'name' => 'service_total',
      'vname' => 'LBL_SERVICE_TOTAL',
      'type' => 'int',
      'len' => 4,
      'audited' => true,
    ),
    'service_used' => 
    array (
      'name' => 'service_used',
      'vname' => 'LBL_SERVICE_USED',
      'type' => 'int',
      'len' => 4,
      'audited' => true,
    ),
    'service_left' => 
    array (
      'name' => 'service_left',
      'vname' => 'LBL_SERVICE_LEFT',
      'type' => 'int',
      'len' => 4,
      'audited' => true,
    ),
    'date_return' => 
    array (
      'name' => 'date_return',
      'vname' => 'LBL_DATE_RETURN',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'prod_returned' => 
    array (
      'name' => 'prod_returned',
      'vname' => 'LBL_PROD_RETURNED',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => false,
    ),
    'share_users' => 
    array (
      'name' => 'share_users',
      'vname' => 'LBL_SHARING_USERS',
      'type' => 'enum',
      'source' => 'non-db',
      'module' => 'Users',
      'function' => 'getShareUserOptions',
      'isMultiSelect' => true,
      'isCheckBoxList' => true,
      'displayGroupBy' => 'users.department',
      'massupdate' => false,
    ),
    'share_users_ex' => 
    array (
      'name' => 'share_users_ex',
      'vname' => 'LBL_SHARING_USERS',
      'type' => 'enum',
      'source' => 'non-db',
      'module' => 'Users',
      'function' => 'getShareUserOptions',
      'isMultiSelect' => true,
      'isCheckBoxList' => true,
      'displayGroupBy' => 'users.department',
      'massupdate' => false,
    ),
    'membership_card' => 
    array (
      'name' => 'membership_card',
      'vname' => 'LBL_MEMBERSHIP_CARD',
      'type' => 'varchar',
      'len' => 50,
      'source' => 'non-db',
    ),
    'point_current' => 
    array (
      'name' => 'point_current',
      'vname' => 'LBL_POINT_CURRENT',
      'type' => 'double',
      'precision' => 0,
      'source' => 'non-db',
    ),
    'table_params' => 
    array (
      'name' => 'table_params',
      'vname' => 'Params',
      'type' => 'text',
    ),
    'acc_bill_addr' => 
    array (
      'name' => 'acc_bill_addr',
      'vname' => 'LBL_ACC_BILL_ADDR',
      'type' => 'text',
      'source' => 'non-db',
    ),
    're_calc_profit' => 
    array (
      'name' => 're_calc_profit',
      'vname' => 'LBL_RE_CALC_PROFIT',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => false,
      'default' => 0,
    ),
    'date_record' => 
    array (
      'name' => 'date_record',
      'vname' => 'LBL_DATE_RECORD',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'is_export_vat' => 
    array (
      'name' => 'is_export_vat',
      'vname' => 'LBL_IS_EXPORT_VAT',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => false,
      'default' => 0,
    ),
  ),
  'optimistic_locking' => true,
  'templates' => 
  array (
    'assignable' => 'assignable',
    'groups' => 'groups',
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
