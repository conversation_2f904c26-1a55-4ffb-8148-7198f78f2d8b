incomCRM.language.setLanguage('Users', {"ERR_DELETE_RECORD":"M\u1ed9t s\u1ed1 h\u1ed3 s\u01a1 ph\u1ea3i \u0111\u01b0\u1ee3c ch\u1ec9 \u0111\u1ecbnh \u0111\u1ec3 x\u00f3a t\u00e0i kho\u1ea3n.","ERR_EMAIL_INCORRECT":"Cung c\u1ea5p m\u1ed9t \u0111\u1ecba ch\u1ec9 email h\u1ee3p l\u1ec7 \u0111\u1ec3 t\u1ea1o v\u00e0 g\u1eedi m\u1eadt kh\u1ea9u.","ERR_EMAIL_NO_OPTS":"Kh\u00f4ng t\u00ecm th\u1ea5y c\u00e0i \u0111\u1eb7t cho Inbound Email.","ERR_ENTER_CONFIRMATION_PASSWORD":"Vui l\u00f2ng nh\u1eadp m\u1eadt kh\u1ea9u c\u1ee7a b\u1ea1n \u0111\u1ec3 x\u00e1c nh\u1eadn","ERR_ENTER_NEW_PASSWORD":"Vui l\u00f2ng nh\u1eadp m\u1eadt kh\u1ea9u m\u1edbi c\u1ee7a b\u1ea1n","ERR_ENTER_OLD_PASSWORD":"Vui l\u00f2ng nh\u1eadp m\u1eadt kh\u1ea9u c\u0169","ERR_IE_FAILURE1":"[Click v\u00e0o \u0111\u00e2y \u0111\u1ec3 quay l\u1ea1i]","ERR_IE_FAILURE2":"C\u00f3 m\u1ed9t l\u1ed7i nh\u1ecf khi k\u1ebft n\u1ed1i t\u1edbi t\u00e0i kho\u1ea3n email.  Vui l\u00f2ng ki\u1ec3m tra l\u1ea1i thi\u1ebft l\u1eadp c\u1ee7a b\u1ea1n v\u00e0 th\u1eed l\u1ea1i.","ERR_IE_MISSING_REQUIRED":"Thi\u1ebft l\u1eadp cho Inbound Email \u0111ang thi\u1ebfu m\u1ed9t v\u00e0i th\u00f4ng tin. Vui l\u00f2ng ki\u1ec3m tra l\u1ea1i thi\u1ebft l\u1eadp c\u1ee7a b\u1ea1n v\u00e0 th\u1eed l\u1ea1i. N\u1ebfu b\u1ea1n kh\u00f4ng ph\u1ea3i thi\u1ebft l\u1eadp trong Inbound Email vui l\u00f2ng x\u00f3a t\u1ea5t c\u1ea3 c\u00e1c tr\u01b0\u1eddng trong ph\u1ea7n n\u00e0y.","ERR_INVALID_PASSWORD":"B\u1ea1n ph\u1ea3i nh\u1eadp t\u00ean \u0111\u0103ng nh\u1eadp v\u00e0 m\u1eadt kh\u1ea9u h\u1ee3p l\u1ec7.","ERR_NO_LOGIN_MOBILE":"L\u1ea7n login \u0111\u1ea7u tin c\u1ee7a b\u1ea1n t\u1ea1i \u1ee9ng d\u1ee5ng n\u00e0y ph\u1ea3i d\u00f9ng m\u1ed9t tr\u00ecnh duy\u1ec7t kh\u00f4ng ph\u1ea3i tr\u00ean mobile ho\u1eb7c ch\u1ebf \u0111\u1ed9 b\u00ecnh th\u01b0\u1eddng. Vui l\u00f2ng quay l\u1ea1i v\u1edbi m\u1ed9t tr\u00ecnh duy\u1ec7t \u0111\u1ea7y \u0111\u1ee7 t\u00ednh n\u0103ng ho\u1eb7c click v\u00e0o link b\u00ean d\u01b0\u1edbi. Ch\u00fang t\u00f4i xin l\u1ed7i v\u00ec s\u1ef1 b\u1ea5t ti\u1ec7n n\u00e0y.","ERR_LAST_ADMIN_1":"T\u00ean \u0111\u0103ng nh\u1eadp \"","ERR_LAST_ADMIN_2":"\" l\u00e0 ng\u01b0\u1eddi d\u00f9ng cu\u1ed1i c\u00f9ng v\u1edbi quy\u1ec1n truy c\u1eadp l\u00e0 qu\u1ea3n tr\u1ecb h\u1ec7 th\u1ed1ng. \u00cdt nh\u1ea5t ng\u01b0\u1eddi d\u00f9ng ph\u1ea3i c\u00f3 quy\u1ec1n qu\u1ea3n tr\u1ecb h\u1ec7 th\u1ed1ng (Administrator)","ERR_PASSWORD_CHANGE_FAILED_1":"Thay \u0111\u1ed5i m\u1eadt kh\u1ea9u ng\u01b0\u1eddi d\u00f9ng ","ERR_PASSWORD_CHANGE_FAILED_2":" kh\u00f4ng th\u00e0nh c\u00f4ng. M\u1eadt kh\u1ea9u m\u1edbi ph\u1ea3i \u0111\u01b0\u1ee3c thi\u1ebft l\u1eadp.","ERR_PASSWORD_INCORRECT_OLD_1":"M\u1eadt kh\u1ea9u c\u0169 kh\u00f4ng h\u1ee3p l\u1ec7 cho ng\u01b0\u1eddi d\u00f9ng n\u00e0y ","ERR_PASSWORD_INCORRECT_OLD_2":". Nh\u1eadp l\u1ea1i th\u00f4ng tin m\u1eadt kh\u1ea9u.","ERR_PASSWORD_MISMATCH":"X\u00e1c nh\u1eadn m\u1eadt kh\u1ea9u kh\u00f4ng h\u1ee3p l\u1ec7.","ERR_PASSWORD_USERNAME_MISSMATCH":"B\u1ea1n ph\u1ea3i ch\u1ec9 \u0111\u1ecbnh T\u00ean ng\u01b0\u1eddi d\u00f9ng v\u00e0 \u0110\u1ecba ch\u1ec9 email h\u1ee3p l\u1ec7.","ERR_PASSWORD_LINK_EXPIRED":"Li\u00ean k\u1ebft c\u1ee7a b\u1ea1n \u0111\u00e3 h\u1ebft h\u1ea1n, xin vui l\u00f2ng t\u1ea1o m\u1edbi","ERR_REENTER_PASSWORDS":"Vui l\u00f2ng nh\u1eadp x\u00e1c nh\u1eadn m\u1eadt kh\u1ea9u. Gi\u00e1 tr\u1ecb m\u1eadt kh\u1ea9u m\u1edbi v\u00e0 x\u00e1c nh\u1eadn m\u1eadt kh\u1ea9u kh\u00f4ng kh\u1edbp nhau.","ERR_REPORT_LOOP":"H\u1ec7 th\u1ed1ng ph\u00e1t hi\u1ec7n c\u00f3 m\u1ed9t b\u00e1o c\u00e1o l\u1eb7p l\u1ea1i. Ng\u01b0\u1eddi d\u00f9ng kh\u00f4ng th\u1ec3 b\u00e1o c\u00e1o t\u1edbi ch\u00ednh h\u1ecd v\u00e0 c\u00e1c nh\u00e0 qu\u1ea3n l\u00fd c\u0169ng kh\u00f4ng th\u1ec3 b\u00e1o c\u00e1o cho h\u1ecd.","ERR_RULES_NOT_MET":"M\u1eadt kh\u1ea9u m\u00e0 b\u1ea1n \u0111\u00e3 nh\u1eadp kh\u00f4ng \u0111\u00e1p \u1ee9ng y\u00eau c\u1ea7u m\u1eadt kh\u1ea9u. H\u00e3y th\u1eed l\u1ea1i.","ERR_USER_INFO_NOT_FOUND":"Kh\u00f4ng t\u00ecm th\u1ea5y th\u00f4ng tin ng\u01b0\u1eddi d\u00f9ng","ERR_USER_NAME_EXISTS_1":"T\u00ean \u0111\u0103ng nh\u1eadp ","ERR_USER_NAME_EXISTS_2":" \u0111\u00e3 t\u1ed3n t\u1ea1i. T\u1ea1o b\u1ea3n sao ng\u01b0\u1eddi d\u00f9ng kh\u00f4ng cho ph\u00e9p. Thay \u0111\u1ed5i t\u00ean \u0111\u0103ng nh\u1eadp \u0111\u1ec3 c\u00f3 t\u00ean \u0111\u0103ng nh\u1eadp duy nh\u1ea5t.","ERR_USER_IS_LOCKED_OUT":"T\u00e0i kho\u1ea3n n\u00e0y \u0111\u00e3 b\u1ecb kh\u00f3a trong \u1ee9ng d\u1ee5ng v\u00e0 kh\u00f4ng th\u1ec3 \u0111\u0103ng nh\u1eadp.","ERR_EMAIL_NOT_SENT_ADMIN":"H\u1ec7 th\u1ed1ng kh\u00f4ng th\u1ec3 x\u1eed l\u00fd y\u00eau c\u1ea7u c\u1ee7a b\u1ea1n. H\u00e3y ki\u1ec3m tra: ","ERR_SMTP_URL_SMTP_PORT":"SMTP Server URL v\u00e0 Port","ERR_SMTP_USERNAME_SMTP_PASSWORD":"T\u00ean ng\u01b0\u1eddi d\u00f9ng SMTP v\u00e0 M\u1eadt kh\u1ea9u SMTP","ERR_RECIPIENT_EMAIL":"\u0110\u1ecba ch\u1ec9 email c\u1ee7a ng\u01b0\u1eddi nh\u1eadn","ERR_SERVER_STATUS":"T\u00ecnh tr\u1ea1ng m\u00e1y ch\u1ee7 c\u1ee7a b\u1ea1n","ERR_SERVER_SMTP_EMPTY":"H\u1ec7 th\u1ed1ng n\u00e0y kh\u00f4ng th\u1ec3 g\u1eedi m\u1ed9t email cho ng\u01b0\u1eddi d\u00f9ng. H\u00e3y ki\u1ec3m tra Mail Transfer Agent trong C\u00e0i \u0111\u1eb7t Email. ","LBL_ADDRESS_CITY":"Th\u00e0nh ph\u1ed1","LBL_ADDRESS_COUNTRY":"Qu\u1ed1c gia","LBL_ADDRESS_INFORMATION":"\u0110\u1ecba ch\u1ec9","LBL_ADDRESS_POSTALCODE":"M\u00e3 b\u01b0u \u0111i\u1ec7n","LBL_ADDRESS_STATE":"Qu\u1eadn\/Huy\u1ec7n","LBL_ADDRESS_STREET":"\u0110\u01b0\u1eddng","LBL_ADDRESS":"\u0110\u1ecba ch\u1ec9","LBL_ADMIN_USER":"H\u1ec7 th\u1ed1ng qu\u1ea3n tr\u1ecb vi\u00ean","LBL_ADMIN_DESC":"Ng\u01b0\u1eddi d\u00f9ng c\u00f3 th\u1ec3 truy c\u1eadp v\u00e0o trang qu\u1ea3n l\u00fd t\u1ea5t c\u1ea3 h\u1ed3 s\u01a1.","LBL_REGULAR_DESC":"Ng\u01b0\u1eddi d\u00f9ng c\u00f3 th\u1ec3 truy c\u1eadp c\u00e1c module v\u00e0 c\u00e1c h\u1ed3 s\u01a1 d\u1ef1a tr\u00ean vai tr\u00f2.","LBL_ADMIN":"Qu\u1ea3n tr\u1ecb h\u1ec7 th\u1ed1ng","LBL_ANY_EMAIL":"Email","LBL_ANY_PHONE":"\u0110i\u1ec7n tho\u1ea1i","LBL_BUTTON_CREATE":"T\u1ea1o","LBL_BUTTON_EDIT":"Ch\u1ec9nh s\u1eeda","LBL_CALENDAR_OPTIONS":"T\u00f9y ch\u1ecdn l\u1ecbch","LBL_CHANGE_PASSWORD":"Thay \u0111\u1ed5i m\u1eadt kh\u1ea9u","LBL_CHANGE_SYSTEM_PASSWORD":"Xin vui l\u00f2ng cung c\u1ea5p m\u1ed9t m\u1eadt kh\u1ea9u m\u1edbi.","LBL_CHANGE_PASSWORD_TITLE":"\u0110\u1ed5i m\u1eadt kh\u1ea9u","LBL_CHOOSE_A_KEY":"Ch\u1ecdn m\u1ed9t kh\u00f3a \u0111\u1ec3 ng\u0103n ch\u1eb7n xu\u1ea5t b\u1ea3n tr\u00e1i ph\u00e9p l\u1ecbch bi\u1ec3u c\u1ee7a b\u1ea1n","LBL_CHOOSE_WHICH":"Ch\u1ecdn tab b\u1ea1n mu\u1ed1n hi\u1ec3n th\u1ecb","LBL_CITY":"Th\u00e0nh ph\u1ed1","LBL_CLEAR_BUTTON_TITLE":"X\u00f3a","LBL_CONFIRM_PASSWORD":"X\u00e1c nh\u1eadn m\u1eadt kh\u1ea9u","LBL_CONFIRM_REGULAR_USER":"B\u1ea1n \u0111\u00e3 thay \u0111\u1ed5i c\u00e1c lo\u1ea1i ng\u01b0\u1eddi d\u00f9ng t\u1eeb h\u1ec7 th\u1ed1ng qu\u1ea3n tr\u1ecb vi\u00ean \u0111\u1ec3 s\u1eed d\u1ee5ng th\u01b0\u1eddng xuy\u00ean. Sau khi l\u01b0u thay \u0111\u1ed5i n\u00e0y, ng\u01b0\u1eddi s\u1eed d\u1ee5ng s\u1ebd kh\u00f4ng c\u00f2n c\u00f3 quy\u1ec1n qu\u1ea3n tr\u1ecb h\u1ec7 th\u1ed1ng.\\n\\nNh\u1ea5p OK.? \u0111\u1ec3 ti\u1ebfn h\u00e0nh \\ nNh\u1ea5p Cancel.? tr\u1edf l\u1ea1i \u0111\u1ec3 ghi l\u1ea1i.","LBL_COUNTRY":"Qu\u1ed1c gia","LBL_CURRENCY_TEXT":"Ti\u1ec1n t\u1ec7 m\u1eb7c \u0111\u1ecbnh","LBL_CURRENCY":"Ti\u1ec1n t\u1ec7","LBL_CURRENCY_EXAMPLE":"V\u00ed d\u1ee5 hi\u1ec3n th\u1ecb ti\u1ec1n","LBL_CURRENCY_SIG_DIGITS":"S\u1ed1 ch\u1eef s\u1ed1 ph\u1ea7n th\u1eadp ph\u00e2n","LBL_CURRENCY_SIG_DIGITS_DESC":"S\u1ed1 kho\u1ea3ng tr\u1ed1ng cho hi\u1ec3n th\u1ecb ti\u1ec1n","LBL_NUMBER_GROUPING_SEP":"K\u00ed t\u1ef1 ph\u00e2n c\u00e1ch h\u00e0ng ng\u00e0n","LBL_NUMBER_GROUPING_SEP_TEXT":"K\u00ed t\u1ef1 ng\u01b0\u1eddi d\u00f9ng s\u1eed d\u1ee5ng \u0111\u1ec3 ph\u00e2n c\u00e1ch h\u00e0ng ng\u00e0n","LBL_DECIMAL_SEP":"K\u00ed hi\u1ec7u th\u1eadp ph\u00e2n","LBL_DECIMAL_SEP_TEXT":"K\u00ed t\u1ef1 d\u00f9ng \u0111\u1ec3 ph\u00e2n c\u00e1ch ph\u1ea7n nguy\u00ean v\u00e0 ph\u1ea7n th\u1eadp ph\u00e2n","LBL_DATE_FORMAT_TEXT":"Thi\u1ebft l\u1eadp \u0111\u1ecbnh d\u1ea1ng hi\u1ec3n th\u1ecb cho ng\u00e0y th\u00e1ng","LBL_DATE_FORMAT":"\u0110\u1ecbnh d\u1ea1ng ng\u00e0y:","LBL_DEFAULT_SUBPANEL_TITLE":"Ng\u01b0\u1eddi d\u00f9ng","LBL_DEPARTMENT":"Ph\u00f2ng ban","LBL_DESCRIPTION":"M\u00f4 t\u1ea3","LBL_DISPLAY_TABS":"Nh\u1eefng tab hi\u1ec3n th\u1ecb","LBL_DST_INSTRUCTIONS":"(+DST) indicates the observance of Daylight Savings Time","LBL_EDIT_TABS":"Tab ch\u1ec9nh s\u1eeda","LBL_EDIT":"Ch\u1ec9nh s\u1eeda","LBL_USER_HASH":"M\u1eadt kh\u1ea9u","LBL_AUTHENTICATE_ID":"M\u00e3 x\u00e1c th\u1ef1c","LBL_ACCOUNT_NAME":"T\u00ean t\u00e0i kho\u1ea3n","LBL_USER_PREFERENCES":"Tu\u1ef3 ch\u1ecdn ng\u01b0\u1eddi d\u00f9ng","LBL_EXT_AUTHENTICATE":"X\u00e1c th\u1ef1c b\u00ean ngo\u00e0i","LBL_EMAIL_OTHER":"Email 2","LBL_EMAIL":"\u0110\u1ecba ch\u1ec9 email","LBL_EMAIL_CHARSET":"Thi\u1ebft l\u1eadp k\u00ed t\u1ef1 Outbound","LBL_EMAIL_EDITOR_OPTION":"\u0110\u1ecbnh d\u1ea1ng so\u1ea1n th\u1ea3o","LBL_EMAIL_GMAIL_DEFAULTS":"M\u1eb7c \u0111\u1ecbnh Prefill Gmail","LBL_EMAIL_LINK_TYPE":"Tr\u00ecnh g\u1edfi nh\u1eadn mail","LBL_EMAIL_LINK_TYPE_HELP":"<b>System Default Mail Client<\/b> : default email client set by the system adminstrator.<br><b>incomSoft Mail Client<\/b> : email client in the incomSoft Emails module.<br><b>External Mail Client<\/b> : other email client, such as Microsoft Outlook.","LBL_EMAIL_NOT_SENT":"H\u1ec7 th\u1ed1ng kh\u00f4ng th\u1ec3 x\u1eed l\u00fd y\u00eau c\u1ea7u c\u1ee7a b\u1ea1n. Xin vui l\u00f2ng li\u00ean h\u1ec7 v\u1edbi c\u00e1c qu\u1ea3n tr\u1ecb h\u1ec7 th\u1ed1ng.","LBL_EMAIL_SHOW_COUNTS":"Hi\u1ec3n th\u1ecb s\u1ed1 email?","LBL_EMAIL_SIGNATURE_ERROR1":"Ch\u1eef k\u00ed bu\u1ed9c ph\u1ea3i c\u00f3 t\u00ean.","LBL_EMAIL_SMTP_SSL":"Ch\u1ea5p nh\u1eadn SMTP qua SSL","LBL_EMAIL_TEMPLATE_MISSING":"Kh\u00f4ng c\u00f3 m\u1eabu email \u0111\u01b0\u1ee3c ch\u1ecdn cho c\u00e1c email c\u00f3 ch\u1ee9a m\u1eadt kh\u1ea9u s\u1ebd \u0111\u01b0\u1ee3c g\u1eedi cho ng\u01b0\u1eddi s\u1eed d\u1ee5ng. H\u00e3y ch\u1ecdn m\u1ed9t m\u1eabu th\u01b0 \u0111i\u1ec7n t\u1eed trong trang Qu\u1ea3n l\u00fd m\u1eadt kh\u1ea9u.","LBL_EMPLOYEE_STATUS":"T\u00ecnh tr\u1ea1ng nh\u00e2n vi\u00ean:","LBL_ERROR":"L\u1ed7i","LBL_EXPORT_CHARSET":"Thi\u1ebft l\u1eadp k\u00ed t\u1ef1 Import\/Export","LBL_EXPORT_CHARSET_DESC":"Ch\u1ecdn k\u00ed t\u1ef1 \u0111\u01b0\u1ee3c d\u00f9ng trong \u0111\u1ecba ph\u01b0\u01a1ng b\u1ea1n. Thu\u1ed9c t\u00ednh n\u00e0y s\u1ebd d\u00f9ng \u0111\u1ec3 nh\u1eadp d\u1eef li\u1ec7u, outbound email, xu\u1ea5t d\u1ea1ng file .csv, PDF v\u00e0 vCard.","LBL_EXPORT_DELIMITER":"Ph\u00e2n c\u00e1ch xu\u1ea5t","LBL_EXPORT_DELIMITER_DESC":"X\u00e1c \u0111\u1ecbnh k\u00ed t\u1ef1 s\u1eed d\u1ee5ng \u0111\u1ec3 chia t\u00e1ch d\u1eef li\u1ec7u xu\u1ea5t","LBL_FAX_PHONE":"Fax","LBL_FAX":"Fax","LBL_FIRST_NAME":"T\u00ean","LBL_GENERATE_PASSWORD_BUTTON_KEY":"G","LBL_SYSTEM_GENERATED_PASSWORD":"H\u1ec7 th\u1ed1ng t\u1ea1o ra m\u1eadt kh\u1ea9u","LBL_GENERATE_PASSWORD_BUTTON_LABEL":"Thi\u1ebft l\u1eadp l\u1ea1i m\u1eadt kh\u1ea9u","LBL_GENERATE_PASSWORD_BUTTON_TITLE":"Thi\u1ebft l\u1eadp l\u1ea1i m\u1eadt kh\u1ea9u [Alt+G]","LBL_GENERATE_PASSWORD":"Thi\u1ebft l\u1eadp l\u1ea1i m\u1eadt kh\u1ea9u","LBL_GROUP_DESC":"Ng\u01b0\u1eddi d\u00f9ng n\u00e0y s\u1ebd ch\u1ec9 \u0111\u01b0\u1ee3c s\u1eed d\u1ee5ng \u0111\u1ec3 g\u00e1n m\u1ee5c t\u1edbi m\u1ed9t nh\u00f3m th\u00f4ng qua ch\u1ee9c n\u0103ng Inbound Email. Ng\u01b0\u1eddi d\u00f9ng n\u00e0y kh\u00f4ng th\u1ec3 login qua giao di\u1ec7n web c\u1ee7a incomSoft.","LBL_GROUP_USER_STATUS":"Nh\u00f3m ng\u01b0\u1eddi d\u00f9ng","LBL_GROUP_USER":"Nh\u00f3m ng\u01b0\u1eddi d\u00f9ng?","LBL_HIDE_TABS":"\u1ea8n tab","LBL_HOME_PHONE":"\u0110T nh\u00e0:","LBL_INBOUND_TITLE":"Th\u00f4ng tin t\u00e0i kho\u1ea3n","LBL_IS_ADMIN":"L\u00e0 qu\u1ea3n tr\u1ecb h\u1ec7 th\u1ed1ng","LBL_LANGUAGE":"Ng\u00f4n ng\u1eef","LBL_LAST_NAME":"H\u1ecd & T\u00ean","LBL_LAST_NAME_SLASH_NAME":"T\u00ean H\u1ecd\/T\u00ean","LBL_LAYOUT_OPTIONS":"T\u00f9y ch\u1ecdn giao di\u1ec7n","LBL_LDAP":"LDAP","LBL_LDAP_AUTHENTICATION":"X\u00e1c th\u1ef1c LDAP","LBL_LIST_ACCEPT_STATUS":"T\u00ecnh tr\u1ea1ng ch\u1ea5p nh\u1eadn","LBL_LIST_ADMIN":"Qu\u1ea3n tr\u1ecb?","LBL_LIST_DEPARTMENT":"Ph\u00f2ng ban","LBL_LIST_EMAIL":"Email","LBL_LIST_FORM_TITLE":"Ng\u01b0\u1eddi d\u00f9ng","LBL_LIST_GROUP":"Nh\u00f3m?","LBL_LIST_LAST_NAME":"H\u1ecd","LBL_LIST_MEMBERSHIP":"Th\u00e0nh vi\u00ean","LBL_LIST_NAME":"H\u1ecd & T\u00ean","LBL_LIST_PRIMARY_PHONE":"\u0110T ch\u00ednh","LBL_LIST_PASSWORD":"M\u1eadt kh\u1ea9u","LBL_LIST_STATUS":"T\u00ecnh tr\u1ea1ng","LBL_LIST_TITLE":"Ti\u00eau \u0111\u1ec1","LBL_LIST_USER_NAME":"T\u00ean \u0111\u0103ng nh\u1eadp","LBL_LOCALE_DEFAULT_NAME_FORMAT":"\u0110\u1ecbnh d\u1ea1ng hi\u1ec3n th\u1ecb t\u00ean","LBL_LOCALE_DESC_FIRST":"[T\u00ean]","LBL_LOCALE_DESC_LAST":"[H\u1ecd]","LBL_LOCALE_DESC_SALUTATION":"[L\u1eddi ch\u00e0o]","LBL_LOCALE_DESC_TITLE":"[Ti\u00eau \u0111\u1ec1]","LBL_LOCALE_EXAMPLE_NAME_FORMAT":"V\u00ed d\u1ee5","LBL_LOCALE_NAME_FORMAT_DESC":"Thi\u1ebft l\u1eadp hi\u1ec3n th\u1ecb h\u1ecd t\u00ean","LBL_LOCALE_NAME_FORMAT_DESC_2":"<br><i>\"s\": C\u00e2u ch\u00e0o <br>\"f\": T\u00ean<br>\"l\": H\u1ecd<\/i>","LBL_SAVED_SEARCH":"L\u01b0u t\u00ecm ki\u1ebfm & giao di\u1ec7n","LBL_LOGIN_BUTTON_KEY":"L","LBL_LOGIN_BUTTON_LABEL":"\u0110\u0103ng nh\u1eadp","LBL_LOGIN_BUTTON_TITLE":"\u0110\u0103ng nh\u1eadp [Alt+L]","LBL_LOGIN_WELCOME_TO":"Xin ch\u00e0o","LBL_LOGIN_OPTIONS":"T\u00f9y ch\u1ecdn","LBL_LOGIN_FORGOT_PASSWORD":"Qu\u00ean m\u1eadt kh\u1ea9u","LBL_LOGIN_SUBMIT":"G\u1eedi","LBL_LOGIN_ATTEMPTS_OVERRUN":"Qu\u00e1 nhi\u1ec1u th\u1ea5t b\u1ea1i trong n\u1ed7 l\u1ef1c \u0111\u0103ng nh\u1eadp.","LBL_LOGIN_LOGIN_TIME_ALLOWED":"B\u1ea1n c\u00f3 th\u1ec3 th\u1eed \u0111\u0103ng nh\u1eadp l\u1ea1i trong ","LBL_LOGIN_LOGIN_TIME_DAYS":"ng\u00e0y.","LBL_LOGIN_LOGIN_TIME_HOURS":"gi\u1edd.","LBL_LOGIN_LOGIN_TIME_MINUTES":"ph\u00fat.","LBL_LOGIN_LOGIN_TIME_SECONDS":"gi\u00e2y.","LBL_LOGIN_ADMIN_CALL":"Xin vui l\u00f2ng li\u00ean h\u1ec7 v\u1edbi c\u00e1c qu\u1ea3n tr\u1ecb h\u1ec7 th\u1ed1ng.","LBL_IE6COMPAT_CHECK":"CRM ph\u00e1t hi\u1ec7n r\u1eb1ng b\u1ea1n \u0111ang s\u1eed d\u1ee5ng Internet Explorer 6, m\u00e0 kh\u00f4ng \u0111\u01b0\u1ee3c h\u1ed7 tr\u1ee3 b\u1edfi t\u1ea5t c\u1ea3 Giao di\u1ec7n CRM. C\u00e1c \"\u0111\u01b0\u1eddng IE6\" ch\u1ee7 \u0111\u1ec1, m\u00e0 s\u1ebd l\u00e0m vi\u1ec7c v\u1edbi Internet Explorer 6, \u0111\u00e3 \u0111\u01b0\u1ee3c ch\u1ecdn cho b\u1ea1n.","LBL_THEME_PICKER_IE6COMPAT_CHECK":"C\u1ea3nh b\u00e1o: Internet Explorer 6 kh\u00f4ng \u0111\u01b0\u1ee3c h\u1ed7 tr\u1ee3 cho c\u00e1c ch\u1ee7 \u0111\u1ec1 \u0111\u01b0\u1ee3c ch\u1ecdn. C\u00e1c \"\u0111\u01b0\u1eddng IE6\" ch\u1ee7 \u0111\u1ec1, m\u00e0 s\u1ebd l\u00e0m vi\u1ec7c v\u1edbi Internet Explorer 6, \u0111\u00e3 \u0111\u01b0\u1ee3c ch\u1ecdn cho b\u1ea1n.","LBL_MAIL_FROMADDRESS":"Tr\u1ea3 l\u1eddi","LBL_MAIL_FROMNAME":"T\u00ean","LBL_MAIL_OPTIONS_TITLE":"T\u00f9y ch\u1ecdn email","LBL_MAIL_SENDTYPE":"B\u1ed9 chuy\u1ec3n mail","LBL_MAIL_SMTPAUTH_REQ":"S\u1eed d\u1ee5ng ch\u1ee9ng th\u1ef1c SMTP?","LBL_MAIL_SMTPPASS":"M\u1eadt kh\u1ea9u SMTP","LBL_MAIL_SMTPPORT":"C\u1ed5ng SMTP","LBL_MAIL_SMTPSERVER":"Server SMTP","LBL_MAIL_SMTPUSER":"T\u00ean \u0111\u0103ng nh\u1eadp SMTP","LBL_MAILMERGE_TEXT":"B\u1eadt h\u1ee3p nh\u1ea5t mail. (H\u1ee3p nh\u1eadt mail c\u0169ng ph\u1ea3i cho ph\u00e9p b\u1edfi qu\u1ea3n tr\u1ecb h\u1ec7 th\u1ed1ng trong ph\u1ea7n C\u1ea5u h\u00ecnh h\u1ec7 th\u1ed1ng)","LBL_MAILMERGE":"H\u1ee3p nh\u1ea5t mail","LBL_MAX_TAB":"S\u1ed1 tab cha","LBL_MAX_TAB_DESCRIPTION":"S\u1ed1 tab hi\u1ec3n ph\u00eda tr\u00ean c\u00f9ng c\u1ee7a trang tr\u01b0\u1edbc khi menu tr\u01b0\u1ee3t xu\u1ea5t hi\u1ec7n","LBL_MAX_SUBTAB":"S\u1ed1 tab con","LBL_MAX_SUBTAB_DESCRIPTION":"S\u1ed1 tab con hi\u1ec3n th\u1ecb tr\u00ean tab tr\u01b0\u1edbc khi menu tr\u01b0\u1ee3t xu\u1ea5t hi\u1ec7n","LBL_MESSENGER_ID":"T\u00ean IM","LBL_MESSENGER_TYPE":"Lo\u1ea1i IM","LBL_MOBILE_PHONE":"Di \u0111\u1ed9ng","LBL_MODIFIED_BY":"Ch\u1ec9nh s\u1eeda b\u1edfi","LBL_MODIFIED_BY_ID":"Ch\u1ec9nh s\u1eeda b\u1edfi ID","LBL_MODULE_NAME":"Ng\u01b0\u1eddi d\u00f9ng","LBL_MODULE_TITLE":"Ng\u01b0\u1eddi d\u00f9ng: Trang ch\u1ee7","LBL_NAME":"H\u1ecd t\u00ean","LBL_NAVIGATION_PARADIGM":"Di chuy\u1ec3n","LBL_NAVIGATION_PARADIGM_DESCRIPTION":"M\u1ed7i module l\u00e0 m\u1ed9t tab hay c\u00e1c module hi\u1ec3n th\u1ecb trong m\u1ed9t nh\u00f3m tab.","LBL_NEW_FORM_TITLE":"Ng\u01b0\u1eddi d\u00f9ng m\u1edbi","LBL_NEW_PASSWORD":"M\u1eadt kh\u1ea9u m\u1edbi","LBL_NEW_PASSWORD1":"M\u1eadt kh\u1ea9u","LBL_NEW_PASSWORD2":"X\u00e1c nh\u1eadn m\u1eadt kh\u1ea9u","LBL_NEW_USER_PASSWORD_1":"M\u1eadt kh\u1ea9u \u0111\u00e3 \u0111\u01b0\u1ee3c thay \u0111\u1ed5i th\u00e0nh c\u00f4ng.","LBL_NEW_USER_PASSWORD_2":"M\u1ed9t email \u0111\u00e3 \u0111\u01b0\u1ee3c g\u1eedi cho ng\u01b0\u1eddi d\u00f9ng c\u00f3 ch\u1ee9a m\u1eadt kh\u1ea9u do h\u1ec7 th\u1ed1ng t\u1ea1o ra.","LBL_NEW_USER_PASSWORD_3":"M\u1eadt kh\u1ea9u \u0111\u01b0\u1ee3c t\u1ea1o ra th\u00e0nh c\u00f4ng.","LBL_NEW_USER_BUTTON_KEY":"N","LBL_NEW_USER_BUTTON_LABEL":"Ng\u01b0\u1eddi d\u00f9ng m\u1edbi","LBL_NEW_USER_BUTTON_TITLE":"Ng\u01b0\u1eddi d\u00f9ng m\u1edbi [Alt+N]","LBL_NORMAL_LOGIN":"Chuy\u1ec3n qua ch\u1ebf \u0111\u1ed9 xem b\u00ecnh th\u01b0\u1eddng","LBL_NOTES":"Ghi ch\u00fa","LBL_OFFICE_PHONE":"\u0110T v\u0103n ph\u00f2ng","LBL_OLD_PASSWORD":"M\u1eadt kh\u1ea9u c\u0169","LBL_OTHER_EMAIL":"Email kh\u00e1c","LBL_OTHER_PHONE":"\u0110T kh\u00e1c","LBL_OTHER":"Kh\u00e1c","LBL_PASSWORD":"M\u1eadt kh\u1ea9u","LBL_PASSWORD_GENERATED":"M\u1eadt kh\u1ea9u m\u1edbi t\u1ea1o ra","LBL_PASSWORD_EXPIRATION_LOGIN":"M\u1eadt kh\u1ea9u c\u1ee7a b\u1ea1n \u0111\u00e3 h\u1ebft h\u1ea1n. Xin vui l\u00f2ng cung c\u1ea5p m\u1ed9t m\u1eadt kh\u1ea9u m\u1edbi.","LBL_PASSWORD_EXPIRATION_GENERATED":"M\u1eadt kh\u1ea9u c\u1ee7a b\u1ea1n \u0111\u01b0\u1ee3c t\u1ea1o ra b\u1edfi h\u1ec7 th\u1ed1ng","LBL_PASSWORD_EXPIRATION_TIME":"M\u1eadt kh\u1ea9u c\u1ee7a b\u1ea1n \u0111\u00e3 h\u1ebft h\u1ea1n. Xin vui l\u00f2ng cung c\u1ea5p m\u1ed9t m\u1eadt kh\u1ea9u m\u1edbi.","LBL_PSW_MODIFIED":"M\u1eadt kh\u1ea9u m\u1edbi thay \u0111\u1ed5i","LBL_PHONE":"\u0110i\u1ec7n tho\u1ea1i","LBL_PICK_TZ_WELCOME":"Ch\u00e0o m\u1eebng \u0111\u00e3 \u0111\u1ebfn v\u1edbi incomSoft","LBL_PICK_TZ_DESCRIPTION":"Tr\u01b0\u1edbc khi ti\u1ebfp t\u1ee5c, vui l\u00f2ng x\u00e1c nh\u1eadn m\u00fai gi\u1edd c\u1ee7a b\u1ea1n. Ch\u1ecdn m\u00fai gi\u1edd t\u1eeb danh s\u00e1ch b\u00ean d\u01b0\u1edbi v\u00e0 ch\u1ecdn L\u01b0u v\u00e0 ti\u1ebfp t\u1ee5c. M\u00fai gi\u1edd n\u00e0y c\u00f3 th\u1ec3 thay \u0111\u1ed5i t\u1ea1i khu v\u1ef1c \"T\u00e0i kho\u1ea3n c\u1ee7a t\u00f4i\"","LBL_PORTAL_ONLY_DESC":"S\u1eed d\u1ee5ng cho c\u00e1c API Portal. \u0110\u00e2y l\u00e0 lo\u1ea1i kh\u00f4ng th\u1ec3 \u0111\u0103ng nh\u1eadp th\u00f4ng qua giao di\u1ec7n web CRM.","LBL_PORTAL_ONLY_USER":"Portal API User?","LBL_POSTAL_CODE":"M\u00e3 c\u1ed5ng","LBL_PRIMARY_ADDRESS":"\u0110\u1ecba ch\u1ec9 ch\u00ednh","LBL_PROMPT_TIMEZONE_TEXT":"Ki\u1ec3m tra m\u00fai gi\u1edd ng\u01b0\u1eddi d\u00f9ng tr\u01b0\u1edbc khi \u0111\u0103ng nh\u1eadp.","LBL_PROMPT_TIMEZONE":"Nh\u1eafc ch\u1ec9nh m\u00fai gi\u1edd","LBL_PROVIDE_USERNAME_AND_EMAIL":"Cung c\u1ea5p T\u00ean ng\u01b0\u1eddi d\u00f9ng v\u00e0 \u0110\u1ecba ch\u1ec9 email.","LBL_PUBLISH_KEY":"M\u00e3 c\u00f4ng c\u1ed9ng","LBL_RECAPTCHA_NEW_CAPTCHA":"Nh\u1eadn m\u1ed9t CAPTCHA m\u1edbi","LBL_RECAPTCHA_SOUND":"Chuy\u1ec3n sang \u00e2m thanh","LBL_RECAPTCHA_IMAGE":"Chuy\u1ec3n sang h\u00ecnh \u1ea3nh","LBL_RECAPTCHA_INSTRUCTION":"\u0110i\u1ec1n hai t\u1eeb d\u01b0\u1edbi","LBL_RECAPTCHA_INSTRUCTION_OPPOSITE":"\u0110i\u1ec1n hai t\u1eeb v\u00e0o b\u00ean ph\u1ea3i","LBL_RECAPTCHA_FILL_FIELD":"Enter the text that appears in the image.","LBL_RECAPTCHA_INVALID_PRIVATE_KEY":"Invalid Recaptcha Private Key","LBL_RECAPTCHA_INVALID_REQUEST_COOKIE":"The challenge parameter of the verify Recaptcha script was incorrect.","LBL_RECAPTCHA_UNKNOWN":"Unknown Recaptcha Error","LBL_RECEIVE_NOTIFICATIONS_TEXT":"Nh\u1eadn email th\u00f4ng b\u00e1o khi m\u1ed9t h\u1ed3 s\u01a1 \u0111\u01b0\u1ee3c g\u00e1n cho b\u1ea1n","LBL_RECEIVE_NOTIFICATIONS":"Th\u00f4ng b\u00e1o tr\u00ean vi\u1ec7c g\u00e1n","LBL_REGISTER":"Ng\u01b0\u1eddi d\u00f9ng m\u1edbi? Vui l\u00f2ng \u0111\u0103ng k\u00fd.","LBL_REGULAR_USER":"Th\u01b0\u1eddng xuy\u00ean s\u1eed d\u1ee5ng","LBL_REMINDER_TEXT":"Thi\u1ebft l\u1eadp m\u1eb7c \u0111\u1ecbnh cho b\u1ed9 nh\u1eafc vi\u1ec7c, cu\u1ed9c g\u1ecdi v\u00e0 cu\u1ed9c h\u1eb9n","LBL_REMINDER":"Hi\u1ec3n th\u1ecb nh\u1eafc vi\u1ec7c?","LBL_REMOVED_TABS":"H\u1ee7y b\u1ecf tab admin","LBL_REPORTS_TO_NAME":"B\u00e1o c\u00e1o \u0111\u1ebfn","LBL_REPORTS_TO":"B\u00e1o c\u00e1o \u0111\u1ebfn","LBL_REPORTS_TO_ID":"B\u00e1o c\u00e1o \u0111\u1ebfn ID:","LBL_REQUEST_SUBMIT":"Y\u00eau c\u1ea7u c\u1ee7a b\u1ea1n \u0111\u00e3 \u0111\u01b0\u1ee3c g\u1eedi.","LBL_RESET_TO_DEFAULT":"Thi\u1ebft l\u1eadp m\u1eb7c \u0111\u1ecbnh","LBL_RESET_PREFERENCES":"T\u00f9y ch\u1ecdn ng\u01b0\u1eddi d\u00f9ng","LBL_RESET_PREFERENCES_WARNING":"B\u1ea1n c\u00f3 ch\u1eafc b\u1ea1n mu\u1ed1n thi\u1ebft l\u1eadp l\u1ea1i t\u1ea5t c\u1ea3 cho t\u00f9y ch\u1ecdn c\u1ee7a b\u1ea1n? C\u1ea3nh b\u00e1o: \u0110i\u1ec1u n\u00e0y c\u0169ng s\u1ebd l\u00e0m b\u1ea1n tho\u00e1t kh\u1ecfi \u1ee9ng d\u1ee5ng.","LBL_RESET_HOMEPAGE":"Trang ch\u1ee7","LBL_RESET_DASHBOARD":"B\u1ea3ng th\u00f4ng tin","LBL_RESET_HOMEPAGE_WARNING":"B\u1ea1n c\u00f3 ch\u1eafc b\u1ea1n mu\u1ed1n thi\u1ebft l\u1eadp l\u1ea1i Trang ch\u1ee7?","LBL_RESET_DASHBOARD_WARNING":"B\u1ea1n c\u00f3 ch\u1eafc b\u1ea1n mu\u1ed1n thi\u1ebft l\u1eadp l\u1ea1i B\u1ea3ng th\u00f4ng tin?","LBL_SALUTATION":"L\u1eddi ch\u00e0o","LBL_ROLES_SUBPANEL_TITLE":"Vai tr\u00f2 ng\u01b0\u1eddi d\u00f9ng","LBL_SEARCH_FORM_TITLE":"T\u00ecm ki\u1ebfm ng\u01b0\u1eddi d\u00f9ng","LBL_SEARCH_URL":"\u0110\u1ecba \u0111i\u1ec3m t\u00ecm ki\u1ebfm","LBL_SELECT_CHECKED_BUTTON_LABEL":"Ch\u1ecdn nh\u1eefng ng\u01b0\u1eddi d\u00f9ng \u0111\u00e1nh d\u1ea5u","LBL_SELECT_CHECKED_BUTTON_TITLE":"Ch\u1ecdn nh\u1eefng ng\u01b0\u1eddi d\u00f9ng \u0111\u00e3 \u0111\u00e1nh d\u1ea5u","LBL_SETTINGS_URL_DESC":"D\u00f9ng URL n\u00e0y khi thi\u1ebft l\u1eadp \u0111\u0103ng nh\u1eadp cho incomSoft Plugin cho MS Outlook v\u00e0 MS Word.","LBL_SETTINGS_URL":"URL","LBL_SIGNATURE":"Ch\u1eef k\u00ed","LBL_SIGNATURE_HTML":"Ch\u1eef k\u00ed HTML","LBL_SIGNATURE_DEFAULT":"S\u1eed d\u1ee5ng ch\u1eef k\u00ed?","LBL_SIGNATURE_PREPEND":"Ch\u1eef k\u00ed \u1edf tr\u00ean ph\u1ea7n tr\u1ea3 l\u1eddi?","LBL_SIGNATURES":"Nh\u1eefng ch\u1eef k\u00ed","LBL_STATE":"Qu\u1eadn\/Huy\u1ec7n","LBL_STATUS":"T\u00ecnh tr\u1ea1ng","LBL_SUBPANEL_LINKS":"Subpanel Links","LBL_SUBPANEL_LINKS_DESCRIPTION":"Trong ph\u1ea7n Xem chi ti\u1ebft, hi\u1ec3n th\u1ecb m\u1ed9t d\u00f2ng link nhanh","LBL_SUBPANEL_TABS":"Subpanel Tabs","LBL_SUBPANEL_TABS_DESCRIPTION":"Trong ph\u1ea7n Xem chi ti\u1ebft, nh\u00f3m Subpanels v\u00e0o trong m\u00f4t tab v\u00e0 hi\u1ec3n th\u1ecb m\u1ed9t tab t\u1ea1i m\u1ed9t th\u1eddi \u0111i\u1ec3m.","LBL_INCOMCRM_LOGIN":"C\u00f3 quy\u1ec1n \u0111\u0103ng nh\u1eadp?","LBL_SUPPORTED_THEME_ONLY":"Ch\u1ec9 c\u00f3 t\u00e1c d\u1ee5ng \u0111\u1ed1i v\u1edbi nh\u1eefng giao di\u1ec7n c\u00f3 h\u1ed7 tr\u1ee3 t\u00ednh n\u0103ng n\u00e0y.","LBL_SWAP_LAST_VIEWED_DESCRIPTION":"Hi\u1ec3n th\u1ecb thanh Xem g\u1ea7n \u0111\u00e2y b\u00ean c\u1ea1nh n\u1ebfu \u0111\u01b0\u1ee3c ch\u1ecdn. Ng\u01b0\u1ee3c l\u1ea1i n\u00f3 s\u1ebd \u1edf tr\u00ean c\u00f9ng.","LBL_SWAP_SHORTCUT_DESCRIPTION":"Hi\u1ec3n th\u1ecb thanh Shortcuts tr\u00ean c\u00f9ng n\u1ebfu \u0111\u01b0\u1ee3c ch\u1ecdn. Ng\u01b0\u1ee3c l\u1ea1i n\u00f3 s\u1ebd \u1edf b\u00ean c\u1ea1nh.","LBL_SWAP_LAST_VIEWED_POSITION":"Xem g\u1ea7n \u0111\u00e2y \u1edf b\u00ean c\u1ea1nh","LBL_SWAP_SHORTCUT_POSITION":"Shortcuts \u1edf tr\u00ean c\u00f9ng","LBL_TAB_TITLE_EMAIL":"Thi\u1ebft l\u1eadp emails","LBL_TAB_TITLE_USER":"Thi\u1ebft l\u1eadp ng\u01b0\u1eddi d\u00f9ng","LBL_THEME":"Giao di\u1ec7n","LBL_TIME_FORMAT_TEXT":"Thi\u1ebft l\u1eadp \u0111\u1ecbnh d\u1ea1ng th\u1eddi gian hi\u1ec3n th\u1ecb.","LBL_TIME_FORMAT":"Th\u1eddi gian hi\u1ec3n th\u1ecb","LBL_TIMEZONE_DST_TEXT":"Observe Daylight Savings","LBL_TIMEZONE_DST":"Daylight Savings","LBL_TIMEZONE_TEXT":"Thi\u1ebft l\u1eadp m\u00fai gi\u1edd hi\u1ec7n t\u1ea1i","LBL_TIMEZONE":"M\u00fai gi\u1edd","LBL_TITLE":"Ch\u1ee9c v\u1ee5","LBL_USE_REAL_NAMES":"Hi\u1ec3n th\u1ecb H\u1ecd t\u00ean \u0111\u1ea7y \u0111\u1ee7","LBL_USE_REAL_NAMES_DESC":"Hi\u1ec3n th\u1ecb h\u1ecd t\u00ean ng\u01b0\u1eddi d\u00f9ng thay th\u1ebf t\u00ean \u0111\u0103ng nh\u1eadp.","LBL_USER_INFORMATION":"Th\u00f4ng tin ng\u01b0\u1eddi d\u00f9ng","LBL_USER_LOCALE":"Thi\u1ebft l\u1eadp \u0111\u1ecba ph\u01b0\u01a1ng","LBL_USER_NAME":"Ng\u01b0\u1eddi d\u00f9ng","LBL_USER_SETTINGS":"Thi\u1ebft l\u1eadp ng\u01b0\u1eddi d\u00f9ng","LBL_USER_TYPE":"Lo\u1ea1i ng\u01b0\u1eddi d\u00f9ng","LBL_USER":"Ng\u01b0\u1eddi d\u00f9ng","LBL_WORK_PHONE":"\u0110T n\u01a1i l\u00e0m vi\u1ec7c","LBL_YOUR_PUBLISH_URL":"Xu\u1ea5t b\u1ea3n t\u1ea1i \u0111\u1ecba ch\u1ec9 c\u1ee7a t\u00f4i","LBL_YOUR_QUERY_URL":"\u0110\u01b0\u1eddng d\u1eabn URL truy v\u1ea5n c\u1ee7a b\u1ea1n","LNK_NEW_USER":"T\u1ea1o ng\u01b0\u1eddi d\u00f9ng","LNK_NEW_PORTAL_USER":"T\u1ea1o ng\u01b0\u1eddi d\u00f9ng API Portal","LNK_NEW_GROUP_USER":"T\u1ea1o Nh\u00f3m ng\u01b0\u1eddi d\u00f9ng","LNK_USER_LIST":"Ng\u01b0\u1eddi d\u00f9ng","LNK_REASSIGN_RECORDS":"B\u1ea3n ghi","LBL_PROSPECT_LIST":"Danh s\u00e1ch c\u01a1 h\u1ed9i","LBL_PROCESSING":"\u0110ang x\u1eed l\u00fd","LBL_UPDATE_FINISH":"C\u1eadp nh\u1eadt th\u00e0nh c\u00f4ng","LBL_AFFECTED":"b\u1ecb \u1ea3nh h\u01b0\u1edfng","LBL_USER_NAME_FOR_ROLE":"Ng\u01b0\u1eddi s\u1eed d\u1ee5ng\/Nh\u00f3m\/Quy\u1ec1n","LBL_APPLY_OPTIMUMS":"\u00c1p d\u1ee5ng t\u1ed1i \u01b0u","LBL_ASSIGN_TO_USER":"G\u00e1n t\u1edbi ng\u01b0\u1eddi d\u00f9ng","LBL_BASIC":"Thi\u1ebft l\u1eadp Inbound","LBL_CERT_DESC":"B\u1eaft bu\u1ed9c x\u00e1c nh\u1eadn h\u1ee3p l\u1ec7 t\u1eeb Ch\u1ee9ng nh\u1eadn h\u1ee3p l\u1ec7 c\u1ee7a mail server  kh\u00f4ng \u0111\u01b0\u1ee3c d\u00f9ng n\u1ebfu t\u1ef1 k\u00ed.","LBL_CERT":"Ch\u1ee9ng nh\u1eadn h\u1ee3p l\u1ec7","LBL_FIND_OPTIMUM_KEY":"f","LBL_FIND_OPTIMUM_MSG":"<br>T\u00ecm c\u00e1c bi\u1ebfn k\u1ebft n\u1ed1i t\u1ed1i \u01b0u.","LBL_FIND_OPTIMUM_TITLE":"T\u00ecm c\u1ea5u h\u00ecnh t\u1ed1i \u01b0u.","LBL_FORCE":"B\u1eaft bu\u1ed9c ph\u1ee7 \u0111\u1ecbnh","LBL_FORCE_DESC":"M\u1ed9t v\u00e0i server IMAP\/POP3 y\u00eau c\u1ea7u c\u00f3 m\u1ed9t b\u1ed9 chuy\u1ec3n \u0111\u1eb7c bi\u1ec7t. Ki\u1ec3m tra b\u1ed9 chuy\u1ec3n khi k\u1ebft n\u1ed1i.","LBL_FOUND_OPTIMUM_MSG":"<br>T\u00ecm thi\u1ebft l\u1eadp t\u1ed1i \u01b0u.\tVui l\u00f2ng nh\u1ea5n n\u00fat b\u00ean d\u01b0\u1edbi \u0111\u1ec3 \u00e1p d\u1ee5ng ch\u00fang cho h\u1ed9p mail c\u1ee7a b\u1ea1n.","LBL_EMAIL_INBOUND_TITLE":"Thi\u1ebft l\u1eadp Inbound Email","LBL_EMAIL_OUTBOUND_TITLE":"Thi\u1ebft l\u1eadp Outbound Email","LBL_LOGIN":"T\u00ean ng\u01b0\u1eddi d\u00f9ng","LBL_MAILBOX_DEFAULT":"INBOX","LBL_MAILBOX_SSL_DESC":"S\u1eed d\u1ee5ng SSL khi k\u1ebft n\u1ed1i. N\u1ebfu SSL kh\u00f4ng ho\u1ea1t \u0111\u1ed9ng, ki\u1ec3m l\u1ea1i c\u00e0i \u0111\u1eb7t m\u00e1y ch\u1ee7 PHP c\u1ee7a b\u1ea1n c\u00f3 bao g\u00f2m \"--with-imap-ssl\" trong c\u1ea5u h\u00ecnh hay ch\u01b0a","LBL_MAILBOX":"Th\u01b0 m\u1ee5c theo d\u00f5i ","LBL_MAILBOX_TYPE":"Ho\u1ea1t \u0111\u1ed9ng c\u00f3 th\u1ec3","LBL_MARK_READ_NO":"Email \u0111\u00e1nh d\u1ea5u x\u00f3a sau khi nh\u1eadp","LBL_MARK_READ_YES":"Email \u0111\u1ec3 l\u1ea1i tr\u00ean server sau khi nh\u1eadp","LBL_MARK_READ_DESC":"Nh\u1eadp v\u00e0o v\u00e0 \u0111\u00e1nh d\u1ea5u \u0111\u1ecdc tr\u00ean mail server, kh\u00f4ng x\u00f3a.","LBL_MARK_READ":"\u0110\u1ec3 l\u1ea1i tin nh\u1eafn tr\u00ean server.","LBL_ONLY_SINCE_NO":"Kh\u00f4ng. Ki\u1ec3m tra l\u1ea1i t\u1ea5t c\u1ea3 email tr\u00ean mail server.","LBL_ONLY_SINCE_YES":"C\u00f3.","LBL_ONLY_SINCE_DESC":"M\u00e1y ch\u1ee7 PHP kh\u00f4ng th\u1ec3 nh\u1eadn th\u1ea5y tin m\u1edbi ch\u01b0a \u0111\u1ecdc khi d\u00f9ng POP3. Ki\u1ec3m tra l\u1ea1i c\u1edd khi duy\u1ec7t qua tin nh\u1eafn t\u1eeb l\u1ea7n ki\u1ec3m mail cu\u1ed1i c\u00f9ng. \u0110i\u1ec1u n\u00e0y s\u1ebd c\u1ea3i thi\u1ec7n \u0111\u00e1ng k\u1ec3 hi\u1ec7u su\u1ea5t c\u1ee7a m\u00e1y ch\u1ee7 th\u01b0 c\u1ee7a b\u1ea1n, n\u1ebfu mail server c\u1ee7a b\u1ea1n  kh\u00f4ng h\u1ed7 tr\u1ee3 IMAP.","LBL_ONLY_SINCE":"Ch\u1ec9 nh\u1eadp v\u00e0o nh\u1eefng email ki\u1ec3m sau c\u00f9ng.","LBL_PORT":"C\u1ed5ng mail server","LBL_SERVER_OPTIONS":"Thi\u1ebft l\u1eadp n\u00e2ng cao","LBL_SERVER_TYPE":"Giao th\u1ee9c mail server","LBL_SERVER_URL":"\u0110\u1ecba ch\u1ec9 mail server","LBL_SSL":"D\u00f9ng SSL","LBL_SSL_DESC":"D\u00f9ng SSL khi k\u1ebft n\u1ed1i t\u1edbi mail server.","LBL_TEST_BUTTON_KEY":"t","LBL_TEST_BUTTON_TITLE":"Ch\u1ea1y th\u1eed [Alt+T]","LBL_TEST_SETTINGS":"Thi\u1ebft l\u1eadp ch\u1ea1y th\u1eed","LBL_TEST_SUCCESSFUL":"K\u1ebft n\u1ed1i th\u00e0nh c\u00f4ng.","LBL_TLS_DESC":"D\u00f9ng Tranport Layer Security khi k\u1ebft n\u1ed1i t\u1edbi mail server - ch\u1ec9 d\u00f9ng \u0111i\u1ec1u n\u00e0y khi mail server c\u1ee7a b\u1ea1n h\u1ed7 tr\u1ee3 c\u1ed5ng n\u00e0y.","LBL_TLS":"D\u00f9ng TLS.","LBL_TOGGLE_ADV":"Hi\u1ec3n th\u1ecb n\u00e2ng cao.","LBL_OWN_OPPS":"Kh\u00f4ng c\u00f3 c\u01a1 h\u1ed9i.","LBL_EXTERNAL_AUTH_ONLY":"X\u00e1c th\u1ef1c ng\u01b0\u1eddi d\u00f9ng n\u00e0y ch\u1ec9 th\u00f4ng qua","LBL_ONLY":"Ch\u1ec9 c\u00f3","LBL_OWN_OPPS_DESC":"Ch\u1ecdn n\u1ebf ng\u01b0\u1eddi d\u00f9ng s\u1ebd kh\u00f4ng \u0111\u01b0\u1ee3c g\u00e1n c\u01a1 h\u1ed9i. S\u1eed d\u1ee5ng thi\u1ebft l\u1eadp n\u00e0y cho nh\u1eefng ng\u01b0\u1eddi l\u00e0 qu\u1ea3n l\u00fd kh\u00f4ng tham gia v\u00e0o b\u00e1n h\u00e0ng. Thi\u1ebft l\u1eadp n\u00e0y \u0111\u01b0\u1ee3c d\u00f9ng cho m\u00f4-\u0111un d\u1ef1 b\u00e1o","LBL_LDAP_ERROR":"L\u1ed7i LDAP: Vui l\u00f2ng li\u00ean h\u1ec7 t\u1edbi Qu\u1ea3n tr\u1ecb ","LBL_LDAP_EXTENSION_ERROR":"L\u1ed7i LDAP: Kh\u00f4ng t\u1ea3i \u0111\u01b0\u1ee3c th\u00e0nh ph\u1ea7n m\u1edf r\u1ed9ng.","LBL_USER_HOLIDAY_SUBPANEL_TITLE":"K\u00ec ngh\u1ec9 ng\u01b0\u1eddi d\u00f9ng","LBL_RESOURCE_NAME":"T\u00ean","LBL_RESOURCE_TYPE":"Lo\u1ea1i","LBL_PDF_SETTINGS":"PDF Settings","LBL_PDF_PAGE_FORMAT":"Page Format","LBL_PDF_PAGE_FORMAT_TEXT":"The format used for pages","LBL_PDF_PAGE_ORIENTATION":"Page Orientation","LBL_PDF_PAGE_ORIENTATION_TEXT":"","LBL_PDF_PAGE_ORIENTATION_P":"Portrait","LBL_PDF_PAGE_ORIENTATION_L":"Landscape","LBL_PDF_MARGIN_HEADER":"Header Margin","LBL_PDF_MARGIN_HEADER_TEXT":"","LBL_PDF_MARGIN_FOOTER":"Footer Margin","LBL_PDF_MARGIN_FOOTER_TEXT":"","LBL_PDF_MARGIN_TOP":"Top Margin","LBL_PDF_MARGIN_TOP_TEXT":"","LBL_PDF_MARGIN_BOTTOM":"Bottom Margin","LBL_PDF_MARGIN_BOTTOM_TEXT":"","LBL_PDF_MARGIN_LEFT":"Left Margin","LBL_PDF_MARGIN_LEFT_TEXT":"","LBL_PDF_MARGIN_RIGHT":"Right Margin","LBL_PDF_MARGIN_RIGHT_TEXT":"","LBL_PDF_FONT_NAME_MAIN":"Font for Header and Body","LBL_PDF_FONT_NAME_MAIN_TEXT":"The selected font will be applied to the text in the header and the body of the PDF Document","LBL_PDF_FONT_SIZE_MAIN":"Main Font Size","LBL_PDF_FONT_SIZE_MAIN_TEXT":"","LBL_PDF_FONT_NAME_DATA":"Font for Footer","LBL_PDF_FONT_NAME_DATA_TEXT":"The selected font will be applied to the text in the footer of the PDF Document","LBL_PDF_FONT_SIZE_DATA":"Data Font Size","LBL_PDF_FONT_SIZE_DATA_TEXT":"","LBL_LAST_ADMIN_NOTICE":"You have selected yourself. You cannot change the User Type or Status of yourself.","LBL_ALLOW_DEPARTMENTS":"Xem \u0111\u01b0\u1ee3c d\u1eef li\u1ec7u c\u1ee7a ph\u00f2ng ban (Quy\u1ec1n NH\u00d3M)","LBL_LIST_NONINHERITABLE":"Kh\u00f4ng k\u1ebf th\u1eeba","LBL_SECURITYGROUP_NONINHERITABLE":"Kh\u00f4ng k\u1ebf th\u1eeba","LBL_TQT_WAREHOUSES":"Thu\u1ed9c kho h\u00e0ng","LBL_ALLOW_BRANCHES":"Xem \u0111\u01b0\u1ee3c d\u1eef li\u1ec7u c\u1ee7a chi nh\u00e1nh (Quy\u1ec1n NH\u00d3M)","LBL_ENABLE_USERS":"Xem \u0111\u01b0\u1ee3c d\u1eef li\u1ec7u c\u1ee7a ng\u01b0\u1eddi d\u00f9ng","LBL_DISABLE_USERS":"KH\u00d4NG xem \u0111\u01b0\u1ee3c d\u1eef li\u1ec7u c\u1ee7a ng\u01b0\u1eddi d\u00f9ng","LBL_TEAM_GROUPS":"B\u1ed9 ph\u1eadn","LBL_ALLOW_TEAMS":"Xem \u0111\u01b0\u1ee3c d\u1eef li\u1ec7u c\u1ee7a b\u1ed9 ph\u1eadn (Quy\u1ec1n NH\u00d3M)","LBL_USER_CODE":"M\u00e3 NV","LBL_TEAMS":"B\u1ed9 ph\u1eadn\/Nh\u00f3m","LBL_HIERARCHICAL":"Ph\u00e2n quy\u1ec1n","LBL_BIRTHDATE":"Ng\u00e0y sinh","LBL_ASSIGN_WORK":"T\u00f3m l\u01b0\u1ee3c CV \u0111\u01b0\u1ee3c giao","LBL_BRANCH_ID":"Chi nh\u00e1nh","LBL_LOCATION_AREA":"Thu\u1ed9c khu v\u1ef1c","LBL_LOGIN_ON":"Thi\u1ebft b\u1ecb \u0111\u0103ng nh\u1eadp","LBL_AVARTAR_IMAGE":"H\u00ecnh \u0111\u1ea1i di\u1ec7n","LBL_BRANCH_ADMIN":"Qu\u1ea3n tr\u1ecb chi nh\u00e1nh","LBL_LIST_BRANCH_ADMIN":"Qu\u1ea3n tr\u1ecb CN","LBL_BRANCH_ADMIN_DESC":"Ng\u01b0\u1eddi d\u00f9ng c\u00f3 th\u1ec3 truy c\u1eadp v\u00e0o trang qu\u1ea3n l\u00fd t\u1ea5t c\u1ea3 h\u1ed3 s\u01a1 thu\u1ed9c chi nh\u00e1nh c\u1ee7a h\u1ecd.","LBL_TABLE_PARAMS":"Table Params","LBL_APP_MAINTENANCE":"H\u1ec7 th\u1ed1ng \u0111ang b\u1ea3o tr\u00ec. Qu\u00fd kh\u00e1ch vui l\u00f2ng quay l\u1ea1i sau!","LBL_APP_AUTH_ERR_KEY":"Th\u00f4ng tin \u0111\u0103ng nh\u1eadp kh\u00f4ng h\u1ee3p l\u1ec7!","LBL_APP_AUTH_MISSING":"T\u00e0i kho\u1ea3n \u0111\u0103ng nh\u1eadp kh\u00f4ng t\u1ed3n t\u1ea1i!","LBL_APP_AUTH_FALURE":"T\u00e0i kho\u1ea3n \u0111\u0103ng nh\u1eadp kh\u00f4ng h\u1ee3p l\u1ec7!","LBL_APP_AUTH_BLOCKED":"T\u00e0i kho\u1ea3n \u0111\u00e3 b\u1ecb kh\u00f3a!","LBL_APP_AUTH_PASSWD":"M\u1eadt kh\u1ea9u kh\u00f4ng h\u1ee3p l\u1ec7!","LBL_APP_AUTH_SUCCESS":"\u0110\u0103ng nh\u1eadp th\u00e0nh c\u00f4ng!","ERR_PASSWORD_LIKE_USERNAME":"M\u1eadt kh\u1ea9u ph\u1ea3i kh\u00e1c t\u00ean \u0111\u0103ng nh\u1eadp","ERR_PASSWORD_MIN_LENGTH":"Chi\u1ec1u d\u00e0i m\u1eadt kh\u1ea9u ph\u1ea3i >= ","ERR_PASSWORD_MISS_LETTER":"M\u1eadt kh\u1ea9u ph\u1ea3i c\u00f3 \u00edt nh\u1ea5t 01 ch\u1eef c\u00e1i: a-z,A-Z","ERR_PASSWORD_MISS_NUMBER":"M\u1eadt kh\u1ea9u ph\u1ea3i c\u00f3 \u00edt nh\u1ea5t 01 ch\u1eef s\u1ed1","ERR_PASSWORD_MISS_SPECIAL":"M\u1eadt kh\u1ea9u ph\u1ea3i c\u00f3 \u00edt nh\u1ea5t 01 k\u00fd t\u1ef1 \u0111\u1eb7c bi\u1ec7t: [|}{~!@#$%^&*()_+=-]","ERR_PASSWORD_EQUAL_CURRENT":"M\u1eadt kh\u1ea9u m\u1edbi ph\u1ea3i kh\u00e1c so v\u1edbi m\u1eadt kh\u1ea9u hi\u1ec7n t\u1ea1i","MSG_PASSWORD_EXPIRED":"M\u1eadt kh\u1ea9u c\u1ee7a b\u1ea1n \u0111\u00e3 h\u1ebft h\u1ea1n, vui l\u00f2ng thay \u0111\u1ed5i m\u1eadt kh\u1ea9u m\u1edbi.","LBL_RPT_VISITED_TITLE":"Th\u1ed1ng k\u00ea l\u1ecbch s\u1eed \u0111\u0103ng nh\u1eadp v\u00e0o ph\u1ea7n m\u1ec1m","LBL_RPT_DATE_VISIT":"Ng\u00e0y \u0111\u0103ng nh\u1eadp","LBL_RPT_VISITED":"S\u1ed1 l\u1ea7n","LBL_RPT_VISIT_BY":"Xem theo","LBL_RPT_TOTAL":"T\u1ed5ng c\u1ed9ng","LBL_RPT_ENABLE_IP":"M\u1edf IP \u0111\u0103ng nh\u1eadp","LBL_RPT_BLOCK_IP":"Kh\u00f3a IP \u0111\u0103ng nh\u1eadp","LBL_LIST_INCOMCRM_LOGIN":"\u0110\u0103ng nh\u1eadp?","LBL_DATE_START":"Ng\u00e0y b\u1eaft \u0111\u1ea7u l\u00e0m vi\u1ec7c","LBL_DATE_END":"Ng\u00e0y ngh\u1ec9 vi\u1ec7c","LBL_WORK_START_DATE_FROM":"Ng\u00e0y B\u0110 l\u00e0m vi\u1ec7c T\u1eeb","LBL_WORK_START_DATE_TO":"\u0110\u1ebfn","LBL_WORK_END_DATE_FROM":"Ng\u00e0y ngh\u1ec9 vi\u1ec7c T\u1eeb","LBL_WORK_END_DATE_TO":"\u0110\u1ebfn","LBL_GENDER":"Gi\u1edbi t\u00ednh","LBL_ACADEMIC_LEVEL":"Tr\u00ecnh \u0111\u1ed9 h\u1ecdc v\u1ea5n","LBL_SPECIALIZED":"Chuy\u00ean ng\u00e0nh","LBL_ID_CITIZEN":"S\u1ed1 CMND\/CCCD","LBL_ISSUED_DATE":"Ng\u00e0y c\u1ea5p","LBL_ISSUED_BY":"N\u01a1i c\u1ea5p","LBL_ADDRESS_PERMANENT":"\u0110\u1ecba ch\u1ec9 th\u01b0\u1eddng tr\u00fa","LBL_ADDRESS_TEMPORARY":"\u0110\u1ecba ch\u1ec9 t\u1ea1m tr\u00fa","LBL_INSURANCE_NUMBER":"S\u1ed1 s\u1ed5 BHXH","LBL_INSURANCE_MONTH":"Th\u00e1ng tham gia BHXH","LBL_MATERNITY_LEAVE":"Ngh\u1ec9 thai s\u1ea3n","LBL_SENIORITY":"Th\u00e2m ni\u00ean","LBL_EMP_PHONE":"\u0110i\u1ec7n tho\u1ea1i","LBL_PRIMARY_ADDRESS2":"Qu\u00ea qu\u00e1n","LBL_CONTRACT_TYPE":"Lo\u1ea1i h\u1ee3p \u0111\u1ed3ng","LBL_CONTRACT_NUMBER":"S\u1ed1 h\u1ee3p \u0111\u1ed3ng","LBL_SALARY":"Ti\u1ec1n l\u01b0\u01a1ng","LBL_CONTRACT_DATE_START":"T\u1eeb ng\u00e0y","LBL_CONTRACT_DATE_END":"\u0110\u1ebfn ng\u00e0y","LBL_REMAINING_DAY":"Ng\u00e0y c\u00f2n l\u1ea1i","LBL_APPENDIX_DATE":"Ng\u00e0y ph\u1ee5 l\u1ee5c","LBL_APPENDIX_NOTE":"Ghi ch\u00fa ph\u1ee5 l\u1ee5c","LBL_ROWINDEX":"STT","LBL_PROBATION_CONTRACT":"H\u1ee3p \u0111\u1ed3ng th\u1eed vi\u1ec7c","LBL_12M_CONTRACT":"H\u1ee3p \u0111\u1ed3ng 12 th\u00e1ng","LBL_UNSPECIFIED_CONTRACT":"H\u1ee3p \u0111\u1ed3ng kh\u00f4ng x\u00e1c \u0111\u1ecbnh th\u1eddi h\u1ea1n"});