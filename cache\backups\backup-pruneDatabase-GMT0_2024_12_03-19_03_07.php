<?php
// created: 2024-12-04 02:03:13
$pruneDatabase = array (
  0 => 'INSERT INTO accounts_opportunities (id, opportunity_id, account_id, date_modified, deleted) VALUES ("136482f2-5514-fef3-21a3-6740378cc5b6", "1cd70b1f-d871-9d8d-9da0-67403720347e", "********-9cb8-5f8c-2b5e-62cce69fde07", "2024-11-22 07:48:19", "1");',
  1 => 'INSERT INTO opportunities (id, date_entered, date_modified, modified_user_id, created_by, deleted, name, description, assigned_user_id, branch_id, department_id, opportunity_type, campaign_id, lead_source, amount, amount_usdollar, currency_id, date_closed, sales_stage, probability, priority, estimated_revenue, opportunity_scope, date_start_process, object, expected_output, actual_output, contract_revenue, actual_receipts, order_number, prod_lock, delivered_all, contact_name, vat_tax, vat_amount, vat_more_amt, vat_other_amt, discount_amount, cost_amount, comm_more_amount, incurred_amount, incurred_other, incurred_domestic, incurred_foreign, commission_amount, arise_cost_amount, profit_amount, rate_usd, is_locked, processing_stage, processing_content, amount_collected, amount_owed, handle_user_id, parent_id, payment_status, money_deposit, money_refund, is_target, account_targets, causes_failure, failure_notes, debt_order, payment_type, account_address, account_tax_code, shipping_fee, no_currency, account_phone, account_email, date_payment, order_type, quote_number, contract_number, contract_date, order_discount, payment_period, project_id, project_name, bank_account_id, disable_auto_price, quotation_date, quotation_version, location_nearby, rate_id, rate_foreign, service_total, service_used, service_left, vat_stock, ordered_merge, warranty_period, table_params, ignore_payment, re_calc_profit, is_auto_cpps, total_amount_in, date_return, prod_returned, vat_spec_amt, vat_imp_amt, vat_total_amount, ext_incurred_amount, int_incurred_amount, is_import, ttt_date_shipment, ttt_date_contract, ttt_date_declaration, ttt_date_storage, ttt_date_arrival, ttt_date_clearance, ttt_fee_amount, ttt_insurance_number, ttt_insurance_date, ttt_insurance_amount, ttt_insurance_extension, ttt_insurance_coop, ttt_date_lc_payment, ttt_lc_number, ttt_date_lc_expired, ttt_lc_amount, ttt_lc_reiss_amt, ttt_lc_repair_amt, ttt_lc_payment_amt, ttt_lc_bank_name, ttt_invoice_number, ttt_invoice_date, ttt_bol, ttt_bol_date, ttt_packinglist, ttt_co, ttt_milltest, ttt_dost, ttt_condi_trading, ttt_declaration, ttt_declaration_number, ttt_declaration_amount, ttt_vat_amount, ttt_verification, ttt_verification_amount, ttt_vat_import, ttt_customs_fee, ttt_clearance, ttt_arrival_notice, ttt_discharging_port, ttt_vessel, ttt_vessel_vn, ttt_owed_contract, ttt_storage_fee, ttt_vessel_fee, ttt_repair_cont_fee, ttt_deposit_cont_fee, ttt_expected_output, ttt_cnt_amount, ttt_foreign_amount, ttt_actual_output, ttt_commodity, ttt_tolerance, ttt_shipment_method, ttt_partial_shipment, ttt_transhipment, ttt_packing, ttt_quality, ttt_order_condi, total_foreign_amount, order_purchase, order_date, quotes_note, shipping_time, payment_method, shipping_note, failure_cause, date_record, is_export_vat, accounting_category_type, holding_time, holding_start, holding_end) VALUES ("1cd70b1f-d871-9d8d-9da0-67403720347e", "2024-11-22 07:48:19", "2024-11-22 07:48:19", "1", "1", "1", "PREVIEW-PTB-2411-0002", "", "1", "2", "3", "3", "", "", "5000000", "0", "-99", "2024-11-22", "Cancel", "0", "0", "5000000", "", "2024-11-22", "Proposal", "1", "1", "5500000", "5500000", "", "0", "0", "", "10", "500000", "0", "0", "0", "530969.61", "0", "0", "0", "0", "0", "0", "530969.61", "4469030.39", "1", "0", "", "", "", "5500000", "", "", "1", "0", "0", "0", "0", "", "", "0", "TM", "Tòa nhà SBI, Công viên phần mềm Quang Trung, Quận 12, Tp.HCM", "**********", "0", "0", "035xxxxx", "", "2025-01-06", "Packages", "", "", "", "", "", "", "", "ee901ddc-9ddb-884e-a360-65322ec9a19f", "0", "", "", "10.846208,106.6401792", "-99", "1", "0", "", "0", "0", "0", "", "YTo0OntzOjE0OiJvcmRlcl9kaXNjb3VudCI7ZDowO3M6MTY6InByb2R1Y3RfZGlzY291bnQiO2k6MDtzOjEwOiJwb2ludF91c2VkIjtpOjA7czoxMToicG9pbnRfdG90YWwiO2Q6NTU7fQ==", "0", "0", "0", "0", "", "0", "", "", "", "", "", "0", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "Giá trên đã bao gồm VAT.\\r\\nBao gồm chi phí vận chuyển, lắp đặt tại TPHCM.\\r\\nBảng báo giá có giá trị trong vòng 30 ngày kề từ ngày báo.", "Chia thành nhiều đợt theo từng đơn đặt hàng của bên A Hàng có sẵn (Tối đa trong vòng 3-5 ngày theo từng đơn hàng của bên A)", "Bên A thanh toán cho Bên B bằng phương thức chuyển khoản hoặc tiền mặt 100% tổng giá trị hợp đồng tối đa sau 30 ngày kể từ khi Bên B giao đủ hàng, nghiệm thu, thanh lý và hóa đơn tài chính theo quy định cho Bên A.", "", "", "", "0", "1", "", "", "");',
  2 => 'INSERT INTO opportunities_contacts (id, contact_id, opportunity_id, contact_role, date_modified, deleted) VALUES ("1483e319-60eb-926c-0a7f-67403724a564", "71c583b3-08be-7b9d-56cb-62cce647aa46", "1cd70b1f-d871-9d8d-9da0-67403720347e", "Primary Decision Maker", "2024-11-22 07:48:19", "1");',
  3 => 'INSERT INTO opportunities_tqt_products (id, deleted, date_modified, modified_user_id, opportunity_id, product_id, group_price_id, product_gift_id, ref_id, parent_id, parent_note, product_combo, warehouse_id, ordering, product_code, product_name, prod_note, prod_quantity, prod_price, prod_orig_price, prod_sale_price, prod_discount, prod_discount_amt, prod_promotion, prod_promotion_amt, prod_more_percent, prod_vat_tax, prod_vat_amount, prod_amount, prod_total_amount, prod_orig_amount, prod_profit_amount, product_unit, prod_unit_opt, prod_unit_qty, prod_unit_length, prod_unit_width, prod_unit_price, product_type, product_gift, prod_convert_kg, prod_total_kg, prod_convert_mt, prod_total_mt, product_lots, prod_lot_id, delivered_qty, missing_qty, prod_custom, prod_num_uses, prod_date_start, prod_repeat_cycle, handling_note, prod_foreign_price, prod_foreign_amount, prod_actual_price, prod_stock_price, prod_more_amount, prod_comm_amount, prod_comm_more_amt, prod_more_price, prod_comm_price, prod_more_ret_tax, prod_more_ret_amt, prod_more_receive_amt, prod_more_note, prod_01_comm_per, prod_01_comm_amt, prod_01_comm_ret_tax, prod_01_comm_ret_amt, prod_01_comm_receive_amt, prod_01_comm_note, prod_02_comm_per, prod_02_comm_amt, prod_02_comm_ret_tax, prod_02_comm_ret_amt, prod_02_comm_receive_amt, prod_02_comm_note, prod_tax_exempt, prod_per_cpps, prod_amount_cpps, prod_total_stock_price, prod_total_amount_in, have_warehoused, prod_ext_incur_amount, prod_value_vat_import, prod_vat_tax_import, prod_vat_amount_import, prod_value_vat_spec, prod_vat_tax_spec, prod_vat_amount_spec, prod_value_vat_amount, prod_int_incur_amount, product_primary, product_set, prod_orig2_price, prod_return_orig_price, prod_custom_unit_qty, prod_custom_unit_price) VALUES ("28555304-cdad-8042-7a40-6740371f45d4", "1", "2024-11-22 07:48:19", "1", "1cd70b1f-d871-9d8d-9da0-67403720347e", "2fbeeb53-3ea9-ffbe-e9e2-631992554659", "761b03bd-ee3d-85e5-3916-65f2ba1711a7", "7e1518a7-bfce-8568-fed5-61a975e33607", "", "", "a88451ed-36f5-7a47-44e0-64ed4b2808cc|2", "1dd0", "2e5ac341-c6b6-0c24-34f2-55530a9c12bc", "1", "A+", "A+", "", "1", "5000000", "430969.61", "5000000", "0", "0", "0", "0", "0", "10", "500000", "5000000", "5500000", "430969.61", "4569030.39", "Bộ", "0", "0", "0", "0", "0", "0", "0", "5.1", "5.1", "0", "0", "", "", "0", "1", "", "0", "", "", "", "0", "0", "5000000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "", "430969.6122", "430969.61", "0", "0");',
  4 => 'INSERT INTO opportunities_tqt_products (id, deleted, date_modified, modified_user_id, opportunity_id, product_id, group_price_id, product_gift_id, ref_id, parent_id, parent_note, product_combo, warehouse_id, ordering, product_code, product_name, prod_note, prod_quantity, prod_price, prod_orig_price, prod_sale_price, prod_discount, prod_discount_amt, prod_promotion, prod_promotion_amt, prod_more_percent, prod_vat_tax, prod_vat_amount, prod_amount, prod_total_amount, prod_orig_amount, prod_profit_amount, product_unit, prod_unit_opt, prod_unit_qty, prod_unit_length, prod_unit_width, prod_unit_price, product_type, product_gift, prod_convert_kg, prod_total_kg, prod_convert_mt, prod_total_mt, product_lots, prod_lot_id, delivered_qty, missing_qty, prod_custom, prod_num_uses, prod_date_start, prod_repeat_cycle, handling_note, prod_foreign_price, prod_foreign_amount, prod_actual_price, prod_stock_price, prod_more_amount, prod_comm_amount, prod_comm_more_amt, prod_more_price, prod_comm_price, prod_more_ret_tax, prod_more_ret_amt, prod_more_receive_amt, prod_more_note, prod_01_comm_per, prod_01_comm_amt, prod_01_comm_ret_tax, prod_01_comm_ret_amt, prod_01_comm_receive_amt, prod_01_comm_note, prod_02_comm_per, prod_02_comm_amt, prod_02_comm_ret_tax, prod_02_comm_ret_amt, prod_02_comm_receive_amt, prod_02_comm_note, prod_tax_exempt, prod_per_cpps, prod_amount_cpps, prod_total_stock_price, prod_total_amount_in, have_warehoused, prod_ext_incur_amount, prod_value_vat_import, prod_vat_tax_import, prod_vat_amount_import, prod_value_vat_spec, prod_vat_tax_spec, prod_vat_amount_spec, prod_value_vat_amount, prod_int_incur_amount, product_primary, product_set, prod_orig2_price, prod_return_orig_price, prod_custom_unit_qty, prod_custom_unit_price) VALUES ("290609b8-d3c3-9559-9e58-674037fc6c82", "1", "2024-11-22 07:48:19", "1", "1cd70b1f-d871-9d8d-9da0-67403720347e", "a88451ed-36f5-7a47-44e0-64ed4b2808cc", "761b03bd-ee3d-85e5-3916-65f2ba1711a7", "701b41cf-f25c-efa5-e3d8-642b7c1c4390", "", "2fbeeb53-3ea9-ffbe-e9e2-631992554659", "2", "1dd0", "2e5ac341-c6b6-0c24-34f2-55530a9c12bc", "2", "adp1", "Áo đồng phục 1", "", "2", "300000", "50000", "0", "0", "0", "0", "0", "0", "-1", "0", "0", "0", "100000", "-100000", "Bộ", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "", "", "0", "2", "", "0", "", "", "", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "", "50000", "50000", "0", "0");',
  5 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("63a87a35-6dfb-74ad-774c-673bf7ffaa42", "1", "2024-11-19 02:28:26", "1", "admin", "z21232f297a57a5a743894a0e4a801fc3z9b045c31ea6aff22f11de768a37fa0e5z");',
  6 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("488483a4-cafc-e59c-cb1a-67341095fcfd", "1", "2024-11-13 02:36:48", "1", "admin", "z21232f297a57a5a743894a0e4a801fc3z9b045c31ea6aff22f11de768a37fa0e5z");',
  7 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("bab3f3b6-7e61-7ee6-e271-673d8ed24c80", "1", "2024-11-20 07:22:11", "1", "admin", "z21232f297a57a5a743894a0e4a801fc3z9b045c31ea6aff22f11de768a37fa0e5z");',
  8 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("44637f79-0c81-c158-0b47-674552476fff", "1", "2024-11-26 04:47:35", "1", "admin", "z21232f297a57a5a743894a0e4a801fc3z9b045c31ea6aff22f11de768a37fa0e5z");',
  9 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("e0226351-ef4a-b1c8-3ca0-674817b4c33b", "1", "2024-11-28 07:12:29", "1", "admin", "z21232f297a57a5a743894a0e4a801fc3z9b045c31ea6aff22f11de768a37fa0e5z");',
);
?>
