

{math equation="floor(left / right)"
	left=12
		right=$templateMeta.maxColumns
			assign=colClass
	}

<section class="container2 search-form ml-0 search_form_adv">
	<div class="row">
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="name_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_NAME' module='Contacts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.name_advanced.value) <= 0}
	{assign var="value" value=$fields.name_advanced.default_value }
{else}
	{assign var="value" value=$fields.name_advanced.value }
{/if}
{if isTypeNumber($fields.name_advanced.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.name_advanced.name}' id='{$fields.name_advanced.name}' size='30' maxlength='255' value='{$value}' title='' tabindex=''  /> 

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="phone_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_ANY_PHONE' module='Contacts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.phone_advanced.value) <= 0}
	{assign var="value" value=$fields.phone_advanced.default_value }
{else}
	{assign var="value" value=$fields.phone_advanced.value }
{/if}
{if isTypeNumber($fields.phone_advanced.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.phone_advanced.name}' id='{$fields.phone_advanced.name}' size='30'  value='{$value}' title='' tabindex=''  /> 

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="birthdate_from_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_BIRTHDATE_FROM' module='Contacts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{assign var=date_value value=$fields.birthdate_from_advanced.value }
<input autocomplete="off" type="text" name="{$fields.birthdate_from_advanced.name}" id="{$fields.birthdate_from_advanced.name}" value="{$date_value}" title=''  tabindex='' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.birthdate_from_advanced.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.birthdate_from_advanced.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.birthdate_from_advanced.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="account_name_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_ACCOUNT_NAME' module='Contacts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.account_name_advanced.value) <= 0}
	{assign var="value" value=$fields.account_name_advanced.default_value }
{else}
	{assign var="value" value=$fields.account_name_advanced.value }
{/if}
{if isTypeNumber($fields.account_name_advanced.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.account_name_advanced.name}' id='{$fields.account_name_advanced.name}' size='30' maxlength='255' value='{$value}' title='' tabindex=''  /> 

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="primary_address_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_ANY_ADDRESS' module='Contacts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.primary_address_advanced.value) <= 0}
	{assign var="value" value=$fields.primary_address_advanced.default_value }
{else}
	{assign var="value" value=$fields.primary_address_advanced.value }
{/if}
{if isTypeNumber($fields.primary_address_advanced.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.primary_address_advanced.name}' id='{$fields.primary_address_advanced.name}' size='30'  value='{$value}' title='' tabindex=''  /> 

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="birthdate_to_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_BIRTHDATE_TO' module='Contacts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{assign var=date_value value=$fields.birthdate_to_advanced.value }
<input autocomplete="off" type="text" name="{$fields.birthdate_to_advanced.name}" id="{$fields.birthdate_to_advanced.name}" value="{$date_value}" title=''  tabindex='' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.birthdate_to_advanced.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.birthdate_to_advanced.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.birthdate_to_advanced.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="title_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_TITLE' module='Contacts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.title_advanced.value) <= 0}
	{assign var="value" value=$fields.title_advanced.default_value }
{else}
	{assign var="value" value=$fields.title_advanced.value }
{/if}
{if isTypeNumber($fields.title_advanced.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.title_advanced.name}' id='{$fields.title_advanced.name}' size='30' maxlength='100' value='{$value}' title='' tabindex=''  /> 

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="email_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_ANY_EMAIL' module='Contacts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.email_advanced.value) <= 0}
	{assign var="value" value=$fields.email_advanced.default_value }
{else}
	{assign var="value" value=$fields.email_advanced.value }
{/if}
{if isTypeNumber($fields.email_advanced.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.email_advanced.name}' id='{$fields.email_advanced.name}' size='30'  value='{$value}' title='' tabindex=''  /> 

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="independence_day_from_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_INDEPENDENCE_DAY_FROM' module='Contacts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{assign var=date_value value=$fields.independence_day_from_advanced.value }
<input autocomplete="off" type="text" name="{$fields.independence_day_from_advanced.name}" id="{$fields.independence_day_from_advanced.name}" value="{$date_value}" title=''  tabindex='' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.independence_day_from_advanced.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.independence_day_from_advanced.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.independence_day_from_advanced.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="position_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_POSITION' module='Contacts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.position_advanced.value) <= 0}
	{assign var="value" value=$fields.position_advanced.default_value }
{else}
	{assign var="value" value=$fields.position_advanced.value }
{/if}
{if isTypeNumber($fields.position_advanced.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.position_advanced.name}' id='{$fields.position_advanced.name}' size='30' maxlength='200' value='{$value}' title='' tabindex=''  /> 

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="department_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_DEPARTMENT' module='Contacts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.department_advanced.value) <= 0}
	{assign var="value" value=$fields.department_advanced.default_value }
{else}
	{assign var="value" value=$fields.department_advanced.value }
{/if}
{if isTypeNumber($fields.department_advanced.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.department_advanced.name}' id='{$fields.department_advanced.name}' size='30' maxlength='255' value='{$value}' title='' tabindex=''  /> 

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="independence_day_to_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_INDEPENDENCE_DAY_TO' module='Contacts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{assign var=date_value value=$fields.independence_day_to_advanced.value }
<input autocomplete="off" type="text" name="{$fields.independence_day_to_advanced.name}" id="{$fields.independence_day_to_advanced.name}" value="{$date_value}" title=''  tabindex='' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.independence_day_to_advanced.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.independence_day_to_advanced.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.independence_day_to_advanced.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="lead_source_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_LEAD_SOURCE' module='Contacts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="lead_source_advanced[]" id="{$fields.lead_source_advanced.name}" size="4" multiple="1"  >
{html_options options=$fields.lead_source_advanced.options selected=$fields.lead_source_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="care_priority_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_CARE_PRIORITY' module='Contacts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="care_priority_advanced[]" id="{$fields.care_priority_advanced.name}" size="4" multiple="1"  >
{html_options options=$fields.care_priority_advanced.options selected=$fields.care_priority_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="need_care_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_NEED_CARE' module='Contacts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{assign var="yes" value=""}
{assign var="no" value=""}
{assign var="default" value=""}

{if strval($fields.need_care_advanced.value) == "1"}
	{assign var="yes" value="SELECTED"}
{elseif strval($fields.need_care_advanced.value) == "0"}
	{assign var="no" value="SELECTED"}
{else}
	{assign var="default" value="SELECTED"}
{/if}

<select id="{$fields.need_care_advanced.name}" name="{$fields.need_care_advanced.name}" tabindex="" style="width:80px !important;" >
 <option value="" {$default}></option>
 <option value = "0" {$no}> {$APP.LBL_SEARCH_DROPDOWN_NO}</option>
 <option value = "1" {$yes}> {$APP.LBL_SEARCH_DROPDOWN_YES}</option>
</select>


									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="transaction_level_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_TRANSACTION_LEVEL' module='Contacts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="transaction_level_advanced[]" id="{$fields.transaction_level_advanced.name}" size="4" multiple="1"  >
{html_options options=$fields.transaction_level_advanced.options selected=$fields.transaction_level_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="intimate_level_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_INTIMATE_LEVEL' module='Contacts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="intimate_level_advanced[]" id="{$fields.intimate_level_advanced.name}" size="4" multiple="1"  >
{html_options options=$fields.intimate_level_advanced.options selected=$fields.intimate_level_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="contact_position_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_CONTACT_POSITION' module='Contacts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="contact_position_advanced[]" id="{$fields.contact_position_advanced.name}" size="4" multiple="1"  >
{html_options options=$fields.contact_position_advanced.options selected=$fields.contact_position_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="branch_id_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_BRANCH_ID' module='Contacts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="branch_id_advanced[]" id="{$fields.branch_id_advanced.name}" size="4" multiple="1"  onchange="changeBranchDepartments(this, this.form.department_id_advanced)" >
{html_options options=$fields.branch_id_advanced.options selected=$fields.branch_id_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="department_id_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_DEPARTMENT_ID' module='Contacts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="department_id_advanced[]" id="{$fields.department_id_advanced.name}" size="4" multiple="1"  onchange="changeDepartmentUsers(this, this.form.assigned_user_id_advanced)" >
{html_options options=$fields.department_id_advanced.options selected=$fields.department_id_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="assigned_user_id_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_ASSIGNED_TO' module='Contacts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="assigned_user_id_advanced[]" id="{$fields.assigned_user_id_advanced.name}" size="4" multiple="1" Array >
{html_options options=$fields.assigned_user_id_advanced.options selected=$fields.assigned_user_id_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="account_branch_advanced">
											Chi nhánh KH:
									</label>
				<div class="col-7 ">
											
<select name="account_branch_advanced[]" id="{$fields.account_branch_advanced.name}" size="4" multiple="1"  onchange="changeBranchDepartments(this, this.form.account_department_advanced)" >
{html_options options=$fields.account_branch_advanced.options selected=$fields.account_branch_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="account_department_advanced">
											Phòng ban KH:
									</label>
				<div class="col-7 ">
											
<select name="account_department_advanced[]" id="{$fields.account_department_advanced.name}" size="4" multiple="1"  onchange="changeDepartmentUsers(this, this.form.account_user_id_advanced)" >
{html_options options=$fields.account_department_advanced.options selected=$fields.account_department_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="account_user_id_advanced">
											Nv.Qlý KH:
									</label>
				<div class="col-7 ">
											
<select name="account_user_id_advanced[]" id="{$fields.account_user_id_advanced.name}" size="4" multiple="1" Array >
{html_options options=$fields.account_user_id_advanced.options selected=$fields.account_user_id_advanced.value}
</select>

									</div>
			</div>
				</div>
	</div>
</section>

{if $HAS_ADVANCED_SEARCH}
<table cellspacing="0" cellpadding="0" border="0" class="mt-10">
<tr>
	<td scope="row" nowrap="nowrap" class="pt-10">
		<a style="font-size:10pt; text-decoration:none;" onclick="incomCRM.searchForm.searchFormSelect('{$module}|basic_search','{$module}|advanced_search')" href="#">[ {$APP.LNK_BASIC_SEARCH} ]</a>
	</td>
</tr>
<tr>
	<td>&nbsp;</td>
</tr>
</table>
{/if}

{if $DISPLAY_SAVED_SEARCH}
<div class="saved-search-adv table-responsive mt-10 pt-10">
<table cellspacing="0" cellpadding="0" border="0">
<tr>
	<td rowspan="2" width="40%">
		<a class='tabFormAdvLink' onhover href='javascript:toggleInlineSearch()'>
		<img src='{incomCRM_getimagepath file="advanced_search.gif"}' id='up_down_img' border="0" />&nbsp;{$APP.LNK_SAVED_VIEWS}
		</a><br/>
		<input type='hidden' id='showSSDIV' name='showSSDIV' value='{$SHOWSSDIV}' />
	</td>
	<td scope='row' width="20%" nowrap="nowrap">
		{incomCRM_translate label='LBL_SAVE_SEARCH_AS' module='SavedSearch'}:
	</td>
	<td nowrap>
		<input type='text' name='saved_search_name' />
		<input type='hidden' name='search_module' value='' />
		<input type='hidden' name='saved_search_action' value='' />
		<input title='{$APP.LBL_SAVE_BUTTON_LABEL}' value='{$APP.LBL_SAVE_BUTTON_LABEL}' class='button' type='button' name='saved_search_submit' onclick='incomCRM.savedViews.setChooser(); return incomCRM.savedViews.saved_search_action("save");' />
	</td>
</tr>
<tr>
	<td scope='row' nowrap="nowrap">
		{incomCRM_translate label='LBL_MODIFY_CURRENT_SEARCH' module='SavedSearch'}:
	</td>
	<td nowrap>
		<input class='button' onclick='incomCRM.savedViews.setChooser(); return incomCRM.savedViews.saved_search_action("update")' value='{$APP.LBL_UPDATE}' title='{$APP.LBL_UPDATE}' name='ss_update' id='ss_update' type='button' />
		<input class='button' onclick='return incomCRM.savedViews.saved_search_action("delete", "{incomCRM_translate label='LBL_DELETE_CONFIRM' module='SavedSearch'}")' value='{$APP.LBL_DELETE}' title='{$APP.LBL_DELETE}' name='ss_delete' id='ss_delete' type='button' />
		<br/><span id='curr_search_name'></span>
	</td>
</tr>
<tr>
	<td colspan="3">
		<div style="{$DISPLAYSS}" id="inlineSavedSearch">{$SAVED_SEARCH}</div>
	</td>
</tr>
</table>
</div>
{/if}

<script type="text/javascript">
{literal}
if( typeof(loadSSL_Scripts) == 'function' ) {
	loadSSL_Scripts();
}
YAHOO.util.Event.onDOMReady(function(){
	var form = null;
	if( document.search_form ) form = document.search_form;
	else if( document.popup_query_form ) form = document.popup_query_form;
	else return;
	if( form ) {
		if( form.branch_id_advanced ) {
			if( form.department_id_advanced )
				changeBranchDepartments(form.branch_id_advanced, form.department_id_advanced);
			else if( form.department_advanced )
				changeBranchDepartments(form.branch_id_advanced, form.department_advanced);
		}
		if( form.department_id_advanced && form.assigned_user_id_advanced )
			changeDepartmentUsers(form.department_id_advanced, form.assigned_user_id_advanced);
		if( form.location_district_advanced && form.location_city_advanced )
			changeParentSelectedOption(form.location_city_advanced, form.location_district_advanced, 'location_district_dom');
	}
});
{/literal}
</script>

<!-- Begin Meta-Data Javascript -->
<script type="text/javascript">{literal}
YAHOO.util.Event.onDOMReady(function(){
	if( document.search_form && document.search_form.account_department_advanced ) {
		if( document.search_form.account_branch_advanced )
			changeBranchDepartments(document.search_form.account_branch_advanced, document.search_form.account_department_advanced);
		if( document.search_form.account_user_id_advanced )
			changeDepartmentUsers(document.search_form.account_department_advanced, document.search_form.account_user_id_advanced);
	}
});
{/literal}</script>
<!-- End Meta-Data Javascript -->
{literal}<script type="text/javascript">if(typeof sqs_objects == 'undefined'){var sqs_objects = new Array;}
sqs_objects['search_form_modified_by_name_advanced'] = {"form":"search_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["modified_by_name_advanced","modified_user_id_advanced"],"required_list":["modified_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['search_form_created_by_name_advanced'] = {"form":"search_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["created_by_name_advanced","created_by_advanced"],"required_list":["created_by"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['search_form_assigned_user_name_advanced'] = {"form":"search_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["assigned_user_name_advanced","assigned_user_id_advanced"],"required_list":["assigned_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['search_form_account_name_advanced'] = {"form":"search_form","method":"query","modules":["Accounts"],"group":"or","field_list":["name","id"],"populate_list":["search_form_account_name_advanced","account_id_advanced"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"required_list":["account_id"],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['search_form_report_to_name_advanced'] = {"form":"search_form","method":"get_contact_array","modules":["Contacts"],"field_list":["salutation","first_name","last_name","id"],"populate_list":["report_to_name_advanced","reports_to_id_advanced","reports_to_id_advanced","reports_to_id_advanced"],"required_list":["reports_to_id"],"group":"or","conditions":[{"name":"first_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"last_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['search_form_campaign_name_advanced'] = {"form":"search_form","method":"query","modules":["Campaigns"],"group":"or","field_list":["name","id"],"populate_list":["campaign_name_advanced","campaign_id_advanced"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"required_list":["campaign_id"],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
</script>{/literal}