incomCRM.language.setLanguage('PayRolls', {"LBL_ID":"ID","LBL_DATE_ENTERED":"Ng\u00e0y t\u1ea1o","LBL_DATE_MODIFIED":"Ng\u00e0y c\u1eadp nh\u1eadt","LBL_MODIFIED":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_MODIFIED_ID":"ID ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_MODIFIED_NAME":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_CREATED":"Ng\u01b0\u1eddi t\u1ea1o","LBL_CREATED_ID":"ID ng\u01b0\u1eddi t\u1ea1o","LBL_DESCRIPTION":"Ghi ch\u00fa","LBL_DELETED":"\u0110\u00e3 x\u00f3a?","LBL_NAME":"M\u00e3 phi\u1ebfu","LBL_CREATED_USER":"Ng\u01b0\u1eddi t\u1ea1o","LBL_MODIFIED_USER":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_ASSIGNED_TO":"Nv.Ql\u00fd","LBL_ASSIGNED_TO_ID":"ID Nv.Ql\u00fd","LBL_ASSIGNED_TO_NAME":"Nv.Ql\u00fd","LBL_ASSIGNED_TO_USER":"Nv.Ql\u00fd","LBL_ASSIGNED_USER":"Nv.Ql\u00fd","LBL_MODULE_NAME":"B\u1ea3ng l\u01b0\u01a1ng nh\u00e2n vi\u00ean","LBL_MODULE_TITLE":"B\u1ea3ng l\u01b0\u01a1ng nh\u00e2n vi\u00ean: Trang ch\u1ee7","LBL_LIST_FORM_TITLE":"Danh s\u00e1ch b\u1ea3ng l\u01b0\u01a1ng nh\u00e2n vi\u00ean","LNK_NEW_RECORD":"Th\u00eam m\u1edbi","LNK_LIST":"Danh s\u00e1ch b\u1ea3ng l\u01b0\u01a1ng","LBL_SEARCH_FORM_TITLE":"T\u00ecm ki\u1ebfm","LBL_NEW_FORM_TITLE":"Th\u00eam m\u1edbi","LBL_NEW_MULTI_TITLE":"Th\u00eam nhi\u1ec1u","LBL_APPROVED":"X\u00e1c nh\u1eadn?","LBL_DATE_PERFORM":"Ng\u00e0y","LBL_AMOUNT":"Ti\u1ec1n l\u01b0\u01a1ng","LBL_PAY_AMT":"\u0110\u00e3 tr\u1ea3","LBL_OWE_AMT":"C\u00f2n n\u1ee3","LBL_FILENAME":"\u0110\u00ednh k\u00e8m","LBL_FILE_URL":"\u0110\u01b0\u1eddng d\u1eabn file","LBL_FILE_MIME_TYPE":"\u0110\u1ecbnh d\u1ea1ng file","LBL_ACCOUNT_ID":"ID Nh\u00e2n vi\u00ean","LBL_ACCOUNT_CODE":"M\u00e3 nh\u00e2n vi\u00ean","LBL_ACCOUNT_NAME":"T\u00ean nh\u00e2n vi\u00ean","LBL_ACCOUNTS":"Nh\u00e2n vi\u00ean","LBL_DATE_PERFORM_FROM":"Ng\u00e0y l\u01b0\u01a1ng t\u1eeb"});