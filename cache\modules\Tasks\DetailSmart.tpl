
<section id="DetailViewBody" class="view-module-{$module|strtolower}">

<form action="index.php" method="post" name="{$form_name|default:'DetailView'}" id="form" class="_view-{$instanceName}">
<table cellpadding="1" cellspacing="0" border="0" width="100%">
<tr>
<td class="buttons" align="left" valign="top" nowrap>
<input type="hidden" name="module" value="{$module}" />
<input type="hidden" name="action" value="EditView" />
<input type="hidden" name="record" value="{$fields.id.value}" />
<input type="hidden" name="return_action" />
<input type="hidden" name="return_module" />
<input type="hidden" name="return_id" />
<input type="hidden" name="module_tab" /> 
<input type="hidden" name="isDuplicate" value="false" />
<input type="hidden" name="offset" value="{$offset}" />
<input type="hidden" name="to_pdf" value="1" />
<input type="hidden" name="is_ajax_call" value="{$smarty.request.is_ajax_call}" />
<input type="hidden" name="return_url" value="{$smarty.request.return_url|escape:'url'}" />
{if $bean->aclAccess("edit")}<input title="{$APP.LBL_EDIT_BUTTON_TITLE}" accessKey="{$APP.LBL_EDIT_BUTTON_KEY}" class="button bedit" onclick="luloSmart.edit( this.form.module.value, this.form.record.value {if empty($smarty.request.is_ajax_call)},{ldelim}autoReload:true{rdelim}{/if});" type="button" name="Edit" id="edit_button" value="{$APP.LBL_EDIT_BUTTON_LABEL}" />{/if} 
{if $bean->aclAccess("delete")}<input title="{$APP.LBL_DELETE_BUTTON_TITLE}" accessKey="{$APP.LBL_DELETE_BUTTON_KEY}" class="button delete" onclick="this.form.return_module.value='Tasks'; this.form.return_action.value='index'; this.form.action.value='Delete'; return luloSmart.remove(this.form);" type="submit" name="button" value="{$APP.LBL_DELETE_BUTTON_LABEL}" />{/if} 
<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="{if !empty($smarty.request.return_module) && !empty($smarty.request.return_id)}this.form.return_module.value='{$smarty.request.return_module}'; this.form.return_action.value='DetailView'; this.form.return_id.value='{$smarty.request.return_id}'; {/if}luloSmart.close(null, this.form);" type="button" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> 
</td>
<td class="buttons" align="left" valign="top" nowrap>
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=Tasks", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</td>
<td align="right" width="90%">{$ADMIN_EDIT}</td>
</tr>
<tr>
<td colspan="30" nowrap="nowrap" style="padding-top:5px">
<input type="button" class="button highlight" name="btn" value=" Tạo công việc nhánh " onclick="locationLink('index.php?module={$module}&action=EditView&main_id={$fields.id.value}&main_name={$fields.subject.value}')" />
{if $fields.transactions.value eq "kpiLoi"}<input type="button" class="button highlight" name="btn" value=" {$APP.LNK_PRINT} Biên bản xử lý lỗi " onclick="popupWindow('index.php?module={$module}&action=PrintView&record={$fields.id.value}&to_pdf=true')" />{/if}
{if $fields.transactions.value neq "kpiLoi"} <input type="button" class="button highlight" name="btn" value=" Lập biên bản " onclick="locationLink('index.php?module={$module}&action=EditView&task_relate_id={$fields.id.value}&transactions=kpiLoi&parent_name={$company_name}&parent_id={$company_id}&user_err_id={$fields.assigned_user_id.value}&user_err_name={$fields.assigned_user_name.value}')" />{/if}
</td>
</tr>
</table>
</form>
{incomCRM_include include=$includes}
<div id="DEFAULT" class="detailview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<div class="detail view {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="DEFAULT_GROUP" style="display:block">
<table id='detailpanel_1' cellspacing='{$gridline}'>
<tr>
<td width='150' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CONTENT' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</td>
<td  id='_content_field' >
{counter name="panelFieldCount"}

{$fields.content.value|url2html|nl2br}
{if !empty($ATTACHMENTS)}<br/><div class="f-11 my-10"><strong>{$MOD.LBL_ATTACHMENT_FILES}:</strong></div><div id="attachment_files" class="pl-10">{$ATTACHMENTS}</div>{/if}
</td>
</tr>
<tr>
<td colspan='2' id='_content_field' class="chat-box-group">
<div class="labelsOnTop _content_label">
{$COMMENT_SEARCH}
</div>
{counter name="panelFieldCount"}
{$COMMENT_BOX}	
</td>
</tr>
<tr>
<td width='150' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ASAP' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</td>
<td  id='_asap_field' >
{counter name="panelFieldCount"}
{$iconApprove}	
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("DEFAULT").style.display='none';</script>
{/if}
<div id="LBL_MODULE_SUBPANEL_TITLE" class="detailview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_MODULE_SUBPANEL_TITLE');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_MODULE_SUBPANEL_TITLE_IMG" border="0" />
{incomCRM_translate label='LBL_MODULE_SUBPANEL_TITLE' module='Tasks'}
</a>
</h4>
<div class="detail view {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_MODULE_SUBPANEL_TITLE_GROUP" style="display:block">
<table id='detailpanel_2' cellspacing='{$gridline}'>
<tr>
<td width='150' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_SUBJECT' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</td>
<td  id='_subject_field' >
{counter name="panelFieldCount"}

<span id='{$fields.subject.name}' >
{$fields.subject.value}
</span>
</td>
</tr>
<tr>
<td width='150' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_NEXT_STEP' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</td>
<td  id='_next_step_field' >
{counter name="panelFieldCount"}

{$fields.next_step.value|url2html|nl2br}
</td>
</tr>
<tr>
<td width='150' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_TRANSACTIONS' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</td>
<td  id='_transactions_field' >
{counter name="panelFieldCount"}

{ $fields.transactions.options[$fields.transactions.value]}
</td>
</tr>
<tr>
<td width='150' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_STATUS' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</td>
<td  id='_status_field' >
{counter name="panelFieldCount"}

{ $fields.status.options[$fields.status.value]}
</td>
</tr>
<tr>
<td width='150' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_START_DATE' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</td>
<td  id='_date_start_field' >
{counter name="panelFieldCount"}

<span id='{$fields.date_start.name}' >
{$fields.date_start.value}
</span>
</td>
</tr>
<tr>
<td width='150' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DUE_DATE' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</td>
<td  id='_date_due_field' >
{counter name="panelFieldCount"}

<span id='{$fields.date_due.name}' >
{$fields.date_due.value}
</span>
</td>
</tr>
<tr>
<td width='150' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_MEETING_DATE' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</td>
<td  id='_date_meeting_field' >
{counter name="panelFieldCount"}

<span id='{$fields.date_meeting.name}' >
{$fields.date_meeting.value}
</span>
</td>
</tr>
<tr>
<td width='150' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_COMPLETION_DATE' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</td>
<td  id='_date_completion_field' >
{counter name="panelFieldCount"}

<span id='{$fields.date_completion.name}' >
{$fields.date_completion.value}
</span>
</td>
</tr>
<tr>
<td width='150' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_PROCESSING_TIME' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</td>
<td  id='_processing_time_field' >
{counter name="panelFieldCount"}

<span id='{$fields.processing_time.name}' >
{$fields.processing_time.value}
</span>
{if !empty($fields.processing_time.value)}{$MOD.LBL_TIME_UNIT}{/if}
</td>
</tr>
<tr>
<td width='150' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_OPPORTUNITY_NAME' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</td>
<td  id='_opportunity_name_field' >
{counter name="panelFieldCount"}

{if !empty($fields.opportunity_id.value)}<a href="index.php?module=Opportunities&action=DetailView&record={$fields.opportunity_id.value}">{/if}
{$fields.opportunity_name.value}
{if !empty($fields.opportunity_id.value)}</a>{/if}
</td>
</tr>
<tr>
<td width='150' scope="row">
{$fields.parent_name.options[$fields.parent_type.value]|default:$MOD.LBL_LIST_RELATED_TO}:
</td>
<td  id='_parent_name_field' >
{counter name="panelFieldCount"}

<a href="index.php?module={$fields.parent_type.value}&action=DetailView&record={$fields.parent_id.value}" class="tabDetailViewDFLink">{$fields.parent_name.value}</a>
</td>
</tr>
<tr>
<td width='150' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_MAIN_TASK_NAME' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</td>
<td  id='_main_name_field' >
{counter name="panelFieldCount"}

{if !empty($fields.main_id.value)}<a href="index.php?module=Tasks&action=DetailView&record={$fields.main_id.value}">{/if}
{$fields.main_name.value}
{if !empty($fields.main_id.value)}</a>{/if}
</td>
</tr>
<tr>
<td width='150' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_USER_ERR_NAME' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</td>
<td  id='_user_err_name_field' >
{counter name="panelFieldCount"}
{if $fields.transactions.value eq "kpiLoi"}{$fields.user_err_name.value}{else}-{/if}	
</td>
</tr>
<tr>
<td width='150' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DESCRIPTION_ERROR' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</td>
<td  id='_description_error_field' >
{counter name="panelFieldCount"}

{$fields.description_error.value|url2html|nl2br}
</td>
</tr>
<tr>
<td width='150' scope="row">
{if $fields.transactions.value eq "kpiLoi"}Tác vụ liên quan:{else} Biên bản liên quan:{/if}
</td>
<td  id='_task_relate_name_field' >
{counter name="panelFieldCount"}
{if $fields.transactions.value eq "kpiLoi" && !empty($bien_ban_xuly)}{$bien_ban_xuly}{else}<a style="color:red;" title="Test lập BB" href="index.php?module=Tasks&action=DetailView&record={$fields.task_relate_id.value}" target="_blank">{$bien_ban_xuly}</a>{/if}&nbsp;	
</td>
</tr>
<tr>
<td width='150' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CONFIRM' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</td>
<td  id='_confirm_field' >
{counter name="panelFieldCount"}

{if strval($fields.confirm.value) == "1" || strval($fields.confirm.value) == "yes" || strval($fields.confirm.value) == "on"} 
{assign var="checked" value="CHECKED"}
{else}
{assign var="checked" value=""}
{/if}
<input type="checkbox" class="checkbox" name="{$fields.confirm.name}" disabled="true" {$checked} />
{if empty($fields.confirm.value) and !empty($fields.user_err_id.value) and ($current_user->id eq $fields.user_err_id.value)} &nbsp; <input type="button" name="btn" value=" Xác nhận " class="button highlight" onclick="confirmApproved();" />{/if}
</td>
</tr>
<tr>
<td width='150' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</td>
<td  id='_assigned_user_name_field' >
{counter name="panelFieldCount"}

{$fields.assigned_user_name.value}
</td>
</tr>
<tr>
<td width='150' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_GROUP_NAME' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</td>
<td  id='_group_name_field' >
{counter name="panelFieldCount"}

{if !empty($fields.group_id.value)}<a href="index.php?module=Tasks_Groups&action=DetailView&record={$fields.group_id.value}">{/if}
{$fields.group_name.value}
{if !empty($fields.group_id.value)}</a>{/if}
</td>
</tr>
<tr>
<td width='150' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_APPROVAL_LEVEL_NAME' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</td>
<td  id='_approval_level_name_field' >
{counter name="panelFieldCount"}

{if !empty($fields.approval_level_id.value)}<a href="index.php?module=Approval_Levels&action=DetailView&record={$fields.approval_level_id.value}">{/if}
{$fields.approval_level_name.value}
{if !empty($fields.approval_level_id.value)}</a>{/if}
</td>
</tr>
<tr>
<td width='150' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_SHARE_USERS' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</td>
<td  id='_share_users_field' >
{counter name="panelFieldCount"}

{if !empty($fields.share_users.value)}
{multienum_to_array string=$fields.share_users.value assign="vals"}
{foreach from=$vals item=item}
<li>{ $fields.share_users.options.$item }</li>
{/foreach}
{/if}
</td>
</tr>
<tr>
<td width='150' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_REMINDER' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</td>
<td  id='_reminder_time_field' >
{counter name="panelFieldCount"}

<span id='{$fields.reminder_time.name}' >
{$fields.reminder_time.value}
</span>
</td>
</tr>
<tr>
<td width='150' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_PRIORITY' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</td>
<td  id='_priority_field' >
{counter name="panelFieldCount"}
<input name="priority" type="checkbox" class="checkbox" value="Urgent" disabled {if $fields.priority.value eq "Urgent"}checked{/if} />	
</td>
</tr>
<tr>
<td width='150' scope="row">
{if $fields.transactions.value eq "Mission"}{$MOD.LBL_NOTE2}{else}{$MOD.LBL_NOTE}{/if}
</td>
<td  id='_note_field' >
{counter name="panelFieldCount"}

{$fields.note.value|url2html|nl2br}
</td>
</tr>
<tr>
<td width='150' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DATE_ENTERED' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</td>
<td  id='_date_entered_field' >
{counter name="panelFieldCount"}
{$fields.date_entered.value} {$APP.LBL_BY} {$fields.created_by_name.value}	
</td>
</tr>
<tr>
<td width='150' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DATE_MODIFIED' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</td>
<td  id='_date_modified_field' >
{counter name="panelFieldCount"}
{$fields.date_modified.value} {$APP.LBL_BY} {$fields.modified_by_name.value}	
</td>
</tr>
<tr>
<td width='150' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_LOCATION_NEARBY' module='Tasks'}
{/capture}
{$label|strip_semicolon}:
</td>
<td  id='_location_nearby_field' >
{counter name="panelFieldCount"}

{if !empty($fields.location_nearby.value)}
{if strpos($fields.location_nearby.value, ' ') === false and strpos($fields.location_nearby.value, ',') !== false }
<div id="gmap{$fields.id.value}{$fields.location_nearby.name}" style="width: 500px; height: 250px;" class="corner2 gmap"></div>
{capture name="mapOptions" assign="mapOptions"}
{ldelim}
"address":"{$fields.location_address.value}"
,"dynamicUpdate":{ldelim}
"module":"{$module}", "record":"{$fields.id.value}"
, "field":"{$fields.location_address.name}"
, "type":"method", "method":LULO.ajaxUpdateTableParams	{rdelim}
{rdelim}
{/capture}
{if !empty($fields.location_address.value)}
<div class="addr" style="padding-bottom:5px">{$fields.location_address.value}</div>
{/if}
<a href="#" target="_blank" onclick='return LULO.GMapLocation.show("gmap{$fields.id.value}{$fields.location_nearby.name}", "{$fields.location_nearby.value}", {$mapOptions|lulo_clear_string:''}, this);'>[ {$APP.LBL_VIEW_MAPS} ]</a>
{else}
{$fields.location_nearby.value}
{/if}
{/if}
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_MODULE_SUBPANEL_TITLE").style.display='none';</script>
{/if}

<!-- Begin Meta-Data Javascript -->
<script type="text/javascript">function confirmApproved() {ldelim}if( !confirm("Bạn xác nhận nội dung vi phạm này?") ) return;var success = function(data){ldelim}ajaxStatus.hideStatus();var result = convertDataResult(data);if( result && $.isPlainObject(result) ) {ldelim}if( result.error ) {ldelim} if( result.msg ) alert( result.msg );{rdelim}else locationLink("index.php?module={$module}&action=DetailView&record={$fields.id.value}");{rdelim}else ajaxStatus.flashStatus( incomCRM.language.get('app_strings', 'ERR_HANDLE_PROCESSING') );{rdelim};var url = "index.php?module=Tasks&action=approved&record={$fields.id.value}"+ "&to_pdf=true&is_ajax_call=1"; ajaxStatus.showStatus(incomCRM.language.get('app_strings', 'LBL_PROCESSING_REQUEST'));$.ajax({ldelim}url:url,success:success,error:success{rdelim});{rdelim}</script>
<!-- End Meta-Data Javascript -->
</section>