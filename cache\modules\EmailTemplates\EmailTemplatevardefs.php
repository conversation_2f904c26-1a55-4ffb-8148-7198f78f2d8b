<?php
// created: 2025-03-03 22:47:48
$<PERSON><PERSON><PERSON><PERSON><PERSON>["dictionary"]["EmailTemplate"] = array (
  'table' => 'email_templates',
  'comment' => 'Templates used in email processing',
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_NAME',
      'type' => 'id',
      'required' => true,
      'reportable' => false,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'required' => true,
      'comment' => 'Date record created',
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'required' => true,
      'comment' => 'Date record last modified',
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_ASSIGNED_TO',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'reportable' => true,
      'isnull' => 'false',
      'dbType' => 'id',
      'comment' => 'User who last modified record',
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'vname' => 'LBL_CREATED_BY',
      'type' => 'varchar',
      'len' => '36',
      'comment' => 'User who created record',
    ),
    'published' => 
    array (
      'name' => 'published',
      'vname' => 'LBL_PUBLISHED',
      'type' => 'varchar',
      'len' => '3',
      'comment' => '',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'varchar',
      'len' => '255',
      'comment' => 'Email template name',
      'importable' => 'required',
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'comment' => 'Email template description',
    ),
    'subject' => 
    array (
      'name' => 'subject',
      'vname' => 'LBL_SUBJECT',
      'type' => 'varchar',
      'len' => '255',
      'comment' => 'Email subject to be used in resulting email',
    ),
    'body' => 
    array (
      'name' => 'body',
      'vname' => 'LBL_BODY',
      'type' => 'text',
      'comment' => 'Plain text body to be used in resulting email',
    ),
    'body_html' => 
    array (
      'name' => 'body_html',
      'vname' => 'LBL_PLAIN_TEXT',
      'type' => 'longtext',
      'comment' => 'HTML formatted email body to be used in resulting email',
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'required' => true,
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'text_only' => 
    array (
      'name' => 'text_only',
      'vname' => 'LBL_TEXT_ONLY',
      'type' => 'bool',
      'required' => false,
      'reportable' => false,
      'comment' => 'Should be checked if email template is to be sent in text only',
    ),
  ),
  'indices' => 
  array (
    0 => 
    array (
      'name' => 'email_templatespk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_email_template_name',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'name',
      ),
    ),
  ),
  'relationships' => 
  array (
  ),
  'custom_fields' => false,
);
?>
