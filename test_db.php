<?php
// Test database connection
$host = 'localhost';
$dbname = 'inco_demochuan';
$username = 'inco_demochuan';
$password = 'xOyOH1vBCsA*sl!2';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "Kết nối database thành công!";
} catch(PDOException $e) {
    echo "Lỗi kết nối database: " . $e->getMessage();
}

// Test basic PHP
echo "<br>PHP đang hoạt động: " . phpversion();
echo "<br>Th<PERSON>i gian hiện tại: " . date('Y-m-d H:i:s');
?>
