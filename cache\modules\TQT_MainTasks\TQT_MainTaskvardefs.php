<?php
// created: 2025-03-03 22:47:49
$<PERSON><PERSON><PERSON><PERSON><PERSON>["dictionary"]["TQT_MainTask"] = array (
  'table' => 'tqt_maintasks',
  'audited' => true,
  'duplicate_merge' => false,
  'unified_search' => false,
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'varchar',
      'len' => '250',
      'acl' => true,
      'audited' => true,
      'comment' => 'Account Name',
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'tqt_maintasks_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'tqt_maintasks_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'branch_id' => 
    array (
      'name' => 'branch_id',
      'vname' => 'LBL_BRANCH_ID',
      'type' => 'enum',
      'options' => 'users_branch_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'BranchID of assigned user',
    ),
    'department_id' => 
    array (
      'name' => 'department_id',
      'vname' => 'LBL_DEPARTMENT_ID',
      'type' => 'enum',
      'options' => 'users_department_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'DepartmentID of assigned user',
    ),
    'assigned_user_id' => 
    array (
      'name' => 'assigned_user_id',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'vname' => 'LBL_ASSIGNED_TO_ID',
      'group' => 'assigned_user_name',
      'type' => 'relate',
      'table' => 'users',
      'module' => 'Users',
      'isnull' => 'false',
      'dbType' => 'id',
      'audited' => true,
      'reportable' => true,
      'duplicate_merge' => 'disabled',
      'comment' => 'User ID assigned to record',
    ),
    'assigned_user_name' => 
    array (
      'name' => 'assigned_user_name',
      'link' => 'assigned_user_link',
      'vname' => 'LBL_ASSIGNED_TO_NAME',
      'rname' => 'user_name',
      'type' => 'relate',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'assigned_user_id',
      'module' => 'Users',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'assigned_user_link' => 
    array (
      'name' => 'assigned_user_link',
      'type' => 'link',
      'relationship' => 'tqt_maintasks_assigned_user',
      'vname' => 'LBL_ASSIGNED_TO_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
      'duplicate_merge' => 'enabled',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'table' => 'users',
    ),
    'rowindex' => 
    array (
      'name' => 'rowindex',
      'vname' => 'LBL_ROWINDEX',
      'type' => 'int',
      'len' => '6',
    ),
    'contact_key' => 
    array (
      'name' => 'contact_key',
      'vname' => 'LBL_CONTACT_KEY',
      'type' => 'text',
      'audited' => true,
    ),
    'budget' => 
    array (
      'name' => 'budget',
      'vname' => 'LBL_BUDGET',
      'type' => 'varchar',
      'len' => '50',
      'audited' => true,
    ),
    'deadline' => 
    array (
      'name' => 'deadline',
      'vname' => 'LBL_DEADLINE',
      'type' => 'date',
      'massupdate' => false,
      'audited' => true,
    ),
    'eval_confirm' => 
    array (
      'name' => 'eval_confirm',
      'vname' => 'LBL_EVAL_CONFIRM',
      'type' => 'enum',
      'options' => 'maintask_eval_confirm_dom',
      'len' => '100',
      'massupdate' => false,
      'audited' => true,
    ),
    'eval_notes' => 
    array (
      'name' => 'eval_notes',
      'vname' => 'LBL_EVAL_NOTES',
      'type' => 'text',
    ),
    'deadline_late' => 
    array (
      'name' => 'deadline_late',
      'vname' => 'LBL_DEADLINE_LATE',
      'type' => 'bool',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'days_late' => 
    array (
      'name' => 'days_late',
      'vname' => 'LBL_DAYS_LATE',
      'type' => 'int',
      'len' => '6',
      'audited' => true,
    ),
    'status' => 
    array (
      'name' => 'status',
      'vname' => 'LBL_STATUS',
      'type' => 'enum',
      'options' => 'maintask_status_dom',
      'len' => '100',
      'massupdate' => false,
      'audited' => true,
      'comment' => 'Use: Only for Search and Update from SummaryWeek module',
    ),
    'date_start' => 
    array (
      'name' => 'date_start',
      'vname' => 'LBL_DATE_START',
      'type' => 'date',
      'massupdate' => false,
      'audited' => true,
      'comment' => 'Use: Only for Search and Update from SummaryWeek module',
    ),
    'date_end' => 
    array (
      'name' => 'date_end',
      'vname' => 'LBL_DATE_END',
      'type' => 'date',
      'massupdate' => false,
      'audited' => true,
      'comment' => 'Use: Only for Search and Update from SummaryWeek module',
    ),
    'handle_user_id' => 
    array (
      'name' => 'handle_user_id',
      'vname' => 'LBL_HANDLE_USER_ID',
      'type' => 'id',
      'massupdate' => false,
      'audited' => true,
    ),
    'handle_user_name' => 
    array (
      'name' => 'handle_user_name',
      'rname' => 'user_name',
      'id_name' => 'handle_user_id',
      'vname' => 'LBL_HANDLE_USER_NAME',
      'source' => 'non-db',
      'type' => 'relate',
      'link' => 'handle_users',
      'module' => 'Users',
      'massupdate' => false,
    ),
    'handle_users' => 
    array (
      'name' => 'handle_users',
      'vname' => 'LBL_HANDLE_USERS',
      'type' => 'link',
      'source' => 'non-db',
      'relationship' => 'handle_users_tqt_maintasks',
    ),
    'engineer_user_id' => 
    array (
      'name' => 'engineer_user_id',
      'vname' => 'LBL_ENGINNEER_USER_ID',
      'type' => 'id',
      'massupdate' => false,
      'audited' => true,
    ),
    'engineer_user_name' => 
    array (
      'name' => 'engineer_user_name',
      'rname' => 'user_name',
      'id_name' => 'engineer_user_id',
      'vname' => 'LBL_ENGINNEER_USER_NAME',
      'source' => 'non-db',
      'type' => 'relate',
      'link' => 'engineer_users',
      'module' => 'Users',
      'massupdate' => false,
    ),
    'engineer_users' => 
    array (
      'name' => 'engineer_users',
      'vname' => 'LBL_ENGINNEER_USERS',
      'type' => 'link',
      'source' => 'non-db',
      'relationship' => 'engineer_users_tqt_maintasks',
    ),
    'parent_type' => 
    array (
      'name' => 'parent_type',
      'vname' => 'LBL_PARENT_TYPE',
      'type' => 'parent_type',
      'dbType' => 'varchar',
      'group' => 'parent_name',
      'len' => '50',
      'audited' => true,
      'comment' => 'incomCRM module the MainTask is associated with',
    ),
    'parent_id' => 
    array (
      'name' => 'parent_id',
      'vname' => 'LBL_PARENT_ID',
      'type' => 'id',
      'required' => false,
      'reportable' => true,
      'audited' => true,
      'comment' => 'The ID of the incomCRM item specified in parent_type',
    ),
    'parent_name' => 
    array (
      'name' => 'parent_name',
      'parent_type' => 'record_type_display',
      'type_name' => 'parent_type',
      'id_name' => 'parent_id',
      'vname' => 'LBL_RELATED_TO',
      'type' => 'parent',
      'source' => 'non-db',
      'options' => 'record_type_display_sales',
      'massupdate' => false,
    ),
    'opportunities' => 
    array (
      'name' => 'opportunities',
      'type' => 'link',
      'relationship' => 'opportunities_tqt_maintasks',
      'vname' => 'LBL_OPPORTUNITIES',
      'source' => 'non-db',
    ),
    'contracts' => 
    array (
      'name' => 'contracts',
      'type' => 'link',
      'relationship' => 'contracts_tqt_maintasks',
      'vname' => 'LBL_CONTRACTS',
      'source' => 'non-db',
    ),
    'project_teams' => 
    array (
      'name' => 'project_teams',
      'type' => 'link',
      'relationship' => 'tqt_maintasks_project_teams',
      'vname' => 'LBL_PROJECT_TEAMS',
      'source' => 'non-db',
    ),
    'tqt_attachments' => 
    array (
      'name' => 'tqt_attachments',
      'type' => 'link',
      'relationship' => 'tqt_maintasks_attachments',
      'vname' => 'LBL_ATTACHMENTS',
      'source' => 'non-db',
    ),
    'tasks' => 
    array (
      'name' => 'tasks',
      'type' => 'link',
      'relationship' => 'tqt_maintasks_tasks',
      'module' => 'Tasks',
      'bean_name' => 'Task',
      'source' => 'non-db',
      'vname' => 'LBL_TASKS',
    ),
    'meetings' => 
    array (
      'name' => 'meetings',
      'type' => 'link',
      'relationship' => 'tqt_maintasks_meetings',
      'module' => 'Meetings',
      'bean_name' => 'Meeting',
      'source' => 'non-db',
      'vname' => 'LBL_MEETINGS',
    ),
    'calls' => 
    array (
      'name' => 'calls',
      'type' => 'link',
      'relationship' => 'tqt_maintasks_calls',
      'module' => 'Calls',
      'bean_name' => 'Call',
      'source' => 'non-db',
      'vname' => 'LBL_CALLS',
    ),
    'documents' => 
    array (
      'name' => 'documents',
      'type' => 'link',
      'relationship' => 'tqt_maintasks_documents',
      'module' => 'Documents',
      'bean_name' => 'Document',
      'source' => 'non-db',
      'vname' => 'LBL_DOCUMENTS',
    ),
    'tqt_subtasks' => 
    array (
      'name' => 'tqt_subtasks',
      'type' => 'link',
      'relationship' => 'tqt_maintasks_tqt_subtasks',
      'source' => 'non-db',
      'vname' => 'LBL_TQT_SUBTASKS',
    ),
    'tqt_summaryweeks' => 
    array (
      'name' => 'tqt_summaryweeks',
      'type' => 'link',
      'relationship' => 'tqt_maintasks_tqt_summaryweeks',
      'source' => 'non-db',
      'vname' => 'LBL_TQT_SUMMARYWEEKS',
    ),
    'SecurityGroups' => 
    array (
      'name' => 'SecurityGroups',
      'type' => 'link',
      'relationship' => 'securitygroups_tqt_maintasks',
      'module' => 'SecurityGroups',
      'bean_name' => 'SecurityGroup',
      'source' => 'non-db',
      'vname' => 'LBL_SECURITYGROUPS',
    ),
  ),
  'indices' => 
  array (
    'id' => 
    array (
      'name' => 'tqt_maintaskspk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    'idx_tqt_maintasks_branch_id' => 
    array (
      'name' => 'idx_tqt_maintasks_branch_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
      ),
    ),
    'idx_tqt_maintasks_department_id' => 
    array (
      'name' => 'idx_tqt_maintasks_department_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'department_id',
      ),
    ),
    'idx_tqt_maintasks_branch_dept' => 
    array (
      'name' => 'idx_tqt_maintasks_branch_dept',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
        1 => 'department_id',
      ),
    ),
    'idx_tqt_maintasks_assigned' => 
    array (
      'name' => 'idx_tqt_maintasks_assigned',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'assigned_user_id',
      ),
    ),
    0 => 
    array (
      'name' => 'idx_main_del_name',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'name',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_main_del_status',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'status',
      ),
    ),
    2 => 
    array (
      'name' => 'idx_main_del_confirm',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'eval_confirm',
      ),
    ),
    3 => 
    array (
      'name' => 'idx_main_del_assigned',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'assigned_user_id',
      ),
    ),
    4 => 
    array (
      'name' => 'idx_main_del_handle_user',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'handle_user_id',
      ),
    ),
    5 => 
    array (
      'name' => 'idx_main_del_parent_type',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'parent_type',
      ),
    ),
    6 => 
    array (
      'name' => 'idx_main_parent_group',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'parent_type',
        1 => 'parent_id',
      ),
    ),
  ),
  'relationships' => 
  array (
    'tqt_maintasks_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_MainTasks',
      'rhs_table' => 'tqt_maintasks',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'tqt_maintasks_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_MainTasks',
      'rhs_table' => 'tqt_maintasks',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
    'tqt_maintasks_assigned_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_MainTasks',
      'rhs_table' => 'tqt_maintasks',
      'rhs_key' => 'assigned_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'handle_users_tqt_maintasks' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_MainTasks',
      'rhs_table' => 'tqt_maintasks',
      'rhs_key' => 'handle_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'engineer_users_tqt_maintasks' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_MainTasks',
      'rhs_table' => 'tqt_maintasks',
      'rhs_key' => 'engineer_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'opportunities_tqt_maintasks' => 
    array (
      'lhs_module' => 'Opportunities',
      'lhs_table' => 'opportunities',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_MainTasks',
      'rhs_table' => 'tqt_maintasks',
      'rhs_key' => 'parent_id',
      'relationship_type' => 'one-to-many',
      'relationship_role_column' => 'parent_type',
      'relationship_role_column_value' => 'Opportunities',
    ),
    'contracts_tqt_maintasks' => 
    array (
      'lhs_module' => 'Contracts',
      'lhs_table' => 'contracts',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_MainTasks',
      'rhs_table' => 'tqt_maintasks',
      'rhs_key' => 'parent_id',
      'relationship_type' => 'one-to-many',
      'relationship_role_column' => 'parent_type',
      'relationship_role_column_value' => 'Contracts',
    ),
    'tqt_maintasks_tasks' => 
    array (
      'lhs_module' => 'TQT_MainTasks',
      'lhs_table' => 'tqt_maintasks',
      'lhs_key' => 'id',
      'rhs_module' => 'Tasks',
      'rhs_table' => 'tasks',
      'rhs_key' => 'parent_id',
      'relationship_type' => 'one-to-many',
      'relationship_role_column' => 'parent_type',
      'relationship_role_column_value' => 'TQT_MainTasks',
    ),
    'tqt_maintasks_meetings' => 
    array (
      'lhs_module' => 'TQT_MainTasks',
      'lhs_table' => 'tqt_maintasks',
      'lhs_key' => 'id',
      'rhs_module' => 'Meetings',
      'rhs_table' => 'meetings',
      'rhs_key' => 'parent_id',
      'relationship_type' => 'one-to-many',
      'relationship_role_column' => 'parent_type',
      'relationship_role_column_value' => 'TQT_MainTasks',
    ),
    'tqt_maintasks_calls' => 
    array (
      'lhs_module' => 'TQT_MainTasks',
      'lhs_table' => 'tqt_maintasks',
      'lhs_key' => 'id',
      'rhs_module' => 'Calls',
      'rhs_table' => 'calls',
      'rhs_key' => 'parent_id',
      'relationship_type' => 'one-to-many',
      'relationship_role_column' => 'parent_type',
      'relationship_role_column_value' => 'TQT_MainTasks',
    ),
    'tqt_maintasks_documents' => 
    array (
      'lhs_module' => 'TQT_MainTasks',
      'lhs_table' => 'tqt_maintasks',
      'lhs_key' => 'id',
      'rhs_module' => 'Documents',
      'rhs_table' => 'documents',
      'rhs_key' => 'id',
      'relationship_type' => 'many-to-many',
      'join_table' => 'linked_documents',
      'join_key_lhs' => 'parent_id',
      'join_key_rhs' => 'document_id',
      'relationship_role_column' => 'parent_type',
      'relationship_role_column_value' => 'TQT_MainTasks',
    ),
  ),
  'optimistic_lock' => true,
  'templates' => 
  array (
    'assignable' => 'assignable',
    'groups' => 'groups',
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
