incomCRM.language.setLanguage('Cashes', {"LBL_ID":"ID","LBL_DATE_ENTERED":"Ng\u00e0y t\u1ea1o","LBL_DATE_MODIFIED":"Ng\u00e0y c\u1eadp nh\u1eadt","LBL_MODIFIED":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_MODIFIED_ID":"ID ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_MODIFIED_NAME":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_CREATED":"Ng\u01b0\u1eddi t\u1ea1o","LBL_CREATED_ID":"ID ng\u01b0\u1eddi t\u1ea1o","LBL_DESCRIPTION":"Di\u1ec5n gi\u1ea3i","LBL_DELETED":"\u0110\u00e3 x\u00f3a?","LBL_NAME":"S\u1ed1 phi\u1ebfu","LBL_CREATED_USER":"Ng\u01b0\u1eddi t\u1ea1o","LBL_MODIFIED_USER":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_ASSIGNED_TO":"Nv.Ql\u00fd","LBL_ASSIGNED_TO_ID":"ID Nv.K\u1ebf to\u00e1n","LBL_ASSIGNED_TO_NAME":"Nv.K\u1ebf to\u00e1n","LBL_ASSIGNED_TO_USER":"Nv.Ql\u00fd","LBL_ASSIGNED_USER":"Nv.K\u1ebf to\u00e1n","LBL_MODULE_NAME":"Qu\u1ea3n l\u00fd Thu\/Chi","LBL_MODULE_TITLE":"Qu\u1ea3n l\u00fd Thu\/Chi: Trang ch\u1ee7","LNK_NEW_RECORD":"Th\u00eam m\u1edbi","LNK_LIST":"Danh s\u00e1ch phi\u1ebfu","LBL_LIST_FORM_TITLE":"Danh s\u00e1ch phi\u1ebfu Thu\/Chi","LBL_SEARCH_FORM_TITLE":"T\u00ecm ki\u1ebfm","LBL_MODULE_SUBPANEL_TITLE":"Th\u00f4ng tin phi\u1ebfu","LBL_IMPORT_EXCEL":"Nh\u1eadp Excel","LNK_NEW_CASH_TYPE_1":"T\u1ea1o phi\u1ebfu Thu","LNK_NEW_CASH_TYPE_2":"T\u1ea1o phi\u1ebfu Chi","LBL_CASH_TYPE_1":"Phi\u1ebfu Thu","LBL_CASH_TYPE_2":"Phi\u1ebfu Chi","LBL_ACCOUNTING_TYPE_1":"H\u1ea1ch to\u00e1n - Ti\u1ec1n Thu","LBL_ACCOUNTING_TYPE_2":"H\u1ea1ch to\u00e1n - Ti\u1ec1n Chi","LBL_ACCOUNTING_TYPE_TITLE":"H\u1ea1ch to\u00e1n \u0111\u01a1n h\u00e0ng","LBL_MULTI_PAYMENT_TYPE":"Thu ti\u1ec1n nhi\u1ec1u h\u00ecnh th\u1ee9c","LBL_LIST_PROCESS_TITLE":"Qu\u1ea3n l\u00fd Thu\/Chi","LBL_CASH_TYPE":"Lo\u1ea1i phi\u1ebfu","LBL_STATUS":"Tr\u1ea1ng th\u00e1i","LBL_RECORD_STATUS":"Tr\u1ea1ng th\u00e1i ghi qu\u1ef9","LBL_ACCOUNTING_STATUS":"T\u00ecnh tr\u1ea1ng h\u1ea1ch to\u00e1n","LBL_BRANCHES":"Chi nh\u00e1nh","LBL_DATE_PERFORM":"Ng\u00e0y ch\u1ee9ng t\u1eeb","LBL_DATE_ACCOUNTING":"Ng\u00e0y h\u1ea1ch to\u00e1n","LBL_AMOUNT":"S\u1ed1 ti\u1ec1n","LBL_NOTES":"\u0110\u00ednh k\u00e8m","LBL_DOCUMENT_NO":"S\u1ed1 ch\u1ee9ng t\u1eeb","LBL_DOCUMENT_ATTACH":"Ch\u1ee9ng t\u1eeb k\u00e8m theo","LBL_CASH_PERSON":"Ng\u01b0\u1eddi n\u1ed9p\/nh\u1eadn","LBL_ADDRESS":"\u0110\u1ecba ch\u1ec9","LBL_REASON_DETAIL":"Chi ti\u1ebft l\u00fd do","LBL_PAYMENT_TYPE":"H\u00ecnh th\u1ee9c thanh to\u00e1n","LBL_LIST_PAYMENT_TYPE":"H\u00ecnh th\u1ee9c TT","LBL_AMT_ACCOUNTING":"Ti\u1ec1n h\u1ea1ch to\u00e1n","LBL_TRANSFER_PAYMENT":"H\u00ecnh th\u1ee9c TT Thu","LBL_TRANSFER_BANK_ID":"T\u00e0i kho\u1ea3n Thu","LBL_CURRENCY_ID":"Ti\u1ec1n t\u1ec7","LBL_CURRENCY":"Ti\u1ec1n t\u1ec7","LBL_RATE_USD":"T\u1ec9 gi\u00e1","LBL_EXCHANGE_RATE_DIFF":"H\u1ea1ch to\u00e1n ch\u00eanh l\u1ec7ch t\u1ef7 gi\u00e1?","LBL_EXCHANGE_RATE_DIFF2":"T\u1ef1 \u0111\u1ed9ng h\u1ea1ch to\u00e1n ch\u00eanh l\u1ec7ch t\u1ef7 gi\u00e1?","LBL_AMT_DIFF":"Ti\u1ec1n ch\u00eanh l\u1ec7ch","LBL_CASH_REASON_ID":"ID Lo\u1ea1i","LBL_CASH_REASON_NAME":"Theo","LBL_CASH_REASONS":"Lo\u1ea1i thu\/chi","LBL_ACCOUNT_ID":"ID Kh\u00e1ch h\u00e0ng","LBL_ACCOUNT_NAME":"Kh\u00e1ch h\u00e0ng\/NCC","LBL_ACCOUNTS":"Kh\u00e1ch h\u00e0ng","LBL_REFERENCE_CODE":"M\u00e3 k\u1ebf to\u00e1n","LBL_OPPORTUNITY_ID":"ID \u0111\u01a1n h\u00e0ng","LBL_OPPORTUNITY_CODE":"S\u1ed1 h\u00f3a \u0111\u01a1n","LBL_OPPORTUNITY_NAME":"PO No.","LBL_OPPORTUNITIES":"\u0110\u01a1n h\u00e0ng","LBL_CASH_ACCOUNTING":"H\u1ea1ch to\u00e1n","LBL_PROJECT_ID":"ID D\u1ef1 \u00e1n","LBL_PROJECT_NAME":"T\u00ean d\u1ef1 \u00e1n","LBL_PROJECTS":"D\u1ef1 \u00e1n","LBL_ATTACHMENT_FILES":"\u0110\u00ednh k\u00e8m","LBL_REASON_TYPE_1":"Thu theo","LBL_REASON_TYPE_2":"Chi theo","LBL_CASH_PERSON_1":"Ng\u01b0\u1eddi n\u1ed9p","LBL_CASH_PERSON_2":"Ng\u01b0\u1eddi nh\u1eadn","LBL_ACCOUNTING_STATUS_NONE":"Kh\u00f4ng h\u1ea1ch to\u00e1n","LBL_RELATED_ID":"Phi\u1ebfu li\u00ean quan","LBL_DATE_ENTERED_FROM":"Ng\u00e0y t\u1ea1o T\u1eeb","LBL_DATE_MODIFIED_FROM":"Ng\u00e0y s\u1eeda T\u1eeb","LBL_DATE_PERFORM_FROM":"Ng\u00e0y ch\u1ee9ng t\u1eeb T\u1eeb","LBL_DATE_ACCOUNTING_FROM":"Ng\u00e0y h\u1ea1ch to\u00e1n T\u1eeb","LBL_STATUS_PROCESSING":"X\u1eed l\u00fd: ","LBL_RPT_COMPARE_DEBT_CUSTOMERS":"\u0110\u1ed1i chi\u1ebfu c\u00f4ng n\u1ee3 Kh\u00e1ch h\u00e0ng\/Nh\u00e0 cung c\u1ea5p","LBL_RPT_COMPARE_DEBT_PRODUCTS":"\u0110\u1ed1i chi\u1ebfu c\u00f4ng n\u1ee3 Kh\u00e1ch h\u00e0ng\/Nh\u00e0 cung c\u1ea5p - Theo S\u1ea3n ph\u1ea9m","LBL_RPT_COMPARE_DEBT_PROJECTS":"\u0110\u1ed1i chi\u1ebfu c\u00f4ng n\u1ee3 Kh\u00e1ch h\u00e0ng\/Nh\u00e0 cung c\u1ea5p - Theo D\u1ef1 \u00e1n","LBL_RPT_COMPARE_DEBT_ORDERS":"\u0110\u1ed1i chi\u1ebfu c\u00f4ng n\u1ee3 Kh\u00e1ch h\u00e0ng\/Nh\u00e0 cung c\u1ea5p - Theo \u0110\u01a1n h\u00e0ng","LBL_RPT_LIST_EXPENSE":"Th\u1ed1ng k\u00ea c\u00e1c kho\u1ea3n chi","LBL_SALE_USER_ID":"ID Nv.B\u00e1n h\u00e0ng","LBL_SALE_USER_NAME":"Nv.B\u00e1n h\u00e0ng","LBL_SALE_USERS":"Nv.B\u00e1n h\u00e0ng","LBL_RPT_REPORT_WORKING":"B\u00e1o c\u00e1o Thu\/Chi","LBL_CHART_REASON_MONTHLY":"Th\u1ed1ng k\u00ea Lo\u1ea1i Thu\/Chi \u2013 Theo th\u00e1ng","LBL_DUPLICATE_BUTTON_LABEL":"Nh\u00e2n \u0111\u00f4i","LBL_LIST_EXPENSES":"C\u00e1c kho\u1ea3n chi","LBL_SECURITYGROUPS":"Security Groups","LBL_SECURITYGROUPS_SUBPANEL_TITLE":"Security Groups","LBL_ACCOUNTING_CODE_ID":"ID K\u1ebf to\u00e1n","LBL_ACCOUNTING_CODE_NAME":"TK k\u1ebf to\u00e1n","LBL_ACCOUNTING_CODES":"T\u00e0i kho\u1ea3n k\u1ebf to\u00e1n","LBL_IS_LOCKED":"Kh\u00f3a d\u1eef li\u1ec7u?","LBL_BANK_ACCOUNT_ID":"ID Ng\u00e2n h\u00e0ng","LBL_BANK_ACCOUNT_NAME":"TK Ng\u00e2n h\u00e0ng","LBL_BANK_ACCOUNTS":"T\u00e0i kho\u1ea3n ng\u00e2n h\u00e0ng"});