<?php
// created: 2025-02-26 15:18:10
$<PERSON><PERSON><PERSON><PERSON><PERSON>["dictionary"]["Checkin_Date"] = array (
  'table' => 'checkin_dates',
  'audited' => false,
  'unified_search' => false,
  'duplicate_merge' => false,
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'L<PERSON>_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'name',
      'dbType' => 'varchar',
      'len' => 255,
      'audited' => true,
      'required' => true,
      'unified_search' => true,
      'merge_filter' => 'selected',
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
      'comment' => 'Full text of the note',
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'checkin_dates_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'checkin_dates_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'branch_id' => 
    array (
      'name' => 'branch_id',
      'vname' => 'LBL_BRANCH_ID',
      'type' => 'enum',
      'options' => 'users_branch_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'BranchID of assigned user',
    ),
    'department_id' => 
    array (
      'name' => 'department_id',
      'vname' => 'LBL_DEPARTMENT_ID',
      'type' => 'enum',
      'options' => 'users_department_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'DepartmentID of assigned user',
    ),
    'assigned_user_id' => 
    array (
      'name' => 'assigned_user_id',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'vname' => 'LBL_ASSIGNED_TO_ID',
      'group' => 'assigned_user_name',
      'type' => 'relate',
      'table' => 'users',
      'module' => 'Users',
      'isnull' => 'false',
      'dbType' => 'id',
      'audited' => true,
      'reportable' => true,
      'duplicate_merge' => 'disabled',
      'comment' => 'User ID assigned to record',
      'massupdate' => false,
    ),
    'assigned_user_name' => 
    array (
      'name' => 'assigned_user_name',
      'link' => 'assigned_user_link',
      'vname' => 'LBL_ASSIGNED_TO_NAME',
      'rname' => 'user_name',
      'type' => 'relate',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'assigned_user_id',
      'module' => 'Users',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'assigned_user_link' => 
    array (
      'name' => 'assigned_user_link',
      'type' => 'link',
      'relationship' => 'checkin_dates_assigned_user',
      'vname' => 'LBL_ASSIGNED_TO_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
      'duplicate_merge' => 'enabled',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'table' => 'users',
    ),
    'date_perform' => 
    array (
      'name' => 'date_perform',
      'vname' => 'LBL_DATE_PERFORM',
      'type' => 'datetime',
      'massupdate' => false,
      'display_default' => 'now',
    ),
    'checkin_type' => 
    array (
      'name' => 'checkin_type',
      'vname' => 'LBL_CHECKIN_TYPE',
      'type' => 'enum',
      'options' => 'checkin_type_dom',
      'dbType' => 'char',
      'len' => 1,
      'audited' => true,
      'massupdate' => false,
      'default' => '1',
    ),
    'target_day' => 
    array (
      'name' => 'target_day',
      'vname' => 'LBL_TARGET_DAY',
      'type' => 'text',
    ),
    'target_month' => 
    array (
      'name' => 'target_month',
      'vname' => 'LBL_TARGET_MONTH',
      'type' => 'text',
    ),
    'sales_result' => 
    array (
      'name' => 'sales_result',
      'vname' => 'LBL_SALES_RESULT',
      'type' => 'text',
    ),
    'call_00' => 
    array (
      'name' => 'call_00',
      'vname' => 'LBL_CALL_00',
      'type' => 'tinyint',
      'len' => 3,
    ),
    'call_01' => 
    array (
      'name' => 'call_01',
      'vname' => 'LBL_CALL_01',
      'type' => 'tinyint',
      'len' => 3,
    ),
    'call_02' => 
    array (
      'name' => 'call_02',
      'vname' => 'LBL_CALL_02',
      'type' => 'tinyint',
      'len' => 3,
    ),
    'call_03' => 
    array (
      'name' => 'call_03',
      'vname' => 'LBL_CALL_03',
      'type' => 'tinyint',
      'len' => 3,
    ),
    'call_04' => 
    array (
      'name' => 'call_04',
      'vname' => 'LBL_CALL_04',
      'type' => 'tinyint',
      'len' => 3,
    ),
    'call_05' => 
    array (
      'name' => 'call_05',
      'vname' => 'LBL_CALL_05',
      'type' => 'tinyint',
      'len' => 3,
    ),
    'meet_01' => 
    array (
      'name' => 'meet_01',
      'vname' => 'LBL_MEET_01',
      'type' => 'tinyint',
      'len' => 3,
    ),
    'meet_02' => 
    array (
      'name' => 'meet_02',
      'vname' => 'LBL_MEET_02',
      'type' => 'tinyint',
      'len' => 3,
    ),
    'meet_03' => 
    array (
      'name' => 'meet_03',
      'vname' => 'LBL_MEET_03',
      'type' => 'tinyint',
      'len' => 3,
    ),
    'meet_04' => 
    array (
      'name' => 'meet_04',
      'vname' => 'LBL_MEET_04',
      'type' => 'tinyint',
      'len' => 3,
    ),
    'meet_05' => 
    array (
      'name' => 'meet_05',
      'vname' => 'LBL_MEET_05',
      'type' => 'tinyint',
      'len' => 3,
    ),
    'latlng' => 
    array (
      'name' => 'latlng',
      'vname' => 'LBL_LATLNG',
      'type' => 'varchar',
      'len' => 100,
      'id_address' => 'location_nearby',
    ),
    'location_nearby' => 
    array (
      'name' => 'location_nearby',
      'vname' => 'LBL_LOCATION_NEARBY',
      'type' => 'varchar',
      'len' => 250,
    ),
    'company_distance' => 
    array (
      'name' => 'company_distance',
      'vname' => 'LBL_COMPANY_DISTANCE',
      'type' => 'double',
    ),
    'checkout_latlng' => 
    array (
      'name' => 'checkout_latlng',
      'vname' => 'LBL_CHECKOUT_LATLNG',
      'type' => 'varchar',
      'len' => 100,
      'id_address' => 'checkout_location',
    ),
    'checkout_location' => 
    array (
      'name' => 'checkout_location',
      'vname' => 'LBL_CHECKOUT_LOCATION',
      'type' => 'varchar',
      'len' => 250,
    ),
    'checkout_distance' => 
    array (
      'name' => 'checkout_distance',
      'vname' => 'LBL_CHECKOUT_DISTANCE',
      'type' => 'double',
    ),
    'date_in' => 
    array (
      'name' => 'date_in',
      'vname' => 'LBL_DATE',
      'type' => 'date',
      'source' => 'non-db',
      'massupdate' => false,
      'importable' => 'false',
    ),
    'time_check_in' => 
    array (
      'name' => 'time_check_in',
      'vname' => 'LBL_TIME_CHECK_IN',
      'type' => 'char',
      'len' => 5,
      'source' => 'non-db',
      'massupdate' => false,
      'importable' => 'false',
    ),
    'time_check_out' => 
    array (
      'name' => 'time_check_out',
      'vname' => 'LBL_TIME_CHECK_OUT',
      'type' => 'char',
      'len' => 5,
      'massupdate' => false,
      'importable' => 'false',
    ),
  ),
  'indices' => 
  array (
    'id' => 
    array (
      'name' => 'checkin_datespk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    'idx_checkin_dates_branch_id' => 
    array (
      'name' => 'idx_checkin_dates_branch_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
      ),
    ),
    'idx_checkin_dates_department_id' => 
    array (
      'name' => 'idx_checkin_dates_department_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'department_id',
      ),
    ),
    'idx_checkin_dates_branch_dept' => 
    array (
      'name' => 'idx_checkin_dates_branch_dept',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
        1 => 'department_id',
      ),
    ),
    'idx_checkin_dates_assigned' => 
    array (
      'name' => 'idx_checkin_dates_assigned',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'assigned_user_id',
      ),
    ),
    0 => 
    array (
      'name' => 'idx_checkin_date_id_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'id',
        1 => 'deleted',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_checkin_date_assigned_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'assigned_user_id',
      ),
    ),
    2 => 
    array (
      'name' => 'idx_checkin_date_type_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'checkin_type',
      ),
    ),
  ),
  'relationships' => 
  array (
    'checkin_dates_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Checkin_Dates',
      'rhs_table' => 'checkin_dates',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'checkin_dates_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Checkin_Dates',
      'rhs_table' => 'checkin_dates',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
    'checkin_dates_assigned_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Checkin_Dates',
      'rhs_table' => 'checkin_dates',
      'rhs_key' => 'assigned_user_id',
      'relationship_type' => 'one-to-many',
    ),
  ),
  'optimistic_lock' => true,
  'templates' => 
  array (
    'assignable' => 'assignable',
    'groups' => 'groups',
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
