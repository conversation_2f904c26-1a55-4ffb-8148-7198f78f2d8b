
<section id="EditViewViewBody" class="view-module-{$module|strtolower}">

<form action="index.php" method="POST" name="{$form_name}" id="{$form_id}" class="_edit-{$instanceName}" {$enctype}>
<input type="hidden" name="module" value="{$module}" />
<input type="hidden" name="action" id="action" />
{if isset($smarty.request.isDuplicate) && $smarty.request.isDuplicate eq "true"}
<input type="hidden" name="record" id="record" value="" />
<input type="hidden" name="duplicateSave" value="true" />
{else}
<input type="hidden" name="record" id="record" value="{$fields.id.value}" />
{/if}
<input type="hidden" name="return_module" value="{$smarty.request.return_module}" />
<input type="hidden" name="return_action" value="{if $smarty.request.return_action eq 'LoadTabSubpanels'}DetailView{else}{$smarty.request.return_action}{/if}" />
<input type="hidden" name="return_id" value="{$smarty.request.return_id}" />
<input type="hidden" name="isDuplicate" value="false" />
<input type="hidden" name="module_tab" /> 
<input type="hidden" name="contact_role" />
{if !empty($smarty.request.return_module)}
<input type="hidden" name="relate_to" value="{if $smarty.request.return_relationship}{$smarty.request.return_relationship}{else}{$smarty.request.return_module}{/if}" />
<input type="hidden" name="relate_id" value="{$smarty.request.return_id}" />
{/if}
<input type="hidden" name="offset" value="{$offset}" />
{if empty($fields.id.value)}
<input type="hidden" name="_notify_random" value="{php}echo create_guid();{/php}" />
{/if}
<input type="hidden" name="isFromEditView" value="{$module}" />
<input type="hidden" name="type" value="{$fields.type.value}" />
<input type="hidden" name="rowindex" value="{$fields.rowindex.value}" />
<input type="hidden" name="o_account_id" value="{$fields.account_id.value}" />
<table width="100%" cellpadding="0" cellspacing="0" border="0">
<tr>
<td class="buttons">
{if $bean->aclAccess("save")}<input title="{$APP.LBL_SAVE_BUTTON_TITLE}" accessKey="{$APP.LBL_SAVE_BUTTON_KEY}" class="button save" onclick="{if $isDuplicate}this.form.return_id.value=''; {/if}this.form.action.value='Save'; return check_form('EditView');" type="submit" name="button" value="{$APP.LBL_SAVE_BUTTON_LABEL}" />{/if} 
{if !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($fields.id.value))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {elseif !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($smarty.request.return_id))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {else}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='index'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {/if}
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=Contracts", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</td>
</tr>
</table>
{incomCRM_include include=$includes}
<div id="DEFAULT" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="DEFAULT_GROUP" style="display:block">
<table width="100%" border="0" cellspacing="1" cellpadding="0">
<tr>
<td valign="top" id='code_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CODE' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='30%' id='_code_field' >
{counter name="panelFieldCount"}

{if strlen($fields.code.value) <= 0}
{assign var="value" value=$fields.code.default_value }
{else}
{assign var="value" value=$fields.code.value }
{/if}
{if isTypeNumber($fields.code.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.code.name}' id='{$fields.code.name}' size='30' maxlength='255' value='{$value}' title='' tabindex='100'  /> 

</td>
<td valign="top" id='opportunity_name_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_OPPORTUNITY_NAME' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='30%' id='_opportunity_name_field' >
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{else}{/if}<input type="hidden" name="{$fields.opportunity_name.id_name}" id="{$fields.opportunity_name.id_name}" value="{$fields.opportunity_id.value}" />
<input type="text" name="{$fields.opportunity_name.name}" class="sqsEnabled" tabindex="101" id="{$fields.opportunity_name.name}" size="16" value="{$fields.opportunity_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{else}{/if}
<input type="button" name="btn_{$fields.opportunity_name.name}" id="btn_{$fields.opportunity_name.name}" tabindex="101" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.opportunity_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"EditView","field_to_name_array":{"id":"opportunity_id","name":"opportunity_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.opportunity_name.name}" id="btn_clr_{$fields.opportunity_name.name}" tabindex="101" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.opportunity_name.name}.value=""; this.form.{$fields.opportunity_name.id_name}.value=""; this.form.{$fields.opportunity_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
{if !empty($isSmartView)}</div>{else}{/if}<script type="text/javascript">enableQS(false);</script>
</td>
</tr>
<tr>
<td valign="top" id='name_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_NAME' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='30%' id='_name_field' >
{counter name="panelFieldCount"}

{if strlen($fields.name.value) <= 0}
{assign var="value" value=$fields.name.default_value }
{else}
{assign var="value" value=$fields.name.value }
{/if}
{if isTypeNumber($fields.name.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.name.name}' id='{$fields.name.name}' size='30' maxlength='255' value='{$value}' title='' tabindex='102'  /> 

</td>
<td valign="top" id='assigned_user_name_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='30%' id='_assigned_user_name_field' >
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{else}{/if}<input type="hidden" name="{$fields.assigned_user_name.id_name}" id="{$fields.assigned_user_name.id_name}" value="{$fields.assigned_user_id.value}" />
<input type="text" name="{$fields.assigned_user_name.name}" class="sqsEnabled" tabindex="103" id="{$fields.assigned_user_name.name}" size="16" value="{$fields.assigned_user_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{else}{/if}
<input type="button" name="btn_{$fields.assigned_user_name.name}" id="btn_{$fields.assigned_user_name.name}" tabindex="103" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.assigned_user_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"EditView","field_to_name_array":{"id":"assigned_user_id","user_name":"assigned_user_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.assigned_user_name.name}" id="btn_clr_{$fields.assigned_user_name.name}" tabindex="103" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.assigned_user_name.name}.value=""; this.form.{$fields.assigned_user_name.id_name}.value=""; this.form.{$fields.assigned_user_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
{if !empty($isSmartView)}</div>{else}{/if}<script type="text/javascript">enableQS(false);</script>
</td>
</tr>
<tr>
<td valign="top" id='payment_type_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_PAYMENT_TYPE' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_payment_type_field' >
{counter name="panelFieldCount"}

<select name="{$fields.payment_type.name}" id="{$fields.payment_type.name}" title='' tabindex="104"  >
{if isset($fields.payment_type.value) && $fields.payment_type.value != ''}
{html_options options=$fields.payment_type.options selected=$fields.payment_type.value}
{else}
{html_options options=$fields.payment_type.options selected=$fields.payment_type.default}
{/if}
</select>
</td>
<td valign="top" id='debt_user_name_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DEBT_USER_NAME' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_debt_user_name_field' >
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{else}{/if}<input type="hidden" name="{$fields.debt_user_name.id_name}" id="{$fields.debt_user_name.id_name}" value="{$fields.debt_user_id.value}" />
<input type="text" name="{$fields.debt_user_name.name}" class="sqsEnabled" tabindex="105" id="{$fields.debt_user_name.name}" size="16" value="{$fields.debt_user_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{else}{/if}
<input type="button" name="btn_{$fields.debt_user_name.name}" id="btn_{$fields.debt_user_name.name}" tabindex="105" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.debt_user_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"EditView","field_to_name_array":{"id":"debt_user_id","user_name":"debt_user_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.debt_user_name.name}" id="btn_clr_{$fields.debt_user_name.name}" tabindex="105" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.debt_user_name.name}.value=""; this.form.{$fields.debt_user_name.id_name}.value=""; this.form.{$fields.debt_user_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
{if !empty($isSmartView)}</div>{else}{/if}<script type="text/javascript">enableQS(false);</script>
</td>
</tr>
<tr>
<td valign="top" id='payment_note_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_PAYMENT_NOTE' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_payment_note_field' >
{counter name="panelFieldCount"}

{if empty($fields.payment_note.value)}
{assign var="value" value=$fields.payment_note.default_value }
{else}
{assign var="value" value=$fields.payment_note.value }
{/if}
<textarea id="{$fields.payment_note.name}" name="{$fields.payment_note.name}" rows="2" cols="40" title='' tabindex="106"  >{$value}</textarea>
</td>
<td valign="top" id='date_signed_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DATE_SIGNED' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_date_signed_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.date_signed.value }
<input autocomplete="off" type="text" name="{$fields.date_signed.name}" id="{$fields.date_signed.name}" value="{$date_value}" title=''  tabindex='107' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.date_signed.name}_trigger" align="absmiddle" />
&nbsp;(<span class="dateFormat">{$USER_DATEFORMAT}</span>)
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.date_signed.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.date_signed.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
</tr>
<tr>
<td valign="top" id='amt_before_discount_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_AMT_BEFORE_DISCOUNT' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_amt_before_discount_field' >
{counter name="panelFieldCount"}

{if strlen($fields.amt_before_discount.value) <= 0}
{assign var="value" value=$fields.amt_before_discount.default_value }
{else}
{assign var="value" value=$fields.amt_before_discount.value }
{/if}
{if isTypeNumber($fields.amt_before_discount.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.amt_before_discount.name}' id='{$fields.amt_before_discount.name}' size='30'  value='{$value}' title='' tabindex='108'  /> 

</td>
<td valign="top" id='date_start_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DATE_START' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_date_start_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.date_start.value }
<input autocomplete="off" type="text" name="{$fields.date_start.name}" id="{$fields.date_start.name}" value="{$date_value}" title=''  tabindex='109' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.date_start.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.date_start.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.date_start.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
</tr>
<tr>
<td valign="top" id='product_discount_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_PRODUCT_DISCOUNT' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_product_discount_field' >
{counter name="panelFieldCount"}

{if strlen($fields.product_discount.value) <= 0}
{assign var="value" value=$fields.product_discount.default_value }
{else}
{assign var="value" value=$fields.product_discount.value }
{/if}
{if isTypeNumber($fields.product_discount.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.product_discount.name}' id='{$fields.product_discount.name}' size='30'  value='{$value}' title='' tabindex='110'  /> 

</td>
<td valign="top" id='date_end_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DATE_END' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_date_end_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.date_end.value }
<input autocomplete="off" type="text" name="{$fields.date_end.name}" id="{$fields.date_end.name}" value="{$date_value}" title=''  tabindex='111' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.date_end.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.date_end.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.date_end.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
</tr>
<tr>
<td valign="top" id='product_amt_discount_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_PRODUCT_AMT_DISCOUNT' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_product_amt_discount_field' >
{counter name="panelFieldCount"}

{if strlen($fields.product_amt_discount.value) <= 0}
{assign var="value" value=$fields.product_amt_discount.default_value }
{else}
{assign var="value" value=$fields.product_amt_discount.value }
{/if}
{if isTypeNumber($fields.product_amt_discount.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.product_amt_discount.name}' id='{$fields.product_amt_discount.name}' size='30'  value='{$value}' title='' tabindex='112'  /> 

</td>
<td valign="top" id='date_expired_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DATE_EXPIRED' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_date_expired_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.date_expired.value }
<input autocomplete="off" type="text" name="{$fields.date_expired.name}" id="{$fields.date_expired.name}" value="{$date_value}" title=''  tabindex='113' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.date_expired.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.date_expired.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.date_expired.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
</tr>
<tr>
<td valign="top" id='amt_after_discount_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_AMT_AFTER_DISCOUNT' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_amt_after_discount_field' >
{counter name="panelFieldCount"}

{if strlen($fields.amt_after_discount.value) <= 0}
{assign var="value" value=$fields.amt_after_discount.default_value }
{else}
{assign var="value" value=$fields.amt_after_discount.value }
{/if}
{if isTypeNumber($fields.amt_after_discount.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.amt_after_discount.name}' id='{$fields.amt_after_discount.name}' size='30'  value='{$value}' title='' tabindex='114'  /> 

</td>
<td valign="top" id='date_sent_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DATE_SENT' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_date_sent_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.date_sent.value }
<input autocomplete="off" type="text" name="{$fields.date_sent.name}" id="{$fields.date_sent.name}" value="{$date_value}" title=''  tabindex='115' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.date_sent.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.date_sent.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.date_sent.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
</tr>
<tr>
<td valign="top" id='discount_percent_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DISCOUNT_PERCENT' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_discount_percent_field' >
{counter name="panelFieldCount"}

{if strlen($fields.discount_percent.value) <= 0}
{assign var="value" value=$fields.discount_percent.default_value }
{else}
{assign var="value" value=$fields.discount_percent.value }
{/if}
{if isTypeNumber($fields.discount_percent.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.discount_percent.name}' id='{$fields.discount_percent.name}' size='30'  value='{$value}' title='' tabindex='116'  /> 

</td>
<td valign="top" id='date_receipt_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DATE_RECEIPT' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_date_receipt_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.date_receipt.value }
<input autocomplete="off" type="text" name="{$fields.date_receipt.name}" id="{$fields.date_receipt.name}" value="{$date_value}" title=''  tabindex='117' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.date_receipt.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.date_receipt.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.date_receipt.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
</tr>
<tr>
<td valign="top" id='contract_amount_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTRACT_AMOUNT' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_contract_amount_field' >
{counter name="panelFieldCount"}

{if strlen($fields.contract_amount.value) <= 0}
{assign var="value" value=$fields.contract_amount.default_value }
{else}
{assign var="value" value=$fields.contract_amount.value }
{/if}
{if isTypeNumber($fields.contract_amount.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.contract_amount.name}' id='{$fields.contract_amount.name}' size='30'  value='{$value}' title='' tabindex='118'  /> 

</td>
<td valign="top" id='cnt_type_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CNT_TYPE' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='30%' id='_cnt_type_field' >
{counter name="panelFieldCount"}

<select name="{$fields.cnt_type.name}" id="{$fields.cnt_type.name}" title='' tabindex="119"  >
{if isset($fields.cnt_type.value) && $fields.cnt_type.value != ''}
{html_options options=$fields.cnt_type.options selected=$fields.cnt_type.value}
{else}
{html_options options=$fields.cnt_type.options selected=$fields.cnt_type.default}
{/if}
</select>
</td>
</tr>
<tr>
<td valign="top" id='contract_tax_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTRACT_TAX' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_contract_tax_field' >
{counter name="panelFieldCount"}

<select name="{$fields.contract_tax.name}" id="{$fields.contract_tax.name}" title='' tabindex="120"  >
{if isset($fields.contract_tax.value) && $fields.contract_tax.value != ''}
{html_options options=$fields.contract_tax.options selected=$fields.contract_tax.value}
{else}
{html_options options=$fields.contract_tax.options selected=$fields.contract_tax.default}
{/if}
</select>
</td>
<td valign="top" id='market_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_MARKET' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_market_field' >
{counter name="panelFieldCount"}

<select name="{$fields.market.name}" id="{$fields.market.name}" title='' tabindex="121"  >
{if isset($fields.market.value) && $fields.market.value != ''}
{html_options options=$fields.market.options selected=$fields.market.value}
{else}
{html_options options=$fields.market.options selected=$fields.market.default}
{/if}
</select>
</td>
</tr>
<tr>
<td valign="top" id='amount_total_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_AMOUNT_TOTAL' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_amount_total_field' >
{counter name="panelFieldCount"}

{if strlen($fields.amount_total.value) <= 0}
{assign var="value" value=$fields.amount_total.default_value }
{else}
{assign var="value" value=$fields.amount_total.value }
{/if}
{if isTypeNumber($fields.amount_total.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.amount_total.name}' id='{$fields.amount_total.name}' size='30'  value='{$value}' title='' tabindex='122'  /> 

</td>
<td valign="top" id='refund_percentage_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_REFUND_PERCENTAGE' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_refund_percentage_field' >
{counter name="panelFieldCount"}

{if strlen($fields.refund_percentage.value) <= 0}
{assign var="value" value=$fields.refund_percentage.default_value }
{else}
{assign var="value" value=$fields.refund_percentage.value }
{/if}
{if isTypeNumber($fields.refund_percentage.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.refund_percentage.name}' id='{$fields.refund_percentage.name}' size='30'  value='{$value}' title='' tabindex='123'  /> 

</td>
</tr>
<tr>
<td valign="top" id='amount_note_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_AMOUNT_NOTE' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_amount_note_field' >
{counter name="panelFieldCount"}

<span id='{$fields.amount_note.name}' >
{$fields.amount_note.value}
</span>
</td>
<td valign="top" id='refund_amount_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_REFUND_AMOUNT' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_refund_amount_field' >
{counter name="panelFieldCount"}

{if strlen($fields.refund_amount.value) <= 0}
{assign var="value" value=$fields.refund_amount.default_value }
{else}
{assign var="value" value=$fields.refund_amount.value }
{/if}
{if isTypeNumber($fields.refund_amount.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.refund_amount.name}' id='{$fields.refund_amount.name}' size='30'  value='{$value}' title='' tabindex='125'  /> 

</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("DEFAULT").style.display='none';</script>
{/if}
<div id="LBL_ACCOUNT_INFO" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_ACCOUNT_INFO');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_ACCOUNT_INFO_IMG" border="0" />
{incomCRM_translate label='LBL_ACCOUNT_INFO' module='Contracts'}
</a>
</h4>
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_ACCOUNT_INFO_GROUP" style="display:block">
<table width="100%" border="0" cellspacing="1" cellpadding="0">
<tr>
<td valign="top" id='account_name_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACCOUNT_NAME' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='30%' id='_account_name_field' >
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{else}{/if}<input type="hidden" name="{$fields.account_name.id_name}" id="{$fields.account_name.id_name}" value="{$fields.account_id.value}" />
<input type="text" name="{$fields.account_name.name}" class="sqsEnabled" tabindex="126" id="{$fields.account_name.name}" size="16" value="{$fields.account_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{else}{/if}
<input type="button" name="btn_{$fields.account_name.name}" id="btn_{$fields.account_name.name}" tabindex="126" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.account_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"EditView","field_to_name_array":{"id":"account_id","name":"account_name","account_code":"account_code","tax_code":"account_tax_code","phone_office":"account_phone","phone_fax":"account_fax","account_type":"account_type","account_status":"account_status","location_area":"account_region","billing_address_street":"account_billing_address","billing_representation":"account_representation","billing_position":"account_position"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.account_name.name}" id="btn_clr_{$fields.account_name.name}" tabindex="126" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.account_name.name}.value=""; this.form.{$fields.account_name.id_name}.value=""; this.form.{$fields.account_name.name}.focus();this.form.account_id.value=""; this.form.account_name.value=""; this.form.account_code.value="";' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
{if !empty($isSmartView)}</div>{else}{/if}<script type="text/javascript">enableQS(false);</script>
</td>
<td valign="top" id='account_code_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACCOUNT_CODE' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_account_code_field' >
{counter name="panelFieldCount"}

{if strlen($fields.account_code.value) <= 0}
{assign var="value" value=$fields.account_code.default_value }
{else}
{assign var="value" value=$fields.account_code.value }
{/if}
{if isTypeNumber($fields.account_code.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.account_code.name}' id='{$fields.account_code.name}' size='30'  value='{$value}' title='' tabindex='127'  readonly="readonly" /> 

</td>
</tr>
<tr>
<td valign="top" id='account_billing_address_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACCOUNT_BILLING_ADDRESS' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_account_billing_address_field' >
{counter name="panelFieldCount"}

{if strlen($fields.account_billing_address.value) <= 0}
{assign var="value" value=$fields.account_billing_address.default_value }
{else}
{assign var="value" value=$fields.account_billing_address.value }
{/if}
{if isTypeNumber($fields.account_billing_address.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.account_billing_address.name}' id='{$fields.account_billing_address.name}' size='30'  value='{$value}' title='' tabindex='128'  readonly="readonly" /> 

</td>
<td valign="top" id='account_type_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACCOUNT_TYPE' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_account_type_field' >
{counter name="panelFieldCount"}

{if strlen($fields.account_type.value) <= 0}
{assign var="value" value=$fields.account_type.default_value }
{else}
{assign var="value" value=$fields.account_type.value }
{/if}
{if isTypeNumber($fields.account_type.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.account_type.name}' id='{$fields.account_type.name}' size='30'  value='{$value}' title='' tabindex='129'  readonly="readonly" /> 

</td>
</tr>
<tr>
<td valign="top" id='account_tax_code_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACCOUNT_TAX_CODE' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_account_tax_code_field' >
{counter name="panelFieldCount"}

{if strlen($fields.account_tax_code.value) <= 0}
{assign var="value" value=$fields.account_tax_code.default_value }
{else}
{assign var="value" value=$fields.account_tax_code.value }
{/if}
{if isTypeNumber($fields.account_tax_code.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.account_tax_code.name}' id='{$fields.account_tax_code.name}' size='30'  value='{$value}' title='' tabindex='130'  readonly="readonly" /> 

</td>
<td valign="top" id='account_region_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACCOUNT_REGION' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_account_region_field' >
{counter name="panelFieldCount"}

{if strlen($fields.account_region.value) <= 0}
{assign var="value" value=$fields.account_region.default_value }
{else}
{assign var="value" value=$fields.account_region.value }
{/if}
{if isTypeNumber($fields.account_region.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.account_region.name}' id='{$fields.account_region.name}' size='30'  value='{$value}' title='' tabindex='131'  readonly="readonly" /> 

</td>
</tr>
<tr>
<td valign="top" id='account_phone_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACCOUNT_PHONE' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_account_phone_field' >
{counter name="panelFieldCount"}

{if strlen($fields.account_phone.value) <= 0}
{assign var="value" value=$fields.account_phone.default_value }
{else}
{assign var="value" value=$fields.account_phone.value }
{/if}
{if isTypeNumber($fields.account_phone.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.account_phone.name}' id='{$fields.account_phone.name}' size='30'  value='{$value}' title='' tabindex='132'  readonly="readonly" /> 

</td>
<td valign="top" id='account_status_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACCOUNT_STATUS' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_account_status_field' >
{counter name="panelFieldCount"}

{if strlen($fields.account_status.value) <= 0}
{assign var="value" value=$fields.account_status.default_value }
{else}
{assign var="value" value=$fields.account_status.value }
{/if}
{if isTypeNumber($fields.account_status.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.account_status.name}' id='{$fields.account_status.name}' size='30'  value='{$value}' title='' tabindex='133'  readonly="readonly" /> 

</td>
</tr>
<tr>
<td valign="top" id='account_representation_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACCOUNT_REPRESENTATION' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_account_representation_field' >
{counter name="panelFieldCount"}

{if strlen($fields.account_representation.value) <= 0}
{assign var="value" value=$fields.account_representation.default_value }
{else}
{assign var="value" value=$fields.account_representation.value }
{/if}
{if isTypeNumber($fields.account_representation.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.account_representation.name}' id='{$fields.account_representation.name}' size='30' maxlength='255' value='{$value}' title='' tabindex='134'  /> 

</td>
<td valign="top" id='account_position_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACCOUNT_POSITION' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_account_position_field' >
{counter name="panelFieldCount"}

{if strlen($fields.account_position.value) <= 0}
{assign var="value" value=$fields.account_position.default_value }
{else}
{assign var="value" value=$fields.account_position.value }
{/if}
{if isTypeNumber($fields.account_position.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.account_position.name}' id='{$fields.account_position.name}' size='30' maxlength='255' value='{$value}' title='' tabindex='135'  /> 

</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_ACCOUNT_INFO").style.display='none';</script>
{/if}
<div id="LBL_CONTRACT_NOTE" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_CONTRACT_NOTE');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_CONTRACT_NOTE_IMG" border="0" />
{incomCRM_translate label='LBL_CONTRACT_NOTE' module='Contracts'}
</a>
</h4>
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_CONTRACT_NOTE_GROUP" style="display:block">
<table width="100%" border="0" cellspacing="1" cellpadding="0">
<tr>
<td valign="top" id='contract_note_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTRACT_NOTE' module='Contracts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" colspan='3' id='_contract_note_field' >
{counter name="panelFieldCount"}

{if empty($fields.contract_note.value)}
{assign var="value" value=$fields.contract_note.default_value }
{else}
{assign var="value" value=$fields.contract_note.value }
{/if}
<textarea id="{$fields.contract_note.name}" name="{$fields.contract_note.name}" rows="5" cols="80" title='' tabindex="136"  >{$value}</textarea>
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_CONTRACT_NOTE").style.display='none';</script>
{/if}

<div class="buttons" style="margin-top:15px;">
{if $bean->aclAccess("save")}<input title="{$APP.LBL_SAVE_BUTTON_TITLE}" accessKey="{$APP.LBL_SAVE_BUTTON_KEY}" class="button save" onclick="{if $isDuplicate}this.form.return_id.value=''; {/if}this.form.action.value='Save'; return check_form('EditView');" type="submit" name="button" value="{$APP.LBL_SAVE_BUTTON_LABEL}" />{/if} 
{if !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($fields.id.value))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {elseif !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($smarty.request.return_id))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {else}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='index'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {/if}
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=Contracts", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</div>
</form>
{$set_focus_block}
</section>{literal}
<script type="text/javascript">
addToValidate('EditView', 'id', 'id', false, '{/literal}{incomCRM_translate label='LBL_ID' module='Contracts'}{literal}' );
addToValidate('EditView', 'date_entered_date', 'date', false, 'Ngày tạo' );
addToValidate('EditView', 'date_modified_date', 'date', false, 'Ngày cập nhật' );
addToValidate('EditView', 'modified_user_id', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_ID' module='Contracts'}{literal}' );
addToValidate('EditView', 'modified_by_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_NAME' module='Contracts'}{literal}' );
addToValidate('EditView', 'created_by', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_CREATED_ID' module='Contracts'}{literal}' );
addToValidate('EditView', 'created_by_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_CREATED' module='Contracts'}{literal}' );
addToValidate('EditView', 'deleted', 'bool', false, '{/literal}{incomCRM_translate label='LBL_DELETED' module='Contracts'}{literal}' );
addToValidate('EditView', 'name', 'name', true, '{/literal}{incomCRM_translate label='LBL_NAME' module='Contracts'}{literal}' );
addToValidate('EditView', 'description', 'text', false, '{/literal}{incomCRM_translate label='LBL_DESCRIPTION' module='Contracts'}{literal}' );
addToValidate('EditView', 'branch_id', 'enum', false, '{/literal}{incomCRM_translate label='LBL_BRANCH_ID' module='Contracts'}{literal}' );
addToValidate('EditView', 'department_id', 'enum', false, '{/literal}{incomCRM_translate label='LBL_DEPARTMENT_ID' module='Contracts'}{literal}' );
addToValidate('EditView', 'assigned_user_id', 'relate', false, '{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO_ID' module='Contracts'}{literal}' );
addToValidate('EditView', 'assigned_user_name', 'relate', true, '{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='Contracts'}{literal}' );
addToValidate('EditView', 'rowindex', 'int', false, '{/literal}{incomCRM_translate label='LBL_ROW_INDEX' module='Contracts'}{literal}' );
addToValidate('EditView', 'code', 'varchar', true, '{/literal}{incomCRM_translate label='LBL_CODE' module='Contracts'}{literal}' );
addToValidate('EditView', 'is_locked', 'bool', false, '{/literal}{incomCRM_translate label='LBL_IS_LOCKED' module='Contracts'}{literal}' );
addToValidate('EditView', 'signed', 'bool', false, '{/literal}{incomCRM_translate label='LBL_SIGNED' module='Contracts'}{literal}' );
addToValidate('EditView', 'date_signed', 'date', false, '{/literal}{incomCRM_translate label='LBL_DATE_SIGNED' module='Contracts'}{literal}' );
addToValidate('EditView', 'date_signed_to', 'date', false, '{/literal}{incomCRM_translate label='LBL_LIST_DATE_SIGNED_TO' module='Contracts'}{literal}' );
addToValidate('EditView', 'date_start', 'date', false, '{/literal}{incomCRM_translate label='LBL_DATE_START' module='Contracts'}{literal}' );
addToValidate('EditView', 'date_end', 'date', false, '{/literal}{incomCRM_translate label='LBL_DATE_END' module='Contracts'}{literal}' );
addToValidate('EditView', 'date_expired', 'date', false, '{/literal}{incomCRM_translate label='LBL_DATE_EXPIRED' module='Contracts'}{literal}' );
addToValidate('EditView', 'status', 'enum', false, '{/literal}{incomCRM_translate label='LBL_STATUS' module='Contracts'}{literal}' );
addToValidate('EditView', 'stage', 'enum', false, '{/literal}{incomCRM_translate label='LBL_STAGE' module='Contracts'}{literal}' );
addToValidate('EditView', 'type', 'enum', false, '{/literal}{incomCRM_translate label='LBL_TYPE' module='Contracts'}{literal}' );
addToValidate('EditView', 'cnt_type', 'enum', true, '{/literal}{incomCRM_translate label='LBL_CNT_TYPE' module='Contracts'}{literal}' );
addToValidate('EditView', 'market', 'enum', false, '{/literal}{incomCRM_translate label='LBL_MARKET' module='Contracts'}{literal}' );
addToValidate('EditView', 'dept_warning', 'bool', false, '{/literal}{incomCRM_translate label='LBL_DEPT_WARNING' module='Contracts'}{literal}' );
addToValidate('EditView', 'currency', 'currency', false, '{/literal}{incomCRM_translate label='LBL_CURRENCY' module='Contracts'}{literal}' );
addToValidate('EditView', 'currency_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_CURRENCY' module='Contracts'}{literal}' );
addToValidate('EditView', 'payment_type', 'enum', false, '{/literal}{incomCRM_translate label='LBL_PAYMENT_TYPE' module='Contracts'}{literal}' );
addToValidate('EditView', 'payment_note', 'text', false, '{/literal}{incomCRM_translate label='LBL_PAYMENT_NOTE' module='Contracts'}{literal}' );
addToValidate('EditView', 'contract_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_CONTRACT_AMOUNT' module='Contracts'}{literal}' );
addToValidate('EditView', 'contract_tax', 'enum', false, '{/literal}{incomCRM_translate label='LBL_CONTRACT_TAX' module='Contracts'}{literal}' );
addToValidate('EditView', 'tax_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TAX_AMOUNT' module='Contracts'}{literal}' );
addToValidate('EditView', 'amount_before_tax', 'currency', false, '{/literal}{incomCRM_translate label='LBL_AMOUNT_BEFORE_TAX' module='Contracts'}{literal}' );
addToValidate('EditView', 'amount_after_tax', 'currency', false, '{/literal}{incomCRM_translate label='LBL_AMOUNT_AFTER_TAX' module='Contracts'}{literal}' );
addToValidate('EditView', 'amount_total', 'currency', false, '{/literal}{incomCRM_translate label='LBL_AMOUNT_TOTAL' module='Contracts'}{literal}' );
addToValidate('EditView', 'amount_note', 'text', false, '{/literal}{incomCRM_translate label='LBL_AMOUNT_NOTE' module='Contracts'}{literal}' );
addToValidate('EditView', 'discount_percent', 'double', false, '{/literal}{incomCRM_translate label='LBL_DISCOUNT_PERCENT' module='Contracts'}{literal}' );
addToValidate('EditView', 'discount_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_DISCOUNT_AMOUNT' module='Contracts'}{literal}' );
addToValidate('EditView', 'product_discount', 'double', false, '{/literal}{incomCRM_translate label='LBL_PRODUCT_DISCOUNT' module='Contracts'}{literal}' );
addToValidate('EditView', 'product_amt_discount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_PRODUCT_AMT_DISCOUNT' module='Contracts'}{literal}' );
addToValidate('EditView', 'amt_before_discount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_AMT_BEFORE_DISCOUNT' module='Contracts'}{literal}' );
addToValidate('EditView', 'amt_after_discount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_AMT_AFTER_DISCOUNT' module='Contracts'}{literal}' );
addToValidate('EditView', 'refund_percentage', 'double', false, '{/literal}{incomCRM_translate label='LBL_REFUND_PERCENTAGE' module='Contracts'}{literal}' );
addToValidate('EditView', 'refund_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_REFUND_AMOUNT' module='Contracts'}{literal}' );
addToValidate('EditView', 'reduce_price', 'currency', false, '{/literal}{incomCRM_translate label='LBL_REDUCE_PRICE' module='Contracts'}{literal}' );
addToValidate('EditView', 'commission', 'currency', false, '{/literal}{incomCRM_translate label='LBL_COMMISSION' module='Contracts'}{literal}' );
addToValidate('EditView', 'industry[]', 'multienum', false, '{/literal}{incomCRM_translate label='LBL_INDUSTRY' module='Contracts'}{literal}' );
addToValidate('EditView', 'responsible', 'text', false, '{/literal}{incomCRM_translate label='LBL_RESPONSIBLE' module='Contracts'}{literal}' );
addToValidate('EditView', 'account_representation', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_REPRESENTATION' module='Contracts'}{literal}' );
addToValidate('EditView', 'account_position', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_POSITION' module='Contracts'}{literal}' );
addToValidate('EditView', 'contract_note', 'text', false, '{/literal}{incomCRM_translate label='LBL_CONTRACT_NOTE' module='Contracts'}{literal}' );
addToValidate('EditView', 'parent_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_RELATED_ID' module='Contracts'}{literal}' );
addToValidate('EditView', 'parent_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_RELATED_NAME' module='Contracts'}{literal}' );
addToValidate('EditView', 'parent_code', 'relate', false, '{/literal}{incomCRM_translate label='LBL_RELATED_CODE' module='Contracts'}{literal}' );
addToValidate('EditView', 'account_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_ID' module='Contracts'}{literal}' );
addToValidate('EditView', 'account_name', 'relate', true, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_NAME' module='Contracts'}{literal}' );
addToValidate('EditView', 'account_code', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_CODE' module='Contracts'}{literal}' );
addToValidate('EditView', 'account_phone', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_PHONE' module='Contracts'}{literal}' );
addToValidate('EditView', 'account_fax', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_FAX' module='Contracts'}{literal}' );
addToValidate('EditView', 'account_status', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_STATUS' module='Contracts'}{literal}' );
addToValidate('EditView', 'account_type', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_TYPE' module='Contracts'}{literal}' );
addToValidate('EditView', 'account_type2', 'enum', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_TYPE' module='Contracts'}{literal}' );
addToValidate('EditView', 'account_tax_code', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_TAX_CODE' module='Contracts'}{literal}' );
addToValidate('EditView', 'account_region', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_REGION' module='Contracts'}{literal}' );
addToValidate('EditView', 'account_billing_address', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_BILLING_ADDRESS' module='Contracts'}{literal}' );
addToValidate('EditView', 'debt_user_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_DEBT_USER_ID' module='Contracts'}{literal}' );
addToValidate('EditView', 'debt_user_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_DEBT_USER_NAME' module='Contracts'}{literal}' );
addToValidate('EditView', 'sw_status', 'enum', false, '{/literal}{incomCRM_translate label='LBL_SW_STATUS' module='Contracts'}{literal}' );
addToValidate('EditView', 'sw_date_start', 'date', false, '{/literal}{incomCRM_translate label='LBL_SW_DATE_START' module='Contracts'}{literal}' );
addToValidate('EditView', 'sw_date_end', 'date', false, '{/literal}{incomCRM_translate label='LBL_SW_DATE_END' module='Contracts'}{literal}' );
addToValidate('EditView', 'deadline', 'date', false, '{/literal}{incomCRM_translate label='LBL_DEADLINE' module='Contracts'}{literal}' );
addToValidate('EditView', 'deadline_late', 'bool', false, '{/literal}{incomCRM_translate label='LBL_DEADLINE_LATE' module='Contracts'}{literal}' );
addToValidate('EditView', 'days_late', 'int', false, '{/literal}{incomCRM_translate label='LBL_DAYS_LATE' module='Contracts'}{literal}' );
addToValidate('EditView', 'eval_confirm', 'enum', false, '{/literal}{incomCRM_translate label='LBL_EVAL_CONFIRM' module='Contracts'}{literal}' );
addToValidate('EditView', 'eval_notes', 'text', false, '{/literal}{incomCRM_translate label='LBL_EVAL_NOTES' module='Contracts'}{literal}' );
addToValidate('EditView', 'handle_user_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_HANDLE_USER_ID' module='Contracts'}{literal}' );
addToValidate('EditView', 'handle_user_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_HANDLE_USER_NAME' module='Contracts'}{literal}' );
addToValidate('EditView', 'engineer_user_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_ENGINNEER_USER_ID' module='Contracts'}{literal}' );
addToValidate('EditView', 'engineer_user_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_ENGINNEER_USER_NAME' module='Contracts'}{literal}' );
addToValidate('EditView', 'date_sent', 'date', false, '{/literal}{incomCRM_translate label='LBL_DATE_SENT' module='Contracts'}{literal}' );
addToValidate('EditView', 'date_receipt', 'date', false, '{/literal}{incomCRM_translate label='LBL_DATE_RECEIPT' module='Contracts'}{literal}' );
addToValidate('EditView', 'opportunity_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_OPPORTUNITY_ID' module='Contracts'}{literal}' );
addToValidate('EditView', 'opportunity_name', 'relate', true, '{/literal}{incomCRM_translate label='LBL_OPPORTUNITY_NAME' module='Contracts'}{literal}' );
addToValidate('EditView', 'opportunity_code', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_OPPORTUNITY_CODE' module='Contracts'}{literal}' );
addToValidateBinaryDependency('EditView', 'assigned_user_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Contracts'}{literal}{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO' module='Contracts'}{literal}', 'assigned_user_id' );
addToValidateBinaryDependency('EditView', 'account_name', 'alpha', true, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Contracts'}{literal}{/literal}{incomCRM_translate label='LBL_ACCOUNT_NAME' module='Contracts'}{literal}', 'account_id' );
addToValidateBinaryDependency('EditView', 'debt_user_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Contracts'}{literal}{/literal}{incomCRM_translate label='LBL_DEBT_USER_NAME' module='Contracts'}{literal}', 'debt_user_id' );
addToValidateBinaryDependency('EditView', 'opportunity_name', 'alpha', true, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Contracts'}{literal}{/literal}{incomCRM_translate label='LBL_OPPORTUNITY_NAME' module='Contracts'}{literal}', 'opportunity_id' );
</script>
<script type="text/javascript">if(typeof sqs_objects == 'undefined'){var sqs_objects = new Array;}
sqs_objects['EditView_opportunity_name'] = {"form":"EditView","method":"query","modules":["Opportunities"],"group":"or","field_list":["name","id"],"populate_list":["opportunity_name","opportunity_id"],"required_list":["parent_id"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['EditView_assigned_user_name'] = {"form":"EditView","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["assigned_user_name","assigned_user_id"],"required_list":["assigned_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['EditView_debt_user_name'] = {"form":"EditView","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["debt_user_name","debt_user_id"],"required_list":["debt_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['EditView_account_name'] = {"form":"EditView","method":"query","modules":["Accounts"],"group":"or","field_list":["name","id","account_code","tax_code","phone_office","phone_fax","account_type","account_status","location_area","billing_address_street","billing_representation","billing_position"],"populate_list":["EditView_account_name","account_id","account_code","account_tax_code","account_phone","account_fax","account_type","account_status","account_region","account_billing_address","account_representation","account_position"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"required_list":["account_id"],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
</script>{/literal}
