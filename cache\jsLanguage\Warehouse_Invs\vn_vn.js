incomCRM.language.setLanguage('Warehouse_Invs', {"LBL_ID":"ID","LBL_DATE_ENTERED":"Ng\u00e0y t\u1ea1o","LBL_DATE_MODIFIED":"Ng\u00e0y c\u1eadp nh\u1eadt","LBL_MODIFIED":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_MODIFIED_ID":"ID ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_MODIFIED_NAME":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_CREATED":"Ng\u01b0\u1eddi t\u1ea1o","LBL_CREATED_ID":"ID ng\u01b0\u1eddi t\u1ea1o","LBL_DESCRIPTION":"Ghi ch\u00fa","LBL_DELETED":"\u0110\u00e3 x\u00f3a?","LBL_NAME":"M\u00e3 phi\u1ebfu","LBL_CREATED_USER":"Ng\u01b0\u1eddi t\u1ea1o","LBL_MODIFIED_USER":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_ASSIGNED_TO":"Nv.Ki\u1ec3m kho","LBL_ASSIGNED_TO_ID":"ID Nv.Ki\u1ec3m kho","LBL_ASSIGNED_TO_NAME":"Nv.Ki\u1ec3m kho","LBL_ASSIGNED_TO_USER":"Nv.Ql\u00fd","LBL_ASSIGNED_USER":"Nv.Ki\u1ec3m kho","LBL_MODULE_NAME":"Ki\u1ec3m kho","LBL_MODULE_TITLE":"Ki\u1ec3m kho: Trang ch\u1ee7","LNK_LIST":"Danh s\u00e1ch phi\u1ebfu","LNK_NEW_RECORD":"Ki\u1ec3m kho","LBL_LIST_FORM_TITLE":"Danh s\u00e1ch ki\u1ec3m kho","LBL_SEARCH_FORM_TITLE":"T\u00ecm ki\u1ebfm","LBL_STATUS":"Tr\u1ea1ng th\u00e1i","LBL_DATE_PERFORM":"Ng\u00e0y ki\u1ec3m","LBL_TQT_WAREHOUSE_ID":"ID Kho h\u00e0ng","LBL_TQT_WAREHOUSE_NAME":"Kho h\u00e0ng","LBL_TQT_WAREHOUSES":"Kho h\u00e0ng","LBL_WAREHOUSE_IN_ID":"ID Phi\u1ebfu nh\u1eadp","LBL_WAREHOUSE_IN_NAME":"Phi\u1ebfu nh\u1eadp","LBL_WAREHOUSE_INS":"Phi\u1ebfu nh\u1eadp","LBL_WAREHOUSE_OUT_ID":"ID Phi\u1ebfu xu\u1ea5t","LBL_WAREHOUSE_OUT_NAME":"Phi\u1ebfu xu\u1ea5t","LBL_WAREHOUSE_OUTS":"Phi\u1ebfu xu\u1ea5t","LBL_BTN_DRAFT":"L\u01b0u t\u1ea1m","LBL_BTN_APPROVE":"Ki\u1ec3m k\u00ea","LBL_BTN_CANCEL":"H\u1ee7y","LBL_BTN_RETAKE":"L\u1ea5y l\u1ea1i hi\u1ec7u ch\u1ec9nh","LBL_RPT_INVENTORIES":"Th\u1ed1ng k\u00ea ki\u1ec3m kho","LBL_TQT_PRODUCTS":"Danh s\u00e1ch s\u1ea3n ph\u1ea9m ki\u1ec3m kho","LBL_QUANTITY":"SL Ki\u1ec3m","LBL_IN_STOCK":"SL Th\u1ef1c t\u1ebf","LBL_QTY_DIFF":"SL l\u1ec7ch","LBL_PRICE":"\u0110\u01a1n gi\u00e1"});