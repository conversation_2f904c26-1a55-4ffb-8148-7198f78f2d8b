incomCRM.language.setLanguage('Calendar', {"LBL_MODULE_NAME":"L\u1ecbch c\u00f4ng vi\u1ec7c","LBL_MODULE_TITLE":"L\u1ecbch c\u00f4ng vi\u1ec7c","LNK_NEW_CALL":"S\u1eafp x\u1ebfp cu\u1ed9c g\u1ecdi","LNK_NEW_MEETING":"S\u1eafp x\u1ebfp cu\u1ed9c h\u1ecdp","LNK_NEW_APPOINTMENT":"T\u1ea1o Cu\u1ed9c h\u1eb9n","LNK_NEW_TASK":"T\u1ea1o c\u00f4ng vi\u1ec7c","LNK_CALL_LIST":"Danh s\u00e1ch cu\u1ed9c g\u1ecdi","LNK_MEETING_LIST":"Danh s\u00e1ch cu\u1ed9c h\u1ecdp","LNK_TASK_LIST":"Danh s\u00e1ch c\u00f4ng vi\u1ec7c","LNK_VIEW_CALENDAR":"H\u00f4m nay","LNK_IMPORT_CALLS":"Nh\u1eadp danh s\u00e1ch cu\u1ed9c g\u1ecdi","LNK_IMPORT_MEETINGS":"Nh\u1eadp danh s\u00e1ch cu\u1ed9c h\u1ecdp","LNK_IMPORT_TASKS":"Nh\u1eadp Danh s\u00e1ch c\u00f4ng vi\u1ec7c","LBL_DAY":"Ng\u00e0y","LBL_MONTH":"Th\u00e1ng","LBL_YEAR":"N\u0103m","LBL_WEEK":"Tu\u1ea7n","LBL_PREVIOUS_MONTH":"Th\u00e1ng v\u1eeba qua","LBL_PREVIOUS_DAY":"Ng\u00e0y v\u1eeba qua","LBL_PREVIOUS_YEAR":"N\u0103m v\u1eeba qua","LBL_PREVIOUS_WEEK":"Tu\u1ea7n v\u1eeba qua","LBL_NEXT_MONTH":"Th\u00e1ng k\u1ebf ti\u1ebfp","LBL_NEXT_DAY":"Ng\u00e0y k\u1ebf ti\u1ebfp","LBL_NEXT_YEAR":"N\u0103m k\u1ebf ti\u1ebfp","LBL_NEXT_WEEK":"Tu\u1ea7n k\u1ebf ti\u1ebfp","LBL_AM":"S\u00e1ng","LBL_PM":"Chi\u1ec1u","LBL_SCHEDULED":"S\u1eafp x\u1ebfp","LBL_BUSY":"B\u1eadn","LBL_CONFLICT":"Xung \u0111\u1ed9t","LBL_USER_CALENDARS":"L\u1ecbch c\u00f4ng t\u00e1c c\u00e1 nh\u00e2n","LBL_SHARED":"Chia s\u1ebb","LBL_PREVIOUS_SHARED":"Tr\u01b0\u1edbc \u0111\u00f3","LBL_NEXT_SHARED":"K\u1ebf ti\u1ebfp","LBL_SHARED_CAL_TITLE":"L\u1ecbch chung","LBL_USERS":"Ng\u01b0\u1eddi d\u00f9ng","LBL_REFRESH":"L\u00e0m m\u1edbi","LBL_EDIT":"Ch\u1ec9nh s\u1eeda","LBL_SELECT_USERS":"Ch\u1ecdn ng\u01b0\u1eddi d\u00f9ng \u0111\u01b0\u1ee3c chia s\u1ebb","LBL_FILTER_BY_TEAM":"Ch\u1ecdn ng\u01b0\u1eddi d\u00f9ng theo nh\u00f3m:","LBL_ASSIGNED_TO_NAME":"Ph\u00e2n c\u00f4ng cho","LBL_DATE":"Ng\u00e0y gi\u1edd b\u1eaft \u0111\u1ea7u","LBL_SECURITYGROUPS":"Filter user list by Security Group","LBL_QUICK_CREATE":"T\u1ea1o Nhanh","LBL_DEPARTMENT_TASK":"Ph\u00f2ng Ban","LBL_TEAMS_TASK":"\u0110\u1ed9i\/ Nh\u00f3m","LBL_EMPLOYEE_TASK":"Nh\u00e2n Vi\u00ean","LBL_ACCOUNT_NAME":"Kh\u00e1ch h\u00e0ng","LBL_DATE_FROM":"T\u1eeb ng\u00e0y","LBL_NUM_WEEK":"S\u1ed1 tu\u1ea7n","LNK_REPORT_BY_WORKOFEMPLOYEE":"B\u00e1o c\u00e1o t\u1ed5ng h\u1ee3p l\u1ecbch c\u00f4ng vi\u1ec7c c\u1ee7a nh\u00e2n vi\u00ean (S\u00e1ng-Chi\u1ec1u-T\u1ed1i)","LNK_REPORT_BY_EMPLOYEE_ACCOUNTS":"T\u1ed5ng h\u1ee3p t\u00e1c v\u1ee5 theo tu\u1ea7n, theo kh\u00e1ch h\u00e0ng c\u1ee7a m\u1ed7i nh\u00e2n vi\u00ean","LNK_REPORT_QUICK_ACTIVITIES":"B\u00e1o c\u00e1o t\u1ed5ng h\u1ee3p c\u00e1c ho\u1ea1t \u0111\u1ed9ng c\u1ee7a kh\u00e1ch h\u00e0ng","LBL_REPORT_EXPORT_CALENDAR":"B\u00e1o c\u00e1o n\u1ed9i dung c\u00f4ng vi\u1ec7c - Theo l\u1ecbch c\u00f4ng vi\u1ec7c","LBL_RPT_MORNING":"S\u00e1ng","LBL_RPT_AFTERNOON":"Chi\u1ec1u","LBL_RPT_EVENING":"T\u1ed1i","LNK_CALENDAR_VIEW":"L\u1ecbch c\u00f4ng vi\u1ec7c","LNK_CALENDAR_LIST":"L\u1ecbch - Danh s\u00e1ch","LNK_CALENDAR_ALL":"T\u1ea5t c\u1ea3 C\u00f4ng vi\u1ec7c","LBL_HOUR":"Gi\u1edd","LBL_OPEN_CALENDAR":"Ch\u1ecdn ng\u00e0y","LNK_NEW_REVIEW":"Th\u00eam \u0110\u00e1nh gi\u00e1","LBL_CALENDAR":"L\u1ecbch","LNK_MOBILE_HOME":"Trang ch\u1ee7","LBL_MOBILE_MY_TASKS":"Vi\u1ec7c h\u00f4m nay - C\u1ee7a T\u00f4i","LBL_MOBILE_WEEK_TASKS":"Vi\u1ec7c trong tu\u1ea7n","LBL_MOBILE_MONTH_TASKS":"Vi\u1ec7c trong th\u00e1ng - C\u1ee7a T\u00f4i","LBL_MOBILE_PROJECT":"Ch\u01b0\u01a1ng tr\u00ecnh Marketing","LBL_EXPIRED_OUT_OF":"Qu\u00e1 h\u1ea1n: ","LBL_EXPIRED_REMAIN":"C\u00f2n: ","LBL_EXPIRED_DAY":"ng\u00e0y","LBL_EXPIRED_HOUR":"gi\u1edd","LBL_EXPIRED_MINUTE":"ph\u00fat","LBL_EXPIRED_SECONDS":"v\u00e0i gi\u00e2y","LBL_EXPIRED_AGO":"tr\u01b0\u1edbc"});