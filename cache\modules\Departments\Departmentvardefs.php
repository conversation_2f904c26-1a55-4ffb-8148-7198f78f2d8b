<?php
// created: 2025-02-26 15:50:50
$G<PERSON><PERSON><PERSON><PERSON>["dictionary"]["Department"] = array (
  'table' => 'departments',
  'audited' => true,
  'unified_search' => false,
  'duplicate_merge' => false,
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'name',
      'dbType' => 'varchar',
      'len' => 200,
      'audited' => true,
      'required' => true,
      'acl' => true,
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'departments_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'departments_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'code' => 
    array (
      'name' => 'code',
      'vname' => 'LBL_CODE',
      'type' => 'varchar',
      'len' => 20,
      'audited' => true,
      'massupdate' => false,
    ),
    'branch_id' => 
    array (
      'name' => 'branch_id',
      'vname' => 'LBL_BRANCH_ID',
      'type' => 'enum',
      'dbType' => 'id',
      'options' => 'users_branch_dom',
      'audited' => true,
      'massupdate' => false,
    ),
    'branches' => 
    array (
      'name' => 'branches',
      'type' => 'link',
      'relationship' => 'branches_departments',
      'vname' => 'LBL_BRANCHES',
      'source' => 'non-db',
    ),
    'users' => 
    array (
      'name' => 'users',
      'type' => 'link',
      'relationship' => 'dept_users',
      'vname' => 'LBL_USERS',
      'source' => 'non-db',
      'comment' => 'List users of this department',
    ),
    'allow_users' => 
    array (
      'name' => 'allow_users',
      'type' => 'link',
      'relationship' => 'users_departments',
      'vname' => 'LBL_ALLOW_USERS',
      'source' => 'non-db',
      'comment' => 'List users can see data of this department',
    ),
    'team_groups' => 
    array (
      'name' => 'team_groups',
      'type' => 'link',
      'relationship' => 'departments_team_groups',
      'source' => 'non-db',
      'vname' => 'LBL_TEAM_GROUPS',
    ),
  ),
  'indices' => 
  array (
    'id' => 
    array (
      'name' => 'departmentspk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    0 => 
    array (
      'name' => 'idx_department_id_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'id',
        1 => 'deleted',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_department_name_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'name',
      ),
    ),
    2 => 
    array (
      'name' => 'idx_department_code_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'code',
      ),
    ),
    3 => 
    array (
      'name' => 'idx_department_branch_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'branch_id',
      ),
    ),
  ),
  'relationships' => 
  array (
    'departments_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Departments',
      'rhs_table' => 'departments',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'departments_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Departments',
      'rhs_table' => 'departments',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
    'branches_departments' => 
    array (
      'lhs_module' => 'Branches',
      'lhs_table' => 'branches',
      'lhs_key' => 'id',
      'rhs_module' => 'Departments',
      'rhs_table' => 'departments',
      'rhs_key' => 'branch_id',
      'relationship_type' => 'one-to-many',
    ),
    'dept_users' => 
    array (
      'lhs_module' => 'Departments',
      'lhs_table' => 'departments',
      'lhs_key' => 'id',
      'rhs_module' => 'Users',
      'rhs_table' => 'users',
      'rhs_key' => 'department',
      'relationship_type' => 'one-to-many',
    ),
  ),
  'optimistic_lock' => true,
  'templates' => 
  array (
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
