
<section id="DetailViewBody" class="view-module-{$module|strtolower}">

<form action="index.php" method="post" name="{$form_name|default:'DetailView'}" id="form" class="_view-{$instanceName}">
<table cellpadding="1" cellspacing="0" border="0" width="100%">
<tr>
<td class="buttons" align="left" valign="top" nowrap>
<input type="hidden" name="module" value="{$module}" />
<input type="hidden" name="action" value="EditView" />
<input type="hidden" name="record" value="{$fields.id.value}" />
<input type="hidden" name="return_action" />
<input type="hidden" name="return_module" />
<input type="hidden" name="return_id" />
<input type="hidden" name="module_tab" /> 
<input type="hidden" name="isDuplicate" value="false" />
<input type="hidden" name="offset" value="{$offset}" />
{if !isset($isEditEnabled) || (isset($isEditEnabled) && $isEditEnabled)}
<input title="{$APP.LBL_EDIT_BUTTON_TITLE}" accessKey="{$APP.LBL_EDIT_BUTTON_KEY}" class="button bedit" onclick="this.form.return_module.value='{$module}'; this.form.return_action.value='DetailView'; this.form.return_id.value='{$id}'; this.form.action.value='EditView'" type="submit" name="Edit" id='edit_button' value="  {$APP.LBL_EDIT_BUTTON_LABEL}  " />
<input title="{$APP.LBL_DUPLICATE_BUTTON_TITLE}" accessKey="{$APP.LBL_DUPLICATE_BUTTON_KEY}" class="button duplicate" onclick="this.form.return_module.value='{$module}'; this.form.return_action.value='index'; this.form.isDuplicate.value=true; this.form.action.value='EditView'" type="submit" name="Duplicate" value=" {$APP.LBL_DUPLICATE_BUTTON_LABEL} " id='duplicate_button' />
{/if}
{if !isset($isDeleteEnabled) || (isset($isDeleteEnabled) && $isDeleteEnabled)}
<input title="{$APP.LBL_DELETE_BUTTON_TITLE}" accessKey="{$APP.LBL_DELETE_BUTTON_KEY}" class="button delete" onclick="this.form.return_module.value='{$module}'; this.form.return_action.value='ListView'; this.form.action.value='Delete'; return confirm('{$APP.NTC_DELETE_CONFIRMATION}')" type="submit" name="Delete" value=" {$APP.LBL_DELETE_BUTTON_LABEL} " />
{/if}
</td>
<td class="buttons" align="left" valign="top" nowrap>
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=Approval_Levels", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</td>
<td align="right" width="90%">{$ADMIN_EDIT}</td>
</tr>
</table>
</form>
{incomCRM_include include=$includes}
<div id="DEFAULT" class="detailview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<div class="detail view {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="DEFAULT_GROUP" style="display:block">
<table id='detailpanel_1' cellspacing='{$gridline}'>
{$PAGINATION}
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_NAME' module='Approval_Levels'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_name_field' >
{counter name="panelFieldCount"}

<span id='{$fields.name.name}' >
{$fields.name.value}
</span>
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CODE' module='Approval_Levels'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_code_field' >
{counter name="panelFieldCount"}

<span id='{$fields.code.name}' >
{$fields.code.value}
</span>
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DESCRIPTION' module='Approval_Levels'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_description_field' >
{counter name="panelFieldCount"}

{$fields.description.value|url2html|nl2br}
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='Approval_Levels'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_assigned_user_name_field' >
{counter name="panelFieldCount"}

{$fields.assigned_user_name.value}
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_USERS_ALL_DEPARTMENT' module='Approval_Levels'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_users_all_department_field' >
{counter name="panelFieldCount"}

{if !empty($fields.users_all_department.value)}
{multienum_to_array string=$fields.users_all_department.value assign="vals"}
{foreach from=$vals item=item}
<li>{ $fields.users_all_department.options.$item }</li>
{/foreach}
{/if}
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_USERS_ALL_MANAGER' module='Approval_Levels'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_users_all_manager_field' >
{counter name="panelFieldCount"}

{if !empty($fields.users_all_manager.value)}
{multienum_to_array string=$fields.users_all_manager.value assign="vals"}
{foreach from=$vals item=item}
<li>{ $fields.users_all_manager.options.$item }</li>
{/foreach}
{/if}
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_USERS_ALL_GROUP' module='Approval_Levels'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_users_all_group_field' >
{counter name="panelFieldCount"}

{if !empty($fields.users_all_group.value)}
{multienum_to_array string=$fields.users_all_group.value assign="vals"}
{foreach from=$vals item=item}
<li>{ $fields.users_all_group.options.$item }</li>
{/foreach}
{/if}
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_USERS_ALL_BOD' module='Approval_Levels'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_users_all_bod_field' >
{counter name="panelFieldCount"}

{if !empty($fields.users_all_bod.value)}
{multienum_to_array string=$fields.users_all_bod.value assign="vals"}
{foreach from=$vals item=item}
<li>{ $fields.users_all_bod.options.$item }</li>
{/foreach}
{/if}
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DATE_ENTERED' module='Approval_Levels'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_date_entered_field' >
{counter name="panelFieldCount"}
{$fields.date_entered.value} {$APP.LBL_BY} {$fields.created_by_name.value}	
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DATE_MODIFIED' module='Approval_Levels'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_date_modified_field' >
{counter name="panelFieldCount"}
{$fields.date_modified.value} {$APP.LBL_BY} {$fields.modified_by_name.value}	
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("DEFAULT").style.display='none';</script>
{/if}

</section>