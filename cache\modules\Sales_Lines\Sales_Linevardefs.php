<?php
// created: 2025-02-26 15:19:19
$G<PERSON><PERSON><PERSON><PERSON>["dictionary"]["Sales_Line"] = array (
  'table' => 'sales_lines',
  'audited' => true,
  'unified_search' => true,
  'duplicate_merge' => false,
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'name',
      'dbType' => 'varchar',
      'len' => 255,
      'audited' => true,
      'required' => true,
      'unified_search' => true,
      'merge_filter' => 'selected',
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
      'comment' => 'Full text of the note',
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'sales_lines_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'sales_lines_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'branch_id' => 
    array (
      'name' => 'branch_id',
      'vname' => 'LBL_BRANCH_ID',
      'type' => 'enum',
      'options' => 'users_branch_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'BranchID of assigned user',
    ),
    'department_id' => 
    array (
      'name' => 'department_id',
      'vname' => 'LBL_DEPARTMENT_ID',
      'type' => 'enum',
      'options' => 'users_department_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'DepartmentID of assigned user',
    ),
    'assigned_user_id' => 
    array (
      'name' => 'assigned_user_id',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'vname' => 'LBL_ASSIGNED_TO_ID',
      'group' => 'assigned_user_name',
      'type' => 'relate',
      'table' => 'users',
      'module' => 'Users',
      'isnull' => 'false',
      'dbType' => 'id',
      'audited' => true,
      'reportable' => true,
      'duplicate_merge' => 'disabled',
      'comment' => 'User ID assigned to record',
    ),
    'assigned_user_name' => 
    array (
      'name' => 'assigned_user_name',
      'link' => 'assigned_user_link',
      'vname' => 'LBL_ASSIGNED_TO_NAME',
      'rname' => 'user_name',
      'type' => 'relate',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'assigned_user_id',
      'module' => 'Users',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'assigned_user_link' => 
    array (
      'name' => 'assigned_user_link',
      'type' => 'link',
      'relationship' => 'sales_lines_assigned_user',
      'vname' => 'LBL_ASSIGNED_TO_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
      'duplicate_merge' => 'enabled',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'table' => 'users',
    ),
    'status' => 
    array (
      'name' => 'status',
      'vname' => 'LBL_STATUS',
      'type' => 'enum',
      'options' => 'sales_lines_status_dom',
      'len' => 30,
      'audited' => true,
      'massupdate' => true,
      'default' => 'Active',
    ),
    'line_type' => 
    array (
      'name' => 'line_type',
      'vname' => 'LBL_LINE_TYPE',
      'type' => 'enum',
      'options' => 'sales_lines_type_dom',
      'len' => 30,
      'audited' => true,
      'massupdate' => true,
      'default' => 'Sales',
    ),
    'location_area' => 
    array (
      'name' => 'location_area',
      'vname' => 'LBL_LOCATION_AREA',
      'type' => 'enum',
      'options' => 'location_area_dom',
      'len' => 50,
      'audited' => true,
      'massupdate' => true,
    ),
    'location_city' => 
    array (
      'name' => 'location_city',
      'vname' => 'LBL_LOCATION_CITY',
      'type' => 'enum',
      'options' => 'location_cities_dom',
      'len' => 200,
      'audited' => true,
      'massupdate' => true,
    ),
    'location_district' => 
    array (
      'name' => 'location_district',
      'vname' => 'LBL_LOCATION_DISTRICT',
      'type' => 'enum',
      'options' => 'location_district_dom',
      'len' => 200,
      'audited' => true,
      'massupdate' => true,
    ),
    'date_apply_from' => 
    array (
      'name' => 'date_apply_from',
      'vname' => 'LBL_DATE_APPLY_FROM',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
      'validation' => 
      array (
        'type' => 'isbefore',
        'compareto' => 'date_apply_to',
        'blank' => false,
      ),
    ),
    'date_apply_to' => 
    array (
      'name' => 'date_apply_to',
      'vname' => 'LBL_DATE_APPLY_TO',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'cycle_type' => 
    array (
      'name' => 'cycle_type',
      'vname' => 'LBL_CYCLE_TYPE',
      'type' => 'enum',
      'options' => 'sales_lines_cycle_dom',
      'len' => 30,
      'audited' => true,
      'massupdate' => false,
      'default' => 'month',
    ),
    'cycle_days' => 
    array (
      'name' => 'cycle_days',
      'vname' => 'LBL_CYCLE_DAYS',
      'type' => 'varchar',
      'len' => 250,
    ),
    'repeat_day' => 
    array (
      'name' => 'repeat_day',
      'vname' => 'LBL_REPEAT_DAY',
      'type' => 'int',
      'len' => 4,
      'audited' => true,
      'massupdate' => false,
    ),
    'care_days' => 
    array (
      'name' => 'care_days',
      'vname' => 'LBL_CARE_DAYS',
      'type' => 'text',
    ),
    'distributor_id' => 
    array (
      'name' => 'distributor_id',
      'vname' => 'LBL_DISTRIBUTOR_ID',
      'type' => 'id',
      'audited' => true,
      'massupdate' => false,
    ),
    'distributor_name' => 
    array (
      'name' => 'distributor_name',
      'rname' => 'name',
      'id_name' => 'distributor_id',
      'vname' => 'LBL_DISTRIBUTOR_NAME',
      'source' => 'non-db',
      'type' => 'relate',
      'link' => 'distributors',
      'module' => 'Accounts',
      'join_name' => 'distributors',
      'required' => false,
      'massupdate' => false,
    ),
    'distributors' => 
    array (
      'name' => 'distributors',
      'vname' => 'LBL_DISTRIBUTORS',
      'type' => 'link',
      'source' => 'non-db',
      'relationship' => 'distributors_sales_lines',
    ),
    'accounts' => 
    array (
      'name' => 'accounts',
      'vname' => 'LBL_ACCOUNTS',
      'type' => 'link',
      'source' => 'non-db',
      'relationship' => 'sales_lines_accounts',
    ),
    'entry_count' => 
    array (
      'name' => 'entry_count',
      'vname' => 'LBL_ENTRY_COUNT',
      'type' => 'int',
      'len' => 4,
      'source' => 'non-db',
    ),
  ),
  'indices' => 
  array (
    'id' => 
    array (
      'name' => 'sales_linespk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    'idx_sales_lines_branch_id' => 
    array (
      'name' => 'idx_sales_lines_branch_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
      ),
    ),
    'idx_sales_lines_department_id' => 
    array (
      'name' => 'idx_sales_lines_department_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'department_id',
      ),
    ),
    'idx_sales_lines_branch_dept' => 
    array (
      'name' => 'idx_sales_lines_branch_dept',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
        1 => 'department_id',
      ),
    ),
    'idx_sales_lines_assigned' => 
    array (
      'name' => 'idx_sales_lines_assigned',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'assigned_user_id',
      ),
    ),
    0 => 
    array (
      'name' => 'idx_sale_line_id_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'id',
        1 => 'deleted',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_sale_line_assigned_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'assigned_user_id',
      ),
    ),
    2 => 
    array (
      'name' => 'idx_sale_line_del_status',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'status',
      ),
    ),
    3 => 
    array (
      'name' => 'idx_sale_line_del_type',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'line_type',
      ),
    ),
    4 => 
    array (
      'name' => 'idx_sale_line_del_city',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'location_city',
      ),
    ),
    5 => 
    array (
      'name' => 'idx_sale_line_del_dis_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'distributor_id',
      ),
    ),
  ),
  'relationships' => 
  array (
    'sales_lines_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Sales_Lines',
      'rhs_table' => 'sales_lines',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'sales_lines_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Sales_Lines',
      'rhs_table' => 'sales_lines',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
    'sales_lines_assigned_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Sales_Lines',
      'rhs_table' => 'sales_lines',
      'rhs_key' => 'assigned_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'distributors_sales_lines' => 
    array (
      'lhs_module' => 'Accounts',
      'lhs_table' => 'accounts',
      'lhs_key' => 'id',
      'rhs_module' => 'Sales_Lines',
      'rhs_table' => 'sales_lines',
      'rhs_key' => 'distributor_id',
      'relationship_type' => 'one-to-many',
    ),
  ),
  'optimistic_lock' => true,
  'templates' => 
  array (
    'assignable' => 'assignable',
    'groups' => 'groups',
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
