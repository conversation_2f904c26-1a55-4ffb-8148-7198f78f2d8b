
<section id="DetailViewBody" class="view-module-{$module|strtolower}">

<form action="index.php" method="POST" name="{$form_name}" id="{$form_id}" class="_edit-{$instanceName}" {$enctype}>
<input type="hidden" name="module" value="{$module}" />
<input type="hidden" name="action" id="action" />
{if isset($smarty.request.isDuplicate) && $smarty.request.isDuplicate eq "true"}
<input type="hidden" name="record" id="record" value="" />
<input type="hidden" name="duplicateSave" value="true" />
{else}
<input type="hidden" name="record" id="record" value="{$fields.id.value}" />
{/if}
<input type="hidden" name="return_module" value="{$smarty.request.return_module}" />
<input type="hidden" name="return_action" value="{if $smarty.request.return_action eq 'LoadTabSubpanels'}DetailView{else}{$smarty.request.return_action}{/if}" />
<input type="hidden" name="return_id" value="{$smarty.request.return_id}" />
<input type="hidden" name="isDuplicate" value="false" />
<input type="hidden" name="module_tab" /> 
<input type="hidden" name="contact_role" />
{if !empty($smarty.request.return_module)}
<input type="hidden" name="relate_to" value="{if $smarty.request.return_relationship}{$smarty.request.return_relationship}{else}{$smarty.request.return_module}{/if}" />
<input type="hidden" name="relate_id" value="{$smarty.request.return_id}" />
{/if}
<input type="hidden" name="offset" value="{$offset}" />
{if empty($fields.id.value)}
<input type="hidden" name="_notify_random" value="{php}echo create_guid();{/php}" />
{/if}
<input type="hidden" name="isFromEditView" value="{$module}" />
{incomCRM_include include=$includes}
<div id="DEFAULT" class="detailview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<div class="detail view {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="DEFAULT_GROUP" style="display:block">
<table id='detailpanel_1' cellspacing='{$gridline}'>
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_NAME' module='Sales_Lines'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_name_field' >
{counter name="panelFieldCount"}

<span id='{$fields.name.name}' >
{$fields.name.value}
</span>
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_LINE_TYPE' module='Sales_Lines'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_line_type_field' >
{counter name="panelFieldCount"}

{ $fields.line_type.options[$fields.line_type.value]}
</td>
</tr>
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_LOCATION_AREA' module='Sales_Lines'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_location_area_field' >
{counter name="panelFieldCount"}

{ $fields.location_area.options[$fields.location_area.value]}
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_STATUS' module='Sales_Lines'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_status_field' >
{counter name="panelFieldCount"}

{ $fields.status.options[$fields.status.value]}
</td>
</tr>
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_LOCATION_CITY' module='Sales_Lines'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_location_city_field' >
{counter name="panelFieldCount"}

{ $fields.location_city.options[$fields.location_city.value]}
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DISTRIBUTOR_NAME' module='Sales_Lines'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_distributor_name_field' >
{counter name="panelFieldCount"}

{if !empty($fields.distributor_id.value)}<a href="index.php?module=Accounts&action=DetailView&record={$fields.distributor_id.value}">{/if}
{$fields.distributor_name.value}
{if !empty($fields.distributor_id.value)}</a>{/if}
</td>
</tr>
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_LOCATION_DISTRICT' module='Sales_Lines'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_location_district_field' >
{counter name="panelFieldCount"}

{ $fields.location_district.options[$fields.location_district.value]}
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='Sales_Lines'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_assigned_user_name_field' >
{counter name="panelFieldCount"}

{$fields.assigned_user_name.value}
</td>
</tr>
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DESCRIPTION' module='Sales_Lines'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_description_field' >
{counter name="panelFieldCount"}

{$fields.description.value|url2html|nl2br}
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ENTRY_COUNT' module='Sales_Lines'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_entry_count_field' >
{counter name="panelFieldCount"}

<span id='{$fields.entry_count.name}' >
{$fields.entry_count.value}
</span>
</td>
</tr>
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DATE_APPLY' module='Sales_Lines'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_date_apply_from_field' >
{counter name="panelFieldCount"}
<strong>{$fields.date_apply_from.value}</strong>	
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DATE_ENTERED' module='Sales_Lines'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_date_entered_field' >
{counter name="panelFieldCount"}
{$fields.date_entered.value} {$APP.LBL_BY} {$fields.created_by_name.value}	
</td>
</tr>
<tr>
<td width='20%' scope="row">
&nbsp;
</td>
<td width='30%' id='_date_apply_to_field' >
{counter name="panelFieldCount"}
<strong>{$fields.date_apply_to.value}</strong>	
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DATE_MODIFIED' module='Sales_Lines'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_date_modified_field' >
{counter name="panelFieldCount"}
{$fields.date_modified.value} {$APP.LBL_BY} {$fields.modified_by_name.value}	
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("DEFAULT").style.display='none';</script>
{/if}

</form>
{$set_focus_block}
</section>