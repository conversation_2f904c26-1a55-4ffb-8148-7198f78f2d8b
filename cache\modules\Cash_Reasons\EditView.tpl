
<section id="EditViewViewBody" class="view-module-{$module|strtolower}">

<form action="index.php" method="POST" name="{$form_name}" id="{$form_id}" class="_edit-{$instanceName}" {$enctype}>
<input type="hidden" name="module" value="{$module}" />
<input type="hidden" name="action" id="action" />
{if isset($smarty.request.isDuplicate) && $smarty.request.isDuplicate eq "true"}
<input type="hidden" name="record" id="record" value="" />
<input type="hidden" name="duplicateSave" value="true" />
{else}
<input type="hidden" name="record" id="record" value="{$fields.id.value}" />
{/if}
<input type="hidden" name="return_module" value="{$smarty.request.return_module}" />
<input type="hidden" name="return_action" value="{if $smarty.request.return_action eq 'LoadTabSubpanels'}DetailView{else}{$smarty.request.return_action}{/if}" />
<input type="hidden" name="return_id" value="{$smarty.request.return_id}" />
<input type="hidden" name="isDuplicate" value="false" />
<input type="hidden" name="module_tab" /> 
<input type="hidden" name="contact_role" />
{if !empty($smarty.request.return_module)}
<input type="hidden" name="relate_to" value="{if $smarty.request.return_relationship}{$smarty.request.return_relationship}{else}{$smarty.request.return_module}{/if}" />
<input type="hidden" name="relate_id" value="{$smarty.request.return_id}" />
{/if}
<input type="hidden" name="offset" value="{$offset}" />
{if empty($fields.id.value)}
<input type="hidden" name="_notify_random" value="{php}echo create_guid();{/php}" />
{/if}
<input type="hidden" name="isFromEditView" value="{$module}" />
<table width="100%" cellpadding="0" cellspacing="0" border="0">
<tr>
<td class="buttons">
{if $bean->aclAccess("save")}<input title="{$APP.LBL_SAVE_BUTTON_TITLE}" accessKey="{$APP.LBL_SAVE_BUTTON_KEY}" class="button save" onclick="{if $isDuplicate}this.form.return_id.value=''; {/if}this.form.action.value='Save'; return check_form('EditView');" type="submit" name="button" value="{$APP.LBL_SAVE_BUTTON_LABEL}" />{/if} 
{if !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($fields.id.value))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {elseif !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($smarty.request.return_id))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {else}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='index'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {/if}
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=Cash_Reasons", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</td>
</tr>
</table>
{incomCRM_include include=$includes}
<div id="DEFAULT" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="DEFAULT_GROUP" style="display:block">
<table width="100%" border="0" cellspacing="1" cellpadding="0">
<tr>
<td valign="top" id='reason_type_label' width='12%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_REASON_TYPE' module='Cash_Reasons'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_reason_type_field' >
{counter name="panelFieldCount"}

<select name="{$fields.reason_type.name}" id="{$fields.reason_type.name}" title='' tabindex="100"  >
{if isset($fields.reason_type.value) && $fields.reason_type.value != ''}
{html_options options=$fields.reason_type.options selected=$fields.reason_type.value}
{else}
{html_options options=$fields.reason_type.options selected=$fields.reason_type.default}
{/if}
</select>
</td>
<td valign="top" id='cash_group_label' width='18%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CASH_GROUP' module='Cash_Reasons'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_cash_group_field' >
{counter name="panelFieldCount"}

<select name="{$fields.cash_group.name}" id="{$fields.cash_group.name}" title='' tabindex="101"  >
{if isset($fields.cash_group.value) && $fields.cash_group.value != ''}
{html_options options=$fields.cash_group.options selected=$fields.cash_group.value}
{else}
{html_options options=$fields.cash_group.options selected=$fields.cash_group.default}
{/if}
</select>
</td>
</tr>
<tr>
<td valign="top" id='name_label' width='12%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_NAME' module='Cash_Reasons'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_name_field' >
{counter name="panelFieldCount"}

{if strlen($fields.name.value) <= 0}
{assign var="value" value=$fields.name.default_value }
{else}
{assign var="value" value=$fields.name.value }
{/if}
{if isTypeNumber($fields.name.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.name.name}' id='{$fields.name.name}' size='50' maxlength='250' value='{$value}' title='' tabindex='102'  /> 

</td>
<td valign="top" id='cash_project_group_label' width='18%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CASH_PROJECT_GROUP' module='Cash_Reasons'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_cash_project_group_field' >
{counter name="panelFieldCount"}

<select name="{$fields.cash_project_group.name}" id="{$fields.cash_project_group.name}" title='' tabindex="103"  >
{if isset($fields.cash_project_group.value) && $fields.cash_project_group.value != ''}
{html_options options=$fields.cash_project_group.options selected=$fields.cash_project_group.value}
{else}
{html_options options=$fields.cash_project_group.options selected=$fields.cash_project_group.default}
{/if}
</select>
</td>
</tr>
<tr>
<td valign="top" id='accounting_type_label' width='12%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACCOUNTING_TYPE' module='Cash_Reasons'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_accounting_type_field' >
{counter name="panelFieldCount"}

<select name="{$fields.accounting_type.name}" id="{$fields.accounting_type.name}" title='' tabindex="104"  >
{if isset($fields.accounting_type.value) && $fields.accounting_type.value != ''}
{html_options options=$fields.accounting_type.options selected=$fields.accounting_type.value}
{else}
{html_options options=$fields.accounting_type.options selected=$fields.accounting_type.default}
{/if}
</select>
</td>
<td valign="top" id='list_expense_label' width='18%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_LIST_EXPENSE' module='Cash_Reasons'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_list_expense_field' >
{counter name="panelFieldCount"}

{if strval($fields.list_expense.value) == "1" || strval($fields.list_expense.value) == "yes" || strval($fields.list_expense.value) == "on"} 
{assign var="checked" value="CHECKED"}
{else}
{assign var="checked" value=""}
{/if}
<input type="hidden" name="{$fields.list_expense.name}" value="0" /> 
<input type="checkbox" id="{$fields.list_expense.name}" name="{$fields.list_expense.name}" value="1" title='' tabindex="105" {$checked}  />

</td>
</tr>
<tr>
<td valign="top" id='reason_opp_type_label' width='12%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_REASON_OPP_TYPE' module='Cash_Reasons'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_reason_opp_type_field' >
{counter name="panelFieldCount"}

<select name="{$fields.reason_opp_type.name}" id="{$fields.reason_opp_type.name}" title='' tabindex="106"  >
{if isset($fields.reason_opp_type.value) && $fields.reason_opp_type.value != ''}
{html_options options=$fields.reason_opp_type.options selected=$fields.reason_opp_type.value}
{else}
{html_options options=$fields.reason_opp_type.options selected=$fields.reason_opp_type.default}
{/if}
</select>
</td>
<td valign="top" id='ignore_summary_label' width='18%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_IGNORE_SUMMARY' module='Cash_Reasons'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_ignore_summary_field' >
{counter name="panelFieldCount"}

{if strval($fields.ignore_summary.value) == "1" || strval($fields.ignore_summary.value) == "yes" || strval($fields.ignore_summary.value) == "on"} 
{assign var="checked" value="CHECKED"}
{else}
{assign var="checked" value=""}
{/if}
<input type="hidden" name="{$fields.ignore_summary.name}" value="0" /> 
<input type="checkbox" id="{$fields.ignore_summary.name}" name="{$fields.ignore_summary.name}" value="1" title='' tabindex="107" {$checked}  />

</td>
</tr>
<tr>
<td valign="top" id='debt_type_label' width='12%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DEBT_TYPE' module='Cash_Reasons'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_debt_type_field' >
{counter name="panelFieldCount"}

<select name="{$fields.debt_type.name}" id="{$fields.debt_type.name}" title='' tabindex="108"  >
{if isset($fields.debt_type.value) && $fields.debt_type.value != ''}
{html_options options=$fields.debt_type.options selected=$fields.debt_type.value}
{else}
{html_options options=$fields.debt_type.options selected=$fields.debt_type.default}
{/if}
</select>
</td>
<td valign="top" id='ignore_banks_label' width='18%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_IGNORE_BANKS' module='Cash_Reasons'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_ignore_banks_field' >
{counter name="panelFieldCount"}

{if strval($fields.ignore_banks.value) == "1" || strval($fields.ignore_banks.value) == "yes" || strval($fields.ignore_banks.value) == "on"} 
{assign var="checked" value="CHECKED"}
{else}
{assign var="checked" value=""}
{/if}
<input type="hidden" name="{$fields.ignore_banks.name}" value="0" /> 
<input type="checkbox" id="{$fields.ignore_banks.name}" name="{$fields.ignore_banks.name}" value="1" title='' tabindex="109" {$checked}  />

</td>
</tr>
<tr>
<td valign="top" id='description_label' width='12%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DESCRIPTION' module='Cash_Reasons'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" colspan='3' id='_description_field' >
{counter name="panelFieldCount"}

{if empty($fields.description.value)}
{assign var="value" value=$fields.description.default_value }
{else}
{assign var="value" value=$fields.description.value }
{/if}
<textarea id="{$fields.description.name}" name="{$fields.description.name}" rows="2" cols="40" title='' tabindex="110"  >{$value}</textarea>
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("DEFAULT").style.display='none';</script>
{/if}

<div class="buttons" style="margin-top:15px;">
{if $bean->aclAccess("save")}<input title="{$APP.LBL_SAVE_BUTTON_TITLE}" accessKey="{$APP.LBL_SAVE_BUTTON_KEY}" class="button save" onclick="{if $isDuplicate}this.form.return_id.value=''; {/if}this.form.action.value='Save'; return check_form('EditView');" type="submit" name="button" value="{$APP.LBL_SAVE_BUTTON_LABEL}" />{/if} 
{if !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($fields.id.value))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {elseif !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($smarty.request.return_id))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {else}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='index'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {/if}
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=Cash_Reasons", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</div>
</form>
{$set_focus_block}
</section>{literal}
<script type="text/javascript">
addToValidate('EditView', 'id', 'id', false, '{/literal}{incomCRM_translate label='LBL_ID' module='Cash_Reasons'}{literal}' );
addToValidate('EditView', 'date_entered_date', 'date', false, 'Ngày tạo' );
addToValidate('EditView', 'date_modified_date', 'date', false, 'Ngày cập nhật' );
addToValidate('EditView', 'modified_user_id', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_ID' module='Cash_Reasons'}{literal}' );
addToValidate('EditView', 'modified_by_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_NAME' module='Cash_Reasons'}{literal}' );
addToValidate('EditView', 'created_by', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_CREATED_ID' module='Cash_Reasons'}{literal}' );
addToValidate('EditView', 'created_by_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_CREATED' module='Cash_Reasons'}{literal}' );
addToValidate('EditView', 'deleted', 'bool', false, '{/literal}{incomCRM_translate label='LBL_DELETED' module='Cash_Reasons'}{literal}' );
addToValidate('EditView', 'name', 'name', true, '{/literal}{incomCRM_translate label='LBL_NAME' module='Cash_Reasons'}{literal}' );
addToValidate('EditView', 'description', 'text', false, '{/literal}{incomCRM_translate label='LBL_DESCRIPTION' module='Cash_Reasons'}{literal}' );
addToValidate('EditView', 'reason_type', 'enum', true, '{/literal}{incomCRM_translate label='LBL_REASON_TYPE' module='Cash_Reasons'}{literal}' );
addToValidate('EditView', 'accounting_type', 'enum', true, '{/literal}{incomCRM_translate label='LBL_ACCOUNTING_TYPE' module='Cash_Reasons'}{literal}' );
addToValidate('EditView', 'list_expense', 'bool', false, '{/literal}{incomCRM_translate label='LBL_LIST_EXPENSE' module='Cash_Reasons'}{literal}' );
addToValidate('EditView', 'ignore_summary', 'bool', false, '{/literal}{incomCRM_translate label='LBL_IGNORE_SUMMARY' module='Cash_Reasons'}{literal}' );
addToValidate('EditView', 'ignore_banks', 'bool', false, '{/literal}{incomCRM_translate label='LBL_IGNORE_BANKS' module='Cash_Reasons'}{literal}' );
addToValidate('EditView', 'reason_opp_type', 'enum', false, '{/literal}{incomCRM_translate label='LBL_REASON_OPP_TYPE' module='Cash_Reasons'}{literal}' );
addToValidate('EditView', 'cash_group', 'enum', false, '{/literal}{incomCRM_translate label='LBL_CASH_GROUP' module='Cash_Reasons'}{literal}' );
addToValidate('EditView', 'cash_project_group', 'enum', false, '{/literal}{incomCRM_translate label='LBL_CASH_PROJECT_GROUP' module='Cash_Reasons'}{literal}' );
addToValidate('EditView', 'debt_type', 'enum', false, '{/literal}{incomCRM_translate label='LBL_DEBT_TYPE' module='Cash_Reasons'}{literal}' );
addToValidate('EditView', 'is_net_cost', 'bool', false, '{/literal}{incomCRM_translate label='LBL_IS_NET_COST' module='Cash_Reasons'}{literal}' );
</script>
{/literal}
