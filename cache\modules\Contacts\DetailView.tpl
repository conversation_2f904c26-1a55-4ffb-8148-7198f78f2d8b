
<section id="DetailViewBody" class="view-module-{$module|strtolower}">

<form action="index.php" method="post" name="{$form_name|default:'DetailView'}" id="form" class="_view-{$instanceName}">
<table cellpadding="1" cellspacing="0" border="0" width="100%">
<tr>
<td class="buttons" align="left" valign="top" nowrap>
<input type="hidden" name="module" value="{$module}" />
<input type="hidden" name="action" value="EditView" />
<input type="hidden" name="record" value="{$fields.id.value}" />
<input type="hidden" name="return_action" />
<input type="hidden" name="return_module" />
<input type="hidden" name="return_id" />
<input type="hidden" name="module_tab" /> 
<input type="hidden" name="isDuplicate" value="false" />
<input type="hidden" name="offset" value="{$offset}" />
{if $bean->aclAccess("edit")}<input title="{$APP.LBL_EDIT_BUTTON_TITLE}" accessKey="{$APP.LBL_EDIT_BUTTON_KEY}" class="button bedit" onclick="this.form.return_module.value='Contacts'; this.form.return_action.value='DetailView'; this.form.return_id.value='{$id}'; this.form.action.value='EditView';" type="submit" name="Edit" id="edit_button" value="{$APP.LBL_EDIT_BUTTON_LABEL}" />{/if} 
{if $bean->aclAccess("edit")}<input title="{$APP.LBL_DUPLICATE_BUTTON_TITLE}" accessKey="{$APP.LBL_DUPLICATE_BUTTON_KEY}" class="button duplicate" onclick="this.form.return_module.value='Contacts'; this.form.return_action.value='DetailView'; this.form.isDuplicate.value=true; this.form.action.value='EditView'; this.form.return_id.value='{$id}';" type="submit" name="Duplicate" value="{$APP.LBL_DUPLICATE_BUTTON_LABEL}" id="duplicate_button" />{/if} 
{if $bean->aclAccess("delete")}<input title="{$APP.LBL_DELETE_BUTTON_TITLE}" accessKey="{$APP.LBL_DELETE_BUTTON_KEY}" class="button delete" onclick="this.form.return_module.value='Contacts'; this.form.return_action.value='ListView'; this.form.action.value='Delete'; return confirm('{$APP.NTC_DELETE_CONFIRMATION}');" type="submit" name="Delete" value="{$APP.LBL_DELETE_BUTTON_LABEL}" />{/if} 
{if $bean->aclAccess("edit") and $bean->aclAccess("delete")}<input title="{$APP.LBL_DUP_MERGE}" accessKey="M" class="button find_duplicate" onclick="this.form.return_module.value='Contacts'; this.form.return_action.value='DetailView'; this.form.return_id.value='{$id}'; this.form.action.value='Step1'; this.form.module.value='MergeRecords';" type="submit" name="Merge" value="{$APP.LBL_DUP_MERGE}" />{/if} 
</td>
<td class="buttons" align="left" valign="top" nowrap>
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=Contacts", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</td>
<td align="right" width="90%">{$ADMIN_EDIT}</td>
</tr>
</table>
</form>
{incomCRM_include include=$includes}
<div id="LBL_CONTACT_INFORMATION" class="detailview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_CONTACT_INFORMATION');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_CONTACT_INFORMATION_IMG" border="0" />
{incomCRM_translate label='LBL_CONTACT_INFORMATION' module='Contacts'}
</a>
</h4>
<div class="detail view {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_CONTACT_INFORMATION_GROUP" style="display:block">
<table id='detailpanel_1' cellspacing='{$gridline}'>
{$PAGINATION}
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_NAME' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_full_name_field' >
{counter name="panelFieldCount"}

<form name="vcard" action="index.php" style="display: inline;">
<span id='{$fields.full_name.name}'>{$fields.full_name.value}</span>
&nbsp;&nbsp;
<input type="hidden" name="action" value="vCard" />
<input type="hidden" name="record" value="{$fields.id.value}" />
<input type="hidden" name="module" value="{$module}" />
<input type="hidden" name="to_pdf" value="true" />
<input type="hidden" name="vCardButton" value="{$APP.LBL_VCARD}">
</form>
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_OFFICE_PHONE' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_phone_work_field' >
{counter name="panelFieldCount"}

{if !empty($fields.phone_work.value)}
{assign var="phone_value" value=$fields.phone_work.value }
{incomCRM_phone value=$phone_value }
{/if}
</td>
</tr>
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ACCOUNT_NAME' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_account_name_field' >
{counter name="panelFieldCount"}

{if !empty($fields.account_id.value)}<a href="index.php?module=Accounts&action=DetailView&record={$fields.account_id.value}">{/if}
{$fields.account_name.value}
{if !empty($fields.account_id.value)}</a>{/if}
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_OTHER_PHONE' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_phone_other_field' >
{counter name="panelFieldCount"}

{if !empty($fields.phone_other.value)}
{assign var="phone_value" value=$fields.phone_other.value }
{incomCRM_phone value=$phone_value }
{/if}
</td>
</tr>
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CONTACT_POSITION' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_contact_position_field' >
{counter name="panelFieldCount"}

{ $fields.contact_position.options[$fields.contact_position.value]}
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_PRIMARY_ADDRESS_STREET' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_primary_address_street_field' >
{counter name="panelFieldCount"}

{$fields.primary_address_street.value|url2html|nl2br}
</td>
</tr>
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_GENDER' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_gender_field' >
{counter name="panelFieldCount"}

{ $fields.gender.options[$fields.gender.value]}
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_INDEPENDENCE_DAY' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_independence_day_field' >
{counter name="panelFieldCount"}

<span id='{$fields.independence_day.name}' >
{$fields.independence_day.value}
</span>
</td>
</tr>
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_POSITION' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_position_field' >
{counter name="panelFieldCount"}

<span id='{$fields.position.name}' >
{$fields.position.value}
</span>
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ASSISTANT' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_assistant_field' >
{counter name="panelFieldCount"}

<span id='{$fields.assistant.name}' >
{$fields.assistant.value}
</span>
</td>
</tr>
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DEPARTMENT' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_department_field' >
{counter name="panelFieldCount"}

<span id='{$fields.department.name}' >
{$fields.department.value}
</span>
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ASSISTANT_PHONE' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_assistant_phone_field' >
{counter name="panelFieldCount"}

{if !empty($fields.assistant_phone.value)}
{assign var="phone_value" value=$fields.assistant_phone.value }
{incomCRM_phone value=$phone_value }
{/if}
</td>
</tr>
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_BIRTHDATE' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_birthdate_field' >
{counter name="panelFieldCount"}

<span id='{$fields.birthdate.name}' >
{$fields.birthdate.value}
</span>
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_REPORTS_TO' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_report_to_name_field' >
{counter name="panelFieldCount"}

{if !empty($fields.reports_to_id.value)}<a href="index.php?module=Contacts&action=DetailView&record={$fields.reports_to_id.value}">{/if}
{$fields.report_to_name.value}
{if !empty($fields.reports_to_id.value)}</a>{/if}
</td>
</tr>
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_MOBILE_PHONE' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_phone_mobile_field' >
{counter name="panelFieldCount"}

{if !empty($fields.phone_mobile.value)}
{assign var="phone_value" value=$fields.phone_mobile.value }
{incomCRM_phone value=$phone_value }
{/if}
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_TRANSACTION_LEVEL' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_transaction_level_field' >
{counter name="panelFieldCount"}

{ $fields.transaction_level.options[$fields.transaction_level.value]}
</td>
</tr>
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_HOME_PHONE' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_phone_home_field' >
{counter name="panelFieldCount"}

{if !empty($fields.phone_home.value)}
{assign var="phone_value" value=$fields.phone_home.value }
{incomCRM_phone value=$phone_value }
{/if}
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_TRANSACTION_COMMENT' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_transaction_comment_field' >
{counter name="panelFieldCount"}

{$fields.transaction_comment.value|url2html|nl2br}
</td>
</tr>
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ALT_ADDRESS_STREET' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_alt_address_street_field' >
{counter name="panelFieldCount"}

{$fields.alt_address_street.value|url2html|nl2br}
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_FOREIGNER_REP' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_foreigner_rep_field' >
{counter name="panelFieldCount"}

{if strval($fields.foreigner_rep.value) == "1" || strval($fields.foreigner_rep.value) == "yes" || strval($fields.foreigner_rep.value) == "on"} 
{assign var="checked" value="CHECKED"}
{else}
{assign var="checked" value=""}
{/if}
<input type="checkbox" class="checkbox" name="{$fields.foreigner_rep.name}" disabled="true" {$checked} />
</td>
</tr>
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_assigned_user_name_field' >
{counter name="panelFieldCount"}

{$fields.assigned_user_name.value}
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DATE_ENTERED' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_date_entered_field' >
{counter name="panelFieldCount"}
{$fields.date_entered.value} {$APP.LBL_BY} {$fields.created_by_name.value}	
</td>
</tr>
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_EMAIL_ADDRESS' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_email1_field' >
{counter name="panelFieldCount"}

<span id='{$fields.email1.name}' >
{$fields.email1.value}
</span>
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DATE_MODIFIED' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_date_modified_field' >
{counter name="panelFieldCount"}
{$fields.date_modified.value} {$APP.LBL_BY} {$fields.modified_by_name.value}	
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_CONTACT_INFORMATION").style.display='none';</script>
{/if}
<div id="LBL_BANK_ACCOUNT_INFORMATION" class="detailview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_BANK_ACCOUNT_INFORMATION');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_BANK_ACCOUNT_INFORMATION_IMG" border="0" />
{incomCRM_translate label='LBL_BANK_ACCOUNT_INFORMATION' module='Contacts'}
</a>
</h4>
<div class="detail view {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_BANK_ACCOUNT_INFORMATION_GROUP" style="display:none">
<table id='detailpanel_2' cellspacing='{$gridline}'>
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ACCOUNT_HOLDER' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_account_holder_field' >
{counter name="panelFieldCount"}

<span id='{$fields.account_holder.name}' >
{$fields.account_holder.value}
</span>
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ID_NUMBER' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_id_number_field' >
{counter name="panelFieldCount"}

<span id='{$fields.id_number.name}' >
{$fields.id_number.value}
</span>
</td>
</tr>
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ACCOUNT_NUMBER' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_account_number_field' >
{counter name="panelFieldCount"}

<span id='{$fields.account_number.name}' >
{$fields.account_number.value}
</span>
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_PASSPORT_NUMBER' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_passport_number_field' >
{counter name="panelFieldCount"}

<span id='{$fields.passport_number.name}' >
{$fields.passport_number.value}
</span>
</td>
</tr>
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ACCOUNT_BANK_NAME' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_account_bank_name_field' >
{counter name="panelFieldCount"}

<span id='{$fields.account_bank_name.name}' >
{$fields.account_bank_name.value}
</span>
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_COMMISSION_RECIPIENT' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_commission_recipient_field' >
{counter name="panelFieldCount"}

{if strval($fields.commission_recipient.value) == "1" || strval($fields.commission_recipient.value) == "yes" || strval($fields.commission_recipient.value) == "on"} 
{assign var="checked" value="CHECKED"}
{else}
{assign var="checked" value=""}
{/if}
<input type="checkbox" class="checkbox" name="{$fields.commission_recipient.name}" disabled="true" {$checked} />
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_BANK_ACCOUNT_INFORMATION").style.display='none';</script>
{/if}
<div id="LBL_MORE_INFO_CARE" class="detailview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_MORE_INFO_CARE');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_MORE_INFO_CARE_IMG" border="0" />
{incomCRM_translate label='LBL_MORE_INFO_CARE' module='Contacts'}
</a>
</h4>
<div class="detail view {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_MORE_INFO_CARE_GROUP" style="display:none">
<table id='detailpanel_3' cellspacing='{$gridline}'>
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_NEED_CARE' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_need_care_field' >
{counter name="panelFieldCount"}

{if strval($fields.need_care.value) == "1" || strval($fields.need_care.value) == "yes" || strval($fields.need_care.value) == "on"} 
{assign var="checked" value="CHECKED"}
{else}
{assign var="checked" value=""}
{/if}
<input type="checkbox" class="checkbox" name="{$fields.need_care.name}" disabled="true" {$checked} />
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CARE_PRIORITY' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_care_priority_field' >
{counter name="panelFieldCount"}

{ $fields.care_priority.options[$fields.care_priority.value]}
</td>
</tr>
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CARE_SUGGEST' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td colspan='3' id='_care_suggest_field' >
{counter name="panelFieldCount"}

{$fields.care_suggest.value|url2html|nl2br}
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_MORE_INFO_CARE").style.display='none';</script>
{/if}
<div id="LBL_OTHER_INFORMATION" class="detailview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_OTHER_INFORMATION');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_OTHER_INFORMATION_IMG" border="0" />
{incomCRM_translate label='LBL_OTHER_INFORMATION' module='Contacts'}
</a>
</h4>
<div class="detail view {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_OTHER_INFORMATION_GROUP" style="display:none">
<table id='detailpanel_4' cellspacing='{$gridline}'>
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_HOBBIES' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_hobbies_field' >
{counter name="panelFieldCount"}

{$fields.hobbies.value|url2html|nl2br}
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_INTIMATE_LEVEL' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_intimate_level_field' >
{counter name="panelFieldCount"}

{ $fields.intimate_level.options[$fields.intimate_level.value]}
</td>
</tr>
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_TALENT' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_talent_field' >
{counter name="panelFieldCount"}

{$fields.talent.value|url2html|nl2br}
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_INTIMATE_NOTE' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_intimate_note_field' >
{counter name="panelFieldCount"}

{$fields.intimate_note.value|url2html|nl2br}
</td>
</tr>
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_SPORT' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_sport_field' >
{counter name="panelFieldCount"}

{$fields.sport.value|url2html|nl2br}
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_WORK_HISTORY' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_work_history_field' >
{counter name="panelFieldCount"}

{$fields.work_history.value|url2html|nl2br}
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_OTHER_INFORMATION").style.display='none';</script>
{/if}
<div id="LBL_DESCRIPTION_INFORMATION" class="detailview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_DESCRIPTION_INFORMATION');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_DESCRIPTION_INFORMATION_IMG" border="0" />
{incomCRM_translate label='LBL_DESCRIPTION_INFORMATION' module='Contacts'}
</a>
</h4>
<div class="detail view {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_DESCRIPTION_INFORMATION_GROUP" style="display:none">
<table id='detailpanel_5' cellspacing='{$gridline}'>
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DESCRIPTION' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td colspan='3' id='_description_field' >
{counter name="panelFieldCount"}

{$fields.description.value|url2html|nl2br}
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_DESCRIPTION_INFORMATION").style.display='none';</script>
{/if}

</section>