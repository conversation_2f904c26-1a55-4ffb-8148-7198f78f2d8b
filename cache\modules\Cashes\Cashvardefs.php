<?php
// created: 2025-02-26 15:24:03
$G<PERSON><PERSON><PERSON><PERSON>["dictionary"]["Cash"] = array (
  'table' => 'cashes',
  'audited' => true,
  'unified_search' => true,
  'duplicate_merge' => false,
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'name',
      'dbType' => 'varchar',
      'len' => '50',
      'required' => true,
      'audited' => true,
      'acl' => true,
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'cashes_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'cashes_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'branch_id' => 
    array (
      'name' => 'branch_id',
      'vname' => 'LBL_BRANCH_ID',
      'type' => 'enum',
      'options' => 'users_branch_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'BranchID of assigned user',
    ),
    'department_id' => 
    array (
      'name' => 'department_id',
      'vname' => 'LBL_DEPARTMENT_ID',
      'type' => 'enum',
      'options' => 'users_department_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'DepartmentID of assigned user',
    ),
    'assigned_user_id' => 
    array (
      'name' => 'assigned_user_id',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'vname' => 'LBL_ASSIGNED_TO_ID',
      'group' => 'assigned_user_name',
      'type' => 'relate',
      'table' => 'users',
      'module' => 'Users',
      'isnull' => 'false',
      'dbType' => 'id',
      'audited' => true,
      'reportable' => true,
      'duplicate_merge' => 'disabled',
      'comment' => 'User ID assigned to record',
    ),
    'assigned_user_name' => 
    array (
      'name' => 'assigned_user_name',
      'link' => 'assigned_user_link',
      'vname' => 'LBL_ASSIGNED_TO_NAME',
      'rname' => 'user_name',
      'type' => 'relate',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'assigned_user_id',
      'module' => 'Users',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'assigned_user_link' => 
    array (
      'name' => 'assigned_user_link',
      'type' => 'link',
      'relationship' => 'cashes_assigned_user',
      'vname' => 'LBL_ASSIGNED_TO_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
      'duplicate_merge' => 'enabled',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'table' => 'users',
    ),
    'cash_type' => 
    array (
      'name' => 'cash_type',
      'vname' => 'LBL_CASH_TYPE',
      'type' => 'enum',
      'options' => 'cashes_type_dom',
      'len' => '2',
      'audited' => true,
      'massupdate' => false,
    ),
    'status' => 
    array (
      'name' => 'status',
      'vname' => 'LBL_STATUS',
      'type' => 'enum',
      'options' => 'cashes_status_dom',
      'len' => '2',
      'audited' => true,
      'massupdate' => false,
      'display_default' => '1',
    ),
    'record_status' => 
    array (
      'name' => 'record_status',
      'vname' => 'LBL_RECORD_STATUS',
      'type' => 'enum',
      'options' => 'cashes_record_status_dom',
      'len' => '2',
      'audited' => true,
      'display_default' => '1',
    ),
    'accounting_status' => 
    array (
      'name' => 'accounting_status',
      'vname' => 'LBL_ACCOUNTING_STATUS',
      'type' => 'enum',
      'options' => 'cashes_accounting_status_dom',
      'len' => '2',
      'audited' => true,
      'massupdate' => false,
    ),
    'branches' => 
    array (
      'name' => 'branches',
      'vname' => 'LBL_BRANCHES',
      'type' => 'enum',
      'options' => 'users_branch_dom',
      'len' => '50',
      'audited' => true,
    ),
    'date_perform' => 
    array (
      'name' => 'date_perform',
      'vname' => 'LBL_DATE_PERFORM',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
      'display_default' => 'now',
    ),
    'date_accounting' => 
    array (
      'name' => 'date_accounting',
      'vname' => 'LBL_DATE_ACCOUNTING',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'amount' => 
    array (
      'name' => 'amount',
      'vname' => 'LBL_AMOUNT',
      'type' => 'float',
      'dbType' => 'double',
      'audited' => true,
    ),
    'notes' => 
    array (
      'name' => 'notes',
      'type' => 'link',
      'relationship' => 'cashes_notes',
      'module' => 'Notes',
      'bean_name' => 'Note',
      'source' => 'non-db',
      'vname' => 'LBL_NOTES',
    ),
    'document_no' => 
    array (
      'name' => 'document_no',
      'vname' => 'LBL_DOCUMENT_NO',
      'type' => 'varchar',
      'len' => '250',
      'audited' => true,
    ),
    'document_attach' => 
    array (
      'name' => 'document_attach',
      'vname' => 'LBL_DOCUMENT_ATTACH',
      'type' => 'varchar',
      'len' => '250',
      'audited' => true,
    ),
    'cash_person' => 
    array (
      'name' => 'cash_person',
      'vname' => 'LBL_CASH_PERSON',
      'type' => 'varchar',
      'len' => '200',
      'audited' => true,
    ),
    'address' => 
    array (
      'name' => 'address',
      'vname' => 'LBL_ADDRESS',
      'type' => 'varchar',
      'len' => '250',
      'audited' => true,
    ),
    'reason_detail' => 
    array (
      'name' => 'reason_detail',
      'vname' => 'LBL_REASON_DETAIL',
      'type' => 'varchar',
      'len' => '250',
      'audited' => true,
    ),
    'payment_type' => 
    array (
      'name' => 'payment_type',
      'vname' => 'LBL_PAYMENT_TYPE',
      'type' => 'enum',
      'options' => 'cashes_payments_dom',
      'len' => '10',
      'audited' => true,
      'display_default' => 'TM',
    ),
    'transfer_payment' => 
    array (
      'name' => 'transfer_payment',
      'vname' => 'LBL_TRANSFER_PAYMENT',
      'type' => 'enum',
      'options' => 'cashes_payments_dom',
      'len' => '10',
      'audited' => true,
      'massupdate' => false,
    ),
    'transfer_bank_id' => 
    array (
      'name' => 'transfer_bank_id',
      'vname' => 'LBL_TRANSFER_BANK_ID',
      'type' => 'id',
      'function' => 
      array (
        'name' => 'getBankAccountDropDown',
        'returns' => 'html',
      ),
      'required' => false,
      'audited' => true,
      'massupdate' => false,
      'reportable' => false,
    ),
    'currency_id' => 
    array (
      'name' => 'currency_id',
      'type' => 'id',
      'group' => 'currency_id',
      'vname' => 'LBL_CURRENCY_ID',
      'function' => 
      array (
        'name' => 'getCurrencyDropDown',
        'returns' => 'html',
      ),
      'reportable' => false,
      'comment' => 'Currency used for display purposes',
    ),
    'rate_usd' => 
    array (
      'name' => 'rate_usd',
      'vname' => 'LBL_RATE_USD',
      'type' => 'float',
      'dbType' => 'double',
      'audited' => true,
    ),
    'exchange_rate_diff' => 
    array (
      'name' => 'exchange_rate_diff',
      'vname' => 'LBL_EXCHANGE_RATE_DIFF',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => false,
      'default' => 0,
    ),
    'amt_diff' => 
    array (
      'name' => 'amt_diff',
      'vname' => 'LBL_AMT_DIFF',
      'type' => 'double',
      'audited' => true,
    ),
    'cash_reason_id' => 
    array (
      'name' => 'cash_reason_id',
      'type' => 'id',
      'group' => 'cash_reason_id',
      'vname' => 'LBL_CASH_REASON_ID',
      'function' => 
      array (
        'name' => 'getCashReasonDropDown',
        'returns' => 'html',
      ),
      'audited' => true,
      'reportable' => false,
      'massupdate' => false,
    ),
    'cash_reason_name' => 
    array (
      'name' => 'cash_reason_name',
      'rname' => 'name',
      'id_name' => 'cash_reason_id',
      'vname' => 'LBL_CASH_REASON_NAME',
      'table' => 'cash_reasons',
      'type' => 'relate',
      'link' => 'cash_reasons',
      'join_name' => 'cash_reasons',
      'isnull' => 'true',
      'module' => 'Cash_Reasons',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'cash_reasons' => 
    array (
      'name' => 'cash_reasons',
      'type' => 'link',
      'relationship' => 'cash_reasons_related',
      'vname' => 'LBL_CASH_REASONS',
      'source' => 'non-db',
    ),
    'account_id' => 
    array (
      'name' => 'account_id',
      'vname' => 'LBL_ACCOUNT_ID',
      'type' => 'id',
      'required' => false,
      'reportable' => false,
      'audited' => true,
      'massupdate' => false,
    ),
    'account_name' => 
    array (
      'name' => 'account_name',
      'rname' => 'name',
      'id_name' => 'account_id',
      'vname' => 'LBL_ACCOUNT_NAME',
      'table' => 'accounts',
      'type' => 'relate',
      'link' => 'accounts',
      'join_name' => 'accounts',
      'isnull' => 'true',
      'module' => 'Accounts',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'reference_code' => 
    array (
      'name' => 'reference_code',
      'rname' => 'reference_code',
      'id_name' => 'account_id',
      'vname' => 'LBL_REFERENCE_CODE',
      'table' => 'accounts',
      'type' => 'relate',
      'link' => 'accounts',
      'join_name' => 'accounts',
      'isnull' => 'true',
      'module' => 'Accounts',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'accounts' => 
    array (
      'name' => 'accounts',
      'type' => 'link',
      'relationship' => 'accounts_cashes',
      'vname' => 'LBL_ACCOUNTS',
      'source' => 'non-db',
    ),
    'project_id' => 
    array (
      'name' => 'project_id',
      'type' => 'id',
      'vname' => 'LBL_PROJECT_ID',
      'audited' => true,
      'reportable' => false,
      'massupdate' => false,
    ),
    'project_name' => 
    array (
      'name' => 'project_name',
      'rname' => 'name',
      'id_name' => 'project_id',
      'vname' => 'LBL_PROJECT_NAME',
      'table' => 'project',
      'type' => 'relate',
      'link' => 'project',
      'join_name' => 'project',
      'isnull' => 'true',
      'module' => 'Project',
      'source' => 'non-db',
      'massupdate' => true,
    ),
    'project' => 
    array (
      'name' => 'project',
      'type' => 'link',
      'relationship' => 'projects_cashes',
      'vname' => 'LBL_PROJECTS',
      'source' => 'non-db',
    ),
    'opportunity_id' => 
    array (
      'name' => 'opportunity_id',
      'vname' => 'LBL_OPPORTUNITY_ID',
      'type' => 'id',
      'audited' => false,
      'massupdate' => false,
    ),
    'opportunities' => 
    array (
      'name' => 'opportunities',
      'type' => 'link',
      'relationship' => 'cashes_collect_details',
      'vname' => 'LBL_OPPORTUNITIES',
      'source' => 'non-db',
    ),
    'reason_collect' => 
    array (
      'name' => 'reason_collect',
      'type' => 'enum',
      'function' => 
      array (
        'name' => 'getCashReasonDropDown',
        'returns' => 'html',
      ),
      'vname' => 'LBL_REASON_TYPE_1',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'reason_expense' => 
    array (
      'name' => 'reason_expense',
      'type' => 'enum',
      'function' => 
      array (
        'name' => 'getCashReasonDropDown',
        'returns' => 'html',
      ),
      'vname' => 'LBL_REASON_TYPE_2',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'related_id' => 
    array (
      'name' => 'related_id',
      'vname' => 'LBL_RELATED_ID',
      'type' => 'id',
      'audited' => false,
      'massupdate' => false,
    ),
    'amt_accounting' => 
    array (
      'name' => 'amt_accounting',
      'vname' => 'LBL_AMT_ACCOUNTING',
      'type' => 'double',
      'source' => 'non-db',
    ),
    'SecurityGroups' => 
    array (
      'name' => 'SecurityGroups',
      'type' => 'link',
      'relationship' => 'securitygroups_cashes',
      'module' => 'SecurityGroups',
      'bean_name' => 'SecurityGroup',
      'source' => 'non-db',
      'vname' => 'LBL_SECURITYGROUPS',
    ),
    'bank_account_id' => 
    array (
      'name' => 'bank_account_id',
      'vname' => 'LBL_BANK_ACCOUNT_ID',
      'type' => 'id',
      'function' => 
      array (
        'name' => 'getBankAccountDropDown',
        'returns' => 'html',
      ),
      'required' => false,
      'audited' => true,
      'massupdate' => false,
      'reportable' => false,
    ),
    'bank_account_name' => 
    array (
      'name' => 'bank_account_name',
      'rname' => 'bank_cash',
      'db_concat_fields' => 
      array (
        0 => 'bank_number',
        1 => 'bank_name',
      ),
      'id_name' => 'bank_account_id',
      'vname' => 'LBL_BANK_ACCOUNT_NAME',
      'table' => 'bank_accounts',
      'type' => 'relate',
      'link' => 'bank_accounts',
      'join_name' => 'bank_accounts',
      'isnull' => 'true',
      'module' => 'Bank_Accounts',
      'source' => 'non-db',
      'massupdate' => true,
    ),
    'bank_accounts' => 
    array (
      'name' => 'bank_accounts',
      'type' => 'link',
      'relationship' => 'bank_accounts_cashes',
      'vname' => 'LBL_BANK_ACCOUNTS',
      'source' => 'non-db',
    ),
    'list_expenses' => 
    array (
      'name' => 'list_expenses',
      'type' => 'link',
      'relationship' => 'cashes_list_expenses',
      'vname' => 'LBL_LIST_EXPENSES',
      'source' => 'non-db',
    ),
    'is_locked' => 
    array (
      'name' => 'is_locked',
      'vname' => 'LBL_IS_LOCKED',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => false,
      'default' => 0,
    ),
    'accounting_code_id' => 
    array (
      'name' => 'accounting_code_id',
      'vname' => 'LBL_ACCOUNTING_CODE_ID',
      'type' => 'id',
      'function' => 
      array (
        'name' => 'getAccountingCodeDropDown',
        'returns' => 'html',
      ),
      'required' => false,
      'audited' => true,
      'massupdate' => false,
      'reportable' => false,
    ),
    'accounting_code_name' => 
    array (
      'name' => 'accounting_code_name',
      'rname' => 'accounting_code',
      'db_concat_fields' => 
      array (
        0 => 'code',
        1 => 'name',
      ),
      'id_name' => 'accounting_code_id',
      'vname' => 'LBL_ACCOUNTING_CODE_NAME',
      'table' => 'accounting_codes',
      'type' => 'relate',
      'link' => 'accounting_codes',
      'join_name' => 'accounting_codes',
      'isnull' => 'true',
      'module' => 'Accounting_Codes',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'accounting_codes' => 
    array (
      'name' => 'accounting_codes',
      'type' => 'link',
      'relationship' => 'accounting_codes_cashes',
      'vname' => 'LBL_ACCOUNTING_CODES',
      'source' => 'non-db',
    ),
    'sale_user_id' => 
    array (
      'name' => 'sale_user_id',
      'vname' => 'LBL_SALE_USER_ID',
      'type' => 'id',
      'audited' => true,
      'massupdate' => false,
    ),
    'sale_user_name' => 
    array (
      'name' => 'sale_user_name',
      'rname' => 'user_name',
      'id_name' => 'sale_user_id',
      'vname' => 'LBL_SALE_USER_NAME',
      'source' => 'non-db',
      'type' => 'relate',
      'link' => 'sale_users',
      'join_name' => 'sales',
      'table' => 'users',
      'module' => 'Users',
      'massupdate' => false,
    ),
    'sale_users' => 
    array (
      'name' => 'sale_users',
      'vname' => 'LBL_SALE_USERS',
      'type' => 'link',
      'source' => 'non-db',
      'relationship' => 'sale_users_cashes',
    ),
  ),
  'indices' => 
  array (
    'id' => 
    array (
      'name' => 'cashespk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    'idx_cashes_branch_id' => 
    array (
      'name' => 'idx_cashes_branch_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
      ),
    ),
    'idx_cashes_department_id' => 
    array (
      'name' => 'idx_cashes_department_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'department_id',
      ),
    ),
    'idx_cashes_branch_dept' => 
    array (
      'name' => 'idx_cashes_branch_dept',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
        1 => 'department_id',
      ),
    ),
    'idx_cashes_assigned' => 
    array (
      'name' => 'idx_cashes_assigned',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'assigned_user_id',
      ),
    ),
    0 => 
    array (
      'name' => 'idx_cash_id_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'id',
        1 => 'deleted',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_cash_assigned_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'assigned_user_id',
      ),
    ),
    2 => 
    array (
      'name' => 'idx_cash_del_name',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'name',
      ),
    ),
    3 => 
    array (
      'name' => 'idx_cash_del_type',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'cash_type',
      ),
    ),
    4 => 
    array (
      'name' => 'idx_cash_del_status',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'status',
      ),
    ),
    5 => 
    array (
      'name' => 'idx_cash_del_record',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'record_status',
      ),
    ),
    6 => 
    array (
      'name' => 'idx_cash_del_accounting',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'accounting_status',
      ),
    ),
    7 => 
    array (
      'name' => 'idx_cash_del_branches',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'branches',
      ),
    ),
    8 => 
    array (
      'name' => 'idx_cash_del_payments',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'payment_type',
      ),
    ),
    9 => 
    array (
      'name' => 'idx_cash_del_date',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'date_perform',
      ),
    ),
    10 => 
    array (
      'name' => 'idx_cash_del_reason',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'cash_reason_id',
      ),
    ),
    11 => 
    array (
      'name' => 'idx_cash_del_acc',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'account_id',
      ),
    ),
    12 => 
    array (
      'name' => 'idx_cash_del_project',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'project_id',
      ),
    ),
    13 => 
    array (
      'name' => 'idx_cash_del_opp',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'opportunity_id',
      ),
    ),
    14 => 
    array (
      'name' => 'idx_cash_del_related',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'related_id',
      ),
    ),
    15 => 
    array (
      'name' => 'idx_cash_del_transfer_pay',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'transfer_payment',
      ),
    ),
    16 => 
    array (
      'name' => 'idx_cash_del_transfer_bank',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'transfer_bank_id',
      ),
    ),
    17 => 
    array (
      'name' => 'idx_cash_del_exc_rate_diff',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'exchange_rate_diff',
      ),
    ),
    'idx_cash_del_bank' => 
    array (
      'name' => 'idx_cash_del_bank',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'bank_account_id',
      ),
    ),
    'idx_cash_locked' => 
    array (
      'name' => 'idx_cash_locked',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'is_locked',
      ),
    ),
    'idx_cash_del_acc_code' => 
    array (
      'name' => 'idx_cash_del_acc_code',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'accounting_code_id',
      ),
    ),
  ),
  'relationships' => 
  array (
    'cashes_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Cashes',
      'rhs_table' => 'cashes',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'cashes_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Cashes',
      'rhs_table' => 'cashes',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
    'cashes_assigned_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Cashes',
      'rhs_table' => 'cashes',
      'rhs_key' => 'assigned_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'accounts_cashes' => 
    array (
      'lhs_module' => 'Accounts',
      'lhs_table' => 'accounts',
      'lhs_key' => 'id',
      'rhs_module' => 'Cashes',
      'rhs_table' => 'cashes',
      'rhs_key' => 'account_id',
      'relationship_type' => 'one-to-many',
    ),
    'cash_reasons_related' => 
    array (
      'lhs_module' => 'Cash_Reasons',
      'lhs_table' => 'cash_reasons',
      'lhs_key' => 'id',
      'rhs_module' => 'Cashes',
      'rhs_table' => 'cashes',
      'rhs_key' => 'cash_reason_id',
      'relationship_type' => 'one-to-many',
    ),
    'projects_cashes' => 
    array (
      'lhs_module' => 'Project',
      'lhs_table' => 'project',
      'lhs_key' => 'id',
      'rhs_module' => 'Cashes',
      'rhs_table' => 'cashes',
      'rhs_key' => 'project_id',
      'relationship_type' => 'one-to-many',
    ),
    'cashes_notes' => 
    array (
      'lhs_module' => 'Cashes',
      'lhs_table' => 'cashes',
      'lhs_key' => 'id',
      'rhs_module' => 'Notes',
      'rhs_table' => 'notes',
      'rhs_key' => 'parent_id',
      'relationship_type' => 'one-to-many',
      'relationship_role_column' => 'parent_type',
      'relationship_role_column_value' => 'Cashes',
    ),
    'sale_users_cashes' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Cashes',
      'rhs_table' => 'cashes',
      'rhs_key' => 'sale_user_id',
      'relationship_type' => 'one-to-many',
    ),
  ),
  'optimistic_lock' => true,
  'templates' => 
  array (
    'assignable' => 'assignable',
    'groups' => 'groups',
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
