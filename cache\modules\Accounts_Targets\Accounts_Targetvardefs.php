<?php
// created: 2025-03-03 22:47:49
$G<PERSON><PERSON><PERSON><PERSON>["dictionary"]["Accounts_Target"] = array (
  'table' => 'accounts_targets',
  'audited' => true,
  'unified_search' => false,
  'duplicate_merge' => false,
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'varchar',
      'len' => '200',
      'source' => 'non-db',
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'accounts_targets_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'accounts_targets_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'branch_id' => 
    array (
      'name' => 'branch_id',
      'vname' => 'LBL_BRANCH_ID',
      'type' => 'enum',
      'options' => 'users_branch_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'BranchID of assigned user',
    ),
    'department_id' => 
    array (
      'name' => 'department_id',
      'vname' => 'LBL_DEPARTMENT_ID',
      'type' => 'enum',
      'options' => 'users_department_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'DepartmentID of assigned user',
    ),
    'assigned_user_id' => 
    array (
      'name' => 'assigned_user_id',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'vname' => 'LBL_ASSIGNED_TO_ID',
      'group' => 'assigned_user_name',
      'type' => 'relate',
      'table' => 'users',
      'module' => 'Users',
      'isnull' => 'false',
      'dbType' => 'id',
      'audited' => true,
      'reportable' => true,
      'duplicate_merge' => 'disabled',
      'comment' => 'User ID assigned to record',
    ),
    'assigned_user_name' => 
    array (
      'name' => 'assigned_user_name',
      'link' => 'assigned_user_link',
      'vname' => 'LBL_ASSIGNED_TO_NAME',
      'rname' => 'user_name',
      'type' => 'relate',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'assigned_user_id',
      'module' => 'Users',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'assigned_user_link' => 
    array (
      'name' => 'assigned_user_link',
      'type' => 'link',
      'relationship' => 'accounts_targets_assigned_user',
      'vname' => 'LBL_ASSIGNED_TO_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
      'duplicate_merge' => 'enabled',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'table' => 'users',
    ),
    'yy' => 
    array (
      'name' => 'yy',
      'vname' => 'LBL_YY',
      'type' => 'enum',
      'options' => 'years_dom',
      'len' => '4',
      'massupdate' => false,
    ),
    'mm' => 
    array (
      'name' => 'mm',
      'vname' => 'LBL_MM',
      'type' => 'enum',
      'options' => 'months_dom',
      'len' => '2',
      'massupdate' => false,
    ),
    'revenue' => 
    array (
      'name' => 'revenue',
      'vname' => 'LBL_REVENUE',
      'type' => 'float',
      'dbType' => 'double',
      'len' => '16',
      'audited' => true,
    ),
    'output' => 
    array (
      'name' => 'output',
      'vname' => 'LBL_OUTPUT',
      'type' => 'float',
      'dbType' => 'double',
      'len' => '16',
      'audited' => true,
    ),
    'account_id' => 
    array (
      'name' => 'account_id',
      'vname' => 'LBL_ACCOUNT_ID',
      'type' => 'id',
      'reportable' => false,
      'massupdate' => false,
    ),
    'account_name' => 
    array (
      'name' => 'account_name',
      'rname' => 'name',
      'id_name' => 'account_id',
      'vname' => 'LBL_ACCOUNT_NAME',
      'table' => 'accounts',
      'type' => 'relate',
      'link' => 'accounts',
      'join_name' => 'accounts',
      'module' => 'Accounts',
      'isnull' => 'true',
      'dbType' => 'varchar',
      'len' => '255',
      'source' => 'non-db',
      'massupdate' => true,
    ),
    'accounts' => 
    array (
      'name' => 'accounts',
      'type' => 'link',
      'relationship' => 'accounts_accounts_targets',
      'module' => 'Accounts',
      'bean_name' => 'Account',
      'vname' => 'LBL_ACCOUNTS',
      'source' => 'non-db',
    ),
    'tqt_productseries_id' => 
    array (
      'name' => 'tqt_productseries_id',
      'vname' => 'LBL_TQT_PRODUCTSERIES_ID',
      'type' => 'id',
      'required' => false,
      'reportable' => false,
    ),
    'tqt_productseries_name' => 
    array (
      'name' => 'tqt_productseries_name',
      'rname' => 'name',
      'id_name' => 'tqt_productseries_id',
      'vname' => 'LBL_TQT_PRODUCTSERIES_NAME',
      'table' => 'tqt_productseries',
      'type' => 'relate',
      'link' => 'tqt_productseries',
      'join_name' => 'tqt_productseries',
      'isnull' => 'true',
      'module' => 'TQT_ProductSeries',
      'source' => 'non-db',
      'massupdate' => true,
    ),
    'tqt_productseries' => 
    array (
      'name' => 'tqt_productseries',
      'type' => 'link',
      'relationship' => 'tqt_productseries_accounts_targets',
      'module' => 'TQT_ProductSeries',
      'vname' => 'LBL_LIST_PRODUCTSERIES_NAME',
      'source' => 'non-db',
    ),
  ),
  'indices' => 
  array (
    'id' => 
    array (
      'name' => 'accounts_targetspk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    'idx_accounts_targets_branch_id' => 
    array (
      'name' => 'idx_accounts_targets_branch_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
      ),
    ),
    'idx_accounts_targets_department_id' => 
    array (
      'name' => 'idx_accounts_targets_department_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'department_id',
      ),
    ),
    'idx_accounts_targets_branch_dept' => 
    array (
      'name' => 'idx_accounts_targets_branch_dept',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
        1 => 'department_id',
      ),
    ),
    'idx_accounts_targets_assigned' => 
    array (
      'name' => 'idx_accounts_targets_assigned',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'assigned_user_id',
      ),
    ),
    0 => 
    array (
      'name' => 'idx_acc_target_id_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'id',
        1 => 'deleted',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_acc_target_assigned_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'assigned_user_id',
      ),
    ),
    2 => 
    array (
      'name' => 'idx_acc_target_date',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'yy',
        1 => 'mm',
      ),
    ),
    3 => 
    array (
      'name' => 'idx_acc_target_del_acc_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'account_id',
      ),
    ),
    4 => 
    array (
      'name' => 'idx_acc_target_del_series',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'tqt_productseries_id',
      ),
    ),
    5 => 
    array (
      'name' => 'idx_acc_target_acc_yy',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'account_id',
        1 => 'yy',
      ),
    ),
    6 => 
    array (
      'name' => 'idx_acc_target_acc_date',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'account_id',
        1 => 'yy',
        2 => 'mm',
      ),
    ),
  ),
  'relationships' => 
  array (
    'accounts_targets_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Accounts_Targets',
      'rhs_table' => 'accounts_targets',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'accounts_targets_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Accounts_Targets',
      'rhs_table' => 'accounts_targets',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
    'accounts_targets_assigned_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Accounts_Targets',
      'rhs_table' => 'accounts_targets',
      'rhs_key' => 'assigned_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'accounts_accounts_targets' => 
    array (
      'lhs_module' => 'Accounts',
      'lhs_table' => 'accounts',
      'lhs_key' => 'id',
      'rhs_module' => 'Accounts_Targets',
      'rhs_table' => 'accounts_targets',
      'rhs_key' => 'account_id',
      'relationship_type' => 'one-to-many',
    ),
    'tqt_productseries_accounts_targets' => 
    array (
      'lhs_module' => 'TQT_ProductSeries',
      'lhs_table' => 'tqt_productseries',
      'lhs_key' => 'id',
      'rhs_module' => 'Accounts_Targets',
      'rhs_table' => 'accounts_targets',
      'rhs_key' => 'tqt_productseries_id',
      'relationship_type' => 'one-to-many',
    ),
  ),
  'optimistic_lock' => true,
  'templates' => 
  array (
    'assignable' => 'assignable',
    'groups' => 'groups',
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
