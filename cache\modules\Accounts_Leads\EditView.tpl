
<section id="EditViewViewBody" class="view-module-{$module|strtolower}">

<form action="index.php" method="POST" name="{$form_name}" id="{$form_id}" class="_edit-{$instanceName}" {$enctype}>
<input type="hidden" name="module" value="{$module}" />
<input type="hidden" name="action" id="action" />
{if isset($smarty.request.isDuplicate) && $smarty.request.isDuplicate eq "true"}
<input type="hidden" name="record" id="record" value="" />
<input type="hidden" name="duplicateSave" value="true" />
{else}
<input type="hidden" name="record" id="record" value="{$fields.id.value}" />
{/if}
<input type="hidden" name="return_module" value="{$smarty.request.return_module}" />
<input type="hidden" name="return_action" value="{if $smarty.request.return_action eq 'LoadTabSubpanels'}DetailView{else}{$smarty.request.return_action}{/if}" />
<input type="hidden" name="return_id" value="{$smarty.request.return_id}" />
<input type="hidden" name="isDuplicate" value="false" />
<input type="hidden" name="module_tab" /> 
<input type="hidden" name="contact_role" />
{if !empty($smarty.request.return_module)}
<input type="hidden" name="relate_to" value="{if $smarty.request.return_relationship}{$smarty.request.return_relationship}{else}{$smarty.request.return_module}{/if}" />
<input type="hidden" name="relate_id" value="{$smarty.request.return_id}" />
{/if}
<input type="hidden" name="offset" value="{$offset}" />
{if empty($fields.id.value)}
<input type="hidden" name="_notify_random" value="{php}echo create_guid();{/php}" />
{/if}
<input type="hidden" name="isFromEditView" value="{$module}" />
<input type="hidden" name="rating_old" value="{$fields.rating.value}" />
<input type="hidden" name="telesales_saved" value="ts_" />
<table width="100%" cellpadding="0" cellspacing="0" border="0">
<tr>
<td class="buttons">
{if $bean->aclAccess("save")}<input title="{$APP.LBL_SAVE_BUTTON_TITLE}" accessKey="{$APP.LBL_SAVE_BUTTON_KEY}" class="button save" onclick="{if $isDuplicate}this.form.return_id.value=''; {/if}this.form.action.value='Save'; return check_form('EditView');" type="submit" name="button" value="{$APP.LBL_SAVE_BUTTON_LABEL}" />{/if} 
{if !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($fields.id.value))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {elseif !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($smarty.request.return_id))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {else}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='index'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {/if}
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=Accounts_Leads", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</td>
</tr>
</table>
{incomCRM_include include=$includes}
<div id="LBL_ACCOUNT_INFORMATION" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_ACCOUNT_INFORMATION');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_ACCOUNT_INFORMATION_IMG" border="0" />
{incomCRM_translate label='LBL_ACCOUNT_INFORMATION' module='Accounts_Leads'}
</a>
</h4>
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_ACCOUNT_INFORMATION_GROUP" style="display:block">
<table width="100%" border="0" cellspacing="1" cellpadding="0">
<tr>
<td valign="top" id='account_code_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACCOUNT_CODE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_account_code_field' >
{counter name="panelFieldCount"}

<span id='{$fields.account_code.name}' >
{$fields.account_code.value}
</span>
</td>
<td valign="top" id='account_status_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACCOUNT_STATUS' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_account_status_field' >
{counter name="panelFieldCount"}

<select name="{$fields.account_status.name}" id="{$fields.account_status.name}" title='' tabindex="101"  >
{if isset($fields.account_status.value) && $fields.account_status.value != ''}
{html_options options=$fields.account_status.options selected=$fields.account_status.value}
{else}
{html_options options=$fields.account_status.options selected=$fields.account_status.default}
{/if}
</select>
</td>
</tr>
<tr>
<td valign="top" id='name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_NAME' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_name_field' >
{counter name="panelFieldCount"}

{if strlen($fields.name.value) <= 0}
{assign var="value" value=$fields.name.default_value }
{else}
{assign var="value" value=$fields.name.value }
{/if}
{if isTypeNumber($fields.name.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.name.name}' id='{$fields.name.name}' size='30' maxlength='200' value='{$value}' title='' tabindex='102'  /> 

</td>
<td valign="top" id='department_manager_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DEPARTMENT_MANAGER' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_department_manager_field' >
{counter name="panelFieldCount"}

<select name="{$fields.department_manager.name}" id="{$fields.department_manager.name}" title='' tabindex="103"  >
{if isset($fields.department_manager.value) && $fields.department_manager.value != ''}
{html_options options=$fields.department_manager.options selected=$fields.department_manager.value}
{else}
{html_options options=$fields.department_manager.options selected=$fields.department_manager.default}
{/if}
</select>
</td>
</tr>
<tr>
<td valign="top" id='phone_office_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_PHONE_OFFICE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_phone_office_field' >
{counter name="panelFieldCount"}

{if strlen($fields.phone_office.value) <= 0}
{assign var="value" value=$fields.phone_office.default_value }
{else}
{assign var="value" value=$fields.phone_office.value }
{/if}
{if isTypeNumber($fields.phone_office.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.phone_office.name}' id='{$fields.phone_office.name}' size='30' maxlength='100' value='{$value}' title='' tabindex='104'  /> 

</td>
<td valign="top" id='account_warn_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACCOUNT_WARN' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_account_warn_field' >
{counter name="panelFieldCount"}

<select name="{$fields.account_warn.name}" id="{$fields.account_warn.name}" title='' tabindex="105"  >
{if isset($fields.account_warn.value) && $fields.account_warn.value != ''}
{html_options options=$fields.account_warn.options selected=$fields.account_warn.value}
{else}
{html_options options=$fields.account_warn.options selected=$fields.account_warn.default}
{/if}
</select>
</td>
</tr>
<tr>
<td valign="top" id='billing_address_street_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_BILLING_ADDRESS_STREET' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_billing_address_street_field' >
{counter name="panelFieldCount"}

{if empty($fields.billing_address_street.value)}
{assign var="value" value=$fields.billing_address_street.default_value }
{else}
{assign var="value" value=$fields.billing_address_street.value }
{/if}
<textarea id="{$fields.billing_address_street.name}" name="{$fields.billing_address_street.name}" rows="2" cols="40" title='' tabindex="106"  >{$value}</textarea>
</td>
<td valign="top" id='rating_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_RATING' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_rating_field' >
{counter name="panelFieldCount"}

<select name="{$fields.rating.name}" id="{$fields.rating.name}" title='' tabindex="107"  >
{if isset($fields.rating.value) && $fields.rating.value != ''}
{html_options options=$fields.rating.options selected=$fields.rating.value}
{else}
{html_options options=$fields.rating.options selected=$fields.rating.default}
{/if}
</select>
</td>
</tr>
<tr>
<td valign="top" id='shipping_address_street_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_SHIPPING_ADDRESS_STREET' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_shipping_address_street_field' >
{counter name="panelFieldCount"}

{if empty($fields.shipping_address_street.value)}
{assign var="value" value=$fields.shipping_address_street.default_value }
{else}
{assign var="value" value=$fields.shipping_address_street.value }
{/if}
<textarea id="{$fields.shipping_address_street.name}" name="{$fields.shipping_address_street.name}" rows="2" cols="40" title='' tabindex="108"  >{$value}</textarea>
</td>
<td valign="top" id='rate_note_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_RATE_NOTE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_rate_note_field' >
{counter name="panelFieldCount"}

{if empty($fields.rate_note.value)}
{assign var="value" value=$fields.rate_note.default_value }
{else}
{assign var="value" value=$fields.rate_note.value }
{/if}
<textarea id="{$fields.rate_note.name}" name="{$fields.rate_note.name}" rows="2" cols="40" title='' tabindex="109"  >{$value}</textarea>
</td>
</tr>
<tr>
<td valign="top" id='tax_code_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_TAX_CODE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_tax_code_field' >
{counter name="panelFieldCount"}

{if strlen($fields.tax_code.value) <= 0}
{assign var="value" value=$fields.tax_code.default_value }
{else}
{assign var="value" value=$fields.tax_code.value }
{/if}
{if isTypeNumber($fields.tax_code.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.tax_code.name}' id='{$fields.tax_code.name}' size='30' maxlength='30' value='{$value}' title='' tabindex='110'  /> 

</td>
<td valign="top" id='reference_code_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_REFERENCE_CODE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_reference_code_field' >
{counter name="panelFieldCount"}

{if strlen($fields.reference_code.value) <= 0}
{assign var="value" value=$fields.reference_code.default_value }
{else}
{assign var="value" value=$fields.reference_code.value }
{/if}
{if isTypeNumber($fields.reference_code.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.reference_code.name}' id='{$fields.reference_code.name}' size='30' maxlength='50' value='{$value}' title='' tabindex='111'  /> 

</td>
</tr>
<tr>
<td valign="top" id='account_type_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_TYPE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_account_type_field' >
{counter name="panelFieldCount"}

<select name="{$fields.account_type.name}" id="{$fields.account_type.name}" title='' tabindex="112"  >
{if isset($fields.account_type.value) && $fields.account_type.value != ''}
{html_options options=$fields.account_type.options selected=$fields.account_type.value}
{else}
{html_options options=$fields.account_type.options selected=$fields.account_type.default}
{/if}
</select>
</td>
<td valign="top" id='phone_fax_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_FAX' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_phone_fax_field' >
{counter name="panelFieldCount"}

{if strlen($fields.phone_fax.value) <= 0}
{assign var="value" value=$fields.phone_fax.default_value }
{else}
{assign var="value" value=$fields.phone_fax.value }
{/if}
{if isTypeNumber($fields.phone_fax.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.phone_fax.name}' id='{$fields.phone_fax.name}' size='30' maxlength='100' value='{$value}' title='' tabindex='113'  /> 

</td>
</tr>
<tr>
<td valign="top" id='industry_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_INDUSTRY' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_industry_field' >
{counter name="panelFieldCount"}

<select name="{$fields.industry.name}" id="{$fields.industry.name}" title='' tabindex="114"  >
{if isset($fields.industry.value) && $fields.industry.value != ''}
{html_options options=$fields.industry.options selected=$fields.industry.value}
{else}
{html_options options=$fields.industry.options selected=$fields.industry.default}
{/if}
</select>
</td>
<td valign="top" id='website_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_WEBSITE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_website_field' >
{counter name="panelFieldCount"}

{if strlen($fields.website.value) <= 0}
{assign var="value" value=$fields.website.default_value }
{else}
{assign var="value" value=$fields.website.value }
{/if}
{if isTypeNumber($fields.website.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.website.name}' id='{$fields.website.name}' size='30' maxlength='255' value='{$value}' title='' tabindex='115'  /> 

</td>
</tr>
<tr>
<td valign="top" id='transaction_level_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_TRANSACTION_LEVEL' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_transaction_level_field' >
{counter name="panelFieldCount"}

<select name="{$fields.transaction_level.name}" id="{$fields.transaction_level.name}" title='' tabindex="116"  >
{if isset($fields.transaction_level.value) && $fields.transaction_level.value != ''}
{html_options options=$fields.transaction_level.options selected=$fields.transaction_level.value}
{else}
{html_options options=$fields.transaction_level.options selected=$fields.transaction_level.default}
{/if}
</select>
</td>
<td valign="top" id='location_area_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_LOCATION_AREA' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_location_area_field' >
{counter name="panelFieldCount"}

<select name="{$fields.location_area.name}" id="{$fields.location_area.name}" title='' tabindex="117"  >
{if isset($fields.location_area.value) && $fields.location_area.value != ''}
{html_options options=$fields.location_area.options selected=$fields.location_area.value}
{else}
{html_options options=$fields.location_area.options selected=$fields.location_area.default}
{/if}
</select>
</td>
</tr>
<tr>
<td valign="top" id='sector_vertical_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_SECTOR_VERTICAL' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_sector_vertical_field' >
{counter name="panelFieldCount"}

<select name="{$fields.sector_vertical.name}" id="{$fields.sector_vertical.name}" title='' tabindex="118"  >
{if isset($fields.sector_vertical.value) && $fields.sector_vertical.value != ''}
{html_options options=$fields.sector_vertical.options selected=$fields.sector_vertical.value}
{else}
{html_options options=$fields.sector_vertical.options selected=$fields.sector_vertical.default}
{/if}
</select>
</td>
<td valign="top" id='location_city_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_LOCATION_CITY' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_location_city_field' >
{counter name="panelFieldCount"}

<select name="{$fields.location_city.name}" id="{$fields.location_city.name}" title='' tabindex="119"  onchange="changeParentSelectedOption(this, this.form.location_district, 'location_district_dom')" >
{if isset($fields.location_city.value) && $fields.location_city.value != ''}
{html_options options=$fields.location_city.options selected=$fields.location_city.value}
{else}
{html_options options=$fields.location_city.options selected=$fields.location_city.default}
{/if}
</select>
</td>
</tr>
<tr>
<td valign="top" id='account_scope_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACCOUNT_SCOPE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_account_scope_field' >
{counter name="panelFieldCount"}

<select name="{$fields.account_scope.name}" id="{$fields.account_scope.name}" title='' tabindex="120"  >
{if isset($fields.account_scope.value) && $fields.account_scope.value != ''}
{html_options options=$fields.account_scope.options selected=$fields.account_scope.value}
{else}
{html_options options=$fields.account_scope.options selected=$fields.account_scope.default}
{/if}
</select>
</td>
<td valign="top" id='location_district_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_LOCATION_DISTRICT' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_location_district_field' >
{counter name="panelFieldCount"}

<select name="{$fields.location_district.name}" id="{$fields.location_district.name}" title='' tabindex="121"  >
{if isset($fields.location_district.value) && $fields.location_district.value != ''}
{html_options options=$fields.location_district.options selected=$fields.location_district.value}
{else}
{html_options options=$fields.location_district.options selected=$fields.location_district.default}
{/if}
</select>
</td>
</tr>
<tr>
<td valign="top" id='lead_source_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_LEAD_SOURCE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_lead_source_field' >
{counter name="panelFieldCount"}

<select name="{$fields.lead_source.name}" id="{$fields.lead_source.name}" title='' tabindex="122"  >
{if isset($fields.lead_source.value) && $fields.lead_source.value != ''}
{html_options options=$fields.lead_source.options selected=$fields.lead_source.value}
{else}
{html_options options=$fields.lead_source.options selected=$fields.lead_source.default}
{/if}
</select>
</td>
<td valign="top" id='location_ward_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_LOCATION_WARD' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_location_ward_field' >
{counter name="panelFieldCount"}

{if strlen($fields.location_ward.value) <= 0}
{assign var="value" value=$fields.location_ward.default_value }
{else}
{assign var="value" value=$fields.location_ward.value }
{/if}
{if isTypeNumber($fields.location_ward.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.location_ward.name}' id='{$fields.location_ward.name}' size='30' maxlength='100' value='{$value}' title='' tabindex='123'  /> 

</td>
</tr>
<tr>
<td valign="top" id='account_group_name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACCOUNT_GROUP_NAME' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_account_group_name_field' >
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{/if}<input type="hidden" name="{$fields.account_group_name.id_name}" id="{$fields.account_group_name.id_name}" value="{$fields.account_group_id.value}" />
<input type="text" name="{$fields.account_group_name.name}" class="sqsEnabled" tabindex="124" id="{$fields.account_group_name.name}" size="16" value="{$fields.account_group_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{/if}
<input type="button" name="btn_{$fields.account_group_name.name}" id="btn_{$fields.account_group_name.name}" tabindex="124" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.account_group_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"EditView","field_to_name_array":{"id":"account_group_id","name":"account_group_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.account_group_name.name}" id="btn_clr_{$fields.account_group_name.name}" tabindex="124" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.account_group_name.name}.value=""; this.form.{$fields.account_group_name.id_name}.value=""; this.form.{$fields.account_group_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
{if !empty($isSmartView)}</div>{/if}<script type="text/javascript">enableQS(false);</script>
</td>
<td valign="top" id='location_hamlet_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_LOCATION_HAMLET' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_location_hamlet_field' >
{counter name="panelFieldCount"}

{if strlen($fields.location_hamlet.value) <= 0}
{assign var="value" value=$fields.location_hamlet.default_value }
{else}
{assign var="value" value=$fields.location_hamlet.value }
{/if}
{if isTypeNumber($fields.location_hamlet.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.location_hamlet.name}' id='{$fields.location_hamlet.name}' size='30' maxlength='100' value='{$value}' title='' tabindex='125'  /> 

</td>
</tr>
<tr>
<td valign="top" id='assigned_user_name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_assigned_user_name_field' >
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{/if}<input type="hidden" name="{$fields.assigned_user_name.id_name}" id="{$fields.assigned_user_name.id_name}" value="{$fields.assigned_user_id.value}" />
<input type="text" name="{$fields.assigned_user_name.name}" class="sqsEnabled" tabindex="126" id="{$fields.assigned_user_name.name}" size="16" value="{$fields.assigned_user_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{/if}
<input type="button" name="btn_{$fields.assigned_user_name.name}" id="btn_{$fields.assigned_user_name.name}" tabindex="126" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.assigned_user_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"EditView","field_to_name_array":{"id":"assigned_user_id","user_name":"assigned_user_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.assigned_user_name.name}" id="btn_clr_{$fields.assigned_user_name.name}" tabindex="126" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.assigned_user_name.name}.value=""; this.form.{$fields.assigned_user_name.id_name}.value=""; this.form.{$fields.assigned_user_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
{if !empty($isSmartView)}</div>{/if}<script type="text/javascript">enableQS(false);</script>
</td>
<td valign="top" id='parent_name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_MEMBER_OF' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_parent_name_field' >
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{/if}<input type="hidden" name="{$fields.parent_name.id_name}" id="{$fields.parent_name.id_name}" value="{$fields.parent_id.value}" />
<input type="text" name="{$fields.parent_name.name}" class="sqsEnabled" tabindex="127" id="{$fields.parent_name.name}" size="16" value="{$fields.parent_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{/if}
<input type="button" name="btn_{$fields.parent_name.name}" id="btn_{$fields.parent_name.name}" tabindex="127" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.parent_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"EditView","field_to_name_array":{"id":"parent_id","name":"parent_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.parent_name.name}" id="btn_clr_{$fields.parent_name.name}" tabindex="127" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.parent_name.name}.value=""; this.form.{$fields.parent_name.id_name}.value=""; this.form.{$fields.parent_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
{if !empty($isSmartView)}</div>{/if}<script type="text/javascript">enableQS(false);</script>
</td>
</tr>
<tr>
<td valign="top" id='debt_user_name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DEBT_USER_NAME' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_debt_user_name_field' >
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{/if}<input type="hidden" name="{$fields.debt_user_name.id_name}" id="{$fields.debt_user_name.id_name}" value="{$fields.debt_user_id.value}" />
<input type="text" name="{$fields.debt_user_name.name}" class="sqsEnabled" tabindex="128" id="{$fields.debt_user_name.name}" size="16" value="{$fields.debt_user_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{/if}
<input type="button" name="btn_{$fields.debt_user_name.name}" id="btn_{$fields.debt_user_name.name}" tabindex="128" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.debt_user_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"EditView","field_to_name_array":{"id":"debt_user_id","user_name":"debt_user_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.debt_user_name.name}" id="btn_clr_{$fields.debt_user_name.name}" tabindex="128" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.debt_user_name.name}.value=""; this.form.{$fields.debt_user_name.id_name}.value=""; this.form.{$fields.debt_user_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
{if !empty($isSmartView)}</div>{/if}<script type="text/javascript">enableQS(false);</script>
</td>
<td valign="top" id='filename_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_FILENAME' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_filename_field' >
{counter name="panelFieldCount"}
<span id="new_attachment" style="display:{if !empty($fields.filename.value)}none{/if}"><input tabindex="129"  type="file" name="uploadfile" size="30" /></span><span id="old_attachment" style="display:{if empty($fields.filename.value)}none{/if}">{if !empty($fields.filename.value)}<img align="absmiddle" alt="{$fields.filename.value}" height="60" src="{$fields.file_url.value}?d={$fields.date_modified.value|urlencode}" /> {/if}<input tabindex="129"  type="hidden" name="deleteAttachment" value="0" /><input tabindex="129"  type="hidden" name="old_id" value="{$fields.id.value}" /><input tabindex="129"  type="hidden" name="old_filename" value="{$fields.filename.value}" /><input tabindex="129"  type="button" class="button" name="btn" value="{$APP.LBL_REMOVE}" onclick='{if !empty($isDuplicate)}deleteAttachmentCallBack(-1);{else}ajaxStatus.showStatus(incomCRM.language.get("app_strings", "LBL_ATT_REMOVING_ATTACHMENT")); this.form.deleteAttachment.value=1; this.form.action.value="UploadFileRemove"; incomCRM.dashlets.postForm(this.form, deleteAttachmentCallBack); this.form.deleteAttachment.value=0; this.form.action.value="";{/if}' /></span>
</td>
</tr>
<tr>
<td valign="top" id='email1_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_EMAIL' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_email1_field' >
{counter name="panelFieldCount"}

{$fields.email1.value}
</td>
<td valign="top" id='membership_card_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_MEMBERSHIP_CARD' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_membership_card_field' >
{counter name="panelFieldCount"}

<select name="{$fields.membership_card.name}" id="{$fields.membership_card.name}" title='' tabindex="131"  >
{if isset($fields.membership_card.value) && $fields.membership_card.value != ''}
{html_options options=$fields.membership_card.options selected=$fields.membership_card.value}
{else}
{html_options options=$fields.membership_card.options selected=$fields.membership_card.default}
{/if}
</select>
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_ACCOUNT_INFORMATION").style.display='none';</script>
{/if}
<div id="LBL_BUYING_CYCLE_TITLE" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_BUYING_CYCLE_TITLE');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_BUYING_CYCLE_TITLE_IMG" border="0" />
{incomCRM_translate label='LBL_BUYING_CYCLE_TITLE' module='Accounts_Leads'}
</a>
</h4>
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_BUYING_CYCLE_TITLE_GROUP" style="display:none">
<table width="100%" border="0" cellspacing="1" cellpadding="0">
<tr>
<td valign="top" id='periodic_purchase_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_PERIODIC_PURCHASE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_periodic_purchase_field' >
{counter name="panelFieldCount"}

<select name="{$fields.periodic_purchase.name}" id="{$fields.periodic_purchase.name}" title='' tabindex="132"  >
{if isset($fields.periodic_purchase.value) && $fields.periodic_purchase.value != ''}
{html_options options=$fields.periodic_purchase.options selected=$fields.periodic_purchase.value}
{else}
{html_options options=$fields.periodic_purchase.options selected=$fields.periodic_purchase.default}
{/if}
</select>
</td>
<td valign="top" id='cycle_method_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CYCLE_METHOD' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_cycle_method_field' >
{counter name="panelFieldCount"}

<select name="{$fields.cycle_method.name}" id="{$fields.cycle_method.name}" title='' tabindex="133"  >
{if isset($fields.cycle_method.value) && $fields.cycle_method.value != ''}
{html_options options=$fields.cycle_method.options selected=$fields.cycle_method.value}
{else}
{html_options options=$fields.cycle_method.options selected=$fields.cycle_method.default}
{/if}
</select>
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_BUYING_CYCLE_TITLE").style.display='none';</script>
{/if}
<div id="LBL_CONTACT_INFORMATION" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_CONTACT_INFORMATION');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_CONTACT_INFORMATION_IMG" border="0" />
{incomCRM_translate label='LBL_CONTACT_INFORMATION' module='Accounts_Leads'}
</a>
</h4>
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_CONTACT_INFORMATION_GROUP" style="display:none">
<table width="100%" border="0" cellspacing="1" cellpadding="0">
<tr>
<td valign="top" id='primary_contact_name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_PRIMARY_CONTACT_NAME' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_primary_contact_name_field' >
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{/if}<input type="hidden" name="{$fields.primary_contact_name.id_name}" id="{$fields.primary_contact_name.id_name}" value="{$fields.primary_contact_id.value}" />
<input type="text" name="{$fields.primary_contact_name.name}" class="sqsEnabled" tabindex="134" id="{$fields.primary_contact_name.name}" size="16" value="{$fields.primary_contact_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{/if}
<input type="button" name="btn_{$fields.primary_contact_name.name}" id="btn_{$fields.primary_contact_name.name}" tabindex="134" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.primary_contact_name.module}", 1000, 600, "&account_name=\""+ encodeURIComponent(this.form.name.value) +"\"", true, false, {literal}{"call_back_function":"set_return","form_name":"EditView","field_to_name_array":{"id":"primary_contact_id","last_name":"primary_contact_name","position":"primary_contact_position","department":"primary_contact_department","phone_work":"primary_contact_phone_work","phone_mobile":"primary_contact_phone_mobile","phone_fax":"primary_contact_phone_fax","email1":"primary_contact_email","birthdate":"primary_contact_birthdate","alt_address_street":"primary_contact_address","description":"primary_contact_note"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.primary_contact_name.name}" id="btn_clr_{$fields.primary_contact_name.name}" tabindex="134" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.primary_contact_name.name}.value=""; this.form.{$fields.primary_contact_name.id_name}.value=""; this.form.{$fields.primary_contact_name.name}.focus();this.form.primary_contact_name.value=""; this.form.primary_contact_id.value=""; this.form.primary_contact_position.value=""; this.form.primary_contact_department.value=""; this.form.primary_contact_phone_work.value=""; this.form.primary_contact_phone_mobile.value=""; this.form.primary_contact_phone_fax.value=""; this.form.primary_contact_email.value=""; this.form.primary_contact_birthdate.value=""; this.form.primary_contact_address.value=""; this.form.primary_contact_note.value="";' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
{if !empty($isSmartView)}</div>{/if}<script type="text/javascript">enableQS(false);</script>
</td>
<td valign="top" id='secondary_contact_name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_SECONDARY_CONTACT_NAME' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_secondary_contact_name_field' >
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{/if}<input type="hidden" name="{$fields.secondary_contact_name.id_name}" id="{$fields.secondary_contact_name.id_name}" value="{$fields.secondary_contact_id.value}" />
<input type="text" name="{$fields.secondary_contact_name.name}" class="sqsEnabled" tabindex="135" id="{$fields.secondary_contact_name.name}" size="16" value="{$fields.secondary_contact_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{/if}
<input type="button" name="btn_{$fields.secondary_contact_name.name}" id="btn_{$fields.secondary_contact_name.name}" tabindex="135" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.secondary_contact_name.module}", 1000, 600, "&account_name=\""+ encodeURIComponent(this.form.name.value) +"\"", true, false, {literal}{"call_back_function":"set_return","form_name":"EditView","field_to_name_array":{"id":"secondary_contact_id","last_name":"secondary_contact_name","position":"secondary_contact_position","department":"secondary_contact_department","phone_work":"secondary_contact_phone_work","phone_mobile":"secondary_contact_phone_mobile","phone_fax":"secondary_contact_phone_fax","email1":"secondary_contact_email","birthdate":"secondary_contact_birthdate","alt_address_street":"secondary_contact_address","description":"secondary_contact_note"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.secondary_contact_name.name}" id="btn_clr_{$fields.secondary_contact_name.name}" tabindex="135" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.secondary_contact_name.name}.value=""; this.form.{$fields.secondary_contact_name.id_name}.value=""; this.form.{$fields.secondary_contact_name.name}.focus();this.form.secondary_contact_name.value=""; this.form.secondary_contact_id.value=""; this.form.secondary_contact_position.value=""; this.form.secondary_contact_department.value=""; this.form.secondary_contact_phone_work.value=""; this.form.secondary_contact_phone_mobile.value=""; this.form.secondary_contact_phone_fax.value=""; this.form.secondary_contact_email.value=""; this.form.secondary_contact_birthdate.value=""; this.form.secondary_contact_address.value=""; this.form.secondary_contact_note.value="";' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
{if !empty($isSmartView)}</div>{/if}<script type="text/javascript">enableQS(false);</script>
</td>
</tr>
<tr>
<td valign="top" id='primary_contact_position_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTACT_POSITION' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_primary_contact_position_field' >
{counter name="panelFieldCount"}

{if strlen($fields.primary_contact_position.value) <= 0}
{assign var="value" value=$fields.primary_contact_position.default_value }
{else}
{assign var="value" value=$fields.primary_contact_position.value }
{/if}
{if isTypeNumber($fields.primary_contact_position.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.primary_contact_position.name}' id='{$fields.primary_contact_position.name}' size='30' maxlength='200' value='{$value}' title='' tabindex='136'  /> 

</td>
<td valign="top" id='secondary_contact_position_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTACT_POSITION' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_secondary_contact_position_field' >
{counter name="panelFieldCount"}

{if strlen($fields.secondary_contact_position.value) <= 0}
{assign var="value" value=$fields.secondary_contact_position.default_value }
{else}
{assign var="value" value=$fields.secondary_contact_position.value }
{/if}
{if isTypeNumber($fields.secondary_contact_position.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.secondary_contact_position.name}' id='{$fields.secondary_contact_position.name}' size='30' maxlength='200' value='{$value}' title='' tabindex='137'  /> 

</td>
</tr>
<tr>
<td valign="top" id='primary_contact_department_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTACT_DEPARTMENT' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_primary_contact_department_field' >
{counter name="panelFieldCount"}

{if strlen($fields.primary_contact_department.value) <= 0}
{assign var="value" value=$fields.primary_contact_department.default_value }
{else}
{assign var="value" value=$fields.primary_contact_department.value }
{/if}
{if isTypeNumber($fields.primary_contact_department.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.primary_contact_department.name}' id='{$fields.primary_contact_department.name}' size='30' maxlength='255' value='{$value}' title='' tabindex='138'  /> 

</td>
<td valign="top" id='secondary_contact_department_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTACT_DEPARTMENT' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_secondary_contact_department_field' >
{counter name="panelFieldCount"}

{if strlen($fields.secondary_contact_department.value) <= 0}
{assign var="value" value=$fields.secondary_contact_department.default_value }
{else}
{assign var="value" value=$fields.secondary_contact_department.value }
{/if}
{if isTypeNumber($fields.secondary_contact_department.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.secondary_contact_department.name}' id='{$fields.secondary_contact_department.name}' size='30' maxlength='255' value='{$value}' title='' tabindex='139'  /> 

</td>
</tr>
<tr>
<td valign="top" id='primary_contact_birthdate_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTACT_BIRTHDATE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_primary_contact_birthdate_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.primary_contact_birthdate.value }
<input autocomplete="off" type="text" name="{$fields.primary_contact_birthdate.name}" id="{$fields.primary_contact_birthdate.name}" value="{$date_value}" title=''  tabindex='140' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.primary_contact_birthdate.name}_trigger" align="absmiddle" />
&nbsp;(<span class="dateFormat">{$USER_DATEFORMAT}</span>)
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.primary_contact_birthdate.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.primary_contact_birthdate.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
<td valign="top" id='secondary_contact_birthdate_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTACT_BIRTHDATE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_secondary_contact_birthdate_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.secondary_contact_birthdate.value }
<input autocomplete="off" type="text" name="{$fields.secondary_contact_birthdate.name}" id="{$fields.secondary_contact_birthdate.name}" value="{$date_value}" title=''  tabindex='141' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.secondary_contact_birthdate.name}_trigger" align="absmiddle" />
&nbsp;(<span class="dateFormat">{$USER_DATEFORMAT}</span>)
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.secondary_contact_birthdate.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.secondary_contact_birthdate.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
</tr>
<tr>
<td valign="top" id='primary_contact_phone_work_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTACT_PHONE_WORK' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_primary_contact_phone_work_field' >
{counter name="panelFieldCount"}

{if strlen($fields.primary_contact_phone_work.value) <= 0}
{assign var="value" value=$fields.primary_contact_phone_work.default_value }
{else}
{assign var="value" value=$fields.primary_contact_phone_work.value }
{/if}
{if isTypeNumber($fields.primary_contact_phone_work.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.primary_contact_phone_work.name}' id='{$fields.primary_contact_phone_work.name}' size='30' maxlength='100' value='{$value}' title='' tabindex='142'  /> 

</td>
<td valign="top" id='secondary_contact_phone_work_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTACT_PHONE_WORK' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_secondary_contact_phone_work_field' >
{counter name="panelFieldCount"}

{if strlen($fields.secondary_contact_phone_work.value) <= 0}
{assign var="value" value=$fields.secondary_contact_phone_work.default_value }
{else}
{assign var="value" value=$fields.secondary_contact_phone_work.value }
{/if}
{if isTypeNumber($fields.secondary_contact_phone_work.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.secondary_contact_phone_work.name}' id='{$fields.secondary_contact_phone_work.name}' size='30' maxlength='100' value='{$value}' title='' tabindex='143'  /> 

</td>
</tr>
<tr>
<td valign="top" id='primary_contact_phone_mobile_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTACT_PHONE_MOBILE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_primary_contact_phone_mobile_field' >
{counter name="panelFieldCount"}

{if strlen($fields.primary_contact_phone_mobile.value) <= 0}
{assign var="value" value=$fields.primary_contact_phone_mobile.default_value }
{else}
{assign var="value" value=$fields.primary_contact_phone_mobile.value }
{/if}
{if isTypeNumber($fields.primary_contact_phone_mobile.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.primary_contact_phone_mobile.name}' id='{$fields.primary_contact_phone_mobile.name}' size='30' maxlength='100' value='{$value}' title='' tabindex='144'  /> 

</td>
<td valign="top" id='secondary_contact_phone_mobile_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTACT_PHONE_MOBILE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_secondary_contact_phone_mobile_field' >
{counter name="panelFieldCount"}

{if strlen($fields.secondary_contact_phone_mobile.value) <= 0}
{assign var="value" value=$fields.secondary_contact_phone_mobile.default_value }
{else}
{assign var="value" value=$fields.secondary_contact_phone_mobile.value }
{/if}
{if isTypeNumber($fields.secondary_contact_phone_mobile.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.secondary_contact_phone_mobile.name}' id='{$fields.secondary_contact_phone_mobile.name}' size='30' maxlength='100' value='{$value}' title='' tabindex='145'  /> 

</td>
</tr>
<tr>
<td valign="top" id='primary_contact_phone_fax_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTACT_PHONE_FAX' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_primary_contact_phone_fax_field' >
{counter name="panelFieldCount"}

{if strlen($fields.primary_contact_phone_fax.value) <= 0}
{assign var="value" value=$fields.primary_contact_phone_fax.default_value }
{else}
{assign var="value" value=$fields.primary_contact_phone_fax.value }
{/if}
{if isTypeNumber($fields.primary_contact_phone_fax.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.primary_contact_phone_fax.name}' id='{$fields.primary_contact_phone_fax.name}' size='30' maxlength='100' value='{$value}' title='' tabindex='146'  /> 

</td>
<td valign="top" id='secondary_contact_phone_fax_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTACT_PHONE_FAX' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_secondary_contact_phone_fax_field' >
{counter name="panelFieldCount"}

{if strlen($fields.secondary_contact_phone_fax.value) <= 0}
{assign var="value" value=$fields.secondary_contact_phone_fax.default_value }
{else}
{assign var="value" value=$fields.secondary_contact_phone_fax.value }
{/if}
{if isTypeNumber($fields.secondary_contact_phone_fax.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.secondary_contact_phone_fax.name}' id='{$fields.secondary_contact_phone_fax.name}' size='30' maxlength='100' value='{$value}' title='' tabindex='147'  /> 

</td>
</tr>
<tr>
<td valign="top" id='primary_contact_email_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTACT_EMAIL' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_primary_contact_email_field' >
{counter name="panelFieldCount"}

{if strlen($fields.primary_contact_email.value) <= 0}
{assign var="value" value=$fields.primary_contact_email.default_value }
{else}
{assign var="value" value=$fields.primary_contact_email.value }
{/if}
{if isTypeNumber($fields.primary_contact_email.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.primary_contact_email.name}' id='{$fields.primary_contact_email.name}' size='30' maxlength='200' value='{$value}' title='' tabindex='148'  /> 

</td>
<td valign="top" id='secondary_contact_email_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTACT_EMAIL' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_secondary_contact_email_field' >
{counter name="panelFieldCount"}

{if strlen($fields.secondary_contact_email.value) <= 0}
{assign var="value" value=$fields.secondary_contact_email.default_value }
{else}
{assign var="value" value=$fields.secondary_contact_email.value }
{/if}
{if isTypeNumber($fields.secondary_contact_email.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.secondary_contact_email.name}' id='{$fields.secondary_contact_email.name}' size='30' maxlength='200' value='{$value}' title='' tabindex='149'  /> 

</td>
</tr>
<tr>
<td valign="top" id='primary_contact_address_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTACT_ADDRESS' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_primary_contact_address_field' >
{counter name="panelFieldCount"}

{if empty($fields.primary_contact_address.value)}
{assign var="value" value=$fields.primary_contact_address.default_value }
{else}
{assign var="value" value=$fields.primary_contact_address.value }
{/if}
<textarea id="{$fields.primary_contact_address.name}" name="{$fields.primary_contact_address.name}" rows="2" cols="40" title='' tabindex="150"  >{$value}</textarea>
</td>
<td valign="top" id='secondary_contact_address_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTACT_ADDRESS' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_secondary_contact_address_field' >
{counter name="panelFieldCount"}

{if empty($fields.secondary_contact_address.value)}
{assign var="value" value=$fields.secondary_contact_address.default_value }
{else}
{assign var="value" value=$fields.secondary_contact_address.value }
{/if}
<textarea id="{$fields.secondary_contact_address.name}" name="{$fields.secondary_contact_address.name}" rows="2" cols="40" title='' tabindex="151"  >{$value}</textarea>
</td>
</tr>
<tr>
<td valign="top" id='primary_contact_note_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTACT_NOTE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_primary_contact_note_field' >
{counter name="panelFieldCount"}

{if empty($fields.primary_contact_note.value)}
{assign var="value" value=$fields.primary_contact_note.default_value }
{else}
{assign var="value" value=$fields.primary_contact_note.value }
{/if}
<textarea id="{$fields.primary_contact_note.name}" name="{$fields.primary_contact_note.name}" rows="2" cols="40" title='' tabindex="152"  >{$value}</textarea>
</td>
<td valign="top" id='secondary_contact_note_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTACT_NOTE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_secondary_contact_note_field' >
{counter name="panelFieldCount"}

{if empty($fields.secondary_contact_note.value)}
{assign var="value" value=$fields.secondary_contact_note.default_value }
{else}
{assign var="value" value=$fields.secondary_contact_note.value }
{/if}
<textarea id="{$fields.secondary_contact_note.name}" name="{$fields.secondary_contact_note.name}" rows="2" cols="40" title='' tabindex="153"  >{$value}</textarea>
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_CONTACT_INFORMATION").style.display='none';</script>
{/if}
<div id="LBL_MORE_INFO" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_MORE_INFO');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_MORE_INFO_IMG" border="0" />
{incomCRM_translate label='LBL_MORE_INFO' module='Accounts_Leads'}
</a>
</h4>
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_MORE_INFO_GROUP" style="display:none">
<table width="100%" border="0" cellspacing="1" cellpadding="0">
<tr>
<td valign="top" id='english_name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ENGLISH_NAME' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_english_name_field' >
{counter name="panelFieldCount"}

{if strlen($fields.english_name.value) <= 0}
{assign var="value" value=$fields.english_name.default_value }
{else}
{assign var="value" value=$fields.english_name.value }
{/if}
{if isTypeNumber($fields.english_name.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.english_name.name}' id='{$fields.english_name.name}' size='30' maxlength='100' value='{$value}' title='' tabindex='154'  /> 

</td>
<td valign="top" id='employees_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_EMPLOYEES' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_employees_field' >
{counter name="panelFieldCount"}

{if strlen($fields.employees.value) <= 0}
{assign var="value" value=$fields.employees.default_value }
{else}
{assign var="value" value=$fields.employees.value }
{/if}
{if isTypeNumber($fields.employees.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.employees.name}' id='{$fields.employees.name}' size='30' maxlength='10' value='{$value}' title='' tabindex='155'  /> 

</td>
</tr>
<tr>
<td valign="top" id='abbreviation_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ABBREVIATION' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_abbreviation_field' >
{counter name="panelFieldCount"}

{if strlen($fields.abbreviation.value) <= 0}
{assign var="value" value=$fields.abbreviation.default_value }
{else}
{assign var="value" value=$fields.abbreviation.value }
{/if}
{if isTypeNumber($fields.abbreviation.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.abbreviation.name}' id='{$fields.abbreviation.name}' size='30' maxlength='100' value='{$value}' title='' tabindex='156'  /> 

</td>
<td valign="top" id='marking_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_MARKING' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_marking_field' >
{counter name="panelFieldCount"}

{if strval($fields.marking.value) == "1" || strval($fields.marking.value) == "yes" || strval($fields.marking.value) == "on"} 
{assign var="checked" value="CHECKED"}
{else}
{assign var="checked" value=""}
{/if}
<input type="hidden" name="{$fields.marking.name}" value="0" /> 
<input type="checkbox" id="{$fields.marking.name}" name="{$fields.marking.name}" value="1" title='' tabindex="157" {$checked}  />

</td>
</tr>
<tr>
<td valign="top" id='phone_alternate_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_PHONE_ALT' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_phone_alternate_field' >
{counter name="panelFieldCount"}

{if strlen($fields.phone_alternate.value) <= 0}
{assign var="value" value=$fields.phone_alternate.default_value }
{else}
{assign var="value" value=$fields.phone_alternate.value }
{/if}
{if isTypeNumber($fields.phone_alternate.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.phone_alternate.name}' id='{$fields.phone_alternate.name}' size='30' maxlength='100' value='{$value}' title='' tabindex='158'  /> 

</td>
<td valign="top" id='mark_note_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_MARK_NOTE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_mark_note_field' >
{counter name="panelFieldCount"}

{if empty($fields.mark_note.value)}
{assign var="value" value=$fields.mark_note.default_value }
{else}
{assign var="value" value=$fields.mark_note.value }
{/if}
<textarea id="{$fields.mark_note.name}" name="{$fields.mark_note.name}" rows="2" cols="40" title='' tabindex="159"  >{$value}</textarea>
</td>
</tr>
<tr>
<td valign="top" id='description_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DESCRIPTION' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_description_field' >
{counter name="panelFieldCount"}

{if empty($fields.description.value)}
{assign var="value" value=$fields.description.default_value }
{else}
{assign var="value" value=$fields.description.value }
{/if}
<textarea id="{$fields.description.name}" name="{$fields.description.name}" rows="2" cols="40" title='' tabindex="160"  >{$value}</textarea>
</td>
<td valign="top" id='responsible_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_RESPONSIBLE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_responsible_field' >
{counter name="panelFieldCount"}

{if empty($fields.responsible.value)}
{assign var="value" value=$fields.responsible.default_value }
{else}
{assign var="value" value=$fields.responsible.value }
{/if}
<textarea id="{$fields.responsible.name}" name="{$fields.responsible.name}" rows="2" cols="40" title='' tabindex="161"  >{$value}</textarea>
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_MORE_INFO").style.display='none';</script>
{/if}
<div id="LBL_BANK_INFORMATION" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_BANK_INFORMATION');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_BANK_INFORMATION_IMG" border="0" />
{incomCRM_translate label='LBL_BANK_INFORMATION' module='Accounts_Leads'}
</a>
</h4>
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_BANK_INFORMATION_GROUP" style="display:none">
<table width="100%" border="0" cellspacing="1" cellpadding="0">
<tr>
<td valign="top" id='registration_number_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_REGISTRATION_NUMBER' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_registration_number_field' >
{counter name="panelFieldCount"}

{if strlen($fields.registration_number.value) <= 0}
{assign var="value" value=$fields.registration_number.default_value }
{else}
{assign var="value" value=$fields.registration_number.value }
{/if}
{if isTypeNumber($fields.registration_number.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.registration_number.name}' id='{$fields.registration_number.name}' size='30' maxlength='50' value='{$value}' title='' tabindex='162'  /> 

</td>
<td valign="top" id='stock_code_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_STOCK_CODE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_stock_code_field' >
{counter name="panelFieldCount"}

{if strlen($fields.stock_code.value) <= 0}
{assign var="value" value=$fields.stock_code.default_value }
{else}
{assign var="value" value=$fields.stock_code.value }
{/if}
{if isTypeNumber($fields.stock_code.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.stock_code.name}' id='{$fields.stock_code.name}' size='30' maxlength='30' value='{$value}' title='' tabindex='163'  /> 

</td>
</tr>
<tr>
<td valign="top" id='date_issue_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DATE_ISSUE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_date_issue_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.date_issue.value }
<input autocomplete="off" type="text" name="{$fields.date_issue.name}" id="{$fields.date_issue.name}" value="{$date_value}" title=''  tabindex='164' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.date_issue.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.date_issue.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.date_issue.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
<td valign="top" id='charter_capital_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CHARTER_CAPITAL' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_charter_capital_field' >
{counter name="panelFieldCount"}

<select name="{$fields.charter_capital.name}" id="{$fields.charter_capital.name}" title='' tabindex="165"  >
{if isset($fields.charter_capital.value) && $fields.charter_capital.value != ''}
{html_options options=$fields.charter_capital.options selected=$fields.charter_capital.value}
{else}
{html_options options=$fields.charter_capital.options selected=$fields.charter_capital.default}
{/if}
</select>
</td>
</tr>
<tr>
<td valign="top" id='registration_place_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_REGISTRATION_PLACE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_registration_place_field' >
{counter name="panelFieldCount"}

{if strlen($fields.registration_place.value) <= 0}
{assign var="value" value=$fields.registration_place.default_value }
{else}
{assign var="value" value=$fields.registration_place.value }
{/if}
{if isTypeNumber($fields.registration_place.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.registration_place.name}' id='{$fields.registration_place.name}' size='30' maxlength='100' value='{$value}' title='' tabindex='166'  /> 

</td>
<td valign="top" id='on_incorporation_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ON_INCORPORATION' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_on_incorporation_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.on_incorporation.value }
<input autocomplete="off" type="text" name="{$fields.on_incorporation.name}" id="{$fields.on_incorporation.name}" value="{$date_value}" title=''  tabindex='167' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.on_incorporation.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.on_incorporation.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.on_incorporation.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
</tr>
<tr>
<td valign="top" id='bank_account_number_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_BANK_ACCOUNT_NUMBER' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" colspan='3' id='_bank_account_number_field' >
{counter name="panelFieldCount"}

{if strlen($fields.bank_account_number.value) <= 0}
{assign var="value" value=$fields.bank_account_number.default_value }
{else}
{assign var="value" value=$fields.bank_account_number.value }
{/if}
{if isTypeNumber($fields.bank_account_number.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.bank_account_number.name}' id='{$fields.bank_account_number.name}' size='30' maxlength='50' value='{$value}' title='' tabindex='168'  /> 

</td>
</tr>
<tr>
<td valign="top" id='bank_account_owner_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_BANK_ACCOUNT_OWNER' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_bank_account_owner_field' >
{counter name="panelFieldCount"}

{if strlen($fields.bank_account_owner.value) <= 0}
{assign var="value" value=$fields.bank_account_owner.default_value }
{else}
{assign var="value" value=$fields.bank_account_owner.value }
{/if}
{if isTypeNumber($fields.bank_account_owner.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.bank_account_owner.name}' id='{$fields.bank_account_owner.name}' size='30' maxlength='200' value='{$value}' title='' tabindex='169'  /> 

</td>
<td valign="top" id='bank_name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_BANK_NAME' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_bank_name_field' >
{counter name="panelFieldCount"}

{if strlen($fields.bank_name.value) <= 0}
{assign var="value" value=$fields.bank_name.default_value }
{else}
{assign var="value" value=$fields.bank_name.value }
{/if}
{if isTypeNumber($fields.bank_name.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.bank_name.name}' id='{$fields.bank_name.name}' size='30' maxlength='250' value='{$value}' title='' tabindex='170'  /> 

</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_BANK_INFORMATION").style.display='none';</script>
{/if}
<div id="LBL_BILLING_CONTRACT_INFO" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_BILLING_CONTRACT_INFO');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_BILLING_CONTRACT_INFO_IMG" border="0" />
{incomCRM_translate label='LBL_BILLING_CONTRACT_INFO' module='Accounts_Leads'}
</a>
</h4>
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_BILLING_CONTRACT_INFO_GROUP" style="display:none">
<table width="100%" border="0" cellspacing="1" cellpadding="0">
<tr>
<td valign="top" id='billing_representation_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_BILLING_REPRESENTATION' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_billing_representation_field' >
{counter name="panelFieldCount"}

{if strlen($fields.billing_representation.value) <= 0}
{assign var="value" value=$fields.billing_representation.default_value }
{else}
{assign var="value" value=$fields.billing_representation.value }
{/if}
{if isTypeNumber($fields.billing_representation.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.billing_representation.name}' id='{$fields.billing_representation.name}' size='30' maxlength='200' value='{$value}' title='' tabindex='171'  /> 

</td>
<td valign="top" id='billing_position_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_BILLING_POSITION' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_billing_position_field' >
{counter name="panelFieldCount"}

{if strlen($fields.billing_position.value) <= 0}
{assign var="value" value=$fields.billing_position.default_value }
{else}
{assign var="value" value=$fields.billing_position.value }
{/if}
{if isTypeNumber($fields.billing_position.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.billing_position.name}' id='{$fields.billing_position.name}' size='30' maxlength='100' value='{$value}' title='' tabindex='172'  /> 

</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_BILLING_CONTRACT_INFO").style.display='none';</script>
{/if}
<div id="LBL_TELESALES_WORKING_INFO" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_TELESALES_WORKING_INFO');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_TELESALES_WORKING_INFO_IMG" border="0" />
{incomCRM_translate label='LBL_TELESALES_WORKING_INFO' module='Accounts_Leads'}
</a>
</h4>
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_TELESALES_WORKING_INFO_GROUP" style="display:none">
<table width="100%" border="0" cellspacing="1" cellpadding="0">
<tr>
<td valign="top" id='purchase_date_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_PURCHASE_DATE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_purchase_date_field' >
{counter name="panelFieldCount"}

<script type="text/javascript" src="include/incomCRMFields/Fields/Datetimecombo/Datetimecombo.js"></script>
<table border="0" cellpadding="0" cellspacing="0" class="datetimecombo">
<tr>
<td nowrap>
<input autocomplete="off" type="text" id="{$fields.purchase_date.name}_date" value="{$fields[$fields.purchase_date.name].value}" size="11" maxlength="10" title='' tabindex="173" onblur="combo_{$fields.purchase_date.name}.update(); " class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.purchase_date.name}_trigger" align="absmiddle" />&nbsp;
</td>
<td nowrap>
<div id="{$fields.purchase_date.name}_time_section" class="datetime-section"></div>
</td>
</tr>
</table>
<input type="hidden" id="{$fields.purchase_date.name}" name="{$fields.purchase_date.name}" value="{$fields[$fields.purchase_date.name].value}" />
<script type="text/javascript">
var combo_{$fields.purchase_date.name} = new Datetimecombo("{$fields[$fields.purchase_date.name].value}", "{$fields.purchase_date.name}", "{$TIME_FORMAT}", '173', '', '{$fields[$fields.purchase_date.name_flag].value|default:0}', '1', '1'); 
//Render the remaining widget fields
text = combo_{$fields.purchase_date.name}.html('');
document.getElementById('{$fields.purchase_date.name}_time_section').innerHTML = text;

//Call eval on the update function to handle updates to calendar picker object
eval(combo_{$fields.purchase_date.name}.jsscript(''));
</script>
<script type="text/javascript">
function update_{$fields.purchase_date.name}_available() {ldelim}
	YAHOO.util.Event.onAvailable("{$fields.purchase_date.name}_date", this.handleOnAvailable, this); 
{rdelim}

update_{$fields.purchase_date.name}_available.prototype.handleOnAvailable = function(me) {ldelim}
		Calendar.setup ({ldelim}
		onUpdate : update_{$fields.purchase_date.name},
		inputField : "{$fields.purchase_date.name}_date",
		ifFormat : "{$CALENDAR_FORMAT}",
		daFormat : "{$CALENDAR_FORMAT}",
		button : "{$fields.purchase_date.name}_trigger",
		singleClick : true,
		step : 1,
		weekNumbers:false
	{rdelim});
	
	//Call update for first time to round hours and minute values
	combo_{$fields.purchase_date.name}.update();
{rdelim}

var obj_{$fields.purchase_date.name} = new update_{$fields.purchase_date.name}_available(); 
</script>

</td>
<td valign="top" id='support_user_name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_SUPPORT_USER_NAME' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_support_user_name_field' >
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{/if}<input type="hidden" name="{$fields.support_user_name.id_name}" id="{$fields.support_user_name.id_name}" value="{$fields.support_user_id.value}" />
<input type="text" name="{$fields.support_user_name.name}" class="sqsEnabled" tabindex="174" id="{$fields.support_user_name.name}" size="16" value="{$fields.support_user_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{/if}
<input type="button" name="btn_{$fields.support_user_name.name}" id="btn_{$fields.support_user_name.name}" tabindex="174" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.support_user_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"EditView","field_to_name_array":{"id":"support_user_id","user_name":"support_user_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.support_user_name.name}" id="btn_clr_{$fields.support_user_name.name}" tabindex="174" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.support_user_name.name}.value=""; this.form.{$fields.support_user_name.id_name}.value=""; this.form.{$fields.support_user_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
{if !empty($isSmartView)}</div>{/if}<script type="text/javascript">enableQS(false);</script>
</td>
</tr>
<tr>
<td valign="top" id='ts_customer_need_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_TS_CUSTOMER_NEED' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_ts_customer_need_field' >
{counter name="panelFieldCount"}
<input type="hidden" id="{$fields.ts_customer_need.name}_multiselect" name="{$fields.ts_customer_need.name}_multiselect" value="true" />
{multienum_to_array string=$fields.ts_customer_need.value default=$fields.ts_customer_need.default assign="values"}
<div style="" id="{$fields.ts_customer_need.name}_multi_grp" class="multi-enum-grp multi-enum-grp-default">
{counter name="rowCount" start=0 print=false assign="rowCount"}
{foreach from=$fields.ts_customer_need.options item=option key=value}
{if $value}
<label id="_grp_{if isset($fields.ts_customer_need.groupBy) and isset($fields.ts_customer_need.groupBy[$value])}{ $fields.ts_customer_need.groupBy[$value]}{/if}" style="white-space: nowrap">
<input type="checkbox" id="{$fields.ts_customer_need.name}_checkbox{$rowCount}" name="{$fields.ts_customer_need.name}[]" value="{$value}" {if in_array(strval($value), $values)}checked="checked"{/if}  /> {$option}
</label>
{counter name="rowCount"}
{/if}
{/foreach}
</div>
</td>
<td valign="top" id='telesales_status_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_TELESALES_STATUS' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_telesales_status_field' >
{counter name="panelFieldCount"}

<select name="{$fields.telesales_status.name}" id="{$fields.telesales_status.name}" title='' tabindex="176"  >
{if isset($fields.telesales_status.value) && $fields.telesales_status.value != ''}
{html_options options=$fields.telesales_status.options selected=$fields.telesales_status.value}
{else}
{html_options options=$fields.telesales_status.options selected=$fields.telesales_status.default}
{/if}
</select>
</td>
</tr>
<tr>
<td valign="top" id='ts_products_need_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_TS_PRODUCTS_NEED' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_ts_products_need_field' >
{counter name="panelFieldCount"}
<input type="hidden" id="{$fields.ts_products_need.name}_multiselect" name="{$fields.ts_products_need.name}_multiselect" value="true" />
{multienum_to_array string=$fields.ts_products_need.value default=$fields.ts_products_need.default assign="values"}
<div style="" id="{$fields.ts_products_need.name}_multi_grp" class="multi-enum-grp multi-enum-grp-default">
{counter name="rowCount" start=0 print=false assign="rowCount"}
{foreach from=$fields.ts_products_need.options item=option key=value}
{if $value}
<label id="_grp_{if isset($fields.ts_products_need.groupBy) and isset($fields.ts_products_need.groupBy[$value])}{ $fields.ts_products_need.groupBy[$value]}{/if}" style="white-space: nowrap">
<input type="checkbox" id="{$fields.ts_products_need.name}_checkbox{$rowCount}" name="{$fields.ts_products_need.name}[]" value="{$value}" {if in_array(strval($value), $values)}checked="checked"{/if}  /> {$option}
</label>
{counter name="rowCount"}
{/if}
{/foreach}
</div>
</td>
<td valign="top" id='ts_notes_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_TS_NOTES' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_ts_notes_field' >
{counter name="panelFieldCount"}

{if empty($fields.ts_notes.value)}
{assign var="value" value=$fields.ts_notes.default_value }
{else}
{assign var="value" value=$fields.ts_notes.value }
{/if}
<textarea id="{$fields.ts_notes.name}" name="{$fields.ts_notes.name}" rows="2" cols="40" title='' tabindex="178"  >{$value}</textarea>
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_TELESALES_WORKING_INFO").style.display='none';</script>
{/if}

<div class="buttons" style="margin-top:15px;">
{if $bean->aclAccess("save")}<input title="{$APP.LBL_SAVE_BUTTON_TITLE}" accessKey="{$APP.LBL_SAVE_BUTTON_KEY}" class="button save" onclick="{if $isDuplicate}this.form.return_id.value=''; {/if}this.form.action.value='Save'; return check_form('EditView');" type="submit" name="button" value="{$APP.LBL_SAVE_BUTTON_LABEL}" />{/if} 
{if !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($fields.id.value))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {elseif !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($smarty.request.return_id))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {else}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='index'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {/if}
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=Accounts_Leads", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</div>
</form>
{$set_focus_block}
<!-- Begin Meta-Data Javascript -->
<script type="text/javascript">
{if !empty($fields.location_city.value)}
changeParentSelectedOption(document.{$form_name}.location_city, document.{$form_name}.location_district, 'location_district_dom');
{/if}
{literal}
function deleteAttachmentCallBack(text) {
	if( text == "true" || text == -1 ) {
		if( text == "true" ) ajaxStatus.hideStatus();
		document.getElementById("new_attachment").style.display = "";
		document.getElementById("old_attachment").innerHTML = "";
	}
	else {
		document.getElementById("new_attachment").style.display = "none";
		ajaxStatus.flashStatus(incomCRM.language.get("app_strings", "ERR_ATT_REMOVING_ATTACHMENT"), 2000);
	}
}
{/literal}
</script>
<!-- End Meta-Data Javascript -->
</section>{literal}
<script type="text/javascript">
addToValidate('EditView', 'id', 'id', false, '{/literal}{incomCRM_translate label='LBL_ID' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'date_entered_date', 'date', false, 'Ngày tạo' );
addToValidate('EditView', 'date_modified_date', 'date', false, 'Ngày sửa' );
addToValidate('EditView', 'modified_user_id', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_ID' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'modified_by_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_NAME' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'created_by', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_CREATED_ID' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'created_by_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_CREATED' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'deleted', 'bool', false, '{/literal}{incomCRM_translate label='LBL_DELETED' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'name', 'name', true, '{/literal}{incomCRM_translate label='LBL_NAME' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'description', 'text', false, '{/literal}{incomCRM_translate label='LBL_DESCRIPTION' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'branch_id', 'enum', false, '{/literal}{incomCRM_translate label='LBL_BRANCH_ID' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'department_id', 'enum', false, '{/literal}{incomCRM_translate label='LBL_DEPARTMENT_ID' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'assigned_user_id', 'relate', false, '{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO_ID' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'assigned_user_name', 'relate', true, '{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'account_type', 'enum', true, '{/literal}{incomCRM_translate label='LBL_TYPE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'industry', 'enum', false, '{/literal}{incomCRM_translate label='LBL_INDUSTRY' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'annual_revenue', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ANNUAL_REVENUE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'billing_address_street', 'text', true, '{/literal}{incomCRM_translate label='LBL_BILLING_ADDRESS_STREET' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'billing_address_street_2', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_BILLING_ADDRESS_STREET_2' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'billing_address_street_3', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_BILLING_ADDRESS_STREET_3' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'billing_address_street_4', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_BILLING_ADDRESS_STREET_4' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'billing_address_city', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_BILLING_ADDRESS_CITY' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'billing_address_state', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_BILLING_ADDRESS_STATE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'billing_address_postalcode', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_BILLING_ADDRESS_POSTALCODE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'billing_address_country', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_BILLING_ADDRESS_COUNTRY' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'rating', 'enum', false, '{/literal}{incomCRM_translate label='LBL_RATING' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'phone_office', 'phone', true, '{/literal}{incomCRM_translate label='LBL_PHONE_OFFICE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'phone_alternate', 'phone', false, '{/literal}{incomCRM_translate label='LBL_PHONE_ALT' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'phone_fax', 'phone', false, '{/literal}{incomCRM_translate label='LBL_FAX' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'website', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_WEBSITE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'ownership', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_OWNERSHIP' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'employees', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_EMPLOYEES' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'ticker_symbol', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TICKER_SYMBOL' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'shipping_address_street', 'text', false, '{/literal}{incomCRM_translate label='LBL_SHIPPING_ADDRESS_STREET' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'shipping_address_street_2', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_SHIPPING_ADDRESS_STREET_2' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'shipping_address_street_3', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_SHIPPING_ADDRESS_STREET_3' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'shipping_address_street_4', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_SHIPPING_ADDRESS_STREET_4' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'shipping_address_city', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_SHIPPING_ADDRESS_CITY' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'shipping_address_state', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_SHIPPING_ADDRESS_STATE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'shipping_address_postalcode', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_SHIPPING_ADDRESS_POSTALCODE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'shipping_address_country', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_SHIPPING_ADDRESS_COUNTRY' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'email1', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_EMAIL' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'email2', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_OTHER_EMAIL' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'overdue_debts', 'float', false, '{/literal}{incomCRM_translate label='LBL_OVERDUE_DEBTS' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'sic_code', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_SIC_CODE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'parent_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_PARENT_ACCOUNT_ID' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'parent_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_MEMBER_OF' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'email_opt_out', 'bool', false, '{/literal}{incomCRM_translate label='LBL_EMAIL_OPT_OUT' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'invalid_email', 'bool', false, '{/literal}{incomCRM_translate label='LBL_INVALID_EMAIL' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'campaign_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_CAMPAIGN_ID' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'campaign_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_CAMPAIGN' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'filename', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_FILENAME' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'file_mime_type', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_FILE_MIME_TYPE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'file_url', 'function', false, '{/literal}{incomCRM_translate label='LBL_FILE_URL' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'cycle_method', 'enum', false, '{/literal}{incomCRM_translate label='LBL_CYCLE_METHOD' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'receivable_debts', 'float', false, '{/literal}{incomCRM_translate label='LBL_RECEIVABLE_DEBTS' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'date_debt_expired', 'date', false, '{/literal}{incomCRM_translate label='LBL_DATE_DEBT_EXPIRED' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'care_level', 'enum', false, '{/literal}{incomCRM_translate label='LBL_CARE_LEVEL' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'buying_level', 'enum', false, '{/literal}{incomCRM_translate label='LBL_BUYING_LEVEL' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'saleplan_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_ID' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'plan_month', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_PLAN_MONTH' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'plan_year', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_PLAN_YEAR' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'plan_user', 'id', false, '{/literal}{incomCRM_translate label='LBL_ID' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'plan_activities', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_PLAN_ACTIVITIES' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'plan_opportunities', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_PLAN_OPPORTUNITIES' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'sp_notes', 'text', false, '{/literal}{incomCRM_translate label='LBL_PLAN_NOTES' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'sp_note_histories', 'text', false, '{/literal}{incomCRM_translate label='LBL_PLAN_NOTE_HISTORIES' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'plan_opp_expected_amount', 'float', false, '{/literal}{incomCRM_translate label='LBL_PLAN_OPP_EXPECTED_AMOUNT' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'plan_opp_actual_amount', 'float', false, '{/literal}{incomCRM_translate label='LBL_PLAN_OPP_ACTUAL_AMOUNT' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'plan_opp_amount_percent', 'float', false, '{/literal}{incomCRM_translate label='LBL_PLAN_OPP_AMOUNT_PERCENT' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'plan_total_expected_amount', 'float', false, '{/literal}{incomCRM_translate label='LBL_PLAN_TOTAL_EXPECTED_AMOUNT' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'plan_total_actual_amount', 'float', false, '{/literal}{incomCRM_translate label='LBL_PLAN_TOTAL_ACTUAL_AMOUNT' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'plan_total_amount_percent', 'float', false, '{/literal}{incomCRM_translate label='LBL_PLAN_OPP_AMOUNT_PERCENT' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'plan_opp_expected_output', 'float', false, '{/literal}{incomCRM_translate label='LBL_PLAN_OPP_EXPECTED_OUTPUT' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'plan_opp_actual_output', 'float', false, '{/literal}{incomCRM_translate label='LBL_PLAN_OPP_ACTUAL_OUTPUT' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'plan_opp_output_percent', 'float', false, '{/literal}{incomCRM_translate label='LBL_PLAN_OPP_AMOUNT_PERCENT' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'plan_total_expected_output', 'float', false, '{/literal}{incomCRM_translate label='LBL_PLAN_TOTAL_EXPECTED_OUTPUT' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'plan_total_actual_output', 'float', false, '{/literal}{incomCRM_translate label='LBL_PLAN_TOTAL_ACTUAL_OUTPUT' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'plan_total_output_percent', 'float', false, '{/literal}{incomCRM_translate label='LBL_PLAN_OPP_AMOUNT_PERCENT' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'account_group_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_GROUP_ID' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'account_group_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_GROUP_NAME' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'telesales_saved', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TELESALES' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'ts_customer_need[]', 'enum', false, '{/literal}{incomCRM_translate label='LBL_TS_CUSTOMER_NEED' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'ts_products_need[]', 'enum', false, '{/literal}{incomCRM_translate label='LBL_TS_PRODUCTS_NEED' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'ts_need_month[]', 'enum', false, '{/literal}{incomCRM_translate label='LBL_TS_NEED_MONTH' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'ts_note_other', 'text', false, '{/literal}{incomCRM_translate label='LBL_TS_NOTE_OTHER' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'ts_notes', 'text', false, '{/literal}{incomCRM_translate label='LBL_TS_NOTES' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'customer_demand', 'text', false, '{/literal}{incomCRM_translate label='LBL_CUSTOMER_DEMAND' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'telesales_status', 'enum', false, '{/literal}{incomCRM_translate label='LBL_TELESALES_STATUS' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'check_meets[]', 'enum', false, '{/literal}{incomCRM_translate label='LBL_CHECK_MEETS' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'debt_norm_min', 'double', false, '{/literal}{incomCRM_translate label='LBL_DEBT_NORM_MIN' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'debt_norm_max', 'double', false, '{/literal}{incomCRM_translate label='LBL_DEBT_NORM_MAX' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'debt_delay_day', 'int', false, '{/literal}{incomCRM_translate label='LBL_DEBT_DELAY_DAY' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'is_locked', 'bool', false, '{/literal}{incomCRM_translate label='LBL_IS_LOCKED' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'sales_line_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_SALES_LINE_ID' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'sales_line_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_SALES_LINE_NAME' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'line_order', 'int', false, '{/literal}{incomCRM_translate label='LBL_LINE_ORDER' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'order_discount', 'double', false, '{/literal}{incomCRM_translate label='LBL_ORDER_DISCOUNT' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'unit_management', 'enum', false, '{/literal}{incomCRM_translate label='LBL_UNIT_MANAGEMENT' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'account_status', 'enum', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_STATUS' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'account_code', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_CODE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'code_order', 'int', false, '{/literal}{incomCRM_translate label='LBL_CODE_ORDER' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'reference_code', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_REFERENCE_CODE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'english_name', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ENGLISH_NAME' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'abbreviation', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ABBREVIATION' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'tax_code', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TAX_CODE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'registration_number', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_REGISTRATION_NUMBER' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'stock_code', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_STOCK_CODE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'date_issue', 'date', false, '{/literal}{incomCRM_translate label='LBL_DATE_ISSUE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'registration_place', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_REGISTRATION_PLACE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'bank_account_number', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_BANK_ACCOUNT_NUMBER' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'bank_account_owner', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_BANK_ACCOUNT_OWNER' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'bank_name', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_BANK_NAME' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'charter_capital', 'enum', false, '{/literal}{incomCRM_translate label='LBL_CHARTER_CAPITAL' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'on_incorporation', 'date', false, '{/literal}{incomCRM_translate label='LBL_ON_INCORPORATION' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'department_manager', 'enum', true, '{/literal}{incomCRM_translate label='LBL_DEPARTMENT_MANAGER' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'transaction_level', 'enum', false, '{/literal}{incomCRM_translate label='LBL_TRANSACTION_LEVEL' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'sector_vertical', 'enum', false, '{/literal}{incomCRM_translate label='LBL_SECTOR_VERTICAL' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'account_scope', 'enum', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_SCOPE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'lead_source', 'enum', false, '{/literal}{incomCRM_translate label='LBL_LEAD_SOURCE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'purchase_date_date', 'date', false, 'Ngày hẹn' );
addToValidate('EditView', 'periodic_purchase', 'enum', false, '{/literal}{incomCRM_translate label='LBL_PERIODIC_PURCHASE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'location_area', 'enum', false, '{/literal}{incomCRM_translate label='LBL_LOCATION_AREA' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'location_city', 'enum', false, '{/literal}{incomCRM_translate label='LBL_LOCATION_CITY' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'location_district', 'enum', false, '{/literal}{incomCRM_translate label='LBL_LOCATION_DISTRICT' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'rate_note', 'text', false, '{/literal}{incomCRM_translate label='LBL_RATE_NOTE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'marking', 'bool', false, '{/literal}{incomCRM_translate label='LBL_MARKING' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'mark_note', 'text', false, '{/literal}{incomCRM_translate label='LBL_MARK_NOTE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'primary_contact_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_PRIMARY_CONTACT_ID' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'primary_contact_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_PRIMARY_CONTACT_NAME' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'primary_contact_position', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_POSITION' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'primary_contact_department', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_DEPARTMENT' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'primary_contact_phone_work', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_PHONE_WORK' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'primary_contact_phone_mobile', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_PHONE_MOBILE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'primary_contact_phone_home', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_PRIMARY_CONTACT_PHONE_HOME' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'primary_contact_phone_fax', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_PHONE_FAX' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'primary_contact_email', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_EMAIL' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'primary_contact_birthdate', 'date', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_BIRTHDATE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'primary_contact_address', 'text', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_ADDRESS' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'primary_contact_note', 'text', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_NOTE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'secondary_contact_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_SECONDARY_CONTACT_ID' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'secondary_contact_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_SECONDARY_CONTACT_NAME' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'secondary_contact_position', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_POSITION' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'secondary_contact_department', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_DEPARTMENT' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'secondary_contact_phone_work', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_PHONE_WORK' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'secondary_contact_phone_mobile', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_PHONE_MOBILE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'secondary_contact_phone_home', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_SECONDARY_CONTACT_PHONE_HOME' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'secondary_contact_phone_fax', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_PHONE_FAX' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'secondary_contact_email', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_EMAIL' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'secondary_contact_birthdate', 'date', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_BIRTHDATE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'secondary_contact_address', 'text', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_ADDRESS' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'secondary_contact_note', 'text', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_NOTE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'business_field[]', 'enum', false, '{/literal}{incomCRM_translate label='LBL_BUSINESS_FIELD' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'responsible', 'text', false, '{/literal}{incomCRM_translate label='LBL_RESPONSIBLE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'company_info', 'text', false, '{/literal}{incomCRM_translate label='LBL_COMPANY_INFO' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'import_mark', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_IMPORT_MARK' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'billing_representation', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_BILLING_REPRESENTATION' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'billing_position', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_BILLING_POSITION' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'location_ward', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_LOCATION_WARD' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'location_hamlet', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_LOCATION_HAMLET' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'latlng', 'text', false, '{/literal}{incomCRM_translate label='LBL_LATLNG' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'debt_user_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_DEBT_USER_ID' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'debt_user_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_DEBT_USER_NAME' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'support_user_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_SUPPORT_USER_ID' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'support_user_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_SUPPORT_USER_NAME' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'products', 'enum', false, '{/literal}{incomCRM_translate label='LBL_PRODUCTS' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'date_assigned_date', 'date', false, 'Ngày gán sau cùng' );
addToValidate('EditView', 'old_user_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_ASSIGNED_USER_NAME' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'decentralization', 'enum', false, '{/literal}{incomCRM_translate label='LBL_DECENTRALIZATION' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'norms_debts', 'double', false, '{/literal}{incomCRM_translate label='LBL_NORMS_DEBTS' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'full_name', 'text', false, '{/literal}{incomCRM_translate label='LBL_FULL_NAME' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'name_phone', 'text', false, '{/literal}{incomCRM_translate label='LBL_FULL_NAME' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'membership_card', 'enum', false, '{/literal}{incomCRM_translate label='LBL_MEMBERSHIP_CARD' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'point_total', 'double', false, '{/literal}{incomCRM_translate label='LBL_POINT_TOTAL' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'point_used', 'double', false, '{/literal}{incomCRM_translate label='LBL_POINT_USED' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'point_current', 'double', false, '{/literal}{incomCRM_translate label='LBL_POINT_CURRENT' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'user_shared', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_PROJECT_TEAMS' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'account_warn', 'enum', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_WARN' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'use_feature', 'enum', false, '{/literal}{incomCRM_translate label='LBL_USE_FEATURE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'reason_feature', 'enum', false, '{/literal}{incomCRM_translate label='LBL_REASON_FEATURE' module='Accounts_Leads'}{literal}' );
addToValidate('EditView', 'usage_status', 'enum', false, '{/literal}{incomCRM_translate label='LBL_USAGE_STATUS' module='Accounts_Leads'}{literal}' );
addToValidateBinaryDependency('EditView', 'assigned_user_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Accounts_Leads'}{literal}{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO' module='Accounts_Leads'}{literal}', 'assigned_user_id' );
addToValidateBinaryDependency('EditView', 'parent_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Accounts_Leads'}{literal}{/literal}{incomCRM_translate label='LBL_MEMBER_OF' module='Accounts_Leads'}{literal}', 'parent_id' );
addToValidateBinaryDependency('EditView', 'account_group_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Accounts_Leads'}{literal}{/literal}{incomCRM_translate label='LBL_ACCOUNT_GROUP_NAME' module='Accounts_Leads'}{literal}', 'account_group_id' );
addToValidateBinaryDependency('EditView', 'debt_user_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Accounts_Leads'}{literal}{/literal}{incomCRM_translate label='LBL_DEBT_USER_NAME' module='Accounts_Leads'}{literal}', 'debt_user_id' );
addToValidateBinaryDependency('EditView', 'support_user_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Accounts_Leads'}{literal}{/literal}{incomCRM_translate label='LBL_SUPPORT_USER_NAME' module='Accounts_Leads'}{literal}', 'support_user_id' );
</script>
<script type="text/javascript">if(typeof sqs_objects == 'undefined'){var sqs_objects = new Array;}
sqs_objects['EditView_account_group_name'] = {"form":"EditView","method":"query","modules":["Account_Groups"],"group":"or","field_list":["name","id"],"populate_list":["account_group_name","account_group_id"],"required_list":["parent_id"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['EditView_assigned_user_name'] = {"form":"EditView","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["assigned_user_name","assigned_user_id"],"required_list":["assigned_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['EditView_parent_name'] = {"form":"EditView","method":"query","modules":["Accounts"],"group":"or","field_list":["name","id"],"populate_list":["EditView_parent_name","parent_id"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"required_list":["parent_id"],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['EditView_debt_user_name'] = {"form":"EditView","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["debt_user_name","debt_user_id"],"required_list":["debt_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['EditView_support_user_name'] = {"form":"EditView","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["support_user_name","support_user_id"],"required_list":["support_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
</script>{/literal}
