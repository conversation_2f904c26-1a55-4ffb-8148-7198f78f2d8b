

{math equation="floor(left / right)"
	left=12
		right=$templateMeta.maxColumns
			assign=colClass
	}

<section class="container2 search-form ml-0 search_form_basic">
	<div class="row">
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12">
			<div class="row">
				<label class="col-5 pr-0" for="code_basic">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_CODE' module='Cycle_Plans'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.code_basic.value) <= 0}
	{assign var="value" value=$fields.code_basic.default_value }
{else}
	{assign var="value" value=$fields.code_basic.value }
{/if}
{if isTypeNumber($fields.code_basic.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.code_basic.name}' id='{$fields.code_basic.name}' size='30' maxlength='50' value='{$value}' title='' tabindex=''  /> 
									</div>
			</div>
		</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12">
			<div class="row">
				<label class="col-5 pr-0" for="plan_year_basic">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_PLAN_YEAR' module='Cycle_Plans'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="plan_year_basic[]" id="{$fields.plan_year_basic.name}" size="1"   >
{html_options options=$fields.plan_year_basic.options selected=$fields.plan_year_basic.value}
</select>
									</div>
			</div>
		</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12">
			<div class="row">
				<label class="col-5 pr-0" for="plan_month_basic">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_PLAN_MONTH' module='Cycle_Plans'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="plan_month_basic[]" id="{$fields.plan_month_basic.name}" size="1"   >
{html_options options=$fields.plan_month_basic.options selected=$fields.plan_month_basic.value}
</select>
									</div>
			</div>
		</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12">
			<div class="row">
				<label class="col-5 pr-0" for="assigned_user_id_basic">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='Cycle_Plans'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="assigned_user_id_basic[]" id="{$fields.assigned_user_id_basic.name}" size="1"   >
{html_options options=$fields.assigned_user_id_basic.options selected=$fields.assigned_user_id_basic.value}
</select>
									</div>
			</div>
		</div>
	</div>
</section>

{if $HAS_ADVANCED_SEARCH}
<table cellspacing="0" cellpadding="0" border="0" class="mt-10">
<tr>
	<td scope="row" nowrap="nowrap" class="pt-10">
	<a style="font-size:10pt; text-decoration:none;" onclick="incomCRM.searchForm.searchFormSelect('{$module}|advanced_search','{$module}|basic_search')" href="#">[ {$APP.LNK_ADVANCED_SEARCH} ]</a>
	</td>
</tr>
</table>
{/if}

{literal}<script type="text/javascript">if(typeof sqs_objects == 'undefined'){var sqs_objects = new Array;}
sqs_objects['search_form_modified_by_name_basic'] = {"form":"search_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["modified_by_name_basic","modified_user_id_basic"],"required_list":["modified_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['search_form_created_by_name_basic'] = {"form":"search_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["created_by_name_basic","created_by_basic"],"required_list":["created_by"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['search_form_assigned_user_name_basic'] = {"form":"search_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["assigned_user_name_basic","assigned_user_id_basic"],"required_list":["assigned_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
</script>{/literal}