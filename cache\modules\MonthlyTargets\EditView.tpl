
<section id="EditViewViewBody" class="view-module-{$module|strtolower}">

<form action="index.php" method="POST" name="{$form_name}" id="{$form_id}" class="_edit-{$instanceName}" {$enctype}>
<input type="hidden" name="module" value="{$module}" />
<input type="hidden" name="action" id="action" />
{if isset($smarty.request.isDuplicate) && $smarty.request.isDuplicate eq "true"}
<input type="hidden" name="record" id="record" value="" />
<input type="hidden" name="duplicateSave" value="true" />
{else}
<input type="hidden" name="record" id="record" value="{$fields.id.value}" />
{/if}
<input type="hidden" name="return_module" value="{$smarty.request.return_module}" />
<input type="hidden" name="return_action" value="{if $smarty.request.return_action eq 'LoadTabSubpanels'}DetailView{else}{$smarty.request.return_action}{/if}" />
<input type="hidden" name="return_id" value="{$smarty.request.return_id}" />
<input type="hidden" name="isDuplicate" value="false" />
<input type="hidden" name="module_tab" /> 
<input type="hidden" name="contact_role" />
{if !empty($smarty.request.return_module)}
<input type="hidden" name="relate_to" value="{if $smarty.request.return_relationship}{$smarty.request.return_relationship}{else}{$smarty.request.return_module}{/if}" />
<input type="hidden" name="relate_id" value="{$smarty.request.return_id}" />
{/if}
<input type="hidden" name="offset" value="{$offset}" />
{if empty($fields.id.value)}
<input type="hidden" name="_notify_random" value="{php}echo create_guid();{/php}" />
{/if}
<input type="hidden" name="isFromEditView" value="{$module}" />
<input type="hidden" name="total_per" id="total_per" value="{$fields.total_per.value}" />
<table width="100%" cellpadding="0" cellspacing="0" border="0">
<tr>
<td class="buttons">
{if $bean->aclAccess("save")}<input title="{$APP.LBL_SAVE_BUTTON_TITLE}" accessKey="{$APP.LBL_SAVE_BUTTON_KEY}" class="button save" onclick="{if $isDuplicate}this.form.return_id.value=''; {/if}this.form.action.value='Save'; return check_form('EditView');" type="submit" name="button" value="{$APP.LBL_SAVE_BUTTON_LABEL}" />{/if} 
{if !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($fields.id.value))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {elseif !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($smarty.request.return_id))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {else}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='index'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {/if}
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=MonthlyTargets", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</td>
</tr>
</table>
{incomCRM_include include=$includes}
<div id="DEFAULT" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="DEFAULT_GROUP" style="display:block">
<table width="100%" border="0" cellspacing="1" cellpadding="0">
<tr>
<td valign="top" id='mm_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_MM' module='MonthlyTargets'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_mm_field' >
{counter name="panelFieldCount"}

<select name="{$fields.mm.name}" id="{$fields.mm.name}" title='' tabindex="100"  >
{if isset($fields.mm.value) && $fields.mm.value != ''}
{html_options options=$fields.mm.options selected=$fields.mm.value}
{else}
{html_options options=$fields.mm.options selected=$fields.mm.default}
{/if}
</select>
</td>
<td valign="top" id='assigned_user_name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='MonthlyTargets'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_assigned_user_name_field' >
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{/if}<input type="hidden" name="{$fields.assigned_user_name.id_name}" id="{$fields.assigned_user_name.id_name}" value="{$fields.assigned_user_id.value}" />
<input type="text" name="{$fields.assigned_user_name.name}" class="sqsEnabled" tabindex="101" id="{$fields.assigned_user_name.name}" size="16" value="{$fields.assigned_user_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{/if}
<input type="button" name="btn_{$fields.assigned_user_name.name}" id="btn_{$fields.assigned_user_name.name}" tabindex="101" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.assigned_user_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"EditView","field_to_name_array":{"id":"assigned_user_id","user_name":"assigned_user_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.assigned_user_name.name}" id="btn_clr_{$fields.assigned_user_name.name}" tabindex="101" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.assigned_user_name.name}.value=""; this.form.{$fields.assigned_user_name.id_name}.value=""; this.form.{$fields.assigned_user_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
{if !empty($isSmartView)}</div>{/if}<script type="text/javascript">enableQS(false);</script>
</td>
</tr>
<tr>
<td valign="top" id='yy_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_YY' module='MonthlyTargets'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_yy_field' >
{counter name="panelFieldCount"}

<select name="{$fields.yy.name}" id="{$fields.yy.name}" title='' tabindex="102"  >
{if isset($fields.yy.value) && $fields.yy.value != ''}
{html_options options=$fields.yy.options selected=$fields.yy.value}
{else}
{html_options options=$fields.yy.options selected=$fields.yy.default}
{/if}
</select>
</td>
<td valign="top" id='description_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DESCRIPTION' module='MonthlyTargets'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_description_field' >
{counter name="panelFieldCount"}

{if empty($fields.description.value)}
{assign var="value" value=$fields.description.default_value }
{else}
{assign var="value" value=$fields.description.value }
{/if}
<textarea id="{$fields.description.name}" name="{$fields.description.name}" rows="3" cols="40" title='' tabindex="103"  >{$value}</textarea>
</td>
</tr>
<tr>
<td valign="top" id='revenue_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_REVENUE' module='MonthlyTargets'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_revenue_field' >
{counter name="panelFieldCount"}

{if strlen($fields.revenue.value) <= 0}
{assign var="value" value=$fields.revenue.default_value }
{else}
{assign var="value" value=$fields.revenue.value }
{/if}
{if isTypeNumber($fields.revenue.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.revenue.name}' id='{$fields.revenue.name}' size='30'  value='{$value}' title='' tabindex='104'  /> 

</td>
<td valign="top" id='ts_revenue_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_TS_REVENUE' module='MonthlyTargets'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_ts_revenue_field' >
{counter name="panelFieldCount"}
{if ($current_user->hierarchical=="QLY")}<input tabindex="105"  type="text" name="ts_revenue" id="ts_revenue" size="30" value="{$fields.ts_revenue.value}"  onblur="calcPercent('ts_revenue');"/>{else}-{/if}
</td>
</tr>
<tr>
<td valign="top" id='collection_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_COLLECTION' module='MonthlyTargets'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_collection_field' >
{counter name="panelFieldCount"}

{if strlen($fields.collection.value) <= 0}
{assign var="value" value=$fields.collection.default_value }
{else}
{assign var="value" value=$fields.collection.value }
{/if}
{if isTypeNumber($fields.collection.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.collection.name}' id='{$fields.collection.name}' size='30'  value='{$value}' title='' tabindex='106'  /> 

</td>
<td valign="top" id='ts_collection_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_TS_COLLECTION' module='MonthlyTargets'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_ts_collection_field' >
{counter name="panelFieldCount"}
{if ($current_user->hierarchical=="QLY")}<input tabindex="107"  type="text" name="ts_collection" id="ts_collection" size="30" value="{$fields.ts_collection.value}"  onblur="calcPercent('ts_collection');"/>{else}-{/if}
</td>
</tr>
<tr>
<td valign="top" id='new_shop_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_NEW_SHOP' module='MonthlyTargets'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_new_shop_field' >
{counter name="panelFieldCount"}

{if strlen($fields.new_shop.value) <= 0}
{assign var="value" value=$fields.new_shop.default_value }
{else}
{assign var="value" value=$fields.new_shop.value }
{/if}
{if isTypeNumber($fields.new_shop.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.new_shop.name}' id='{$fields.new_shop.name}' size='30' maxlength='4' value='{$value}' title='' tabindex='108'  /> 

</td>
<td valign="top" id='ts_new_shop_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_TS_NEW_SHOP' module='MonthlyTargets'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_ts_new_shop_field' >
{counter name="panelFieldCount"}
{if ($current_user->hierarchical=="QLY")}<input tabindex="109"  type="text" name="ts_new_shop" id="ts_new_shop" size="30" value="{$fields.ts_new_shop.value}"  onblur="calcPercent('ts_new_shop');"/>{else}-{/if}
</td>
</tr>
<tr>
<td valign="top" id='output_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_OUTPUT' module='MonthlyTargets'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" colspan='3' id='_output_field' >
{counter name="panelFieldCount"}

{if strlen($fields.output.value) <= 0}
{assign var="value" value=$fields.output.default_value }
{else}
{assign var="value" value=$fields.output.value }
{/if}
{if isTypeNumber($fields.output.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.output.name}' id='{$fields.output.name}' size='30'  value='{$value}' title='' tabindex='110'  /> 

</td>
</tr>
<tr>
<td valign="top" id='error_kpi_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ERROR_KPI' module='MonthlyTargets'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_error_kpi_field' >
{counter name="panelFieldCount"}

{if strlen($fields.error_kpi.value) <= 0}
{assign var="value" value=$fields.error_kpi.default_value }
{else}
{assign var="value" value=$fields.error_kpi.value }
{/if}
{if isTypeNumber($fields.error_kpi.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.error_kpi.name}' id='{$fields.error_kpi.name}' size='30' maxlength='5' value='{$value}' title='' tabindex='111'  /> 

</td>
<td valign="top" id='ts_kpi_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_TS_KPI' module='MonthlyTargets'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_ts_kpi_field' >
{counter name="panelFieldCount"}
{if ($current_user->hierarchical=="QLY")}<input tabindex="112"  type="text" name="ts_kpi" id="ts_kpi" size="30" value="{$fields.ts_kpi.value}"  onblur="calcPercent('ts_kpi');"/>{else}-{/if}
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("DEFAULT").style.display='none';</script>
{/if}

<div class="buttons" style="margin-top:15px;">
{if $bean->aclAccess("save")}<input title="{$APP.LBL_SAVE_BUTTON_TITLE}" accessKey="{$APP.LBL_SAVE_BUTTON_KEY}" class="button save" onclick="{if $isDuplicate}this.form.return_id.value=''; {/if}this.form.action.value='Save'; return check_form('EditView');" type="submit" name="button" value="{$APP.LBL_SAVE_BUTTON_LABEL}" />{/if} 
{if !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($fields.id.value))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {elseif !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($smarty.request.return_id))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {else}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='index'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {/if}
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=MonthlyTargets", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</div>
</form>
{$set_focus_block}
<!-- Begin Meta-Data Javascript -->
<script type="text/javascript">
		{literal}
		function calcProductUsers()
			{
				var tl=0;
				var rev = luloParseValue(document.getElementById("ts_revenue").value);
				var col = luloParseValue(document.getElementById("ts_collection").value);
				var shop = luloParseValue(document.getElementById("ts_new_shop").value);
				var kpi = luloParseValue(document.getElementById("ts_kpi").value);
				tl = rev + col + shop + kpi;
				document.getElementById("total_per").value = tl;
			}
			function calcPercent(name) {

				var elem = document.getElementById(name);
				var val = luloParseValue( elem.value ); 
				if(val < 0) {
					alert("% Trọng số không được nhỏ hơn 0%");
					elem.value = "";
					elem.focus();
					val = 0;
				}
				else if(val > 100){
					alert("% Trọng số không được lớn hơn 100%");
					elem.value = "";
					elem.focus();
					val = 0;
				}
				calcProductUsers();
				val = luloParseValue( document.getElementById("total_per").value);
				if( val > 100 ) {
					alert("Tổng % trọng số không được lớn hơn 100%");
					elem.value = "";
					elem.focus();
				}
			}
		{/literal}
		</script>
<!-- End Meta-Data Javascript -->
</section>{literal}
<script type="text/javascript">
addToValidate('EditView', 'id', 'id', false, '{/literal}{incomCRM_translate label='LBL_ID' module='MonthlyTargets'}{literal}' );
addToValidate('EditView', 'date_entered_date', 'date', false, 'Ngày tạo' );
addToValidate('EditView', 'date_modified_date', 'date', false, 'Ngày cập nhật' );
addToValidate('EditView', 'modified_user_id', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_ID' module='MonthlyTargets'}{literal}' );
addToValidate('EditView', 'modified_by_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_NAME' module='MonthlyTargets'}{literal}' );
addToValidate('EditView', 'created_by', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_CREATED_ID' module='MonthlyTargets'}{literal}' );
addToValidate('EditView', 'created_by_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_CREATED' module='MonthlyTargets'}{literal}' );
addToValidate('EditView', 'deleted', 'bool', false, '{/literal}{incomCRM_translate label='LBL_DELETED' module='MonthlyTargets'}{literal}' );
addToValidate('EditView', 'name', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_NAME' module='MonthlyTargets'}{literal}' );
addToValidate('EditView', 'description', 'text', false, '{/literal}{incomCRM_translate label='LBL_DESCRIPTION' module='MonthlyTargets'}{literal}' );
addToValidate('EditView', 'branch_id', 'enum', false, '{/literal}{incomCRM_translate label='LBL_BRANCH_ID' module='MonthlyTargets'}{literal}' );
addToValidate('EditView', 'department_id', 'enum', false, '{/literal}{incomCRM_translate label='LBL_DEPARTMENT_ID' module='MonthlyTargets'}{literal}' );
addToValidate('EditView', 'assigned_user_id', 'relate', false, '{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO_ID' module='MonthlyTargets'}{literal}' );
addToValidate('EditView', 'assigned_user_name', 'relate', true, '{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='MonthlyTargets'}{literal}' );
addToValidate('EditView', 'yy', 'enum', true, '{/literal}{incomCRM_translate label='LBL_YY' module='MonthlyTargets'}{literal}' );
addToValidate('EditView', 'mm', 'enum', true, '{/literal}{incomCRM_translate label='LBL_MM' module='MonthlyTargets'}{literal}' );
addToValidate('EditView', 'revenue', 'double', false, '{/literal}{incomCRM_translate label='LBL_REVENUE' module='MonthlyTargets'}{literal}' );
addToValidate('EditView', 'output', 'double', false, '{/literal}{incomCRM_translate label='LBL_OUTPUT' module='MonthlyTargets'}{literal}' );
addToValidate('EditView', 'collection', 'double', false, '{/literal}{incomCRM_translate label='LBL_COLLECTION' module='MonthlyTargets'}{literal}' );
addToValidate('EditView', 'new_shop', 'int', false, '{/literal}{incomCRM_translate label='LBL_NEW_SHOP' module='MonthlyTargets'}{literal}' );
addToValidate('EditView', 'ts_revenue', 'double', false, '{/literal}{incomCRM_translate label='LBL_TS_REVENUE' module='MonthlyTargets'}{literal}' );
addToValidate('EditView', 'ts_collection', 'double', false, '{/literal}{incomCRM_translate label='LBL_TS_COLLECTION' module='MonthlyTargets'}{literal}' );
addToValidate('EditView', 'ts_new_shop', 'double', false, '{/literal}{incomCRM_translate label='LBL_TS_NEW_SHOP' module='MonthlyTargets'}{literal}' );
addToValidate('EditView', 'ts_kpi', 'double', false, '{/literal}{incomCRM_translate label='LBL_TS_KPI' module='MonthlyTargets'}{literal}' );
addToValidate('EditView', 'total_per', 'int', false, '{/literal}{incomCRM_translate label='LBL_TOTAL_PER' module='MonthlyTargets'}{literal}' );
addToValidate('EditView', 'error_kpi', 'double', false, '{/literal}{incomCRM_translate label='LBL_ERROR_KPI' module='MonthlyTargets'}{literal}' );
addToValidateBinaryDependency('EditView', 'assigned_user_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='MonthlyTargets'}{literal}{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO' module='MonthlyTargets'}{literal}', 'assigned_user_id' );
</script>
<script type="text/javascript">if(typeof sqs_objects == 'undefined'){var sqs_objects = new Array;}
sqs_objects['EditView_assigned_user_name'] = {"form":"EditView","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["assigned_user_name","assigned_user_id"],"required_list":["assigned_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
</script>{/literal}
