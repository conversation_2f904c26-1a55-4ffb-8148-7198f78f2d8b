

<form action="index.php" method="POST" name="{$form_name}" id="{$form_id}" class="_edit-{$instanceName}" {$enctype}>
<input type="hidden" name="module" value="{$module}" />
<input type="hidden" name="action" id="action" />
{if isset($smarty.request.isDuplicate) && $smarty.request.isDuplicate eq "true"}
<input type="hidden" name="record" id="record" value="" />
<input type="hidden" name="duplicateSave" value="true" />
{else}
<input type="hidden" name="record" id="record" value="{$fields.id.value}" />
{/if}
<input type="hidden" name="return_module" value="{$smarty.request.return_module}" />
<input type="hidden" name="return_action" value="{if $smarty.request.return_action eq 'LoadTabSubpanels'}DetailView{else}{$smarty.request.return_action}{/if}" />
<input type="hidden" name="return_id" value="{$smarty.request.return_id}" />
<input type="hidden" name="isDuplicate" value="false" />
<input type="hidden" name="module_tab" /> 
<input type="hidden" name="contact_role" />
{if !empty($smarty.request.return_module)}
<input type="hidden" name="relate_to" value="{if $smarty.request.return_relationship}{$smarty.request.return_relationship}{else}{$smarty.request.return_module}{/if}" />
<input type="hidden" name="relate_id" value="{$smarty.request.return_id}" />
{/if}
<input type="hidden" name="offset" value="{$offset}" />
{if empty($fields.id.value)}
<input type="hidden" name="_notify_random" value="{php}echo create_guid();{/php}" />
{/if}
<input type="hidden" name="isFromEditView" value="{$module}" />
<input type="hidden" name="object" value="{$fields.object.value|default:'Proposal'}" />
<input type="hidden" name="order_discount" id="order_discount" value="{$fields.order_discount.value}" />
<input type="hidden" name="sector_vertical" id="sector_vertical" value="{$fields.sector_vertical.value}" />
<input type="hidden" name="is_target" id="is_target" value="0" />
<input type="hidden" name="no_currency" id="no_currency" value="{$fields.no_currency.value}" />
<input type="hidden" name="account_id" id="account_id" value="{$fields.account_id.value}" />
<input type="hidden" name="parent_id" id="parent_id" value="{$fields.parent_id.value}" />
<input type="hidden" name="parent_name" id="parent_name" value="{$fields.parent_name.value}" />
<input type="hidden" name="amount" id="amount" value="{$fields.amount.value}" />
<input type="hidden" name="vat_more_amt" id="vat_more_amt" value="0" />
<input type="hidden" name="order_purchase" value="" />
<input type="hidden" name="failure_cause" id="failure_cause" value="{$fields.failure_cause.value}" />
{incomCRM_include include=$includes}
<ul id="searchTabs" class="tablist tab-large pl-10">
<li id="tab_index" class="active">
<a class="current" href="javascript:;">{$MOD_RPT.LBL_NEW_FORM_TITLE}</a>
</li>
<li id="tab_list">
<a href="index.php?module={$module_rpt}&action=Statistics">{$MOD_RPT.LBL_RPT_STATISTICS}</a>
</li>
<li class="tab-buttons d-block floatR">
<div class="grp-buttons mr-10">
<input type="button" name="btn" class="button" value=" Xem trước " onclick="return OpportunityViewer.getInstance('{$instanceName}').preview();" />
<input type="button" name="btn" class="button highlight btn-save" value=" {incomCRM_translate label='LBL_SAVE_PO_BUTTON_LABEL' module='Opportunities'} (F8) " onclick="return OpportunityViewer.getInstance('{$instanceName}').save();" />
<input type="button" name="btn" class="button highlight btn-save btn-order btn-access" value=" {incomCRM_translate label='LBL_SAVE_AMOUNT_BUTTON_LABEL' module='Opportunities'} (F9) " onclick="return OpportunityViewer.getInstance('{$instanceName}').save(1);" />
</div>
</li>
</ul>
<div class="clear"></div>
<div class="container pt-5 form-control {$module_rpt|strtolower} {if $productBoxView}cont-prod-view{else}cont-default{/if}">
{if !empty($smarty.request.return_url)}
<input type="hidden" name="return_url" value="{$smarty.request.return_url|escape:url}" />
{/if}
{if empty($UseShippingFee)}
<input type="hidden" name="shipping_fee" id="shipping_fee" value="{$fields.shipping_fee.value}" />
{/if}
{if empty($UseOrderType)}
<input type="hidden" name="order_type" id="order_type" value="{$fields.order_type.value}" />
{/if}
{if empty($isFeeType)}
<input type="hidden" name="incurred_domestic" id="incurred_domestic" value="0" />
<input type="hidden" name="incurred_foreign" id="incurred_foreign" value="0" />
<input type="hidden" name="incurred_other" id="incurred_other" value="0" />
{/if}
<div class="row panel-1">
<div class="col-6 col-md-12 col-sm-12">
<div class="row">
<div class="col-6 col-sm-12">
<div class="row" id="name_group">
<label class="col-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_NAME' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div class="col-8">

{if strlen($fields.name.value) <= 0}
{assign var="value" value=$fields.name.default_value }
{else}
{assign var="value" value=$fields.name.value }
{/if}
{if isTypeNumber($fields.name.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.name.name}' id='{$fields.name.name}' size='30' maxlength='30' value='{$value}' title='' tabindex='100'  readonly="readonly" /> 
</div>
</div>
<div class="row" id="contract_number_group">
<label class="col-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTRACT_NUMBER' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div class="col-8">

{if strlen($fields.contract_number.value) <= 0}
{assign var="value" value=$fields.contract_number.default_value }
{else}
{assign var="value" value=$fields.contract_number.value }
{/if}
{if isTypeNumber($fields.contract_number.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.contract_number.name}' id='{$fields.contract_number.name}' size='30' maxlength='50' value='{$value}' title='' tabindex='102'  /> 
</div>
</div>
<div class="row" id="order_number_group">
<label class="col-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ORDER_NUMBER' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div class="col-8">

{if strlen($fields.order_number.value) <= 0}
{assign var="value" value=$fields.order_number.default_value }
{else}
{assign var="value" value=$fields.order_number.value }
{/if}
{if isTypeNumber($fields.order_number.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.order_number.name}' id='{$fields.order_number.name}' size='30' maxlength='50' value='{$value}' title='' tabindex='104'  /> 
</div>
</div>
<div class="row" id="opportunity_type_group">
<label class="col-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_LIST_TYPE' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</label>
<div class="col-8">

<select name="{$fields.opportunity_type.name}" id="{$fields.opportunity_type.name}" title='' tabindex="106"  onchange="OpportunityViewer.getInstance('{$instanceName}').changeType()" >
{if isset($fields.opportunity_type.value) && $fields.opportunity_type.value != ''}
{html_options options=$fields.opportunity_type.options selected=$fields.opportunity_type.value}
{else}
{html_options options=$fields.opportunity_type.options selected=$fields.opportunity_type.default}
{/if}
</select>
</div>
</div>
</div>
<div class="col-6 col-sm-12">
<div class="row" id="date_closed_group">
<label class="col-6 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DATE_CLOSED' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</label>
<div class="col-6 col-md-8">

{assign var=date_value value=$fields.date_closed.value }
<input autocomplete="off" type="text" name="{$fields.date_closed.name}" id="{$fields.date_closed.name}" value="{$date_value}" title=''  tabindex='101' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.date_closed.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.date_closed.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.date_closed.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</div>
</div>
<div class="row" id="contract_date_group">
<label class="col-6 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTRACT_DATE' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div class="col-6 col-md-8">

{assign var=date_value value=$fields.contract_date.value }
<input autocomplete="off" type="text" name="{$fields.contract_date.name}" id="{$fields.contract_date.name}" value="{$date_value}" title=''  tabindex='103' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.contract_date.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.contract_date.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.contract_date.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</div>
</div>
<div class="row" id="order_date_group">
<label class="col-6 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ORDER_DATE' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div class="col-6 col-md-8">

{assign var=date_value value=$fields.order_date.value }
<input autocomplete="off" type="text" name="{$fields.order_date.name}" id="{$fields.order_date.name}" value="{$date_value}" title=''  tabindex='105' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.order_date.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.order_date.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.order_date.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</div>
</div>
<div class="row" id="date_start_process_group">
<label class="col-6 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DATE_START_PROCESS' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div class="col-6 col-md-8">

{assign var=date_value value=$fields.date_start_process.value }
<input autocomplete="off" type="text" name="{$fields.date_start_process.name}" id="{$fields.date_start_process.name}" value="{$date_value}" title=''  tabindex='107' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.date_start_process.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.date_start_process.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.date_start_process.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</div>
</div>
</div>
<div class="col-12 my-0">
<div class="row" id="account_name_group">
<label class="col-2 col-sm-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACCOUNT_NAME' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</label>
<div class="col-10 col-sm-8 my-0">
<div class="row">
<div class="col col-sm-12 my-0 pr-0 pr-sm-10 yui-ac-format">
<input type="text" id="account_name" name="account_name" size="30" class="sqsEnabled" tabindex="108" value="{$fields.account_name.value}" placeholder="[Tên] ; [Điện thoại] ; [Địa chỉ] (F3)" />
</div>
<div class="col-auto col-sm-12 my-0">
<input type="button" name="btn_account_name" id="btn_account_name" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='{literal}open_popup("{/literal}{$module_acc|default:'Accounts'}{literal}", 800, 550, "{/literal}{$popup_params|default:''}{literal}", true, false, {"call_back_function":"set_return_focus", "form_name":"{/literal}form_EditSO_Opportunities{literal}", "field_to_name_array":{"id":"account_id", "name":"account_name", "phone_office":"account_phone", "billing_address_street":"account_address", "receivable_debts":"receivable_debts", "overdue_debts":"overdue_debts", "order_discount":"order_discount"}, "passthru_data":{"callback":"OpportunityViewer.selectedAccount"}}, "single", true);{/literal}' tabindex="108" />
<input type="button" name="btn_clr_account_name" id="btn_clr_account_name" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button" onclick="OpportunityViewer.getInstance('{$instanceName}').clearAccountInfo();" value="{$APP.LBL_CLEAR_BUTTON_LABEL}" tabindex="108" />
<input type="button" name="btn_add_account_name" id="btn_add_account_name" title="Thêm khách hàng" class="button" onclick="OpportunityViewer.getInstance('{$instanceName}').callAccountMethod('add', '{$module_acc|default:'Accounts'}', '{$add_params}');" value="{$APP.LBL_NEW_BUTTON_LABEL}" tabindex="50" />
<input type="button" name="btn_edit_account_name" id="btn_edit_account_name" title="Sửa khách hàng" class="button" onclick="OpportunityViewer.getInstance('{$instanceName}').callAccountMethod('edit', '{$module_acc|default:'Accounts'}');" value="{$APP.LNK_EDIT|ucfirst}" tabindex="50" />
</div>
</div>
</div>
</div>
<div class="row" id="account_phone_group">
<label class="col-2 col-sm-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACCOUNT_PHONE' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div class="col-3 col-sm-8">

{if strlen($fields.account_phone.value) <= 0}
{assign var="value" value=$fields.account_phone.default_value }
{else}
{assign var="value" value=$fields.account_phone.value }
{/if}
{if isTypeNumber($fields.account_phone.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.account_phone.name}' id='{$fields.account_phone.name}' size='30' maxlength='100' value='{$value}' title='' tabindex='109'  /> 
</div>
<label class="col-1 col-sm-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACCOUNT_ADDRESS' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div class="col-6 col-sm-8">

{if strlen($fields.account_address.value) <= 0}
{assign var="value" value=$fields.account_address.default_value }
{else}
{assign var="value" value=$fields.account_address.value }
{/if}
{if isTypeNumber($fields.account_address.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.account_address.name}' id='{$fields.account_address.name}' size='30'  value='{$value}' title='' tabindex='110'  /> 
</div>
</div>
</div>
</div>
</div>
<div class="col-3 col-md-6 col-sm-12">
<div class="row" id="estimated_revenue_group">
<label class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_MN_AMOUNT' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div class="col-7 col-md-8">

{if strlen($fields.estimated_revenue.value) <= 0}
{assign var="value" value=$fields.estimated_revenue.default_value }
{else}
{assign var="value" value=$fields.estimated_revenue.value }
{/if}
{if isTypeNumber($fields.estimated_revenue.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.estimated_revenue.name}' id='{$fields.estimated_revenue.name}' size='30'  value='{$value}' title='' tabindex='111'  class="right" readonly="readonly" /> 
</div>
</div>
<div class="row" id="discount_amount_group">
<label class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_LIST_DISCOUNT_AMOUNT' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div class="col-7 col-md-8">

{if strlen($fields.discount_amount.value) <= 0}
{assign var="value" value=$fields.discount_amount.default_value }
{else}
{assign var="value" value=$fields.discount_amount.value }
{/if}
{if isTypeNumber($fields.discount_amount.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.discount_amount.name}' id='{$fields.discount_amount.name}' size='30'  value='{$value}' title='' tabindex='112'  class="right" readonly="readonly" /> 
</div>
</div>
<div class="row" id="vat_tax_group">
<label class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_VAT_TAX' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div class="col-3 pr-0">

<select name="{$fields.vat_tax.name}" id="{$fields.vat_tax.name}" title='' tabindex="113"  onchange="OpportunityViewer.getInstance('{$instanceName}').calcTotal()" >
{if isset($fields.vat_tax.value) && $fields.vat_tax.value != ''}
{html_options options=$fields.vat_tax.options selected=$fields.vat_tax.value}
{else}
{html_options options=$fields.vat_tax.options selected=$fields.vat_tax.default}
{/if}
</select>
</div>
<div class="col-4 col-md-5 pl-0">

{if strlen($fields.vat_amount.value) <= 0}
{assign var="value" value=$fields.vat_amount.default_value }
{else}
{assign var="value" value=$fields.vat_amount.value }
{/if}
{if isTypeNumber($fields.vat_amount.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.vat_amount.name}' id='{$fields.vat_amount.name}' size='30'  value='{$value}' title='' tabindex='114'  class="right" readonly="readonly" /> 
</div>
</div>
<div class="row" id="contract_revenue_group">
<label class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_MN_TOTAL' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div class="col-7 col-md-8">

{if strlen($fields.contract_revenue.value) <= 0}
{assign var="value" value=$fields.contract_revenue.default_value }
{else}
{assign var="value" value=$fields.contract_revenue.value }
{/if}
{if isTypeNumber($fields.contract_revenue.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.contract_revenue.name}' id='{$fields.contract_revenue.name}' size='30'  value='{$value}' title='' tabindex='115'  class="right" readonly="readonly" /> 
</div>
</div>
<div class="row" id="overdue_debts_group">
<label class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_OVERDUE_DEBTS' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div class="col-7 col-md-8">

{if strlen($fields.overdue_debts.value) <= 0}
{assign var="value" value=$fields.overdue_debts.default_value }
{else}
{assign var="value" value=$fields.overdue_debts.value }
{/if}
{if isTypeNumber($fields.overdue_debts.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.overdue_debts.name}' id='{$fields.overdue_debts.name}' size='30'  value='{$value}' title='' tabindex='116'  class="right" readonly="readonly" /> 
</div>
</div>
</div>
<div class="col-3 col-md-6 col-sm-12">
<div class="row" id="receivable_debts_group">
<label class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DEBT_OLD' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div class="col-7 col-md-8">

{if strlen($fields.receivable_debts.value) <= 0}
{assign var="value" value=$fields.receivable_debts.default_value }
{else}
{assign var="value" value=$fields.receivable_debts.value }
{/if}
{if isTypeNumber($fields.receivable_debts.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.receivable_debts.name}' id='{$fields.receivable_debts.name}' size='30'  value='{$value}' title='' tabindex='117'  class="right" readonly="readonly" /> 
</div>
</div>
<div class="row" id="date_entered_group">
<label class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DEBT_TOTAL' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div class="col-7 col-md-8">
<input tabindex="118"  type="text" name="debt_total" id="debt_total" value="" size="30" readonly="readonly" class="right" />
</div>
</div>
<div class="row" id="money_deposit_group">
<label class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_MONEY_DEPOSIT_KEY' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div class="col-7 col-md-8">

{if strlen($fields.money_deposit.value) <= 0}
{assign var="value" value=$fields.money_deposit.default_value }
{else}
{assign var="value" value=$fields.money_deposit.value }
{/if}
{if isTypeNumber($fields.money_deposit.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.money_deposit.name}' id='{$fields.money_deposit.name}' size='30'  value='{$value}' title='' tabindex='119'  class="right" onblur="OpportunityViewer.getInstance('{$instanceName}').calcMoney()" /> 
</div>
</div>
<div class="row" id="money_refund_group">
<label class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_MONEY_REFUND' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div class="col-7 col-md-8">

{if strlen($fields.money_refund.value) <= 0}
{assign var="value" value=$fields.money_refund.default_value }
{else}
{assign var="value" value=$fields.money_refund.value }
{/if}
{if isTypeNumber($fields.money_refund.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.money_refund.name}' id='{$fields.money_refund.name}' size='30'  value='{$value}' title='' tabindex='120'  class="right" readonly="readonly" /> 
</div>
</div>
<div class="row" id="accounting_category_type_group">
<label class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACCOUNTING_CATEGORY_TYPE' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div class="col-7 col-md-8">

{if strval($fields.accounting_category_type.value) == "1" || strval($fields.accounting_category_type.value) == "yes" || strval($fields.accounting_category_type.value) == "on"} 
{assign var="checked" value="CHECKED"}
{else}
{assign var="checked" value=""}
{/if}
<input type="hidden" name="{$fields.accounting_category_type.name}" value="0" /> 
<input type="checkbox" id="{$fields.accounting_category_type.name}" name="{$fields.accounting_category_type.name}" value="1" title='' tabindex="121" {$checked}  />
</div>
</div>
<div class="row" id="ignore_payment_group">
<label class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_IGNORE_PAYMENT' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div class="col-7 col-md-8">

{if strval($fields.ignore_payment.value) == "1" || strval($fields.ignore_payment.value) == "yes" || strval($fields.ignore_payment.value) == "on"} 
{assign var="checked" value="CHECKED"}
{else}
{assign var="checked" value=""}
{/if}
<input type="hidden" name="{$fields.ignore_payment.name}" value="0" /> 
<input type="checkbox" id="{$fields.ignore_payment.name}" name="{$fields.ignore_payment.name}" value="1" title='' tabindex="122" {$checked}  onclick="OpportunityViewer.getInstance('{$instanceName}').calcIgnorePayment(this.checked)" />
</div>
</div>
</div>
</div>
<div class="row panel-2">
<div id="prod_group" class="col-12">
{incomCRM_include type='php' file='custom/modules/Opportunities/EditProducts.php'}
</div>
</div>
<div class="row panel-3">
<div class="col-12">
<table width="100%" cellpadding="0" cellspacing="0" border="0" class="formHeader h3Row">
<tr>
<td><h3>
<a href="javascript:;" onclick="showHideGroup('opportunities_more_info');" class="tabFormAdvLink">
<img id="opportunities_more_info_IMG" src="index.php?entryPoint=getImage&imageName=basic_search.gif" border="0" alt="toggle" />&nbsp;
{$MOD_RPT.LBL_MORE_INFO}
</a>
</h3></td>
</tr>
</table>
</div>
<div class="col-12" id="opportunities_more_info_GROUP">
<div class="row">
<div id="GROUP_01" class="col-4 col-md-6 col-sm-12">
<div class="row" id="point_current_group">
<label id="point_current_label" class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_POINT_CURRENT' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id="_point_current_field" class="col-7 col-md-8 ">

{if strlen($fields.point_current.value) <= 0}
{assign var="value" value=$fields.point_current.default_value }
{else}
{assign var="value" value=$fields.point_current.value }
{/if}
{if isTypeNumber($fields.point_current.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.point_current.name}' id='{$fields.point_current.name}' size='30'  value='{$value}' title='' tabindex='126'  style="color:#F00" readonly="readonly" /> 
</div>
</div>
<div class="row" id="contact_name_group">
<label id="contact_name_label" class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTACT_NAME' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id="_contact_name_field" class="col-7 col-md-8 ">

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{else}{/if}<input type="hidden" name="{$fields.contact_name.id_name}" id="{$fields.contact_name.id_name}" value="{$fields.contact_id.value}" />
<input type="text" name="{$fields.contact_name.name}" class="sqsEnabled" tabindex="127" id="{$fields.contact_name.name}" size="16" value="{$fields.contact_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{else}{/if}
<div class="{if !empty($isSmartView)}col-auto col-s2-12{else}grp-buttons{/if}"><input type="button" name="btn_{$fields.contact_name.name}" id="btn_{$fields.contact_name.name}" tabindex="127" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.contact_name.module}", 1000, 600, "&account_id=\""+ encodeURIComponent(this.form.account_id.value) +"\"", true, false, {literal}{"call_back_function":"set_return","form_name":"form_EditSO_Opportunities","field_to_name_array":{"id":"contact_id","name":"contact_name","phone_mobile":"contact_phone"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.contact_name.name}" id="btn_clr_{$fields.contact_name.name}" tabindex="127" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.contact_name.name}.value=""; this.form.{$fields.contact_name.id_name}.value=""; this.form.{$fields.contact_name.name}.focus();this.form.contact_id.value=""; this.form.contact_name.value=""; this.form.contact_phone.value="";' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
<input type="button" name="btn_add_{$fields.contact_name.name}" id="btn_add_{$fields.contact_name.name}" tabindex="127" class="button btn-add-new" onclick='LULO.resetPopupQS(null, "{$fields.contact_name.module}", "EditPopup"); OpportunityViewer.getInstance().addContact();' value="{$APP.LBL_NEW_BUTTON_LABEL}" />
<input type="button" name="btn_edit_{$fields.contact_name.name}" id="btn_edit_{$fields.contact_name.name}" tabindex="127" class="button btn-add-edit" onclick='LULO.resetPopupQS(null, "{$fields.contact_name.module}", "EditPopup"); if( this.form.{$fields.contact_name.id_name}.value == "" ) alert("{$APP.MSG_DATA_EMPTY}"); else OpportunityViewer.getInstance().addContact(this.form.contact_id.value);' value="{$APP.LNK_EDIT|ucfirst}" />
</div>{if !empty($isSmartView)}</div>{else}{/if}<script type="text/javascript">enableQS(false);</script>
</div>
</div>
<div class="row" id="contact_phone_group">
<label id="contact_phone_label" class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTACT_PHONE' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id="_contact_phone_field" class="col-7 col-md-8 ">

{if strlen($fields.contact_phone.value) <= 0}
{assign var="value" value=$fields.contact_phone.default_value }
{else}
{assign var="value" value=$fields.contact_phone.value }
{/if}
{if isTypeNumber($fields.contact_phone.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.contact_phone.name}' id='{$fields.contact_phone.name}' size='30' maxlength='50' value='{$value}' title='' tabindex='128'  /> 
</div>
</div>
<div class="row" id="project_name_group">
<label id="project_name_label" class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_PROJECT_NAME' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id="_project_name_field" class="col-7 col-md-8 ">

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{else}{/if}<input type="hidden" name="{$fields.project_name.id_name}" id="{$fields.project_name.id_name}" value="{$fields.project_id.value}" />
<input type="text" name="{$fields.project_name.name}" class="sqsEnabled" tabindex="129" id="{$fields.project_name.name}" size="16" value="{$fields.project_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{else}{/if}
<div class="{if !empty($isSmartView)}col-auto col-s2-12{else}grp-buttons{/if}"><input type="button" name="btn_{$fields.project_name.name}" id="btn_{$fields.project_name.name}" tabindex="129" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.project_name.module}", 1000, 600, "&account_id=\""+ encodeURIComponent(this.form.account_id.value) +"\"", true, false, {literal}{"call_back_function":"set_return","form_name":"form_EditSO_Opportunities","field_to_name_array":{"id":"project_id","name":"project_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.project_name.name}" id="btn_clr_{$fields.project_name.name}" tabindex="129" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.project_name.name}.value=""; this.form.{$fields.project_name.id_name}.value=""; this.form.{$fields.project_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
<input type="button" name="btn_add_{$fields.project_name.name}" id="btn_add_{$fields.project_name.name}" tabindex="129" class="button btn-add-new" onclick='LULO.resetPopupQS(null, "{$fields.project_name.module}", "EditPopup"); OpportunityViewer.getInstance().addProject();' value="{$APP.LBL_NEW_BUTTON_LABEL}" />
<input type="button" name="btn_edit_{$fields.project_name.name}" id="btn_edit_{$fields.project_name.name}" tabindex="129" class="button btn-add-edit" onclick='LULO.resetPopupQS(null, "{$fields.project_name.module}", "EditPopup"); if( this.form.{$fields.project_name.id_name}.value == "" ) alert("{$APP.MSG_DATA_EMPTY}"); else OpportunityViewer.getInstance().addProject(this.form.project_id.value);' value="{$APP.LNK_EDIT|ucfirst}" />
</div>{if !empty($isSmartView)}</div>{else}{/if}<script type="text/javascript">enableQS(false);</script>
</div>
</div>
<div class="row" id="handle_user_name_group">
<label id="handle_user_name_label" class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_HANDLE_USER_NAME' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id="_handle_user_name_field" class="col-7 col-md-8 ">

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{else}{/if}<input type="hidden" name="{$fields.handle_user_name.id_name}" id="{$fields.handle_user_name.id_name}" value="{$fields.handle_user_id.value}" />
<input type="text" name="{$fields.handle_user_name.name}" class="sqsEnabled" tabindex="130" id="{$fields.handle_user_name.name}" size="16" value="{$fields.handle_user_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{else}{/if}
<input type="button" name="btn_{$fields.handle_user_name.name}" id="btn_{$fields.handle_user_name.name}" tabindex="130" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.handle_user_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"form_EditSO_Opportunities","field_to_name_array":{"id":"handle_user_id","user_name":"handle_user_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.handle_user_name.name}" id="btn_clr_{$fields.handle_user_name.name}" tabindex="130" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.handle_user_name.name}.value=""; this.form.{$fields.handle_user_name.id_name}.value=""; this.form.{$fields.handle_user_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
{if !empty($isSmartView)}</div>{else}{/if}<script type="text/javascript">enableQS(false);</script>
</div>
</div>
<div class="row" id="assigned_user_name_group">
<label id="assigned_user_name_label" class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</label>
<div id="_assigned_user_name_field" class="col-7 col-md-8 ">

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{else}{/if}<input type="hidden" name="{$fields.assigned_user_name.id_name}" id="{$fields.assigned_user_name.id_name}" value="{$fields.assigned_user_id.value}" />
<input type="text" name="{$fields.assigned_user_name.name}" class="sqsEnabled" tabindex="131" id="{$fields.assigned_user_name.name}" size="16" value="{$fields.assigned_user_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{else}{/if}
<div class="{if !empty($isSmartView)}col-auto col-s2-12{else}grp-buttons{/if}"><input type="button" name="btn_{$fields.assigned_user_name.name}" id="btn_{$fields.assigned_user_name.name}" tabindex="131" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.assigned_user_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"form_EditSO_Opportunities","field_to_name_array":{"id":"assigned_user_id","user_name":"assigned_user_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.assigned_user_name.name}" id="btn_clr_{$fields.assigned_user_name.name}" tabindex="131" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.assigned_user_name.name}.value=""; this.form.{$fields.assigned_user_name.id_name}.value=""; this.form.{$fields.assigned_user_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
</div>{if !empty($isSmartView)}</div>{else}{/if}<script type="text/javascript">enableQS(false);</script>
</div>
</div>
<div class="row" id="description_group">
<label id="description_label" class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DESCRIPTION' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id="_description_field" class="col-7 col-md-8 ">

{if empty($fields.description.value)}
{assign var="value" value=$fields.description.default_value }
{else}
{assign var="value" value=$fields.description.value }
{/if}
<textarea id="{$fields.description.name}" name="{$fields.description.name}" rows="2" cols="30" title='' tabindex="132"  >{$value}</textarea>
</div>
</div>
<div class="row" id="quotes_note_group">
<label id="quotes_note_label" class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_QUOTES_NOTE' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id="_quotes_note_field" class="col-7 col-md-8 ">

{if empty($fields.quotes_note.value)}
{assign var="value" value=$fields.quotes_note.default_value }
{else}
{assign var="value" value=$fields.quotes_note.value }
{/if}
<textarea id="{$fields.quotes_note.name}" name="{$fields.quotes_note.name}" rows="4" cols="60" title='' tabindex="133"  >{$value}</textarea>
</div>
</div>
<div class="row" id="shipping_time_group">
<label id="shipping_time_label" class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_SHIPPING_TIME' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id="_shipping_time_field" class="col-7 col-md-8 ">

{if empty($fields.shipping_time.value)}
{assign var="value" value=$fields.shipping_time.default_value }
{else}
{assign var="value" value=$fields.shipping_time.value }
{/if}
<textarea id="{$fields.shipping_time.name}" name="{$fields.shipping_time.name}" rows="2" cols="30" title='' tabindex="134"  >{$value}</textarea>
</div>
</div>
<div class="row" id="payment_method_group">
<label id="payment_method_label" class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_PAYMENT_METHOD' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id="_payment_method_field" class="col-7 col-md-8 ">

{if empty($fields.payment_method.value)}
{assign var="value" value=$fields.payment_method.default_value }
{else}
{assign var="value" value=$fields.payment_method.value }
{/if}
<textarea id="{$fields.payment_method.name}" name="{$fields.payment_method.name}" rows="2" cols="30" title='' tabindex="135"  >{$value}</textarea>
</div>
</div>
</div>
<div id="GROUP_02" class="col-4 col-md-6 col-sm-12">
<div class="row" id="order_type_group">
<label id="order_type_label" class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ORDER_TYPE' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</label>
<div id="_order_type_field" class="col-7 col-md-8 ">

<select name="{$fields.order_type.name}" id="{$fields.order_type.name}" title='' tabindex="136"  onchange="OpportunityViewer.getInstance('{$instanceName}').calcTotal()" >
{if isset($fields.order_type.value) && $fields.order_type.value != ''}
{html_options options=$fields.order_type.options selected=$fields.order_type.value}
{else}
{html_options options=$fields.order_type.options selected=$fields.order_type.default}
{/if}
</select>
</div>
</div>
<div class="row" id="sales_stage_group">
<label id="sales_stage_label" class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_SALES_STAGE' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</label>
<div id="_sales_stage_field" class="col-7 col-md-8 ">

<select name="{$fields.sales_stage.name}" id="{$fields.sales_stage.name}" title='' tabindex="137"  onchange="OpportunityViewer.getInstance('{$instanceName}').changeSalesStage(this)" >
{if isset($fields.sales_stage.value) && $fields.sales_stage.value != ''}
{html_options options=$fields.sales_stage.options selected=$fields.sales_stage.value}
{else}
{html_options options=$fields.sales_stage.options selected=$fields.sales_stage.default}
{/if}
</select>
</div>
</div>
<div class="row" id="causes_failure_group">
<label id="causes_failure_label" class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CAUSES_FAILURE' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id="_causes_failure_field" class="col-7 col-md-8 ">

<select name="{$fields.causes_failure.name}" id="{$fields.causes_failure.name}" title='' tabindex="138"  >
{if isset($fields.causes_failure.value) && $fields.causes_failure.value != ''}
{html_options options=$fields.causes_failure.options selected=$fields.causes_failure.value}
{else}
{html_options options=$fields.causes_failure.options selected=$fields.causes_failure.default}
{/if}
</select>
</div>
</div>
<div class="row" id="payment_type_group">
<label id="payment_type_label" class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_PAYMENT_TYPE' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id="_payment_type_field" class="col-7 col-md-8 ">

<select name="{$fields.payment_type.name}" id="{$fields.payment_type.name}" title='' tabindex="139"  onchange="calcOpportunityMultiPayment(this.form, true);" >
{if isset($fields.payment_type.value) && $fields.payment_type.value != ''}
{html_options options=$fields.payment_type.options selected=$fields.payment_type.value}
{else}
{html_options options=$fields.payment_type.options selected=$fields.payment_type.default}
{/if}
</select>
</div>
</div>
<div class="row" id="bank_account_id_group">
<label id="bank_account_id_label" class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_BANK_ACCOUNT_NAME' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id="_bank_account_id_field" class="col-7 col-md-8 ">

{$fields.bank_account_id.value}
</div>
</div>
<div class="row" id="payment_period_group">
<label id="payment_period_label" class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_PAYMENT_PERIOD' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id="_payment_period_field" class="col-7 col-md-8 ">

<select name="{$fields.payment_period.name}" id="{$fields.payment_period.name}" title='' tabindex="141"  >
{if isset($fields.payment_period.value) && $fields.payment_period.value != ''}
{html_options options=$fields.payment_period.options selected=$fields.payment_period.value}
{else}
{html_options options=$fields.payment_period.options selected=$fields.payment_period.default}
{/if}
</select>
</div>
</div>
<div class="row" id="holding_time_group">
<label id="holding_time_label" class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_HOLDING_TIME' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id="_holding_time_field" class="col-7 col-md-8 ">

<select name="{$fields.holding_time.name}" id="{$fields.holding_time.name}" title='' tabindex="142"  onchange="OpportunityViewer.getInstance('{$instanceName}').changeHoldingTime(this)" >
{if isset($fields.holding_time.value) && $fields.holding_time.value != ''}
{html_options options=$fields.holding_time.options selected=$fields.holding_time.value}
{else}
{html_options options=$fields.holding_time.options selected=$fields.holding_time.default}
{/if}
</select>
</div>
</div>
<div class="row" id="holding_start_group">
<label id="holding_start_label" class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_HOLDING_START' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id="_holding_start_field" class="col-7 col-md-8 ">

{assign var=date_value value=$fields.holding_start.value }
<input autocomplete="off" type="text" name="{$fields.holding_start.name}" id="{$fields.holding_start.name}" value="{$date_value}" title=''  readonly="readonly" tabindex='143' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.holding_start.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.holding_start.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.holding_start.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</div>
</div>
<div class="row" id="holding_end_group">
<label id="holding_end_label" class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_HOLDING_END' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id="_holding_end_field" class="col-7 col-md-8 ">

{assign var=date_value value=$fields.holding_end.value }
<input autocomplete="off" type="text" name="{$fields.holding_end.name}" id="{$fields.holding_end.name}" value="{$date_value}" title=''  readonly="readonly" tabindex='144' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.holding_end.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.holding_end.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.holding_end.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</div>
</div>
<div class="row" id="rate_id_group">
<label id="rate_id_label" class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_RATE_ID' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id="_rate_id_field" class="col-7 col-md-8 ">

{$fields.rate_id.value}
</div>
</div>
<div class="row" id="rate_foreign_group">
<label id="rate_foreign_label" class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_RATE_FOREIGN' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id="_rate_foreign_field" class="col-7 col-md-8 ">

{if strlen($fields.rate_foreign.value) <= 0}
{assign var="value" value=$fields.rate_foreign.default_value }
{else}
{assign var="value" value=$fields.rate_foreign.value }
{/if}
{if isTypeNumber($fields.rate_foreign.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.rate_foreign.name}' id='{$fields.rate_foreign.name}' size='30'  value='{$value}' title='' tabindex='146'  class="right" onblur="callbackUpdateCurrencyForeignRate(this.form, this.form.rate_id.value);" /> 
</div>
</div>
<div class="row" id="actual_output_group">
<label id="actual_output_label" class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACTUAL_OUTPUT' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id="_actual_output_field" class="col-7 col-md-8 ">

{if strlen($fields.actual_output.value) <= 0}
{assign var="value" value=$fields.actual_output.default_value }
{else}
{assign var="value" value=$fields.actual_output.value }
{/if}
{if isTypeNumber($fields.actual_output.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.actual_output.name}' id='{$fields.actual_output.name}' size='30' maxlength='10' value='{$value}' title='' tabindex='149'  class="right" readonly="readonly" /> 
</div>
</div>
<div class="row" id="commission_amount_group">
<label id="commission_amount_label" class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_COMMISSION_AMOUNT' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id="_commission_amount_field" class="col-7 col-md-8 ">

{if strlen($fields.commission_amount.value) <= 0}
{assign var="value" value=$fields.commission_amount.default_value }
{else}
{assign var="value" value=$fields.commission_amount.value }
{/if}
{if isTypeNumber($fields.commission_amount.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.commission_amount.name}' id='{$fields.commission_amount.name}' size='30'  value='{$value}' title='' tabindex='150'  class="right" readonly="readonly" /> 
</div>
</div>
<div class="row" id="incurred_amount_group">
<label id="incurred_amount_label" class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_INCURRED_AMOUNT' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id="_incurred_amount_field" class="col-7 col-md-8 ">

{if strlen($fields.incurred_amount.value) <= 0}
{assign var="value" value=$fields.incurred_amount.default_value }
{else}
{assign var="value" value=$fields.incurred_amount.value }
{/if}
{if isTypeNumber($fields.incurred_amount.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.incurred_amount.name}' id='{$fields.incurred_amount.name}' size='30'  value='{$value}' title='' tabindex='151'  class="right" readonly="readonly" /> 
</div>
</div>
<div class="row" id="vat_other_amt_group">
<label id="vat_other_amt_label" class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_VAT_OTHER_AMT' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id="_vat_other_amt_field" class="col-7 col-md-8 ">

{if strlen($fields.vat_other_amt.value) <= 0}
{assign var="value" value=$fields.vat_other_amt.default_value }
{else}
{assign var="value" value=$fields.vat_other_amt.value }
{/if}
{if isTypeNumber($fields.vat_other_amt.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.vat_other_amt.name}' id='{$fields.vat_other_amt.name}' size='30'  value='{$value}' title='' tabindex='152'  class="right" readonly="readonly" /> 
</div>
</div>
</div>
<div id="GROUP_03" class="col-4 col-md-6 col-sm-12">
<div class="row" id="shipping_fee_group">
<label id="shipping_fee_label" class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_SHIPPING_FEE' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id="_shipping_fee_field" class="col-7 col-md-8 ">

{if strlen($fields.shipping_fee.value) <= 0}
{assign var="value" value=$fields.shipping_fee.default_value }
{else}
{assign var="value" value=$fields.shipping_fee.value }
{/if}
{if isTypeNumber($fields.shipping_fee.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.shipping_fee.name}' id='{$fields.shipping_fee.name}' size='30'  value='{$value}' title='' tabindex='153'  class="right" onblur="OpportunityViewer.getInstance('{$instanceName}').calcTotal()" /> 
</div>
</div>
<div class="row" id="warranty_period_group">
<label id="warranty_period_label" class="col-5 col-md-4">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_WARRANTY_PERIOD' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id="_warranty_period_field" class="col-7 col-md-8 ">

<select name="{$fields.warranty_period.name}" id="{$fields.warranty_period.name}" title='' tabindex="154"  >
{if isset($fields.warranty_period.value) && $fields.warranty_period.value != ''}
{html_options options=$fields.warranty_period.options selected=$fields.warranty_period.value}
{else}
{html_options options=$fields.warranty_period.options selected=$fields.warranty_period.default}
{/if}
</select>
</div>
</div>
<div class="row" id="is_target_group">
<label id="is_target_label" class="col-12">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ATTACHMENT_FILES' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id="_is_target_field" class="col-12 ">
<div id="opportunities_files">{$ATTACHMENTS}</div>
</div>
</div>
</div>
<div id="GROUP_04" class="col-12 col-md-6 col-sm-12">
<div class="row" id="share_users_group">
<label id="share_users_label" class="col-12">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_SHARING_USERS' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</label>
<div id="_share_users_field" class="col-12 ">
<input type="hidden" id="{$fields.share_users.name}_multiselect" name="{$fields.share_users.name}_multiselect" value="true" />
{multienum_to_array string=$fields.share_users.value default=$fields.share_users.default assign="values"}
<div style="width:100%;" id="{$fields.share_users.name}_multi_grp" class="multi-enum-grp multi-enum-grp-default">
{if isset($fields.share_users.groupBy) and isset($fields.share_users.groupDom)}
<label id="_grp_choose">
<select name="_{$fields.share_users.name}_grp" size="1" onchange="changeFilterSelectedGroup(this.value, '{$fields.share_users.name}_multi_grp' )">
{html_options options=$fields.share_users.groupDom}
</select>
</label>
{/if}
<label id="_grp_all_" class="_grp_all_">
<input type="checkbox" value="__all__" onclick="checkedChangeSel(this, '{$fields.share_users.name}', true)" />
-- {$APP.LBL_LINK_ALL} --
</label>
{counter name="rowCount" start=0 print=false assign="rowCount"}
{foreach from=$fields.share_users.options item=option key=value}
{if $value}
<label id="_grp_{if isset($fields.share_users.groupBy) and isset($fields.share_users.groupBy[$value])}{ $fields.share_users.groupBy[$value]}{/if}" style="white-space: nowrap">
<input type="checkbox" id="{$fields.share_users.name}_checkbox{$rowCount}" name="{$fields.share_users.name}[]" value="{$value}" {if in_array(strval($value), $values)}checked="checked"{/if}  /> {$option}
</label>
{counter name="rowCount"}
{/if}
{/foreach}
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="row">
<div class="col-12 group payment_group" id="payment_group_GROUP">
<div class="payment-group-cont"><div id="payment_group" class="table-responsive"></div></div>
</div>
</div>
<script type="text/javascript" src="modules/Cashes/script.js"></script>
{literal}
<script type="text/javascript">
$(function(){ loadMultiPayment('index.php?module=Cashes&action=MultiPayments&to_pdf=true&canSave=false&opportunity_id={/literal}{$fields.id.value}{literal}'); });
</script>
{/literal}
</div>
</div>

</form>
{$set_focus_block}{literal}
<script type="text/javascript">
addToValidate('form_EditSO_Opportunities', 'id', 'id', false, '{/literal}{incomCRM_translate label='LBL_ID' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'date_entered_date', 'date', false, 'Tổng nợ' );
addToValidate('form_EditSO_Opportunities', 'date_modified_date', 'date', false, 'Ngày cập nhật' );
addToValidate('form_EditSO_Opportunities', 'modified_user_id', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_ID' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'modified_by_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_NAME' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'created_by', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_CREATED_ID' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'created_by_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_CREATED' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'deleted', 'bool', false, '{/literal}{incomCRM_translate label='LBL_DELETED' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'name', 'name', false, '{/literal}{incomCRM_translate label='LBL_NAME' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'description', 'text', false, '{/literal}{incomCRM_translate label='LBL_DESCRIPTION' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'branch_id', 'enum', false, '{/literal}{incomCRM_translate label='LBL_BRANCH_ID' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'department_id', 'enum', false, '{/literal}{incomCRM_translate label='LBL_DEPARTMENT_ID' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'assigned_user_id', 'relate', false, '{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO_ID' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'assigned_user_name', 'relate', true, '{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'overdue_debts', 'float', false, '{/literal}{incomCRM_translate label='LBL_OVERDUE_DEBTS' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'order_purchase', 'enum', false, '{/literal}{incomCRM_translate label='LBL_ORDER_PURCHASE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'opportunity_type', 'enum', true, '{/literal}{incomCRM_translate label='LBL_LIST_TYPE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'account_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_ID' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'account_name', 'relate', true, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_NAME' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'campaign_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_CAMPAIGN_ID' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'campaign_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_CAMPAIGN' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'lead_source', 'enum', false, '{/literal}{incomCRM_translate label='LBL_LEAD_SOURCE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'amount_usdollar', 'currency', false, '{/literal}{incomCRM_translate label='LBL_AMOUNT_USDOLLAR' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'currency_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_CURRENCY' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'currency_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_CURRENCY_NAME' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'currency_symbol', 'relate', false, '{/literal}{incomCRM_translate label='LBL_CURRENCY_SYMBOL' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'date_closed', 'date', true, '{/literal}{incomCRM_translate label='LBL_DATE_CLOSED' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'sales_stage', 'enum', true, '{/literal}{incomCRM_translate label='LBL_SALES_STAGE' module='Opportunities'}{literal}' );
addToValidateRange('form_EditSO_Opportunities', 'probability', 'int', false, '{/literal}{incomCRM_translate label='LBL_PROBABILITY' module='Opportunities'}{literal}', 0, 100 );
addToValidate('form_EditSO_Opportunities', 'rate_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_RATE_ID' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'rate_foreign', 'double', false, '{/literal}{incomCRM_translate label='LBL_RATE_FOREIGN' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'product_name', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TQT_PRODUCT_FULL_NAME' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'is_auto_cpps', 'bool', false, '{/literal}{incomCRM_translate label='LBL_IS_AUTO_CPPS' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'quotes_note', 'text', false, '{/literal}{incomCRM_translate label='LBL_QUOTES_NOTE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'shipping_time', 'text', false, '{/literal}{incomCRM_translate label='LBL_SHIPPING_TIME' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'payment_method', 'text', false, '{/literal}{incomCRM_translate label='LBL_PAYMENT_METHOD' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'shipping_note', 'text', false, '{/literal}{incomCRM_translate label='LBL_SHIPPING_NOTE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'failure_cause', 'text', false, '{/literal}{incomCRM_translate label='LBL_FAILURE_CAUSE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'accounting_category_type', 'bool', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNTING_CATEGORY_TYPE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'incurred_other', 'currency', false, '{/literal}{incomCRM_translate label='LBL_INCURRED_OTHER' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'incurred_domestic', 'currency', false, '{/literal}{incomCRM_translate label='LBL_INCURRED_DOMESTIC' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'incurred_foreign', 'currency', false, '{/literal}{incomCRM_translate label='LBL_INCURRED_FOREIGN' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'processing_stage', 'enum', false, '{/literal}{incomCRM_translate label='LBL_PROCESSING_STAGE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'processing_content', 'text', false, '{/literal}{incomCRM_translate label='LBL_PROCESSING_CONTENT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'amount_collected', 'currency', false, '{/literal}{incomCRM_translate label='LBL_AMOUNT_COLLECTED' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'amount_owed', 'currency', false, '{/literal}{incomCRM_translate label='LBL_AMOUNT_OWED' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'receivable_debts', 'float', false, '{/literal}{incomCRM_translate label='LBL_DEBT_OLD' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'debt_norm_min', 'double', false, '{/literal}{incomCRM_translate label='LBL_DEBT_NORM_MIN' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'debt_norm_max', 'double', false, '{/literal}{incomCRM_translate label='LBL_DEBT_NORM_MAX' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'debt_delay_day', 'int', false, '{/literal}{incomCRM_translate label='LBL_DEBT_DELAY_DAY' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'handle_user_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_HANDLE_USER_ID' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'handle_user_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_HANDLE_USER_NAME' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'parent_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_PARENT_ID' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'parent_code', 'relate', false, '{/literal}{incomCRM_translate label='LBL_PARENT_CODE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'parent_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_PARENT_NAME' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'parent_project_name', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_PROJECT_NAME' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'warranty_period', 'enum', false, '{/literal}{incomCRM_translate label='LBL_WARRANTY_PERIOD' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'bank_account_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_BANK_ACCOUNT_NAME' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'bank_account_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_BANK_ACCOUNT_NAME' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'contract_number', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_CONTRACT_NUMBER' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'contract_date', 'date', false, '{/literal}{incomCRM_translate label='LBL_CONTRACT_DATE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'order_number', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ORDER_NUMBER' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'order_date', 'date', false, '{/literal}{incomCRM_translate label='LBL_ORDER_DATE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'quote_number', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_QUOTE_NUMBER' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'estimated_revenue', 'currency', false, '{/literal}{incomCRM_translate label='LBL_MN_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'priority', 'enum', false, '{/literal}{incomCRM_translate label='LBL_INVOICE_NEED' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'opportunity_scope', 'enum', false, '{/literal}{incomCRM_translate label='LBL_OPPORTUNITY_SCOPE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'object', 'enum', false, '{/literal}{incomCRM_translate label='LBL_OBJECT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'date_start_process', 'date', false, '{/literal}{incomCRM_translate label='LBL_DATE_START_PROCESS' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'date_converter', 'date', false, '{/literal}{incomCRM_translate label='LBL_DATE_CONVERTER' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'is_target', 'bool', false, '{/literal}{incomCRM_translate label='LBL_ATTACHMENT_FILES' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'account_targets', 'bool', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_TARGETS' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'causes_failure', 'enum', false, '{/literal}{incomCRM_translate label='LBL_CAUSES_FAILURE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'failure_notes', 'text', false, '{/literal}{incomCRM_translate label='LBL_FAILURE_NOTES' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'debt_order', 'bool', false, '{/literal}{incomCRM_translate label='LBL_DEBT_ORDER' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'vat_stock', 'bool', false, '{/literal}{incomCRM_translate label='LBL_VAT_STOCK' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'project_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_PROJECT_ID' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'project_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_PROJECT_NAME' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'quotation_date', 'date', false, '{/literal}{incomCRM_translate label='LBL_QUOTATION_DATE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'quotation_version', 'tinyint', false, '{/literal}{incomCRM_translate label='LBL_QUOTATION_VERSION' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_date_shipment', 'date', false, '{/literal}{incomCRM_translate label='LBL_TTT_DATE_SHIPMENT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_date_contract', 'date', false, '{/literal}{incomCRM_translate label='LBL_TTT_DATE_CONTRACT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_date_declaration', 'date', false, '{/literal}{incomCRM_translate label='LBL_TTT_DATE_DECLARATION' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_date_storage', 'date', false, '{/literal}{incomCRM_translate label='LBL_TTT_DATE_STORAGE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_date_arrival', 'date', false, '{/literal}{incomCRM_translate label='LBL_TTT_DATE_ARRIVAL' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_date_clearance', 'date', false, '{/literal}{incomCRM_translate label='LBL_TTT_DATE_CLEARANCE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_fee_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_FEE_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_insurance_number', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_INSURANCE_NUMBER' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_insurance_date', 'date', false, '{/literal}{incomCRM_translate label='LBL_TTT_INSURANCE_DATE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_insurance_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_INSURANCE_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_insurance_extension', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_INSURANCE_EXTENSION' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_insurance_coop', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_INSURANCE_COOP' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_date_lc_payment', 'date', false, '{/literal}{incomCRM_translate label='LBL_TTT_DATE_LC_PAYMENT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_lc_number', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_LC_NUMBER' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_date_lc_expired', 'date', false, '{/literal}{incomCRM_translate label='LBL_TTT_DATE_LC_EXPIRED' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_lc_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_LC_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_lc_reiss_amt', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_LC_REISS_AMT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_lc_repair_amt', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_LC_REPAIR_AMT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_lc_payment_amt', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_LC_PAYMENT_AMT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_lc_bank_name', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_LC_BANK_NAME' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_invoice_number', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_INVOICE_NUMBER' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_invoice_date', 'date', false, '{/literal}{incomCRM_translate label='LBL_TTT_INVOICE_DATE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_bol', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_BOL' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_bol_date', 'date', false, '{/literal}{incomCRM_translate label='LBL_TTT_BOL_DATE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_packinglist', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_PACKINGLIST' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_co', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_CO' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_milltest', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_MILLTEST' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_dost', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_DOST' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_condi_trading', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_CONDI_TRADING' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_declaration', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_DECLARATION' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_declaration_number', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_DECLARATION_NUMBER' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_declaration_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_DECLARATION_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_vat_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_VAT_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_verification', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_VERIFICATION' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_verification_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_VERIFICATION_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_vat_import', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_VAT_IMPORT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_customs_fee', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_CUSTOMS_FEE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_clearance', 'text', false, '{/literal}{incomCRM_translate label='LBL_TTT_CLEARANCE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_arrival_notice', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_ARRIVAL_NOTICE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_discharging_port', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_DISCHARGING_PORT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_vessel', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_VESSEL' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_vessel_vn', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_VESSEL_VN' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_owed_contract', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_OWED_CONTRACT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_storage_fee', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_STORAGE_FEE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_vessel_fee', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_VESSEL_FEE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_repair_cont_fee', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_REPAIR_CONT_FEE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_deposit_cont_fee', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_DEPOSIT_CONT_FEE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_expected_output', 'float', false, '{/literal}{incomCRM_translate label='LBL_TTT_EXPECTED_OUTPUT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_cnt_amount', 'float', false, '{/literal}{incomCRM_translate label='LBL_TTT_CNT_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_foreign_amount', 'float', false, '{/literal}{incomCRM_translate label='LBL_TTT_FOREIGN_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_actual_output', 'float', false, '{/literal}{incomCRM_translate label='LBL_TTT_ACTUAL_OUTPUT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_commodity', 'text', false, '{/literal}{incomCRM_translate label='LBL_TTT_COMMODITY' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_tolerance', 'text', false, '{/literal}{incomCRM_translate label='LBL_TTT_TOLERANCE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_shipment_method', 'text', false, '{/literal}{incomCRM_translate label='LBL_TTT_SHIPMENT_METHOD' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_partial_shipment', 'text', false, '{/literal}{incomCRM_translate label='LBL_TTT_PARTIAL_SHIPMENT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_transhipment', 'text', false, '{/literal}{incomCRM_translate label='LBL_TTT_TRANSHIPMENT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_packing', 'text', false, '{/literal}{incomCRM_translate label='LBL_TTT_PACKING' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_quality', 'text', false, '{/literal}{incomCRM_translate label='LBL_TTT_QUALITY' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ttt_order_condi', 'text', false, '{/literal}{incomCRM_translate label='LBL_TTT_ORDER_CONDI' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'holding_time', 'enum', false, '{/literal}{incomCRM_translate label='LBL_HOLDING_TIME' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'holding_start', 'date', false, '{/literal}{incomCRM_translate label='LBL_HOLDING_START' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'holding_end', 'date', false, '{/literal}{incomCRM_translate label='LBL_HOLDING_END' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'payment_type', 'enum', false, '{/literal}{incomCRM_translate label='LBL_PAYMENT_TYPE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'payment_status', 'enum', false, '{/literal}{incomCRM_translate label='LBL_PAYMENT_STATUS' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'cashes_link', 'text', false, '{/literal}{incomCRM_translate label='LBL_CASHES_LINK' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ignore_payment', 'bool', false, '{/literal}{incomCRM_translate label='LBL_IGNORE_PAYMENT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'is_locked', 'bool', false, '{/literal}{incomCRM_translate label='LBL_IS_LOCKED' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'warehouse_link', 'text', false, '{/literal}{incomCRM_translate label='LBL_WAREHOUSE_LINK' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'order_discount', 'double', false, '{/literal}{incomCRM_translate label='LBL_ORDER_DISCOUNT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'delivered_all', 'bool', false, '{/literal}{incomCRM_translate label='LBL_DELIVERED_ALL' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'expected_output', 'float', false, '{/literal}{incomCRM_translate label='LBL_EXPECTED_OUTPUT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'actual_output', 'float', false, '{/literal}{incomCRM_translate label='LBL_ACTUAL_OUTPUT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'contract_revenue', 'currency', false, '{/literal}{incomCRM_translate label='LBL_MN_TOTAL' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'actual_receipts', 'currency', false, '{/literal}{incomCRM_translate label='LBL_ACTUAL_RECEIPTS' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'total_amount_in', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TOTAL_AMOUNT_IN' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'prod_lock', 'bool', false, '{/literal}{incomCRM_translate label='LBL_PROD_LOCK' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'contact_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_ID' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'contact_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_NAME' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'contact_phone', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_PHONE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'vat_tax', 'enum', false, '{/literal}{incomCRM_translate label='LBL_VAT_TAX' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'vat_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_VAT_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'vat_more_amt', 'currency', false, '{/literal}{incomCRM_translate label='LBL_VAT_MORE_AMT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'vat_other_amt', 'currency', false, '{/literal}{incomCRM_translate label='LBL_VAT_OTHER_AMT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'discount_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_LIST_DISCOUNT_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'cost_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_COST_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'comm_more_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_PROD_COMM_MORE_AMT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'incurred_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_INCURRED_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'commission_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_COMMISSION_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'arise_cost_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_ARISE_COST_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'profit_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_PROFIT_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'rate_usd', 'float', true, '{/literal}{incomCRM_translate label='LBL_RATE_USD' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'date_payment', 'date', false, '{/literal}{incomCRM_translate label='LBL_DATE_PAYMENT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'payment_period', 'enum', false, '{/literal}{incomCRM_translate label='LBL_PAYMENT_PERIOD' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'order_type', 'enum', true, '{/literal}{incomCRM_translate label='LBL_ORDER_TYPE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'acc_reference_code', 'relate', false, '{/literal}{incomCRM_translate label='LBL_ACC_REFERENCE_CODE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'account_email', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_EMAIL' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'account_phone', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_PHONE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'account_address', 'text', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_ADDRESS' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'account_tax_code', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_TAX_CODE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'sector_vertical', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_SECTOR_VERTICAL' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'shipping_fee', 'currency', false, '{/literal}{incomCRM_translate label='LBL_SHIPPING_FEE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'no_currency', 'bool', false, '{/literal}{incomCRM_translate label='LBL_NO_CURRENCY' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'disable_auto_price', 'bool', false, '{/literal}{incomCRM_translate label='LBL_DISABLE_AUTO_PRICE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ordered_merge', 'bool', false, '{/literal}{incomCRM_translate label='LBL_ORDERED_MERGE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'total_foreign_amount', 'double', false, '{/literal}{incomCRM_translate label='LBL_TOTAL_FOREIGN_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'vat_spec_amt', 'currency', false, '{/literal}{incomCRM_translate label='LBL_VAT_SPEC_AMT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'vat_imp_amt', 'currency', false, '{/literal}{incomCRM_translate label='LBL_VAT_IMP_AMT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'vat_total_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_VAT_TOTAL_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'ext_incurred_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_EXT_INCURRED_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'int_incurred_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_INT_INCURRED_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'is_import', 'bool', false, '{/literal}{incomCRM_translate label='LBL_IS_IMPORT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'money_payable', 'double', false, '{/literal}{incomCRM_translate label='LBL_MONEY_PAYABLE' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'money_deposit', 'currency', false, '{/literal}{incomCRM_translate label='LBL_MONEY_DEPOSIT_KEY' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'money_refund', 'currency', false, '{/literal}{incomCRM_translate label='LBL_MONEY_REFUND' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'location_nearby', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_LOCATION_NEARBY' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'service_total', 'int', false, '{/literal}{incomCRM_translate label='LBL_SERVICE_TOTAL' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'service_used', 'int', false, '{/literal}{incomCRM_translate label='LBL_SERVICE_USED' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'service_left', 'int', false, '{/literal}{incomCRM_translate label='LBL_SERVICE_LEFT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'date_return', 'date', false, '{/literal}{incomCRM_translate label='LBL_DATE_RETURN' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'prod_returned', 'bool', false, '{/literal}{incomCRM_translate label='LBL_PROD_RETURNED' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'share_users[]', 'enum', false, '{/literal}{incomCRM_translate label='LBL_SHARING_USERS' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'share_users_ex[]', 'enum', false, '{/literal}{incomCRM_translate label='LBL_SHARING_USERS' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'membership_card', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_MEMBERSHIP_CARD' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'point_current', 'double', false, '{/literal}{incomCRM_translate label='LBL_POINT_CURRENT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'table_params', 'text', false, '{/literal}{incomCRM_translate label='Params' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'acc_bill_addr', 'text', false, '{/literal}{incomCRM_translate label='LBL_ACC_BILL_ADDR' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 're_calc_profit', 'bool', false, '{/literal}{incomCRM_translate label='LBL_RE_CALC_PROFIT' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'date_record', 'date', false, '{/literal}{incomCRM_translate label='LBL_DATE_RECORD' module='Opportunities'}{literal}' );
addToValidate('form_EditSO_Opportunities', 'is_export_vat', 'bool', false, '{/literal}{incomCRM_translate label='LBL_IS_EXPORT_VAT' module='Opportunities'}{literal}' );
addToValidateBinaryDependency('form_EditSO_Opportunities', 'assigned_user_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Opportunities'}{literal}{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO' module='Opportunities'}{literal}', 'assigned_user_id' );
addToValidateBinaryDependency('form_EditSO_Opportunities', 'account_name', 'alpha', true, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Opportunities'}{literal}{/literal}{incomCRM_translate label='LBL_ACCOUNT_NAME' module='Opportunities'}{literal}', 'account_id' );
addToValidateBinaryDependency('form_EditSO_Opportunities', 'handle_user_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Opportunities'}{literal}{/literal}{incomCRM_translate label='LBL_HANDLE_USER_NAME' module='Opportunities'}{literal}', 'handle_user_id' );
addToValidateBinaryDependency('form_EditSO_Opportunities', 'project_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Opportunities'}{literal}{/literal}{incomCRM_translate label='LBL_PROJECT_NAME' module='Opportunities'}{literal}', 'project_id' );
addToValidateBinaryDependency('form_EditSO_Opportunities', 'contact_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Opportunities'}{literal}{/literal}{incomCRM_translate label='LBL_CONTACT_NAME' module='Opportunities'}{literal}', 'contact_id' );
</script>
<script type="text/javascript">if(typeof sqs_objects == 'undefined'){var sqs_objects = new Array;}
sqs_objects['form_EditSO_Opportunities_contact_name'] = {"form":"form_EditSO_Opportunities","method":"get_contact_array","modules":["Contacts"],"field_list":["salutation","first_name","last_name","id","phone_mobile"],"populate_list":["contact_name","contact_id","contact_id","contact_id","contact_phone"],"required_list":["contact_id"],"group":"or","conditions":[{"name":"first_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"last_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['form_EditSO_Opportunities_project_name'] = {"form":"form_EditSO_Opportunities","method":"query","modules":["Project"],"group":"or","field_list":["name","id"],"populate_list":["project_name","project_id"],"required_list":["parent_id"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['form_EditSO_Opportunities_handle_user_name'] = {"form":"form_EditSO_Opportunities","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["handle_user_name","handle_user_id"],"required_list":["handle_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['form_EditSO_Opportunities_assigned_user_name'] = {"form":"form_EditSO_Opportunities","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["assigned_user_name","assigned_user_id"],"required_list":["assigned_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
</script>{/literal}
