<?php
// created: 2025-03-03 22:47:49
$G<PERSON><PERSON><PERSON><PERSON>["dictionary"]["Contracts_Invoice"] = array (
  'table' => 'contracts_invoice',
  'audited' => true,
  'unified_search' => false,
  'duplicate_merge' => false,
  'optimistic_locking' => true,
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'varchar',
      'len' => '100',
      'audited' => true,
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
      'comment' => 'Full text of the note',
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'contracts_invoice_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'contracts_invoice_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'branch_id' => 
    array (
      'name' => 'branch_id',
      'vname' => 'LBL_BRANCH_ID',
      'type' => 'enum',
      'options' => 'users_branch_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'BranchID of assigned user',
    ),
    'department_id' => 
    array (
      'name' => 'department_id',
      'vname' => 'LBL_DEPARTMENT_ID',
      'type' => 'enum',
      'options' => 'users_department_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'DepartmentID of assigned user',
    ),
    'assigned_user_id' => 
    array (
      'name' => 'assigned_user_id',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'vname' => 'LBL_ASSIGNED_TO_ID',
      'group' => 'assigned_user_name',
      'type' => 'relate',
      'table' => 'users',
      'module' => 'Users',
      'isnull' => 'false',
      'dbType' => 'id',
      'audited' => true,
      'reportable' => true,
      'duplicate_merge' => 'disabled',
      'comment' => 'User ID assigned to record',
    ),
    'assigned_user_name' => 
    array (
      'name' => 'assigned_user_name',
      'link' => 'assigned_user_link',
      'vname' => 'LBL_ASSIGNED_TO_NAME',
      'rname' => 'user_name',
      'type' => 'relate',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'assigned_user_id',
      'module' => 'Users',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'assigned_user_link' => 
    array (
      'name' => 'assigned_user_link',
      'type' => 'link',
      'relationship' => 'contracts_invoice_assigned_user',
      'vname' => 'LBL_ASSIGNED_TO_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
      'duplicate_merge' => 'enabled',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'table' => 'users',
    ),
    'rowindex' => 
    array (
      'name' => 'rowindex',
      'vname' => 'LBL_ROWINDEX',
      'type' => 'int',
      'len' => '11',
      'massupdate' => false,
    ),
    'refno' => 
    array (
      'name' => 'refno',
      'vname' => 'LBL_REFNO',
      'type' => 'varchar',
      'len' => '50',
      'audited' => true,
    ),
    'payment_type' => 
    array (
      'name' => 'payment_type',
      'vname' => 'LBL_PAYMENT_TYPE',
      'type' => 'int',
      'len' => '5',
      'audited' => true,
      'massupdate' => false,
      'default' => '0',
    ),
    'date_expired' => 
    array (
      'name' => 'date_expired',
      'vname' => 'LBL_DATE_EXPIRED',
      'type' => 'date',
      'massupdate' => false,
      'audited' => true,
    ),
    'invoice_date' => 
    array (
      'name' => 'invoice_date',
      'vname' => 'LBL_INVOICE_DATE',
      'type' => 'date',
      'massupdate' => false,
      'audited' => true,
    ),
    'invoice_amount' => 
    array (
      'name' => 'invoice_amount',
      'vname' => 'LBL_INVOICE_AMOUNT',
      'type' => 'double',
      'audited' => true,
    ),
    'invoice_tax' => 
    array (
      'name' => 'invoice_tax',
      'vname' => 'LBL_INVOICE_TAX',
      'type' => 'float',
      'audited' => true,
      'default' => '0',
    ),
    'amount_befor_tax' => 
    array (
      'name' => 'amount_befor_tax',
      'vname' => 'LBL_AMOUNT_BEFOR_TAX',
      'type' => 'double',
      'audited' => true,
    ),
    'contract_id' => 
    array (
      'name' => 'contract_id',
      'vname' => 'LBL_CONTRACT_ID',
      'type' => 'id',
      'massupdate' => false,
      'audited' => true,
    ),
    'contract_code' => 
    array (
      'name' => 'contract_code',
      'vname' => 'LBL_CONTRACT_CODE',
      'id_name' => 'contract_id',
      'rname' => 'code',
      'type' => 'relate',
      'link' => 'contracts',
      'module' => 'Contracts',
      'source' => 'non-db',
      'audited' => true,
    ),
    'contract_name' => 
    array (
      'name' => 'contract_name',
      'vname' => 'LBL_CONTRACT_NAME',
      'id_name' => 'contract_id',
      'rname' => 'name',
      'type' => 'relate',
      'link' => 'contracts',
      'module' => 'Contracts',
      'source' => 'non-db',
      'audited' => true,
    ),
    'contracts' => 
    array (
      'name' => 'contracts',
      'vname' => 'LBL_CONTRACTS',
      'type' => 'link',
      'source' => 'non-db',
      'relationship' => 'contracts_contracts_invoice',
    ),
    'SecurityGroups' => 
    array (
      'name' => 'SecurityGroups',
      'type' => 'link',
      'relationship' => 'securitygroups_contracts_invoice',
      'module' => 'SecurityGroups',
      'bean_name' => 'SecurityGroup',
      'source' => 'non-db',
      'vname' => 'LBL_SECURITYGROUPS',
    ),
  ),
  'indices' => 
  array (
    'id' => 
    array (
      'name' => 'contracts_invoicepk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    'idx_contracts_invoice_branch_id' => 
    array (
      'name' => 'idx_contracts_invoice_branch_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
      ),
    ),
    'idx_contracts_invoice_department_id' => 
    array (
      'name' => 'idx_contracts_invoice_department_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'department_id',
      ),
    ),
    'idx_contracts_invoice_branch_dept' => 
    array (
      'name' => 'idx_contracts_invoice_branch_dept',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
        1 => 'department_id',
      ),
    ),
    'idx_contracts_invoice_assigned' => 
    array (
      'name' => 'idx_contracts_invoice_assigned',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'assigned_user_id',
      ),
    ),
    0 => 
    array (
      'name' => 'idx_contracts_invoice_name',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'name',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_contracts_invoice_refno',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'refno',
      ),
    ),
    2 => 
    array (
      'name' => 'idx_contracts_invoice_cnt',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'contract_id',
      ),
    ),
    3 => 
    array (
      'name' => 'idx_contracts_invoice_assigned_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'assigned_user_id',
      ),
    ),
  ),
  'relationships' => 
  array (
    'contracts_invoice_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Contracts_Invoice',
      'rhs_table' => 'contracts_invoice',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'contracts_invoice_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Contracts_Invoice',
      'rhs_table' => 'contracts_invoice',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
    'contracts_invoice_assigned_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Contracts_Invoice',
      'rhs_table' => 'contracts_invoice',
      'rhs_key' => 'assigned_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'contracts_contracts_invoice' => 
    array (
      'lhs_module' => 'Contracts',
      'lhs_table' => 'contracts',
      'lhs_key' => 'id',
      'rhs_module' => 'Contracts_Invoice',
      'rhs_table' => 'contracts_invoice',
      'rhs_key' => 'contract_id',
      'relationship_type' => 'one-to-many',
    ),
  ),
  'templates' => 
  array (
    'assignable' => 'assignable',
    'groups' => 'groups',
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
