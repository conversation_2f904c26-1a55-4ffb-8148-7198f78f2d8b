<?php
// created: 2025-02-26 15:23:57
$G<PERSON><PERSON><PERSON><PERSON>["dictionary"]["Product_Gift"] = array (
  'table' => 'product_gifts',
  'audited' => true,
  'unified_search' => false,
  'duplicate_merge' => false,
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'name',
      'dbType' => 'varchar',
      'len' => 250,
      'acl' => true,
      'audited' => true,
      'unified_search' => true,
      'merge_filter' => 'selected',
      'importable' => 'required',
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
      'comment' => 'Full text of the note',
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'product_gifts_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'product_gifts_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'branch_id' => 
    array (
      'name' => 'branch_id',
      'vname' => 'LBL_BRANCH_ID',
      'type' => 'enum',
      'options' => 'users_branch_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'BranchID of assigned user',
    ),
    'department_id' => 
    array (
      'name' => 'department_id',
      'vname' => 'LBL_DEPARTMENT_ID',
      'type' => 'enum',
      'options' => 'users_department_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'DepartmentID of assigned user',
    ),
    'assigned_user_id' => 
    array (
      'name' => 'assigned_user_id',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'vname' => 'LBL_ASSIGNED_TO_ID',
      'group' => 'assigned_user_name',
      'type' => 'relate',
      'table' => 'users',
      'module' => 'Users',
      'isnull' => 'false',
      'dbType' => 'id',
      'audited' => true,
      'reportable' => true,
      'duplicate_merge' => 'disabled',
      'comment' => 'User ID assigned to record',
    ),
    'assigned_user_name' => 
    array (
      'name' => 'assigned_user_name',
      'link' => 'assigned_user_link',
      'vname' => 'LBL_ASSIGNED_TO_NAME',
      'rname' => 'user_name',
      'type' => 'relate',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'assigned_user_id',
      'module' => 'Users',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'assigned_user_link' => 
    array (
      'name' => 'assigned_user_link',
      'type' => 'link',
      'relationship' => 'product_gifts_assigned_user',
      'vname' => 'LBL_ASSIGNED_TO_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
      'duplicate_merge' => 'enabled',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'table' => 'users',
    ),
    'status' => 
    array (
      'name' => 'status',
      'vname' => 'LBL_STATUS',
      'type' => 'enum',
      'options' => 'products_status_dom',
      'len' => 20,
      'audited' => true,
      'default' => 'Active',
    ),
    'date_valid_from' => 
    array (
      'name' => 'date_valid_from',
      'vname' => 'LBL_DATE_VALID_FROM',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'date_valid_to' => 
    array (
      'name' => 'date_valid_to',
      'vname' => 'LBL_DATE_VALID_TO',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'qty_buy' => 
    array (
      'name' => 'qty_buy',
      'vname' => 'LBL_QTY_BUY',
      'type' => 'int',
      'len' => 4,
      'audited' => true,
    ),
    'qty_gift' => 
    array (
      'name' => 'qty_gift',
      'vname' => 'LBL_QTY_GIFT',
      'type' => 'int',
      'len' => 4,
      'audited' => true,
    ),
    'gift_type' => 
    array (
      'name' => 'gift_type',
      'vname' => 'LBL_GIFT_TYPE',
      'type' => 'enum',
      'options' => 'product_gift_type_dom',
      'dbType' => 'char',
      'len' => 1,
      'audited' => true,
      'massupdate' => false,
      'display_default' => '1',
    ),
    'branch_all' => 
    array (
      'name' => 'branch_all',
      'vname' => 'LBL_BRANCH_ALL',
      'type' => 'enum',
      'options' => 'branch_all_dom',
      'dbType' => 'text',
      'isMultiSelect' => true,
      'isCheckBoxList' => true,
      'audited' => true,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
    ),
    'membership_all' => 
    array (
      'name' => 'membership_all',
      'vname' => 'LBL_MEMBERSHIP_ALL',
      'type' => 'enum',
      'options' => 'membership_card_dom',
      'dbType' => 'varchar',
      'len' => 250,
      'isMultiSelect' => true,
      'isCheckBoxList' => true,
      'audited' => true,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
    ),
    'account_type_all' => 
    array (
      'name' => 'account_type_all',
      'vname' => 'LBL_ACCOUNT_TYPE_ALL',
      'type' => 'enum',
      'options' => 'account_type_dom',
      'dbType' => 'varchar',
      'len' => 250,
      'isMultiSelect' => true,
      'isCheckBoxList' => true,
      'audited' => true,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
    ),
    'tqt_product_id' => 
    array (
      'name' => 'tqt_product_id',
      'vname' => 'LBL_TQT_PRODUCT_ID',
      'type' => 'id',
      'audited' => true,
      'massupdate' => false,
      'required' => false,
      'reportable' => false,
    ),
    'tqt_product_name' => 
    array (
      'name' => 'tqt_product_name',
      'rname' => 'name',
      'id_name' => 'tqt_product_id',
      'vname' => 'LBL_TQT_PRODUCT_NAME',
      'table' => 'tqt_products',
      'type' => 'relate',
      'link' => 'tqt_products',
      'join_name' => 'tqt_products',
      'isnull' => 'true',
      'module' => 'TQT_Products',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'tqt_products' => 
    array (
      'name' => 'tqt_products',
      'type' => 'link',
      'relationship' => 'product_gifts_related',
      'vname' => 'LBL_TQT_PRODUCTS',
      'source' => 'non-db',
    ),
    'product_approved' => 
    array (
      'name' => 'product_approved',
      'type' => 'link',
      'relationship' => 'product_gift_approved',
      'vname' => 'LBL_PRODUCT_APPROVED',
      'source' => 'non-db',
    ),
    'product_codes' => 
    array (
      'name' => 'product_codes',
      'vname' => 'LBL_PRODUCT_CODES',
      'type' => 'varchar',
      'source' => 'non-db',
    ),
    'prod_gift_self' => 
    array (
      'name' => 'prod_gift_self',
      'vname' => 'LBL_PROD_GIFT_SELF',
      'type' => 'bool',
      'source' => 'non-db',
      'display_default' => '0',
      'massupdate' => false,
    ),
  ),
  'indices' => 
  array (
    'id' => 
    array (
      'name' => 'product_giftspk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    'idx_product_gifts_branch_id' => 
    array (
      'name' => 'idx_product_gifts_branch_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
      ),
    ),
    'idx_product_gifts_department_id' => 
    array (
      'name' => 'idx_product_gifts_department_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'department_id',
      ),
    ),
    'idx_product_gifts_branch_dept' => 
    array (
      'name' => 'idx_product_gifts_branch_dept',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
        1 => 'department_id',
      ),
    ),
    'idx_product_gifts_assigned' => 
    array (
      'name' => 'idx_product_gifts_assigned',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'assigned_user_id',
      ),
    ),
    0 => 
    array (
      'name' => 'idx_product_gift_id_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'id',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_product_gift_name_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'name',
      ),
    ),
    2 => 
    array (
      'name' => 'idx_product_gift_status_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'status',
      ),
    ),
    3 => 
    array (
      'name' => 'idx_product_gift_product_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'tqt_product_id',
      ),
    ),
    4 => 
    array (
      'name' => 'idx_product_gift_date_valid',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'date_valid_from',
        1 => 'date_valid_to',
      ),
    ),
  ),
  'relationships' => 
  array (
    'product_gifts_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Product_Gifts',
      'rhs_table' => 'product_gifts',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'product_gifts_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Product_Gifts',
      'rhs_table' => 'product_gifts',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
    'product_gifts_assigned_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Product_Gifts',
      'rhs_table' => 'product_gifts',
      'rhs_key' => 'assigned_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'product_gifts_related' => 
    array (
      'lhs_module' => 'TQT_Products',
      'lhs_table' => 'tqt_products',
      'lhs_key' => 'id',
      'rhs_module' => 'Product_Gifts',
      'rhs_table' => 'product_gifts',
      'rhs_key' => 'tqt_product_id',
      'relationship_type' => 'one-to-many',
    ),
  ),
  'optimistic_lock' => true,
  'templates' => 
  array (
    'assignable' => 'assignable',
    'groups' => 'groups',
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
