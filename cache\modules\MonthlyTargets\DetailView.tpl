
<section id="DetailViewBody" class="view-module-{$module|strtolower}">

<form action="index.php" method="post" name="{$form_name|default:'DetailView'}" id="form" class="_view-{$instanceName}">
<table cellpadding="1" cellspacing="0" border="0" width="100%">
<tr>
<td class="buttons" align="left" valign="top" nowrap>
<input type="hidden" name="module" value="{$module}" />
<input type="hidden" name="action" value="EditView" />
<input type="hidden" name="record" value="{$fields.id.value}" />
<input type="hidden" name="return_action" />
<input type="hidden" name="return_module" />
<input type="hidden" name="return_id" />
<input type="hidden" name="module_tab" /> 
<input type="hidden" name="isDuplicate" value="false" />
<input type="hidden" name="offset" value="{$offset}" />
{if $bean->aclAccess("edit")}<input title="{$APP.LBL_EDIT_BUTTON_TITLE}" accessKey="{$APP.LBL_EDIT_BUTTON_KEY}" class="button bedit" onclick="this.form.return_module.value='MonthlyTargets'; this.form.return_action.value='DetailView'; this.form.return_id.value='{$id}'; this.form.action.value='EditView';" type="submit" name="Edit" id="edit_button" value="{$APP.LBL_EDIT_BUTTON_LABEL}" />{/if} 
{if $bean->aclAccess("edit")}<input title="{$APP.LBL_DUPLICATE_BUTTON_TITLE}" accessKey="{$APP.LBL_DUPLICATE_BUTTON_KEY}" class="button duplicate" onclick="this.form.return_module.value='MonthlyTargets'; this.form.return_action.value='DetailView'; this.form.isDuplicate.value=true; this.form.action.value='EditView'; this.form.return_id.value='{$id}';" type="submit" name="Duplicate" value="{$APP.LBL_DUPLICATE_BUTTON_LABEL}" id="duplicate_button" />{/if} 
{if $bean->aclAccess("delete")}<input title="{$APP.LBL_DELETE_BUTTON_TITLE}" accessKey="{$APP.LBL_DELETE_BUTTON_KEY}" class="button delete" onclick="this.form.return_module.value='MonthlyTargets'; this.form.return_action.value='ListView'; this.form.action.value='Delete'; return confirm('{$APP.NTC_DELETE_CONFIRMATION}');" type="submit" name="Delete" value="{$APP.LBL_DELETE_BUTTON_LABEL}" />{/if} 
<input title="{$APP.LBL_EXPORT}" class="button" type="button" name="exp_button" value="{$APP.LBL_EXPORT}" onclick="window.open('index.php?entryPoint=export&module=MonthlyTargets&uid={$fields.id.value}&members=1')">
</td>
<td class="buttons" align="left" valign="top" nowrap>
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=MonthlyTargets", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</td>
<td align="right" width="90%">{$ADMIN_EDIT}</td>
</tr>
</table>
</form>
{incomCRM_include include=$includes}
<div id="DEFAULT" class="detailview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<div class="detail view {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="DEFAULT_GROUP" style="display:block">
<table id='detailpanel_1' cellspacing='{$gridline}'>
{$PAGINATION}
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_MM' module='MonthlyTargets'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_mm_field' >
{counter name="panelFieldCount"}
{$fields.mm.value}/{$fields.yy.value}	
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_REVENUE' module='MonthlyTargets'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_revenue_field' >
{counter name="panelFieldCount"}

<span id='{$fields.revenue.name}' >
{$fields.revenue.value}
</span>
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='MonthlyTargets'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_assigned_user_name_field' >
{counter name="panelFieldCount"}

{$fields.assigned_user_name.value}
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_COLLECTION' module='MonthlyTargets'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_collection_field' >
{counter name="panelFieldCount"}

<span id='{$fields.collection.name}' >
{$fields.collection.value}
</span>
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DESCRIPTION' module='MonthlyTargets'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_description_field' >
{counter name="panelFieldCount"}

{$fields.description.value|url2html|nl2br}
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_NEW_SHOP' module='MonthlyTargets'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_new_shop_field' >
{counter name="panelFieldCount"}

<span id='{$fields.new_shop.name}' >
{$fields.new_shop.value}
</span>
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DATE_ENTERED' module='MonthlyTargets'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_date_entered_field' >
{counter name="panelFieldCount"}
{$fields.date_entered.value} {$APP.LBL_BY} {$fields.created_by_name.value}	
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_OUTPUT' module='MonthlyTargets'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_output_field' >
{counter name="panelFieldCount"}

<span id='{$fields.output.name}' >
{$fields.output.value}
</span>
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DATE_MODIFIED' module='MonthlyTargets'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_date_modified_field' >
{counter name="panelFieldCount"}
{$fields.date_modified.value} {$APP.LBL_BY} {$fields.modified_by_name.value}	
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ERROR_KPI' module='MonthlyTargets'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_error_kpi_field' >
{counter name="panelFieldCount"}

<span id='{$fields.error_kpi.name}' >
{$fields.error_kpi.value}
</span>
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_TS_REVENUE' module='MonthlyTargets'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_ts_revenue_field' >
{counter name="panelFieldCount"}

<span id='{$fields.ts_revenue.name}' >
{$fields.ts_revenue.value}
</span>
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_TS_COLLECTION' module='MonthlyTargets'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_ts_collection_field' >
{counter name="panelFieldCount"}

<span id='{$fields.ts_collection.name}' >
{$fields.ts_collection.value}
</span>
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_TS_NEW_SHOP' module='MonthlyTargets'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_ts_new_shop_field' >
{counter name="panelFieldCount"}

<span id='{$fields.ts_new_shop.name}' >
{$fields.ts_new_shop.value}
</span>
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_TS_KPI' module='MonthlyTargets'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_ts_kpi_field' >
{counter name="panelFieldCount"}

<span id='{$fields.ts_kpi.name}' >
{$fields.ts_kpi.value}
</span>
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("DEFAULT").style.display='none';</script>
{/if}

</section>