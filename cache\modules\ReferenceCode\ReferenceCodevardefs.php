<?php
// created: 2025-03-03 22:47:49
$G<PERSON><PERSON><PERSON><PERSON>["dictionary"]["ReferenceCode"] = array (
  'table' => 'accounts',
  'audited' => true,
  'unified_search' => false,
  'duplicate_merge' => false,
  'comment' => 'Accounts are organizations or entities that are the target of selling, support, and marketing activities, or have already purchased products or services',
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'type' => 'name',
      'dbType' => 'varchar',
      'vname' => 'LBL_NAME',
      'len' => 200,
      'comment' => 'Name of the Company',
      'audited' => true,
      'unified_search' => true,
      'merge_filter' => 'selected',
      'importable' => 'required',
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
      'comment' => 'Full text of the note',
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'accounts_created_by',
      'vname' => 'LBL_CREATED_BY_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'accounts_modified_user',
      'vname' => 'LBL_MODIFIED_BY_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'branch_id' => 
    array (
      'name' => 'branch_id',
      'vname' => 'LBL_BRANCH_ID',
      'type' => 'enum',
      'options' => 'users_branch_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'BranchID of assigned user',
    ),
    'department_id' => 
    array (
      'name' => 'department_id',
      'vname' => 'LBL_DEPARTMENT_ID',
      'type' => 'enum',
      'options' => 'users_department_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'DepartmentID of assigned user',
    ),
    'assigned_user_id' => 
    array (
      'name' => 'assigned_user_id',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'vname' => 'LBL_ASSIGNED_TO_ID',
      'group' => 'assigned_user_name',
      'type' => 'relate',
      'table' => 'users',
      'module' => 'Users',
      'isnull' => 'false',
      'dbType' => 'id',
      'audited' => true,
      'reportable' => true,
      'duplicate_merge' => 'disabled',
      'comment' => 'User ID assigned to record',
    ),
    'assigned_user_name' => 
    array (
      'name' => 'assigned_user_name',
      'link' => 'assigned_user_link',
      'vname' => 'LBL_ASSIGNED_TO_NAME',
      'rname' => 'user_name',
      'type' => 'relate',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'assigned_user_id',
      'module' => 'Users',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'assigned_user_link' => 
    array (
      'name' => 'assigned_user_link',
      'type' => 'link',
      'relationship' => 'accounts_assigned_user',
      'vname' => 'LBL_ASSIGNED_TO_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
      'duplicate_merge' => 'enabled',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'table' => 'users',
    ),
    'account_type' => 
    array (
      'name' => 'account_type',
      'vname' => 'LBL_TYPE',
      'type' => 'enum',
      'options' => 'account_type_dom',
      'len' => 50,
      'audited' => true,
      'comment' => 'The Company is of this type',
      'display_default' => 'CT',
    ),
    'industry' => 
    array (
      'name' => 'industry',
      'vname' => 'LBL_INDUSTRY',
      'type' => 'enum',
      'options' => 'industry_dom',
      'len' => 50,
      'audited' => true,
      'comment' => 'The company belongs in this industry',
      'merge_filter' => 'disabled',
      'massupdate' => true,
    ),
    'annual_revenue' => 
    array (
      'name' => 'annual_revenue',
      'vname' => 'LBL_ANNUAL_REVENUE',
      'type' => 'varchar',
      'len' => 25,
      'comment' => 'Annual revenue for this company',
    ),
    'billing_address_street' => 
    array (
      'name' => 'billing_address_street',
      'vname' => 'LBL_BILLING_ADDRESS_STREET',
      'type' => 'text',
      'len' => NULL,
      'comment' => 'The street address used for billing address',
      'group' => 'billing_address',
      'merge_filter' => 'enabled',
      'unified_search' => true,
      'audited' => true,
    ),
    'billing_address_street_2' => 
    array (
      'name' => 'billing_address_street_2',
      'vname' => 'LBL_BILLING_ADDRESS_STREET_2',
      'type' => 'varchar',
      'len' => '150',
      'source' => 'non-db',
    ),
    'billing_address_street_3' => 
    array (
      'name' => 'billing_address_street_3',
      'vname' => 'LBL_BILLING_ADDRESS_STREET_3',
      'type' => 'varchar',
      'len' => '150',
      'source' => 'non-db',
    ),
    'billing_address_street_4' => 
    array (
      'name' => 'billing_address_street_4',
      'vname' => 'LBL_BILLING_ADDRESS_STREET_4',
      'type' => 'varchar',
      'len' => '150',
      'source' => 'non-db',
    ),
    'billing_address_city' => 
    array (
      'name' => 'billing_address_city',
      'vname' => 'LBL_BILLING_ADDRESS_CITY',
      'type' => 'varchar',
      'len' => '100',
      'comment' => 'The city used for billing address',
      'group' => 'billing_address',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
    ),
    'billing_address_state' => 
    array (
      'name' => 'billing_address_state',
      'vname' => 'LBL_BILLING_ADDRESS_STATE',
      'type' => 'varchar',
      'len' => '100',
      'group' => 'billing_address',
      'comment' => 'The state used for billing address',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
    ),
    'billing_address_postalcode' => 
    array (
      'name' => 'billing_address_postalcode',
      'vname' => 'LBL_BILLING_ADDRESS_POSTALCODE',
      'type' => 'varchar',
      'len' => '20',
      'group' => 'billing_address',
      'comment' => 'The postal code used for billing address',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
    ),
    'billing_address_country' => 
    array (
      'name' => 'billing_address_country',
      'vname' => 'LBL_BILLING_ADDRESS_COUNTRY',
      'type' => 'varchar',
      'group' => 'billing_address',
      'comment' => 'The country used for the billing address',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
    ),
    'rating' => 
    array (
      'name' => 'rating',
      'vname' => 'LBL_RATING',
      'type' => 'enum',
      'options' => 'account_rate_dom',
      'len' => 30,
      'audited' => true,
      'massupdate' => false,
    ),
    'phone_office' => 
    array (
      'name' => 'phone_office',
      'vname' => 'LBL_PHONE_OFFICE',
      'type' => 'phone',
      'dbType' => 'varchar',
      'len' => 100,
      'audited' => true,
      'unified_search' => true,
      'comment' => 'The office phone number',
      'merge_filter' => 'enabled',
    ),
    'phone_alternate' => 
    array (
      'name' => 'phone_alternate',
      'vname' => 'LBL_PHONE_ALT',
      'type' => 'phone',
      'group' => 'phone_office',
      'dbType' => 'varchar',
      'len' => 100,
      'audited' => true,
      'unified_search' => true,
      'comment' => 'An alternate phone number',
      'merge_filter' => 'enabled',
    ),
    'phone_fax' => 
    array (
      'name' => 'phone_fax',
      'vname' => 'LBL_FAX',
      'type' => 'phone',
      'dbType' => 'varchar',
      'len' => 100,
      'audited' => true,
      'unified_search' => true,
      'comment' => 'The fax phone number of this company',
    ),
    'website' => 
    array (
      'name' => 'website',
      'vname' => 'LBL_WEBSITE',
      'type' => 'varchar',
      'len' => 255,
      'comment' => 'URL of website for the company',
    ),
    'ownership' => 
    array (
      'name' => 'ownership',
      'vname' => 'LBL_OWNERSHIP',
      'type' => 'varchar',
      'len' => 100,
      'comment' => '',
    ),
    'employees' => 
    array (
      'name' => 'employees',
      'vname' => 'LBL_EMPLOYEES',
      'type' => 'varchar',
      'len' => 10,
      'comment' => 'Number of employees, varchar to accomodate for both number (100) or range (50-100)',
    ),
    'ticker_symbol' => 
    array (
      'name' => 'ticker_symbol',
      'vname' => 'LBL_TICKER_SYMBOL',
      'type' => 'varchar',
      'len' => 10,
      'comment' => 'The stock trading (ticker) symbol for the company',
    ),
    'shipping_address_street' => 
    array (
      'name' => 'shipping_address_street',
      'vname' => 'LBL_SHIPPING_ADDRESS_STREET',
      'type' => 'text',
      'len' => NULL,
      'group' => 'shipping_address',
      'comment' => 'The street address used for for shipping purposes',
      'merge_filter' => 'enabled',
      'unified_search' => true,
      'audited' => true,
    ),
    'shipping_address_street_2' => 
    array (
      'name' => 'shipping_address_street_2',
      'vname' => 'LBL_SHIPPING_ADDRESS_STREET_2',
      'type' => 'varchar',
      'len' => 150,
      'source' => 'non-db',
    ),
    'shipping_address_street_3' => 
    array (
      'name' => 'shipping_address_street_3',
      'vname' => 'LBL_SHIPPING_ADDRESS_STREET_3',
      'type' => 'varchar',
      'len' => 150,
      'source' => 'non-db',
    ),
    'shipping_address_street_4' => 
    array (
      'name' => 'shipping_address_street_4',
      'vname' => 'LBL_SHIPPING_ADDRESS_STREET_4',
      'type' => 'varchar',
      'len' => 150,
      'source' => 'non-db',
    ),
    'shipping_address_city' => 
    array (
      'name' => 'shipping_address_city',
      'vname' => 'LBL_SHIPPING_ADDRESS_CITY',
      'type' => 'varchar',
      'len' => 100,
      'group' => 'shipping_address',
      'comment' => 'The city used for the shipping address',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
    ),
    'shipping_address_state' => 
    array (
      'name' => 'shipping_address_state',
      'vname' => 'LBL_SHIPPING_ADDRESS_STATE',
      'type' => 'varchar',
      'len' => 100,
      'group' => 'shipping_address',
      'comment' => 'The state used for the shipping address',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
    ),
    'shipping_address_postalcode' => 
    array (
      'name' => 'shipping_address_postalcode',
      'vname' => 'LBL_SHIPPING_ADDRESS_POSTALCODE',
      'type' => 'varchar',
      'len' => 20,
      'group' => 'shipping_address',
      'comment' => 'The zip code used for the shipping address',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
    ),
    'shipping_address_country' => 
    array (
      'name' => 'shipping_address_country',
      'vname' => 'LBL_SHIPPING_ADDRESS_COUNTRY',
      'type' => 'varchar',
      'group' => 'shipping_address',
      'comment' => 'The country used for the shipping address',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
    ),
    'email1' => 
    array (
      'name' => 'email1',
      'vname' => 'LBL_EMAIL',
      'group' => 'email1',
      'type' => 'varchar',
      'function' => 
      array (
        'name' => 'getEmailAddressWidget',
        'returns' => 'html',
      ),
      'source' => 'non-db',
      'studio' => 'false',
      'audited' => true,
    ),
    'email2' => 
    array (
      'name' => 'email2',
      'vname' => 'LBL_OTHER_EMAIL',
      'group' => 'email2',
      'type' => 'varchar',
      'function' => 
      array (
        'name' => 'getEmailAddressWidget',
        'returns' => 'html',
      ),
      'source' => 'non-db',
      'studio' => 'false',
      'audited' => true,
    ),
    'email_addresses_primary' => 
    array (
      'name' => 'email_addresses_primary',
      'type' => 'link',
      'relationship' => 'accounts_email_addresses_primary',
      'source' => 'non-db',
      'vname' => 'LBL_EMAIL_ADDRESS_PRIMARY',
      'duplicate_merge' => 'disabled',
      'massupdate' => false,
    ),
    'email_addresses' => 
    array (
      'name' => 'email_addresses',
      'type' => 'link',
      'relationship' => 'accounts_email_addresses',
      'source' => 'non-db',
      'vname' => 'LBL_EMAIL_ADDRESSES',
      'reportable' => false,
      'unified_search' => true,
      'massupdate' => false,
      'rel_fields' => 
      array (
        'primary_address' => 
        array (
          'type' => 'bool',
        ),
      ),
    ),
    'overdue_debts' => 
    array (
      'name' => 'overdue_debts',
      'vname' => 'LBL_OVERDUE_DEBTS',
      'type' => 'float',
      'dbType' => 'double',
      'audited' => false,
      'source' => 'non-db',
    ),
    'sic_code' => 
    array (
      'name' => 'sic_code',
      'vname' => 'LBL_SIC_CODE',
      'type' => 'varchar',
      'len' => 50,
      'comment' => 'SIC code of the account',
    ),
    'parent_id' => 
    array (
      'name' => 'parent_id',
      'vname' => 'LBL_PARENT_ACCOUNT_ID',
      'type' => 'id',
      'required' => false,
      'reportable' => false,
      'audited' => true,
      'comment' => 'Account ID of the parent of this account',
    ),
    'parent_name' => 
    array (
      'name' => 'parent_name',
      'rname' => 'name',
      'id_name' => 'parent_id',
      'vname' => 'LBL_MEMBER_OF',
      'type' => 'relate',
      'table' => 'parent_accounts',
      'isnull' => 'true',
      'module' => 'Accounts',
      'massupdate' => false,
      'source' => 'non-db',
      'len' => 36,
      'link' => 'member_of',
      'unified_search' => true,
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
    ),
    'members' => 
    array (
      'name' => 'members',
      'type' => 'link',
      'relationship' => 'member_accounts',
      'module' => 'Accounts',
      'bean_name' => 'Account',
      'source' => 'non-db',
      'vname' => 'LBL_MEMBERS',
      'duplicate_merge' => 'disabled',
    ),
    'member_of' => 
    array (
      'name' => 'member_of',
      'type' => 'link',
      'relationship' => 'member_accounts',
      'module' => 'Accounts',
      'bean_name' => 'Account',
      'link_type' => 'one',
      'source' => 'non-db',
      'vname' => 'LBL_MEMBER_OF',
      'side' => 'left',
      'duplicate_merge' => 'disabled',
    ),
    'email_opt_out' => 
    array (
      'name' => 'email_opt_out',
      'vname' => 'LBL_EMAIL_OPT_OUT',
      'source' => 'non-db',
      'type' => 'bool',
      'massupdate' => false,
      'studio' => 'false',
    ),
    'invalid_email' => 
    array (
      'name' => 'invalid_email',
      'vname' => 'LBL_INVALID_EMAIL',
      'source' => 'non-db',
      'type' => 'bool',
      'massupdate' => false,
      'studio' => 'false',
    ),
    'cases' => 
    array (
      'name' => 'cases',
      'type' => 'link',
      'relationship' => 'account_cases',
      'module' => 'Cases',
      'bean_name' => 'aCase',
      'source' => 'non-db',
      'vname' => 'LBL_CASES',
    ),
    'tasks' => 
    array (
      'name' => 'tasks',
      'type' => 'link',
      'relationship' => 'account_tasks',
      'module' => 'Tasks',
      'bean_name' => 'Task',
      'source' => 'non-db',
      'vname' => 'LBL_TASKS',
    ),
    'notes' => 
    array (
      'name' => 'notes',
      'type' => 'link',
      'relationship' => 'account_notes',
      'module' => 'Notes',
      'bean_name' => 'Note',
      'source' => 'non-db',
      'vname' => 'LBL_NOTES',
    ),
    'meetings' => 
    array (
      'name' => 'meetings',
      'type' => 'link',
      'relationship' => 'account_meetings',
      'module' => 'Meetings',
      'bean_name' => 'Meeting',
      'source' => 'non-db',
      'vname' => 'LBL_MEETINGS',
    ),
    'calls' => 
    array (
      'name' => 'calls',
      'type' => 'link',
      'relationship' => 'account_calls',
      'module' => 'Calls',
      'bean_name' => 'Call',
      'source' => 'non-db',
      'vname' => 'LBL_CALLS',
    ),
    'emails' => 
    array (
      'name' => 'emails',
      'type' => 'link',
      'relationship' => 'emails_accounts_rel',
      'module' => 'Emails',
      'bean_name' => 'Email',
      'source' => 'non-db',
      'vname' => 'LBL_EMAILS',
    ),
    'bugs' => 
    array (
      'name' => 'bugs',
      'type' => 'link',
      'relationship' => 'accounts_bugs',
      'module' => 'Bugs',
      'bean_name' => 'Bug',
      'source' => 'non-db',
      'vname' => 'LBL_BUGS',
    ),
    'contacts' => 
    array (
      'name' => 'contacts',
      'type' => 'link',
      'relationship' => 'accounts_contacts',
      'module' => 'Contacts',
      'bean_name' => 'Contact',
      'source' => 'non-db',
      'vname' => 'LBL_CONTACTS',
    ),
    'opportunities' => 
    array (
      'name' => 'opportunities',
      'type' => 'link',
      'relationship' => 'accounts_opportunities',
      'module' => 'Opportunities',
      'bean_name' => 'Opportunity',
      'source' => 'non-db',
      'vname' => 'LBL_OPPORTUNITY',
    ),
    'project' => 
    array (
      'name' => 'project',
      'type' => 'link',
      'relationship' => 'projects_accounts',
      'module' => 'Project',
      'bean_name' => 'Project',
      'source' => 'non-db',
      'vname' => 'LBL_PROJECTS',
    ),
    'leads' => 
    array (
      'name' => 'leads',
      'type' => 'link',
      'relationship' => 'account_leads',
      'module' => 'Leads',
      'bean_name' => 'Lead',
      'source' => 'non-db',
      'vname' => 'LBL_LEADS',
    ),
    'campaigns' => 
    array (
      'name' => 'campaigns',
      'type' => 'link',
      'relationship' => 'campaign_accounts',
      'module' => 'Campaigns',
      'bean_name' => 'Campaign',
      'source' => 'non-db',
      'vname' => 'LBL_CAMPAIGNS',
    ),
    'campaign_id' => 
    array (
      'name' => 'campaign_id',
      'comment' => 'Campaign that generated Account',
      'vname' => 'LBL_CAMPAIGN_ID',
      'rname' => 'id',
      'id_name' => 'campaign_id',
      'type' => 'id',
      'table' => 'campaigns',
      'isnull' => 'true',
      'module' => 'Campaigns',
      'reportable' => false,
      'massupdate' => false,
      'duplicate_merge' => 'disabled',
    ),
    'campaign_name' => 
    array (
      'name' => 'campaign_name',
      'rname' => 'name',
      'vname' => 'LBL_CAMPAIGN',
      'type' => 'relate',
      'reportable' => false,
      'source' => 'non-db',
      'table' => 'campaigns',
      'id_name' => 'campaign_id',
      'link' => 'campaigns',
      'module' => 'Campaigns',
      'duplicate_merge' => 'disabled',
      'comment' => 'The first campaign name for Account (Meta-data only)',
    ),
    'prospect_lists' => 
    array (
      'name' => 'prospect_lists',
      'type' => 'link',
      'relationship' => 'prospect_list_accounts',
      'module' => 'ProspectLists',
      'source' => 'non-db',
      'vname' => 'LBL_PROSPECT_LIST',
    ),
    'documents' => 
    array (
      'name' => 'documents',
      'type' => 'link',
      'relationship' => 'accounts_documents',
      'module' => 'Documents',
      'bean_name' => 'Document',
      'source' => 'non-db',
      'vname' => 'LBL_DOCUMENTS',
    ),
    'filename' => 
    array (
      'name' => 'filename',
      'vname' => 'LBL_FILENAME',
      'type' => 'varchar',
      'len' => '200',
      'reportable' => true,
      'comment' => 'File name associated with the note (attachment)',
    ),
    'file_mime_type' => 
    array (
      'name' => 'file_mime_type',
      'vname' => 'LBL_FILE_MIME_TYPE',
      'type' => 'varchar',
      'len' => '100',
      'comment' => 'Attachment MIME type',
      'duplicate_merge' => false,
    ),
    'file_url' => 
    array (
      'name' => 'file_url',
      'vname' => 'LBL_FILE_URL',
      'type' => 'function',
      'function_require' => 'include/upload_file.php',
      'function_class' => 'UploadFile',
      'function_name' => 'get_url',
      'function_params' => 
      array (
        0 => 'filename',
        1 => 'id',
        2 => 'date_entered',
        3 => 'date_modified',
      ),
      'source' => 'function',
      'reportable' => false,
      'comment' => 'Path to file (can be URL)',
    ),
    'cycle_plans' => 
    array (
      'name' => 'cycle_plans',
      'vname' => 'LBL_CYCLE_PLANS',
      'type' => 'link',
      'relationship' => 'cycle_plans_accounts',
      'source' => 'non-db',
    ),
    'cycle_method' => 
    array (
      'name' => 'cycle_method',
      'vname' => 'LBL_CYCLE_METHOD',
      'type' => 'enum',
      'options' => 'cycle_method_calculate_dom',
      'len' => 20,
      'audited' => true,
      'massupdate' => false,
      'default' => 'auto',
    ),
    'receivable_debts' => 
    array (
      'name' => 'receivable_debts',
      'vname' => 'LBL_RECEIVABLE_DEBTS',
      'type' => 'float',
      'dbType' => 'double',
      'audited' => false,
      'source' => 'non-db',
    ),
    'date_debt_expired' => 
    array (
      'name' => 'date_debt_expired',
      'vname' => 'LBL_DATE_DEBT_EXPIRED',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'care_level' => 
    array (
      'name' => 'care_level',
      'vname' => 'LBL_CARE_LEVEL',
      'type' => 'enum',
      'options' => 'account_care_level_dom',
      'len' => 20,
      'audited' => true,
      'massupdate' => false,
      'default' => 'not',
    ),
    'buying_level' => 
    array (
      'name' => 'buying_level',
      'vname' => 'LBL_BUYING_LEVEL',
      'type' => 'enum',
      'options' => 'account_care_level_dom',
      'len' => 20,
      'audited' => true,
      'massupdate' => false,
      'default' => 'not',
    ),
    'trackreview' => 
    array (
      'name' => 'trackreview',
      'type' => 'link',
      'relationship' => 'accounts_trackreview',
      'source' => 'non-db',
      'vname' => 'LBL_TRACK_REVIEWS',
    ),
    'invoices' => 
    array (
      'name' => 'invoices',
      'type' => 'link',
      'relationship' => 'accounts_invoices',
      'source' => 'non-db',
      'vname' => 'LBL_INVOICES',
    ),
    'saleplans' => 
    array (
      'name' => 'saleplans',
      'vname' => 'LBL_SALE_PLANS',
      'type' => 'link',
      'relationship' => 'saleplans_accounts',
      'source' => 'non-db',
    ),
    'saleplan_id' => 
    array (
      'name' => 'saleplan_id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'source' => 'non-db',
    ),
    'plan_month' => 
    array (
      'name' => 'plan_month',
      'vname' => 'LBL_PLAN_MONTH',
      'type' => 'varchar',
      'source' => 'non-db',
    ),
    'plan_year' => 
    array (
      'name' => 'plan_year',
      'vname' => 'LBL_PLAN_YEAR',
      'type' => 'varchar',
      'source' => 'non-db',
    ),
    'plan_user' => 
    array (
      'name' => 'plan_user',
      'type' => 'id',
      'source' => 'non-db',
      'vname' => 'LBL_ID',
    ),
    'plan_activities' => 
    array (
      'name' => 'plan_activities',
      'vname' => 'LBL_PLAN_ACTIVITIES',
      'type' => 'varchar',
      'source' => 'non-db',
    ),
    'plan_opportunities' => 
    array (
      'name' => 'plan_opportunities',
      'vname' => 'LBL_PLAN_OPPORTUNITIES',
      'type' => 'varchar',
      'source' => 'non-db',
    ),
    'sp_notes' => 
    array (
      'name' => 'sp_notes',
      'vname' => 'LBL_PLAN_NOTES',
      'type' => 'text',
      'source' => 'non-db',
    ),
    'sp_note_histories' => 
    array (
      'name' => 'sp_note_histories',
      'vname' => 'LBL_PLAN_NOTE_HISTORIES',
      'type' => 'text',
      'source' => 'non-db',
    ),
    'plan_opp_expected_amount' => 
    array (
      'name' => 'plan_opp_expected_amount',
      'vname' => 'LBL_PLAN_OPP_EXPECTED_AMOUNT',
      'type' => 'float',
      'source' => 'non-db',
    ),
    'plan_opp_actual_amount' => 
    array (
      'name' => 'plan_opp_actual_amount',
      'vname' => 'LBL_PLAN_OPP_ACTUAL_AMOUNT',
      'type' => 'float',
      'source' => 'non-db',
    ),
    'plan_opp_amount_percent' => 
    array (
      'name' => 'plan_opp_amount_percent',
      'vname' => 'LBL_PLAN_OPP_AMOUNT_PERCENT',
      'type' => 'float',
      'source' => 'non-db',
    ),
    'plan_total_expected_amount' => 
    array (
      'name' => 'plan_total_expected_amount',
      'vname' => 'LBL_PLAN_TOTAL_EXPECTED_AMOUNT',
      'type' => 'float',
      'source' => 'non-db',
    ),
    'plan_total_actual_amount' => 
    array (
      'name' => 'plan_total_actual_amount',
      'vname' => 'LBL_PLAN_TOTAL_ACTUAL_AMOUNT',
      'type' => 'float',
      'source' => 'non-db',
    ),
    'plan_total_amount_percent' => 
    array (
      'name' => 'plan_total_amount_percent',
      'vname' => 'LBL_PLAN_OPP_AMOUNT_PERCENT',
      'type' => 'float',
      'source' => 'non-db',
    ),
    'plan_opp_expected_output' => 
    array (
      'name' => 'plan_opp_expected_output',
      'vname' => 'LBL_PLAN_OPP_EXPECTED_OUTPUT',
      'type' => 'float',
      'source' => 'non-db',
    ),
    'plan_opp_actual_output' => 
    array (
      'name' => 'plan_opp_actual_output',
      'vname' => 'LBL_PLAN_OPP_ACTUAL_OUTPUT',
      'type' => 'float',
      'source' => 'non-db',
    ),
    'plan_opp_output_percent' => 
    array (
      'name' => 'plan_opp_output_percent',
      'vname' => 'LBL_PLAN_OPP_AMOUNT_PERCENT',
      'type' => 'float',
      'source' => 'non-db',
    ),
    'plan_total_expected_output' => 
    array (
      'name' => 'plan_total_expected_output',
      'vname' => 'LBL_PLAN_TOTAL_EXPECTED_OUTPUT',
      'type' => 'float',
      'source' => 'non-db',
    ),
    'plan_total_actual_output' => 
    array (
      'name' => 'plan_total_actual_output',
      'vname' => 'LBL_PLAN_TOTAL_ACTUAL_OUTPUT',
      'type' => 'float',
      'source' => 'non-db',
    ),
    'plan_total_output_percent' => 
    array (
      'name' => 'plan_total_output_percent',
      'vname' => 'LBL_PLAN_OPP_AMOUNT_PERCENT',
      'type' => 'float',
      'source' => 'non-db',
    ),
    'SecurityGroups' => 
    array (
      'name' => 'SecurityGroups',
      'type' => 'link',
      'relationship' => 'securitygroups_accounts',
      'module' => 'SecurityGroups',
      'bean_name' => 'SecurityGroup',
      'source' => 'non-db',
      'vname' => 'LBL_SECURITYGROUPS',
    ),
    'payrolls' => 
    array (
      'name' => 'payrolls',
      'type' => 'link',
      'relationship' => 'accounts_payrolls',
      'vname' => 'LBL_PAYROLLS',
      'source' => 'non-db',
    ),
    'contracts' => 
    array (
      'name' => 'contracts',
      'type' => 'link',
      'relationship' => 'accounts_contracts',
      'source' => 'non-db',
      'vname' => 'LBL_CONTRACTS',
    ),
    'tqt_orderdetails' => 
    array (
      'name' => 'tqt_orderdetails',
      'type' => 'link',
      'relationship' => 'accounts_tqt_orderdetails',
      'source' => 'non-db',
      'vname' => 'LBL_TQT_ORDER_DETAILS',
    ),
    'accounts_targets' => 
    array (
      'name' => 'accounts_targets',
      'vname' => 'LBL_ACCOUNTS_TARGETS',
      'type' => 'link',
      'relationship' => 'accounts_accounts_targets',
      'source' => 'non-db',
    ),
    'tqt_product_names' => 
    array (
      'name' => 'tqt_product_names',
      'type' => 'link',
      'relationship' => 'accounts_tqt_product_names',
      'source' => 'non-db',
      'vname' => 'LBL_TQT_PRODUCT_NAMES',
    ),
    'account_group_id' => 
    array (
      'name' => 'account_group_id',
      'vname' => 'LBL_ACCOUNT_GROUP_ID',
      'type' => 'id',
      'required' => false,
      'audited' => true,
      'massupdate' => false,
      'reportable' => false,
    ),
    'account_group_name' => 
    array (
      'name' => 'account_group_name',
      'rname' => 'name',
      'id_name' => 'account_group_id',
      'vname' => 'LBL_ACCOUNT_GROUP_NAME',
      'table' => 'account_groups',
      'type' => 'relate',
      'link' => 'account_groups',
      'join_name' => 'account_groups',
      'isnull' => 'true',
      'module' => 'Account_Groups',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'account_groups' => 
    array (
      'name' => 'account_groups',
      'vname' => 'LBL_ACCOUNT_GROUPS',
      'type' => 'link',
      'relationship' => 'accounts_account_groups',
      'source' => 'non-db',
    ),
    'telesales' => 
    array (
      'name' => 'telesales',
      'type' => 'link',
      'relationship' => 'accounts_telesales',
      'source' => 'non-db',
      'vname' => 'LBL_TELESALES',
      'duplicate_merge' => 'disabled',
    ),
    'telesales_saved' => 
    array (
      'name' => 'telesales_saved',
      'vname' => 'LBL_TELESALES',
      'type' => 'varchar',
      'source' => 'non-db',
    ),
    'ts_customer_need' => 
    array (
      'name' => 'ts_customer_need',
      'vname' => 'LBL_TS_CUSTOMER_NEED',
      'type' => 'enum',
      'options' => 'ts_customer_need_dom',
      'len' => 500,
      'source' => 'non-db',
      'audited' => false,
      'massupdate' => false,
      'isMultiSelect' => true,
      'isCheckBoxList' => true,
    ),
    'ts_products_need' => 
    array (
      'name' => 'ts_products_need',
      'vname' => 'LBL_TS_PRODUCTS_NEED',
      'type' => 'enum',
      'options' => 'ts_products_need_dom',
      'len' => 500,
      'source' => 'non-db',
      'audited' => false,
      'massupdate' => false,
      'isMultiSelect' => true,
      'isCheckBoxList' => true,
    ),
    'ts_need_month' => 
    array (
      'name' => 'ts_need_month',
      'vname' => 'LBL_TS_NEED_MONTH',
      'type' => 'enum',
      'options' => 'months_dom',
      'dbType' => 'varchar',
      'len' => '200',
      'source' => 'non-db',
      'audited' => false,
      'massupdate' => false,
      'isMultiSelect' => true,
      'isCheckBoxList' => true,
    ),
    'ts_note_other' => 
    array (
      'name' => 'ts_note_other',
      'vname' => 'LBL_TS_NOTE_OTHER',
      'type' => 'text',
      'source' => 'non-db',
    ),
    'ts_notes' => 
    array (
      'name' => 'ts_notes',
      'vname' => 'LBL_TS_NOTES',
      'type' => 'text',
      'source' => 'non-db',
    ),
    'customer_demand' => 
    array (
      'name' => 'customer_demand',
      'vname' => 'LBL_CUSTOMER_DEMAND',
      'type' => 'text',
      'source' => 'non-db',
    ),
    'telesales_status' => 
    array (
      'name' => 'telesales_status',
      'vname' => 'LBL_TELESALES_STATUS',
      'type' => 'enum',
      'options' => 'telesales_status_dom',
      'len' => 2,
      'audited' => true,
      'massupdate' => false,
      'display_default' => '10',
    ),
    'check_meets' => 
    array (
      'name' => 'check_meets',
      'vname' => 'LBL_CHECK_MEETS',
      'type' => 'enum',
      'options' => 'check_meets_dom',
      'dbType' => 'varchar',
      'len' => 50,
      'audited' => true,
      'massupdate' => false,
      'isMultiSelect' => true,
      'isCheckBoxList' => true,
    ),
    'tqt_growthabilities' => 
    array (
      'name' => 'tqt_growthabilities',
      'type' => 'link',
      'relationship' => 'accounts_tqt_growthabilities',
      'source' => 'non-db',
      'vname' => 'LBL_TQT_GROWTH_ABILITIES',
    ),
    'revenue_promotions' => 
    array (
      'name' => 'revenue_promotions',
      'type' => 'link',
      'relationship' => 'accounts_revenue_promotions',
      'source' => 'non-db',
      'vname' => 'LBL_REVENUE_PROMOTIONS',
    ),
    'tqt_businessfields' => 
    array (
      'name' => 'tqt_businessfields',
      'type' => 'link',
      'relationship' => 'accounts_tqt_businessfields',
      'source' => 'non-db',
      'vname' => 'LBL_TQT_BUSINESSFIELDS',
    ),
    'tqt_product_prices' => 
    array (
      'name' => 'tqt_product_prices',
      'type' => 'link',
      'relationship' => 'accounts_tqt_product_prices',
      'source' => 'non-db',
      'vname' => 'LBL_TQT_PRODUCT_PRICES',
    ),
    'debt_norm_min' => 
    array (
      'name' => 'debt_norm_min',
      'vname' => 'LBL_DEBT_NORM_MIN',
      'type' => 'double',
      'audited' => true,
    ),
    'debt_norm_max' => 
    array (
      'name' => 'debt_norm_max',
      'vname' => 'LBL_DEBT_NORM_MAX',
      'type' => 'double',
      'audited' => true,
    ),
    'debt_delay_day' => 
    array (
      'name' => 'debt_delay_day',
      'vname' => 'LBL_DEBT_DELAY_DAY',
      'type' => 'int',
      'len' => 4,
      'audited' => true,
    ),
    'cashes' => 
    array (
      'name' => 'cashes',
      'type' => 'link',
      'relationship' => 'accounts_cashes',
      'source' => 'non-db',
      'vname' => 'LBL_CASHES',
    ),
    'is_locked' => 
    array (
      'name' => 'is_locked',
      'vname' => 'LBL_IS_LOCKED',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => false,
      'default' => 0,
    ),
    'sales_line_id' => 
    array (
      'name' => 'sales_line_id',
      'vname' => 'LBL_SALES_LINE_ID',
      'type' => 'id',
      'source' => 'non-db',
      'reportable' => false,
      'massupdate' => false,
      'duplicate_merge' => false,
    ),
    'sales_line_name' => 
    array (
      'name' => 'sales_line_name',
      'rname' => 'name',
      'id_name' => 'sales_line_id',
      'vname' => 'LBL_SALES_LINE_NAME',
      'table' => 'sales_lines',
      'type' => 'relate',
      'link' => 'sales_lines',
      'join_name' => 'sla',
      'isnull' => 'true',
      'module' => 'Sales_Lines',
      'source' => 'non-db',
      'massupdate' => false,
      'duplicate_merge' => false,
    ),
    'line_order' => 
    array (
      'name' => 'line_order',
      'vname' => 'LBL_LINE_ORDER',
      'type' => 'int',
      'source' => 'non-db',
      'reportable' => false,
      'massupdate' => false,
      'duplicate_merge' => false,
    ),
    'sales_lines' => 
    array (
      'name' => 'sales_lines',
      'vname' => 'LBL_SALES_LINES',
      'type' => 'link',
      'relationship' => 'sales_lines_accounts',
      'source' => 'non-db',
    ),
    'warehouse_ins' => 
    array (
      'name' => 'warehouse_ins',
      'type' => 'link',
      'relationship' => 'accounts_warehouse_ins',
      'source' => 'non-db',
      'vname' => 'LBL_WAREHOUSE_INS',
    ),
    'order_discount' => 
    array (
      'name' => 'order_discount',
      'vname' => 'LBL_ORDER_DISCOUNT',
      'type' => 'double',
      'audited' => true,
    ),
    'handle_steerings' => 
    array (
      'name' => 'handle_steerings',
      'vname' => 'LBL_HANDLE_STEERINGS',
      'type' => 'link',
      'relationship' => 'accounts_handle_steerings',
      'source' => 'non-db',
    ),
    'warehouse_outs' => 
    array (
      'name' => 'warehouse_outs',
      'type' => 'link',
      'relationship' => 'accounts_warehouse_outs',
      'source' => 'non-db',
      'vname' => 'LBL_WAREHOUSE_OUTS',
    ),
    'unit_management' => 
    array (
      'name' => 'unit_management',
      'vname' => 'LBL_UNIT_MANAGEMENT',
      'type' => 'enum',
      'options' => 'unit_management_dom',
      'len' => 50,
      'audited' => true,
      'massupdate' => false,
      'duplicate_merge' => 'disabled',
    ),
    'account_status' => 
    array (
      'name' => 'account_status',
      'vname' => 'LBL_ACCOUNT_STATUS',
      'type' => 'enum',
      'options' => 'account_status_dom',
      'len' => 30,
      'audited' => true,
      'massupdate' => true,
      'display_default' => 'Active',
    ),
    'account_code' => 
    array (
      'name' => 'account_code',
      'vname' => 'LBL_ACCOUNT_CODE',
      'type' => 'varchar',
      'len' => 30,
      'unified_search' => true,
      'duplicate_merge' => false,
    ),
    'code_order' => 
    array (
      'name' => 'code_order',
      'vname' => 'LBL_CODE_ORDER',
      'type' => 'int',
      'len' => 10,
    ),
    'reference_code' => 
    array (
      'name' => 'reference_code',
      'vname' => 'LBL_REFERENCE_CODE',
      'type' => 'varchar',
      'len' => 50,
      'audited' => true,
      'unified_search' => true,
    ),
    'english_name' => 
    array (
      'name' => 'english_name',
      'vname' => 'LBL_ENGLISH_NAME',
      'type' => 'varchar',
      'len' => 100,
      'audited' => true,
      'unified_search' => true,
    ),
    'abbreviation' => 
    array (
      'name' => 'abbreviation',
      'vname' => 'LBL_ABBREVIATION',
      'type' => 'varchar',
      'len' => 100,
      'audited' => true,
      'unified_search' => true,
    ),
    'tax_code' => 
    array (
      'name' => 'tax_code',
      'vname' => 'LBL_TAX_CODE',
      'type' => 'varchar',
      'len' => 30,
      'audited' => true,
      'unified_search' => true,
      'comment' => 'Tax code of the Company',
    ),
    'registration_number' => 
    array (
      'name' => 'registration_number',
      'vname' => 'LBL_REGISTRATION_NUMBER',
      'type' => 'varchar',
      'len' => 50,
      'audited' => true,
      'comment' => 'Business Registration Number',
    ),
    'stock_code' => 
    array (
      'name' => 'stock_code',
      'vname' => 'LBL_STOCK_CODE',
      'type' => 'varchar',
      'len' => 30,
      'audited' => true,
      'comment' => 'Stock Code',
    ),
    'date_issue' => 
    array (
      'name' => 'date_issue',
      'vname' => 'LBL_DATE_ISSUE',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
      'comment' => 'Date of issue',
    ),
    'registration_place' => 
    array (
      'name' => 'registration_place',
      'vname' => 'LBL_REGISTRATION_PLACE',
      'type' => 'varchar',
      'len' => 100,
      'audited' => true,
      'comment' => 'Place of registration',
    ),
    'bank_account_number' => 
    array (
      'name' => 'bank_account_number',
      'vname' => 'LBL_BANK_ACCOUNT_NUMBER',
      'type' => 'varchar',
      'len' => 50,
      'audited' => true,
      'comment' => 'Bank Account Number',
    ),
    'bank_account_owner' => 
    array (
      'name' => 'bank_account_owner',
      'vname' => 'LBL_BANK_ACCOUNT_OWNER',
      'type' => 'varchar',
      'len' => 200,
      'audited' => true,
      'comment' => 'Bank Account Owner',
    ),
    'bank_name' => 
    array (
      'name' => 'bank_name',
      'vname' => 'LBL_BANK_NAME',
      'type' => 'varchar',
      'len' => 250,
      'audited' => true,
      'comment' => 'Bank Name',
    ),
    'charter_capital' => 
    array (
      'name' => 'charter_capital',
      'vname' => 'LBL_CHARTER_CAPITAL',
      'type' => 'enum',
      'options' => 'charter_capital_dom',
      'len' => 30,
      'audited' => true,
      'massupdate' => false,
    ),
    'on_incorporation' => 
    array (
      'name' => 'on_incorporation',
      'vname' => 'LBL_ON_INCORPORATION',
      'type' => 'date',
      'massupdate' => false,
    ),
    'department_manager' => 
    array (
      'name' => 'department_manager',
      'vname' => 'LBL_DEPARTMENT_MANAGER',
      'type' => 'enum',
      'options' => 'department_manager_dom',
      'len' => 50,
      'audited' => true,
      'massupdate' => true,
      'display_default' => 'KHH',
      'duplicate_merge' => 'disabled',
    ),
    'transaction_level' => 
    array (
      'name' => 'transaction_level',
      'vname' => 'LBL_TRANSACTION_LEVEL',
      'type' => 'enum',
      'options' => 'transaction_level_dom',
      'len' => 30,
      'audited' => true,
      'massupdate' => true,
      'display_default' => 'Prospects',
    ),
    'sector_vertical' => 
    array (
      'name' => 'sector_vertical',
      'vname' => 'LBL_SECTOR_VERTICAL',
      'type' => 'enum',
      'options' => 'sector_vertical_dom',
      'dbType' => 'char',
      'len' => 2,
      'audited' => true,
      'massupdate' => true,
      'qs_ignore' => true,
      'display_default' => 'c0',
    ),
    'account_scope' => 
    array (
      'name' => 'account_scope',
      'vname' => 'LBL_ACCOUNT_SCOPE',
      'type' => 'enum',
      'options' => 'account_scope_dom',
      'len' => 30,
      'audited' => true,
      'massupdate' => true,
    ),
    'lead_source' => 
    array (
      'name' => 'lead_source',
      'vname' => 'LBL_LEAD_SOURCE',
      'type' => 'enum',
      'options' => 'lead_source_dom',
      'len' => 30,
      'audited' => true,
      'massupdate' => true,
    ),
    'purchase_date' => 
    array (
      'name' => 'purchase_date',
      'vname' => 'LBL_PURCHASE_DATE',
      'type' => 'datetime',
      'audited' => true,
      'massupdate' => false,
    ),
    'periodic_purchase' => 
    array (
      'name' => 'periodic_purchase',
      'vname' => 'LBL_PERIODIC_PURCHASE',
      'type' => 'enum',
      'options' => 'periodic_purchase_dom',
      'len' => 30,
      'audited' => true,
      'massupdate' => false,
    ),
    'location_area' => 
    array (
      'name' => 'location_area',
      'vname' => 'LBL_LOCATION_AREA',
      'type' => 'enum',
      'options' => 'location_area_dom',
      'len' => 30,
      'audited' => true,
      'massupdate' => true,
    ),
    'location_city' => 
    array (
      'name' => 'location_city',
      'vname' => 'LBL_LOCATION_CITY',
      'type' => 'enum',
      'options' => 'location_cities_dom',
      'len' => 50,
      'audited' => true,
      'massupdate' => true,
    ),
    'location_district' => 
    array (
      'name' => 'location_district',
      'vname' => 'LBL_LOCATION_DISTRICT',
      'type' => 'enum',
      'options' => 'location_district_dom',
      'len' => 100,
      'audited' => true,
      'massupdate' => true,
    ),
    'rate_note' => 
    array (
      'name' => 'rate_note',
      'vname' => 'LBL_RATE_NOTE',
      'type' => 'text',
    ),
    'marking' => 
    array (
      'name' => 'marking',
      'vname' => 'LBL_MARKING',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => false,
    ),
    'mark_note' => 
    array (
      'name' => 'mark_note',
      'vname' => 'LBL_MARK_NOTE',
      'type' => 'text',
    ),
    'primary_contact_id' => 
    array (
      'name' => 'primary_contact_id',
      'vname' => 'LBL_PRIMARY_CONTACT_ID',
      'type' => 'id',
      'source' => 'non-db',
      'required' => false,
      'reportable' => false,
      'massupdate' => false,
    ),
    'primary_contact_name' => 
    array (
      'name' => 'primary_contact_name',
      'vname' => 'LBL_PRIMARY_CONTACT_NAME',
      'rname' => 'last_name',
      'id_name' => 'primary_contact_id',
      'type' => 'relate',
      'link' => 'contacts',
      'module' => 'Contacts',
      'source' => 'non-db',
      'reportable' => false,
      'massupdate' => false,
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'relationship_fields' => 
      array (
        'position' => 'primary_contact_position',
        'department' => 'primary_contact_department',
        'phone_work' => 'primary_contact_phone_work',
        'phone_mobile' => 'primary_contact_phone_mobile',
        'phone_home' => 'primary_contact_phone_home',
        'phone_fax' => 'primary_contact_phone_fax',
        'email1' => 'primary_contact_email',
        'birthdate' => 'primary_contact_birthdate',
        'alt_address_street' => 'primary_contact_address',
        'description' => 'primary_contact_note',
      ),
    ),
    'primary_contact_position' => 
    array (
      'name' => 'primary_contact_position',
      'vname' => 'LBL_PRIMARY_CONTACT_POSITION',
      'type' => 'varchar',
      'len' => 200,
      'source' => 'non-db',
      'reportable' => false,
      'massupdate' => false,
    ),
    'primary_contact_department' => 
    array (
      'name' => 'primary_contact_department',
      'vname' => 'LBL_PRIMARY_CONTACT_DEPARTMENT',
      'type' => 'varchar',
      'len' => 255,
      'source' => 'non-db',
      'reportable' => false,
      'massupdate' => false,
    ),
    'primary_contact_phone_work' => 
    array (
      'name' => 'primary_contact_phone_work',
      'vname' => 'LBL_PRIMARY_CONTACT_PHONE_WORK',
      'type' => 'varchar',
      'len' => 100,
      'source' => 'non-db',
      'reportable' => false,
      'massupdate' => false,
    ),
    'primary_contact_phone_mobile' => 
    array (
      'name' => 'primary_contact_phone_mobile',
      'vname' => 'LBL_PRIMARY_CONTACT_PHONE_MOBILE',
      'type' => 'varchar',
      'len' => 100,
      'source' => 'non-db',
      'reportable' => false,
      'massupdate' => false,
    ),
    'primary_contact_phone_home' => 
    array (
      'name' => 'primary_contact_phone_home',
      'vname' => 'LBL_PRIMARY_CONTACT_PHONE_HOME',
      'type' => 'varchar',
      'len' => 100,
      'source' => 'non-db',
      'reportable' => false,
      'massupdate' => false,
    ),
    'primary_contact_phone_fax' => 
    array (
      'name' => 'primary_contact_phone_fax',
      'vname' => 'LBL_PRIMARY_CONTACT_PHONE_FAX',
      'type' => 'varchar',
      'len' => 100,
      'source' => 'non-db',
      'reportable' => false,
      'massupdate' => false,
    ),
    'primary_contact_email' => 
    array (
      'name' => 'primary_contact_email',
      'vname' => 'LBL_PRIMARY_CONTACT_EMAIL',
      'group' => 'email',
      'type' => 'varchar',
      'len' => 200,
      'source' => 'non-db',
      'reportable' => false,
      'massupdate' => false,
    ),
    'primary_contact_birthdate' => 
    array (
      'name' => 'primary_contact_birthdate',
      'vname' => 'LBL_PRIMARY_CONTACT_BIRTHDATE',
      'type' => 'date',
      'source' => 'non-db',
      'reportable' => false,
      'massupdate' => false,
    ),
    'primary_contact_address' => 
    array (
      'name' => 'primary_contact_address',
      'vname' => 'LBL_PRIMARY_CONTACT_ADDRESS',
      'type' => 'text',
      'source' => 'non-db',
      'reportable' => false,
      'massupdate' => false,
    ),
    'primary_contact_note' => 
    array (
      'name' => 'primary_contact_note',
      'vname' => 'LBL_PRIMARY_CONTACT_NOTE',
      'type' => 'text',
      'source' => 'non-db',
      'reportable' => false,
      'massupdate' => false,
    ),
    'secondary_contact_id' => 
    array (
      'name' => 'secondary_contact_id',
      'vname' => 'LBL_SECONDARY_CONTACT_ID',
      'type' => 'id',
      'source' => 'non-db',
      'required' => false,
      'reportable' => false,
      'massupdate' => false,
    ),
    'secondary_contact_name' => 
    array (
      'name' => 'secondary_contact_name',
      'vname' => 'LBL_SECONDARY_CONTACT_NAME',
      'rname' => 'last_name',
      'id_name' => 'secondary_contact_id',
      'type' => 'relate',
      'link' => 'contacts',
      'module' => 'Contacts',
      'source' => 'non-db',
      'reportable' => false,
      'massupdate' => false,
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'relationship_fields' => 
      array (
        'position' => 'secondary_contact_position',
        'department' => 'secondary_contact_department',
        'phone_work' => 'secondary_contact_phone_work',
        'phone_mobile' => 'secondary_contact_phone_mobile',
        'phone_home' => 'secondary_contact_phone_home',
        'phone_fax' => 'secondary_contact_phone_fax',
        'email1' => 'secondary_contact_email',
        'birthdate' => 'secondary_contact_birthdate',
        'alt_address_street' => 'secondary_contact_address',
        'description' => 'secondary_contact_note',
      ),
    ),
    'secondary_contact_position' => 
    array (
      'name' => 'secondary_contact_position',
      'vname' => 'LBL_SECONDARY_CONTACT_POSITION',
      'type' => 'varchar',
      'len' => 200,
      'source' => 'non-db',
      'reportable' => false,
      'massupdate' => false,
    ),
    'secondary_contact_department' => 
    array (
      'name' => 'secondary_contact_department',
      'vname' => 'LBL_SECONDARY_CONTACT_DEPARTMENT',
      'type' => 'varchar',
      'len' => 255,
      'source' => 'non-db',
      'reportable' => false,
      'massupdate' => false,
    ),
    'secondary_contact_phone_work' => 
    array (
      'name' => 'secondary_contact_phone_work',
      'vname' => 'LBL_SECONDARY_CONTACT_PHONE_WORK',
      'type' => 'varchar',
      'len' => 100,
      'source' => 'non-db',
      'reportable' => false,
      'massupdate' => false,
    ),
    'secondary_contact_phone_mobile' => 
    array (
      'name' => 'secondary_contact_phone_mobile',
      'vname' => 'LBL_SECONDARY_CONTACT_PHONE_MOBILE',
      'type' => 'varchar',
      'len' => 100,
      'source' => 'non-db',
      'reportable' => false,
      'massupdate' => false,
    ),
    'secondary_contact_phone_home' => 
    array (
      'name' => 'secondary_contact_phone_home',
      'vname' => 'LBL_SECONDARY_CONTACT_PHONE_HOME',
      'type' => 'varchar',
      'len' => 100,
      'source' => 'non-db',
      'reportable' => false,
      'massupdate' => false,
    ),
    'secondary_contact_phone_fax' => 
    array (
      'name' => 'secondary_contact_phone_fax',
      'vname' => 'LBL_SECONDARY_CONTACT_PHONE_FAX',
      'type' => 'varchar',
      'len' => 100,
      'source' => 'non-db',
      'reportable' => false,
      'massupdate' => false,
    ),
    'secondary_contact_email' => 
    array (
      'name' => 'secondary_contact_email',
      'vname' => 'LBL_SECONDARY_CONTACT_EMAIL',
      'group' => 'email',
      'type' => 'varchar',
      'len' => 200,
      'source' => 'non-db',
      'reportable' => false,
      'massupdate' => false,
    ),
    'secondary_contact_birthdate' => 
    array (
      'name' => 'secondary_contact_birthdate',
      'vname' => 'LBL_SECONDARY_CONTACT_BIRTHDATE',
      'type' => 'date',
      'source' => 'non-db',
      'reportable' => false,
      'massupdate' => false,
    ),
    'secondary_contact_address' => 
    array (
      'name' => 'secondary_contact_address',
      'vname' => 'LBL_SECONDARY_CONTACT_ADDRESS',
      'type' => 'text',
      'source' => 'non-db',
      'reportable' => false,
      'massupdate' => false,
    ),
    'secondary_contact_note' => 
    array (
      'name' => 'secondary_contact_note',
      'vname' => 'LBL_SECONDARY_CONTACT_NOTE',
      'type' => 'text',
      'source' => 'non-db',
      'reportable' => false,
      'massupdate' => false,
    ),
    'business_field' => 
    array (
      'name' => 'business_field',
      'vname' => 'LBL_BUSINESS_FIELD',
      'type' => 'enum',
      'dbType' => 'text',
      'options' => 'business_field_dom',
      'len' => 500,
      'isMultiSelect' => true,
      'audited' => true,
      'massupdate' => false,
      'isCheckBoxList' => true,
    ),
    'responsible' => 
    array (
      'name' => 'responsible',
      'vname' => 'LBL_RESPONSIBLE',
      'type' => 'text',
      'audited' => true,
    ),
    'company_info' => 
    array (
      'name' => 'company_info',
      'vname' => 'LBL_COMPANY_INFO',
      'type' => 'text',
    ),
    'import_mark' => 
    array (
      'name' => 'import_mark',
      'vname' => 'LBL_IMPORT_MARK',
      'type' => 'varchar',
      'len' => 50,
      'audited' => true,
    ),
    'billing_representation' => 
    array (
      'name' => 'billing_representation',
      'vname' => 'LBL_BILLING_REPRESENTATION',
      'type' => 'varchar',
      'len' => 200,
      'audited' => true,
    ),
    'billing_position' => 
    array (
      'name' => 'billing_position',
      'vname' => 'LBL_BILLING_POSITION',
      'type' => 'varchar',
      'len' => 100,
      'audited' => true,
    ),
    'location_ward' => 
    array (
      'name' => 'location_ward',
      'vname' => 'LBL_LOCATION_WARD',
      'type' => 'varchar',
      'len' => 100,
      'audited' => true,
    ),
    'location_hamlet' => 
    array (
      'name' => 'location_hamlet',
      'vname' => 'LBL_LOCATION_HAMLET',
      'type' => 'varchar',
      'len' => 100,
      'audited' => true,
    ),
    'latlng' => 
    array (
      'name' => 'latlng',
      'vname' => 'LBL_LATLNG',
      'type' => 'text',
      'audited' => true,
    ),
    'debt_user_id' => 
    array (
      'name' => 'debt_user_id',
      'vname' => 'LBL_DEBT_USER_ID',
      'type' => 'id',
      'audited' => true,
      'massupdate' => false,
    ),
    'debt_user_name' => 
    array (
      'name' => 'debt_user_name',
      'rname' => 'user_name',
      'id_name' => 'debt_user_id',
      'vname' => 'LBL_DEBT_USER_NAME',
      'source' => 'non-db',
      'type' => 'relate',
      'link' => 'debt_users',
      'join_name' => 'debts',
      'table' => 'users',
      'module' => 'Users',
      'massupdate' => false,
    ),
    'debt_users' => 
    array (
      'name' => 'debt_users',
      'vname' => 'LBL_DEBT_USERS',
      'type' => 'link',
      'source' => 'non-db',
      'relationship' => 'debt_users_accounts',
    ),
    'support_user_id' => 
    array (
      'name' => 'support_user_id',
      'vname' => 'LBL_SUPPORT_USER_ID',
      'type' => 'id',
      'audited' => true,
      'massupdate' => false,
    ),
    'support_user_name' => 
    array (
      'name' => 'support_user_name',
      'rname' => 'user_name',
      'id_name' => 'support_user_id',
      'vname' => 'LBL_SUPPORT_USER_NAME',
      'source' => 'non-db',
      'type' => 'relate',
      'link' => 'support_users',
      'join_name' => 'supports',
      'table' => 'users',
      'module' => 'Users',
      'massupdate' => false,
    ),
    'support_users' => 
    array (
      'name' => 'support_users',
      'vname' => 'LBL_SUPPORT_USERS',
      'type' => 'link',
      'source' => 'non-db',
      'relationship' => 'support_users_accounts',
    ),
    'products' => 
    array (
      'name' => 'products',
      'vname' => 'LBL_PRODUCTS',
      'type' => 'enum',
      'options' => 'product_categories_dom',
      'len' => 50,
      'audited' => true,
      'massupdate' => false,
    ),
    'date_assigned' => 
    array (
      'name' => 'date_assigned',
      'vname' => 'LBL_DATE_ASSIGNED',
      'type' => 'datetime',
      'audited' => true,
      'massupdate' => false,
      'comment' => 'Date changed Assigned User',
    ),
    'old_user_id' => 
    array (
      'name' => 'old_user_id',
      'vname' => 'LBL_ASSIGNED_USER_NAME',
      'type' => 'id',
      'audited' => false,
    ),
    'decentralization' => 
    array (
      'name' => 'decentralization',
      'vname' => 'LBL_DECENTRALIZATION',
      'type' => 'enum',
      'options' => 'decentralization_dom',
      'len' => 2,
      'audited' => true,
      'massupdate' => false,
      'display_default' => '2',
    ),
    'norms_debts' => 
    array (
      'name' => 'norms_debts',
      'vname' => 'LBL_NORMS_DEBTS',
      'type' => 'double',
      'audited' => true,
    ),
    'full_name' => 
    array (
      'name' => 'full_name',
      'vname' => 'LBL_FULL_NAME',
      'type' => 'text',
      'source' => 'non-db',
    ),
    'name_phone' => 
    array (
      'name' => 'name_phone',
      'vname' => 'LBL_FULL_NAME',
      'type' => 'text',
      'duplicate_merge' => false,
    ),
    'membership_card' => 
    array (
      'name' => 'membership_card',
      'vname' => 'LBL_MEMBERSHIP_CARD',
      'type' => 'enum',
      'options' => 'membership_card_dom',
      'len' => 2,
      'audited' => true,
      'massupdate' => false,
    ),
    'point_total' => 
    array (
      'name' => 'point_total',
      'vname' => 'LBL_POINT_TOTAL',
      'type' => 'double',
      'precision' => 0,
      'audited' => true,
      'default' => '0',
    ),
    'point_used' => 
    array (
      'name' => 'point_used',
      'vname' => 'LBL_POINT_USED',
      'type' => 'double',
      'precision' => 0,
      'audited' => true,
      'default' => '0',
    ),
    'point_current' => 
    array (
      'name' => 'point_current',
      'vname' => 'LBL_POINT_CURRENT',
      'type' => 'double',
      'precision' => 0,
      'source' => 'non-db',
    ),
    'user_shared' => 
    array (
      'name' => 'user_shared',
      'vname' => 'LBL_PROJECT_TEAMS',
      'type' => 'varchar',
      'len' => 100,
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'account_warn' => 
    array (
      'name' => 'account_warn',
      'vname' => 'LBL_ACCOUNT_WARN',
      'type' => 'enum',
      'options' => 'account_warn_dom',
      'len' => 2,
      'audited' => true,
      'massupdate' => true,
      'default' => 'N',
    ),
    'use_feature' => 
    array (
      'name' => 'use_feature',
      'vname' => 'LBL_USE_FEATURE',
      'type' => 'enum',
      'options' => 'account_use_feature_dom',
      'dbType' => 'char',
      'len' => 2,
      'audited' => true,
      'massupdate' => true,
      'display_default' => '10',
    ),
    'reason_feature' => 
    array (
      'name' => 'reason_feature',
      'vname' => 'LBL_REASON_FEATURE',
      'type' => 'enum',
      'options' => 'account_reason_feature_dom',
      'dbType' => 'char',
      'len' => 3,
      'audited' => true,
      'massupdate' => true,
    ),
    'usage_status' => 
    array (
      'name' => 'usage_status',
      'vname' => 'LBL_USAGE_STATUS',
      'type' => 'enum',
      'options' => 'task_product_result_dom',
      'len' => 2,
      'audited' => true,
      'massupdate' => true,
      'unified_search' => true,
    ),
    'reference_code_update' => 
    array (
      'name' => 'reference_code_update',
      'vname' => 'LBL_REFERENCE_CODE',
      'type' => 'varchar',
      'len' => 100,
      'source' => 'non-db',
    ),
  ),
  'indices' => 
  array (
  ),
  'relationships' => 
  array (
    'accounts_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Accounts',
      'rhs_table' => 'accounts',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'accounts_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Accounts',
      'rhs_table' => 'accounts',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
    'accounts_assigned_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Accounts',
      'rhs_table' => 'accounts',
      'rhs_key' => 'assigned_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'accounts_email_addresses' => 
    array (
      'lhs_module' => 'Accounts',
      'lhs_table' => 'accounts',
      'lhs_key' => 'id',
      'rhs_module' => 'EmailAddresses',
      'rhs_table' => 'email_addresses',
      'rhs_key' => 'id',
      'relationship_type' => 'many-to-many',
      'join_table' => 'email_addr_bean_rel',
      'join_key_lhs' => 'bean_id',
      'join_key_rhs' => 'email_address_id',
      'relationship_role_column' => 'bean_module',
      'relationship_role_column_value' => 'Accounts',
    ),
    'accounts_email_addresses_primary' => 
    array (
      'lhs_module' => 'Accounts',
      'lhs_table' => 'accounts',
      'lhs_key' => 'id',
      'rhs_module' => 'EmailAddresses',
      'rhs_table' => 'email_addresses',
      'rhs_key' => 'id',
      'relationship_type' => 'many-to-many',
      'join_table' => 'email_addr_bean_rel',
      'join_key_lhs' => 'bean_id',
      'join_key_rhs' => 'email_address_id',
      'relationship_role_column' => 'primary_address',
      'relationship_role_column_value' => '1',
    ),
    'member_accounts' => 
    array (
      'lhs_module' => 'Accounts',
      'lhs_table' => 'accounts',
      'lhs_key' => 'id',
      'rhs_module' => 'Accounts',
      'rhs_table' => 'accounts',
      'rhs_key' => 'parent_id',
      'relationship_type' => 'one-to-many',
    ),
    'account_cases' => 
    array (
      'lhs_module' => 'Accounts',
      'lhs_table' => 'accounts',
      'lhs_key' => 'id',
      'rhs_module' => 'Cases',
      'rhs_table' => 'cases',
      'rhs_key' => 'id',
      'join_table' => 'accounts_cases',
      'join_key_lhs' => 'account_id',
      'join_key_rhs' => 'case_id',
      'relationship_type' => 'many-to-many',
    ),
    'account_tasks' => 
    array (
      'lhs_module' => 'Accounts',
      'lhs_table' => 'accounts',
      'lhs_key' => 'id',
      'rhs_module' => 'Tasks',
      'rhs_table' => 'tasks',
      'rhs_key' => 'parent_id',
      'relationship_type' => 'one-to-many',
      'relationship_role_column' => 'parent_type',
      'relationship_role_column_value' => 'Accounts',
    ),
    'account_notes' => 
    array (
      'lhs_module' => 'Accounts',
      'lhs_table' => 'accounts',
      'lhs_key' => 'id',
      'rhs_module' => 'Notes',
      'rhs_table' => 'notes',
      'rhs_key' => 'parent_id',
      'relationship_type' => 'one-to-many',
      'relationship_role_column' => 'parent_type',
      'relationship_role_column_value' => 'Accounts',
    ),
    'account_meetings' => 
    array (
      'lhs_module' => 'Accounts',
      'lhs_table' => 'accounts',
      'lhs_key' => 'id',
      'rhs_module' => 'Meetings',
      'rhs_table' => 'meetings',
      'rhs_key' => 'parent_id',
      'relationship_type' => 'one-to-many',
      'relationship_role_column' => 'parent_type',
      'relationship_role_column_value' => 'Accounts',
    ),
    'account_calls' => 
    array (
      'lhs_module' => 'Accounts',
      'lhs_table' => 'accounts',
      'lhs_key' => 'id',
      'rhs_module' => 'Calls',
      'rhs_table' => 'calls',
      'rhs_key' => 'parent_id',
      'relationship_type' => 'one-to-many',
      'relationship_role_column' => 'parent_type',
      'relationship_role_column_value' => 'Accounts',
    ),
    'account_emails' => 
    array (
      'lhs_module' => 'Accounts',
      'lhs_table' => 'accounts',
      'lhs_key' => 'id',
      'rhs_module' => 'Emails',
      'rhs_table' => 'emails',
      'rhs_key' => 'parent_id',
      'relationship_type' => 'one-to-many',
      'relationship_role_column' => 'parent_type',
      'relationship_role_column_value' => 'Accounts',
    ),
    'account_leads' => 
    array (
      'lhs_module' => 'Accounts',
      'lhs_table' => 'accounts',
      'lhs_key' => 'id',
      'rhs_module' => 'Leads',
      'rhs_table' => 'leads',
      'rhs_key' => 'account_id',
      'relationship_type' => 'one-to-many',
    ),
    'accounts_documents' => 
    array (
      'lhs_module' => 'Accounts',
      'lhs_table' => 'accounts',
      'lhs_key' => 'id',
      'rhs_module' => 'Documents',
      'rhs_table' => 'documents',
      'rhs_key' => 'id',
      'relationship_type' => 'many-to-many',
      'join_table' => 'linked_documents',
      'join_key_lhs' => 'parent_id',
      'join_key_rhs' => 'document_id',
      'relationship_role_column' => 'parent_type',
      'relationship_role_column_value' => 'Accounts',
    ),
    'accounts_trackreview' => 
    array (
      'lhs_module' => 'Accounts',
      'lhs_table' => 'accounts',
      'lhs_key' => 'id',
      'rhs_module' => 'TrackReview',
      'rhs_table' => 'trackreview',
      'rhs_key' => 'parent_id',
      'relationship_type' => 'one-to-many',
      'relationship_role_column' => 'parent_type',
      'relationship_role_column_value' => 'Accounts',
    ),
    'debt_users_accounts' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Accounts',
      'rhs_table' => 'accounts',
      'rhs_key' => 'debt_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'support_users_accounts' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Accounts',
      'rhs_table' => 'accounts',
      'rhs_key' => 'support_user_id',
      'relationship_type' => 'one-to-many',
    ),
  ),
  'optimistic_locking' => true,
  'templates' => 
  array (
    'company' => 'company',
    'assignable' => 'assignable',
    'groups' => 'groups',
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
