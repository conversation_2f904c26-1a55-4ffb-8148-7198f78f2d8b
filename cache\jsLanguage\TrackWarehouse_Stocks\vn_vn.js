incomCRM.language.setLanguage('TrackWarehouse_Stocks', {"LBL_MODULE_NAME":"T\u1ed3n kho theo kho h\u00e0ng","LBL_MODULE_TITLE":"T\u1ed3n kho theo kho h\u00e0ng: Trang ch\u1ee7","LBL_LIST_FORM_TITLE":"T\u1ed3n kho theo kho h\u00e0ng","LBL_TRACK_INVENTORIES_DETAILS":"Chi ti\u1ebft xu\u1ea5t nh\u1eadp","LBL_TRACK_ORDERS":"Phi\u1ebfu xu\u1ea5t - H\u00e0ng b\u00e1n","LBL_TRACK_STOCK_OUTS":"Phi\u1ebfu xu\u1ea5t c\u00f2n l\u1ea1i","LBL_TRACK_STOCK_INS":"Phi\u1ebfu nh\u1eadp kho","LBL_RPT_TRACKING_QUANTITY":"Chi ti\u1ebft s\u1ed1 l\u01b0\u1ee3ng nh\u1eadp xu\u1ea5t theo m\u1eb7t h\u00e0ng","LBL_RPT_TRACKING_INVENTORY":"B\u00e1o c\u00e1o nh\u1eadp xu\u1ea5t t\u1ed3n v\u1eadt t\u01b0 h\u00e0ng h\u00f3a","LBL_RPT_TRACKING_WAREHOUSES":"B\u00e1o c\u00e1o s\u1ed1 l\u01b0\u1ee3ng t\u1ed3n theo kho h\u00e0ng","LBL_RPT_TRACKING_QUANTITY2":"Chi ti\u1ebft nh\u1eadp xu\u1ea5t","LBL_RPT_TRACKING_INVENTORY2":"Nh\u1eadp xu\u1ea5t t\u1ed3n VT","LBL_RPT_TRACKING_WAREHOUSES2":"T\u1ed3n kho theo kho h\u00e0ng"});