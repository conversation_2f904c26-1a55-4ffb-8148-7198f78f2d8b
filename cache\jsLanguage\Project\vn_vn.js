incomCRM.language.setLanguage('Project', {"LBL_ID":"ID","LBL_DATE_ENTERED":"Ng\u00e0y t\u1ea1o","LBL_DATE_MODIFIED":"Ng\u00e0y c\u1eadp nh\u1eadt","LBL_MODIFIED":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_MODIFIED_ID":"ID ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_MODIFIED_NAME":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_CREATED":"Ng\u01b0\u1eddi t\u1ea1o","LBL_CREATED_ID":"ID ng\u01b0\u1eddi t\u1ea1o","LBL_DESCRIPTION":"Ghi ch\u00fa","LBL_DELETED":"\u0110\u00e3 x\u00f3a?","LBL_NAME":"T\u00ean d\u1ef1 \u00e1n","LBL_CREATED_USER":"Ng\u01b0\u1eddi t\u1ea1o","LBL_MODIFIED_USER":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_ASSIGNED_TO":"Nv.Ql\u00fd","LBL_ASSIGNED_TO_ID":"ID Nv.Ql\u00fd","LBL_ASSIGNED_TO_NAME":"Nv.Ql\u00fd","LBL_ASSIGNED_TO_USER":"Nv.Ql\u00fd","LBL_ASSIGNED_USER":"Nv.Ql\u00fd","LBL_MODULE_NAME":"Danh s\u00e1ch d\u1ef1 \u00e1n","LBL_MODULE_TITLE":"Danh s\u00e1ch d\u1ef1 \u00e1n: Trang ch\u1ee7","LBL_SEARCH_FORM_TITLE":"T\u00ecm ki\u1ebfm","LBL_LIST_FORM_TITLE":"Danh s\u00e1ch d\u1ef1 \u00e1n","LBL_NEW_FORM_TITLE":"T\u1ea1o m\u1edbi","LNK_NEW_PROJECT":"T\u1ea1o m\u1edbi","LNK_PROJECT_LIST":"Danh s\u00e1ch","LNK_NEW_PROJECT_TASK":"T\u1ea1o t\u00e1c v\u1ee5 d\u1ef1 \u00e1n","LNK_PROJECT_TASK_LIST":"T\u00e1c v\u1ee5 d\u1ef1 \u00e1n","LBL_DEFAULT_SUBPANEL_TITLE":"D\u1ef1 \u00e1n\/C\u00f4ng tr\u00ecnh","LNK_REPORT_PROJECT":"B\u00e1o c\u00e1o t\u1ed5ng h\u1ee3p d\u1ef1 \u00e1n","LBL_ASSIGNED_USER_ID":"Nv.Ql\u00fd","LBL_ASSIGNED_USER_NAME":"Nv.Ql\u00fd","LBL_TEAM_ID":"Nh\u00f3m","LBL_PDF_PROJECT_NAME":"T\u00ean d\u1ef1 \u00e1n","LBL_ADDRESS":"\u0110\u1ecba ch\u1ec9","LBL_DATE":"Ng\u00e0y","LBL_DATE_START":"Ng\u00e0y b\u1eaft \u0111\u1ea7u","LBL_DATE_END":"Ng\u00e0y k\u1ebft th\u00fac","LBL_PRIORITY":"\u0110\u1ed9 \u01b0u ti\u00ean","LBL_STATUS":"T\u00ecnh tr\u1ea1ng","LBL_MY_PROJECTS":"D\u1ef1 \u00e1n ri\u00eang","LBL_MY_PROJECT_TASKS":"T\u00e1c v\u1ee5 d\u1ef1 \u00e1n ri\u00eang","LBL_TOTAL_ESTIMATED_EFFORT":"T\u1ed5ng s\u1ed1 th\u1eddi l\u01b0\u1ee3ng d\u1ef1 ki\u1ebfn (gi\u1edd)","LBL_TOTAL_ACTUAL_EFFORT":"T\u1ed5ng s\u1ed1 th\u1eddi l\u01b0\u1ee3ng th\u1ef1c t\u1ebf (gi\u1edd)","LBL_LIST_NAME":"T\u00ean d\u1ef1 \u00e1n","LBL_LIST_DAYS":"Ng\u00e0y","LBL_LIST_ASSIGNED_USER_ID":"Nv.Ql\u00fd","LBL_LIST_TOTAL_ESTIMATED_EFFORT":"T\u1ed5ng s\u1ed1 ng\u00e0y c\u00f4ng d\u1ef1 ki\u1ebfn (hrs)","LBL_LIST_TOTAL_ACTUAL_EFFORT":"T\u1ed5ng s\u1ed1 ng\u00e0y c\u00f4ng th\u1ef1c (hrs)","LBL_LIST_UPCOMING_TASKS":"C\u00f4ng vi\u1ec7c s\u1eafp t\u1edbi (1 Week)","LBL_LIST_OVERDUE_TASKS":"C\u00f4ng vi\u1ec7c qu\u00e1 h\u1ea1n","LBL_LIST_OPEN_CASES":"V\u1ee5 vi\u1ec7c m\u1edf","LBL_LIST_END_DATE":"Ng\u00e0y k\u1ebft th\u00fac","LBL_LIST_TEAM_ID":"Nh\u00f3m","LBL_ACCOUNT_NAME":"Kh\u00e1ch h\u00e0ng","LBL_OPPORTUNITY_NAME":"PO No.","LBL_CASHES":"Thu\/Chi","LBL_PROJECT_SUBPANEL_TITLE":"D\u1ef1 \u00e1n\/C\u00f4ng tr\u00ecnh","LBL_PROJECT_TASK_SUBPANEL_TITLE":"T\u00e1c v\u1ee5 d\u1ef1 \u00e1n\/c\u00f4ng tr\u00ecnh","LBL_CONTACT_SUBPANEL_TITLE":"Ng\u01b0\u1eddi li\u00ean h\u1ec7 b\u00ean Kh\u00e1ch h\u00e0ng\/\u0110\u1ed1i t\u00e1c\/Nh\u00e0 th\u1ea7u","LBL_ACCOUNT_SUBPANEL_TITLE":"Kh\u00e1ch h\u00e0ng\/\u0110\u1ed1i t\u00e1c\/Nh\u00e0 th\u1ea7u","LBL_OPPORTUNITY_SUBPANEL_TITLE":"\u0110\u01a1n h\u00e0ng","LBL_CASH_SUBPANEL_TITLE":"Thu\/Chi","LBL_ACTIVITIES_TITLE":"Ho\u1ea1t \u0111\u1ed9ng","LBL_ACTIVITIES_SUBPANEL_TITLE":"C\u00f4ng vi\u1ec7c","LBL_HISTORY_TITLE":"L\u1ecbch s\u1eed","LBL_HISTORY_SUBPANEL_TITLE":"L\u1ecbch s\u1eed","LBL_QUOTE_SUBPANEL_TITLE":"B\u00e1o gi\u00e1","CONTACT_REMOVE_PROJECT_CONFIRM":"B\u1ea1n c\u00f3 mu\u1ed1n x\u00f3a b\u1ecf nh\u1eefng li\u00ean h\u1ec7 t\u1eeb d\u1ef1 \u00e1n n\u00e0y?","LBL_QUICK_NEW_PROJECT":"T\u1ea1o d\u1ef1 \u00e1n","LBL_PROJECT_TASKS_SUBPANEL_TITLE":"T\u00e1c v\u1ee5 d\u1ef1 \u00e1n","LBL_CONTACTS_SUBPANEL_TITLE":"Ng\u01b0\u1eddi li\u00ean h\u1ec7","LBL_ACCOUNTS_SUBPANEL_TITLE":"\u0110\u1ed1i t\u00e1c","LBL_OPPORTUNITIES_SUBPANEL_TITLE":"\u0110\u01a1n h\u00e0ng","LBL_CASES_SUBPANEL_TITLE":"V\u1ee5 vi\u1ec7c","LBL_BUGS_SUBPANEL_TITLE":"L\u1ed7i","LBL_PRODUCTS_SUBPANEL_TITLE":"S\u1ea3n ph\u1ea9m","LBL_TASK_ID":"M\u00e3","LBL_TASK_NAME":"T\u00ean t\u00e1c v\u1ee5","LBL_DURATION":"Th\u1eddi l\u01b0\u1ee3ng","LBL_ACTUAL_DURATION":"Th\u1eddi l\u01b0\u1ee3ng th\u1ef1c t\u1ebf","LBL_START":"B\u1eaft \u0111\u1ea7u","LBL_FINISH":"K\u1ebft th\u00fac","LBL_PREDECESSORS":"Ng\u01b0\u1eddi ph\u1ee5 tr\u00e1ch tr\u01b0\u1edbc \u0111\u00f3","LBL_PERCENT_COMPLETE":"% Ho\u00e0n th\u00e0nh","LBL_MORE":"Th\u00eam","LBL_PERCENT_BUSY":"% b\u1eadn","LBL_TASK_ID_WIDGET":"id","LBL_TASK_NAME_WIDGET":"m\u00f4 t\u1ea3","LBL_DURATION_WIDGET":"th\u1eddi l\u01b0\u1ee3ng","LBL_START_WIDGET":"ng\u00e0y b\u1eaft \u0111\u1ea7u","LBL_FINISH_WIDGET":"ng\u00e0y k\u1ebft th\u00fac","LBL_PREDECESSORS_WIDGET":"predecessors_","LBL_PERCENT_COMPLETE_WIDGET":"% HT","LBL_EDIT_PROJECT_TASKS_TITLE":"Ch\u1ec9nh s\u1eeda t\u00e1c v\u1ee5 d\u1ef1 \u00e1n","LBL_OPPORTUNITIES":"\u0110\u01a1n h\u00e0ng","LBL_LAST_WEEK":"Tr\u01b0\u1edbc","LBL_NEXT_WEEK":"Ti\u1ebfp","LBL_PROJECT_TASKS":"Ti\u1ebfn tr\u00ecnh x\u1eed l\u00fd","LBL_SECURITYGROUPS_SUBPANEL_TITLE":"Security Groups","LBL_LIST_SMART_TITLE":"Ti\u1ebfn \u0111\u1ed9 th\u1ef1c hi\u1ec7n","LBL_RPT_SMART_WORK_PROJECTS":"Ti\u1ebfn \u0111\u1ed9 c\u00f4ng vi\u1ec7c trong d\u1ef1 \u00e1n","LBL_MODULE_SUBPANEL_TITLE":"Th\u00f4ng tin ch\u00ednh","LBL_ALL_ATTACHES":"All Files","LBL_TAB_PRODUCTS":"BC D\u1ef1 \u00e1n","LBL_CODE":"M\u00e3 d\u1ef1 \u00e1n","LBL_PROJ_AMOUNT":"Gi\u00e1 tr\u1ecb d\u1ef1 \u00e1n","LBL_PROJ_TYPE":"Ph\u00e2n lo\u1ea1i","LBL_PROJECT_TEAMS":"Teams","LBL_LIST_ADDRESS":"\u0110\u1ecba ch\u1ec9","LBL_LIST_PROJ_AMOUNT":"Gi\u00e1 tr\u1ecb","LBL_ESTIMATED_START_DATE_FROM":"Ng\u00e0y b\u1eaft \u0111\u1ea7u T\u1eeb","LBL_ESTIMATED_START_DATE_TO":"\u0110\u1ebfn"});