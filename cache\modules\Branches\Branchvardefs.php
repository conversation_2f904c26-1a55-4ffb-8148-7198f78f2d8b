<?php
// created: 2025-02-26 15:50:50
$G<PERSON><PERSON><PERSON><PERSON>["dictionary"]["Branch"] = array (
  'table' => 'branches',
  'audited' => true,
  'unified_search' => false,
  'duplicate_merge' => false,
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'name',
      'dbType' => 'varchar',
      'len' => 200,
      'required' => true,
      'audited' => true,
      'acl' => true,
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'branches_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'branches_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'code' => 
    array (
      'name' => 'code',
      'vname' => 'LBL_CODE',
      'type' => 'varchar',
      'len' => 20,
      'audited' => true,
    ),
    'short_name' => 
    array (
      'name' => 'short_name',
      'vname' => 'LBL_SHORT_NAME',
      'type' => 'varchar',
      'len' => 50,
      'audited' => true,
    ),
    'order_perfix' => 
    array (
      'name' => 'order_perfix',
      'vname' => 'LBL_ORDER_PERFIX',
      'type' => 'varchar',
      'len' => 5,
      'audited' => true,
    ),
    'order_number' => 
    array (
      'name' => 'order_number',
      'vname' => 'LBL_ORDER_NUMBER',
      'type' => 'varchar',
      'len' => 10,
    ),
    'limit_users' => 
    array (
      'name' => 'limit_users',
      'vname' => 'LBL_LIMIT_USERS',
      'type' => 'int',
      'len' => 6,
      'audited' => true,
    ),
    'block_login' => 
    array (
      'name' => 'block_login',
      'vname' => 'LBL_BLOCK_LOGIN',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => false,
    ),
    'phone_number' => 
    array (
      'name' => 'phone_number',
      'vname' => 'LBL_PHONE_NUMBER',
      'type' => 'varchar',
      'len' => 50,
      'audited' => true,
    ),
    'address_street' => 
    array (
      'name' => 'address_street',
      'vname' => 'LBL_ADDRESS_STREET',
      'type' => 'text',
      'audited' => true,
    ),
    'users' => 
    array (
      'name' => 'users',
      'type' => 'link',
      'relationship' => 'branch_users',
      'vname' => 'LBL_USERS',
      'source' => 'non-db',
      'comment' => 'List users of this branch',
    ),
    'allow_users' => 
    array (
      'name' => 'allow_users',
      'type' => 'link',
      'relationship' => 'users_branches',
      'vname' => 'LBL_ALLOW_USERS',
      'source' => 'non-db',
      'comment' => 'List users can see data of this branch',
    ),
    'bank_account_id' => 
    array (
      'name' => 'bank_account_id',
      'vname' => 'LBL_BANK_ACCOUNT_ID',
      'type' => 'id',
      'function' => 
      array (
        'name' => 'getBankAccountDropDown',
        'returns' => 'html',
        'include' => 'modules/Bank_Accounts/Bank_Account.php',
      ),
      'audited' => false,
      'required' => false,
      'massupdate' => false,
    ),
    'account_id' => 
    array (
      'name' => 'account_id',
      'vname' => 'LBL_ACCOUNT_ID',
      'type' => 'id',
      'audited' => true,
      'required' => false,
      'reportable' => false,
      'massupdate' => false,
    ),
    'account_name' => 
    array (
      'name' => 'account_name',
      'rname' => 'name',
      'id_name' => 'account_id',
      'vname' => 'LBL_ACCOUNT_NAME',
      'table' => 'accounts',
      'type' => 'relate',
      'link' => 'accounts',
      'join_name' => 'accounts',
      'isnull' => 'true',
      'module' => 'Accounts',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'accounts' => 
    array (
      'name' => 'accounts',
      'type' => 'link',
      'relationship' => 'accounts_branches',
      'vname' => 'LBL_ACCOUNTS',
      'source' => 'non-db',
    ),
    'company_id' => 
    array (
      'name' => 'company_id',
      'vname' => 'LBL_COMPANY_ID',
      'type' => 'id',
      'audited' => true,
      'required' => false,
      'reportable' => false,
      'massupdate' => false,
    ),
    'company_name' => 
    array (
      'name' => 'company_name',
      'rname' => 'name',
      'id_name' => 'company_id',
      'vname' => 'LBL_COMPANY_NAME',
      'table' => 'accounts',
      'type' => 'relate',
      'link' => 'companies',
      'join_name' => 'companies',
      'isnull' => 'true',
      'module' => 'Accounts',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'companies' => 
    array (
      'name' => 'companies',
      'type' => 'link',
      'relationship' => 'companies_branches',
      'vname' => 'LBL_COMPANIES',
      'source' => 'non-db',
    ),
    'title' => 
    array (
      'name' => 'title',
      'vname' => 'LBL_TITLE',
      'type' => 'text',
      'audited' => true,
    ),
    'quotes_description' => 
    array (
      'name' => 'quotes_description',
      'vname' => 'LBL_QUOTES_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
    ),
    'filename' => 
    array (
      'name' => 'filename',
      'vname' => 'LBL_FILENAME',
      'type' => 'varchar',
      'len' => '200',
      'reportable' => true,
      'comment' => 'File name associated with the note (attachment)',
    ),
    'file_mime_type' => 
    array (
      'name' => 'file_mime_type',
      'vname' => 'LBL_FILE_MIME_TYPE',
      'type' => 'varchar',
      'len' => '100',
      'comment' => 'Attachment MIME type',
    ),
    'file_url' => 
    array (
      'name' => 'file_url',
      'vname' => 'LBL_FILE_URL',
      'type' => 'function',
      'function_require' => 'include/upload_file.php',
      'function_class' => 'UploadFile',
      'function_name' => 'get_url',
      'function_params' => 
      array (
        0 => 'filename',
        1 => 'id',
        2 => 'date_entered',
        3 => 'date_modified',
      ),
      'source' => 'function',
      'reportable' => false,
      'comment' => 'Path to file (can be URL)',
    ),
    'bank_account_ignore_id' => 
    array (
      'name' => 'bank_account_ignore_id',
      'vname' => 'LBL_BANK_ACCOUNT_IGNORE_ID',
      'type' => 'id',
      'function' => 
      array (
        'name' => 'getBankAccountDropDown',
        'returns' => 'html',
        'include' => 'modules/Bank_Accounts/Bank_Account.php',
      ),
      'audited' => false,
      'required' => false,
      'massupdate' => false,
    ),
    'warehouse_id' => 
    array (
      'name' => 'warehouse_id',
      'vname' => 'LBL_WAREHOUSE_ID',
      'type' => 'id',
      'audited' => true,
      'required' => false,
      'reportable' => false,
      'massupdate' => false,
    ),
    'warehouse_name' => 
    array (
      'name' => 'warehouse_name',
      'rname' => 'name',
      'id_name' => 'warehouse_id',
      'vname' => 'LBL_WAREHOUSE_NAME',
      'table' => 'tqt_warehouses',
      'type' => 'relate',
      'link' => 'warehouses',
      'join_name' => 'warehouses',
      'isnull' => 'true',
      'module' => 'TQT_Warehouses',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'warehouses' => 
    array (
      'name' => 'warehouses',
      'type' => 'link',
      'relationship' => 'warehouses_branches',
      'vname' => 'LBL_WAREHOUSES',
      'source' => 'non-db',
    ),
    'product_prices' => 
    array (
      'name' => 'product_prices',
      'type' => 'link',
      'relationship' => 'products_branches_prices',
      'source' => 'non-db',
      'vname' => 'LBL_PRODUCT_PRICES',
    ),
    'price' => 
    array (
      'name' => 'price',
      'vname' => 'LBL_PRICE',
      'type' => 'float',
      'source' => 'non-db',
    ),
    'date_updated' => 
    array (
      'name' => 'date_updated',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'departments' => 
    array (
      'name' => 'departments',
      'type' => 'link',
      'relationship' => 'branches_departments',
      'source' => 'non-db',
      'vname' => 'LBL_DEPARTMENTS',
    ),
  ),
  'indices' => 
  array (
    'id' => 
    array (
      'name' => 'branchespk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    0 => 
    array (
      'name' => 'idx_branch_id_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'id',
        1 => 'deleted',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_branch_name_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'name',
      ),
    ),
    2 => 
    array (
      'name' => 'idx_branch_code_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'code',
      ),
    ),
    3 => 
    array (
      'name' => 'idx_branch_short_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'short_name',
      ),
    ),
  ),
  'relationships' => 
  array (
    'branches_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Branches',
      'rhs_table' => 'branches',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'branches_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Branches',
      'rhs_table' => 'branches',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
    'branch_users' => 
    array (
      'lhs_module' => 'Branches',
      'lhs_table' => 'branches',
      'lhs_key' => 'id',
      'rhs_module' => 'Users',
      'rhs_table' => 'users',
      'rhs_key' => 'branch_id',
      'relationship_type' => 'one-to-many',
    ),
    'accounts_branches' => 
    array (
      'lhs_module' => 'Accounts',
      'lhs_table' => 'accounts',
      'lhs_key' => 'id',
      'rhs_module' => 'Branches',
      'rhs_table' => 'branches',
      'rhs_key' => 'account_id',
      'relationship_type' => 'one-to-many',
    ),
    'companies_branches' => 
    array (
      'lhs_module' => 'Accounts',
      'lhs_table' => 'accounts',
      'lhs_key' => 'id',
      'rhs_module' => 'Branches',
      'rhs_table' => 'branches',
      'rhs_key' => 'company_id',
      'relationship_type' => 'one-to-many',
    ),
    'warehouses_branches' => 
    array (
      'lhs_module' => 'TQT_Warehouses',
      'lhs_table' => 'tqt_warehouses',
      'lhs_key' => 'id',
      'rhs_module' => 'Branches',
      'rhs_table' => 'branches',
      'rhs_key' => 'warehouse_id',
      'relationship_type' => 'one-to-many',
    ),
  ),
  'optimistic_lock' => true,
  'templates' => 
  array (
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
