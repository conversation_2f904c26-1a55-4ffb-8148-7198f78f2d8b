incomCRM.language.setLanguage('Track_Inventories', {"LBL_MODULE_NAME":"Theo d\u00f5i t\u1ed3n kho","LBL_MODULE_TITLE":"Theo d\u00f5i t\u1ed3n kho: Trang ch\u1ee7","LBL_LIST_FORM_TITLE":"Theo d\u00f5i t\u1ed3n kho","LBL_TRACK_INVENTORIES_DETAILS":"Chi ti\u1ebft xu\u1ea5t nh\u1eadp","LBL_TRACK_ORDERS":"Phi\u1ebfu xu\u1ea5t - H\u00e0ng b\u00e1n","LBL_TRACK_STOCK_OUTS":"Phi\u1ebfu xu\u1ea5t c\u00f2n l\u1ea1i","LBL_TRACK_STOCK_INS":"Phi\u1ebfu nh\u1eadp kho","LBL_RPT_TRACKING_QUANTITY":"B\u00e1o c\u00e1o Truy v\u1ea5n s\u1ed1 l\u01b0\u1ee3ng Nh\u1eadp\/Xu\u1ea5t theo s\u1ea3n ph\u1ea9m","LBL_RPT_TRACKING_INVENTORY":"B\u00e1o c\u00e1o Nh\u1eadp\/Xu\u1ea5t\/T\u1ed3n kho","LBL_RPT_TRACKING_WAREHOUSES":"B\u00e1o c\u00e1o T\u1ed3n kho theo t\u1eebng Kho h\u00e0ng","LBL_RPT_TRACKING_QUANTITY2":"Chi ti\u1ebft nh\u1eadp xu\u1ea5t","LBL_RPT_TRACKING_INVENTORY2":"Nh\u1eadp xu\u1ea5t t\u1ed3n VT","LBL_RPT_TRACKING_WAREHOUSES2":"T\u1ed3n theo kho h\u00e0ng","LBL_CHECK_INVENTORY_DIFF":"Ki\u1ec3m tra ch\u00eanh l\u1ec7ch t\u1ed3n kho","LBL_CHECK_OUT_NOT_REDUCE":"Ki\u1ec3m tra Phi\u1ebfu xu\u1ea5t kho ch\u01b0a tr\u1eeb kho"});