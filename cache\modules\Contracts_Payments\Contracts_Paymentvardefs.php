<?php
// created: 2025-03-03 22:47:49
$G<PERSON><PERSON><PERSON><PERSON>["dictionary"]["Contracts_Payment"] = array (
  'table' => 'contracts_payments',
  'audited' => true,
  'unified_search' => true,
  'duplicate_merge' => false,
  'optimistic_locking' => true,
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'name',
      'dbType' => 'varchar',
      'len' => 255,
      'audited' => true,
      'required' => true,
      'unified_search' => true,
      'merge_filter' => 'selected',
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
      'comment' => 'Full text of the note',
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'contracts_payments_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'contracts_payments_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'branch_id' => 
    array (
      'name' => 'branch_id',
      'vname' => 'LBL_BRANCH_ID',
      'type' => 'enum',
      'options' => 'users_branch_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'BranchID of assigned user',
    ),
    'department_id' => 
    array (
      'name' => 'department_id',
      'vname' => 'LBL_DEPARTMENT_ID',
      'type' => 'enum',
      'options' => 'users_department_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'DepartmentID of assigned user',
    ),
    'assigned_user_id' => 
    array (
      'name' => 'assigned_user_id',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'vname' => 'LBL_ASSIGNED_TO_ID',
      'group' => 'assigned_user_name',
      'type' => 'relate',
      'table' => 'users',
      'module' => 'Users',
      'isnull' => 'false',
      'dbType' => 'id',
      'audited' => true,
      'reportable' => true,
      'duplicate_merge' => 'disabled',
      'comment' => 'User ID assigned to record',
    ),
    'assigned_user_name' => 
    array (
      'name' => 'assigned_user_name',
      'link' => 'assigned_user_link',
      'vname' => 'LBL_ASSIGNED_TO_NAME',
      'rname' => 'user_name',
      'type' => 'relate',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'assigned_user_id',
      'module' => 'Users',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'assigned_user_link' => 
    array (
      'name' => 'assigned_user_link',
      'type' => 'link',
      'relationship' => 'contracts_payments_assigned_user',
      'vname' => 'LBL_ASSIGNED_TO_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
      'duplicate_merge' => 'enabled',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'table' => 'users',
    ),
    'rowindex' => 
    array (
      'name' => 'rowindex',
      'vname' => 'LBL_ROW_INDEX',
      'type' => 'int',
      'len' => '11',
      'massupdate' => false,
      'audited' => true,
    ),
    'status' => 
    array (
      'name' => 'status',
      'vname' => 'LBL_STATUS',
      'type' => 'enum',
      'len' => '50',
      'massupdate' => false,
      'options' => 'payments_status_dom',
      'audited' => true,
    ),
    'not_remind' => 
    array (
      'name' => 'not_remind',
      'vname' => 'LBL_NOT_REMIND',
      'type' => 'bool',
      'massupdate' => false,
      'audited' => true,
    ),
    'payment_percentage' => 
    array (
      'name' => 'payment_percentage',
      'vname' => 'LBL_PAYMENT_PERCENTAGE',
      'type' => 'float',
      'massupdate' => false,
      'audited' => true,
    ),
    'payment_amount' => 
    array (
      'name' => 'payment_amount',
      'vname' => 'LBL_PAYMENT_AMOUNT',
      'type' => 'double',
      'massupdate' => false,
      'audited' => true,
    ),
    'payment_period' => 
    array (
      'name' => 'payment_period',
      'vname' => 'LBL_PAYMENT_PERIOD',
      'type' => 'varchar',
      'len' => 200,
      'audited' => true,
      'comment' => 'Thoi han thanh toan',
    ),
    'proceeds_amount' => 
    array (
      'name' => 'proceeds_amount',
      'vname' => 'LBL_PROCEEDS_AMOUNT',
      'type' => 'double',
      'massupdate' => false,
      'comment' => 'Tien da thanh toan (+VAT)',
      'audited' => true,
    ),
    'payment_tax' => 
    array (
      'name' => 'payment_tax',
      'vname' => 'LBL_PAYMENT_TAX',
      'type' => 'float',
      'massupdate' => false,
      'comment' => '% Thue',
      'audited' => true,
    ),
    'received_amount' => 
    array (
      'name' => 'received_amount',
      'vname' => 'LBL_RECEIVED_AMOUNT',
      'type' => 'double',
      'massupdate' => false,
      'comment' => 'Tien da thanh toan (-VAT)',
      'audited' => true,
    ),
    'required_amount' => 
    array (
      'name' => 'required_amount',
      'vname' => 'LBL_REQUIRED_AMOUNT',
      'type' => 'double',
      'massupdate' => false,
      'comment' => 'Tien con no',
      'audited' => true,
    ),
    'date_expected' => 
    array (
      'name' => 'date_expected',
      'vname' => 'LBL_DATE_EXPECTED',
      'type' => 'date',
      'massupdate' => false,
      'comment' => 'Ngay du kien thanh toan',
      'audited' => true,
    ),
    'warning_expected' => 
    array (
      'name' => 'warning_expected',
      'vname' => 'LBL_WARNING_EXPECTED',
      'type' => 'varchar',
      'len' => '250',
      'audited' => true,
    ),
    'accounting_note' => 
    array (
      'name' => 'accounting_note',
      'vname' => 'LBL_ACCOUNTING_NOTE',
      'type' => 'text',
      'comment' => 'Ghi chu hach toan lien quan',
      'audited' => true,
    ),
    'accounting_date' => 
    array (
      'name' => 'accounting_date',
      'vname' => 'LBL_ACCOUNTING_DATE',
      'type' => 'date',
      'massupdate' => false,
      'audited' => true,
    ),
    'accounting_voucher' => 
    array (
      'name' => 'accounting_voucher',
      'vname' => 'LBL_ACCOUNTING_VOUCHER',
      'type' => 'varchar',
      'len' => '250',
      'comment' => 'Chung tu thanh toan',
      'audited' => true,
    ),
    'contract_id' => 
    array (
      'name' => 'contract_id',
      'vname' => 'LBL_CONTRACT_ID',
      'type' => 'id',
      'massupdate' => false,
      'audited' => true,
    ),
    'contract_name' => 
    array (
      'name' => 'contract_name',
      'vname' => 'LBL_CONTRACT_NAME',
      'source' => 'non-db',
      'type' => 'relate',
      'link' => 'link_contract_id',
      'module' => 'Contracts',
      'id_name' => 'contract_id',
      'rname' => 'name',
      'massupdate' => false,
    ),
    'link_contract_id' => 
    array (
      'name' => 'contracts',
      'type' => 'link',
      'source' => 'non-db',
      'relationship' => 'FK_PAYMENT_CONTRACT_ID',
    ),
    'contract_code' => 
    array (
      'name' => 'contract_code',
      'vname' => 'LBL_CONTRACT_CODE',
      'source' => 'non-db',
      'type' => 'relate',
      'link' => 'link_contract_id',
      'module' => 'Contracts',
      'id_name' => 'contract_id',
      'rname' => 'code',
      'massupdate' => false,
    ),
    'contracts_currency' => 
    array (
      'name' => 'contracts_currency',
      'vname' => 'LBL_PM_CONTRACTS_CURRENCY',
      'source' => 'non-db',
      'type' => 'varchar',
    ),
    'contracts_sale' => 
    array (
      'name' => 'contracts_sale',
      'vname' => 'LBL_PM_CONTRACTS_SALE',
      'source' => 'non-db',
      'type' => 'varchar',
    ),
    'contracts_amount' => 
    array (
      'name' => 'contracts_amount',
      'vname' => 'LBL_PM_CONTRACTS_AMOUNT',
      'source' => 'non-db',
      'type' => 'double',
    ),
    'contracts_user_id' => 
    array (
      'name' => 'contracts_user_id',
      'vname' => 'LBL_ASSIGNED_TO_ID',
      'source' => 'non-db',
      'type' => 'id',
    ),
    'contracts_support_id' => 
    array (
      'name' => 'contracts_support_id',
      'vname' => 'LBL_ASSIGNED_TO_ID',
      'source' => 'non-db',
      'type' => 'id',
    ),
    'account_name' => 
    array (
      'name' => 'account_name',
      'vname' => 'LBL_PM_ACCOUNT_NAME',
      'source' => 'non-db',
      'type' => 'varchar',
    ),
    'account_code' => 
    array (
      'name' => 'account_code',
      'vname' => 'LBL_PM_ACCOUNT_CODE',
      'source' => 'non-db',
      'type' => 'varchar',
    ),
    'accounting_user_id' => 
    array (
      'name' => 'accounting_user_id',
      'vname' => 'LBL_ACCOUNTING_USER_ID',
      'type' => 'id',
      'massupdate' => false,
      'audited' => true,
    ),
    'accounting_user_name' => 
    array (
      'name' => 'accounting_user_name',
      'vname' => 'LBL_ACCOUNTING_USER_NAME',
      'source' => 'non-db',
      'type' => 'relate',
      'link' => 'link_accounting_user_id',
      'module' => 'Users',
      'id_name' => 'accounting_user_id',
      'rname' => 'user_name',
      'massupdate' => false,
    ),
    'link_accounting_user_id' => 
    array (
      'name' => 'users',
      'type' => 'link',
      'source' => 'non-db',
      'relationship' => 'FK_ACCOUNTING_USER_ID',
    ),
    'contracts_collections' => 
    array (
      'name' => 'contracts_collections',
      'type' => 'link',
      'relationship' => 'contracts_collections_payments',
      'source' => 'non-db',
      'vname' => 'LBL_CONTRACTS_COLLECTIONS',
    ),
    'pmt_amt' => 
    array (
      'name' => 'pmt_amt',
      'vname' => 'LBL_PMT_AMT',
      'type' => 'double',
      'source' => 'non-db',
    ),
    'SecurityGroups' => 
    array (
      'name' => 'SecurityGroups',
      'type' => 'link',
      'relationship' => 'securitygroups_contracts_payments',
      'module' => 'SecurityGroups',
      'bean_name' => 'SecurityGroup',
      'source' => 'non-db',
      'vname' => 'LBL_SECURITYGROUPS',
    ),
  ),
  'indices' => 
  array (
    'id' => 
    array (
      'name' => 'contracts_paymentspk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    'idx_contracts_payments_branch_id' => 
    array (
      'name' => 'idx_contracts_payments_branch_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
      ),
    ),
    'idx_contracts_payments_department_id' => 
    array (
      'name' => 'idx_contracts_payments_department_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'department_id',
      ),
    ),
    'idx_contracts_payments_branch_dept' => 
    array (
      'name' => 'idx_contracts_payments_branch_dept',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
        1 => 'department_id',
      ),
    ),
    'idx_contracts_payments_assigned' => 
    array (
      'name' => 'idx_contracts_payments_assigned',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'assigned_user_id',
      ),
    ),
    0 => 
    array (
      'name' => 'idx_cont_pmt_contract',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'contract_id',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_cont_pmt_status',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'status',
      ),
    ),
    2 => 
    array (
      'name' => 'idx_cont_pmt_assigned_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'assigned_user_id',
      ),
    ),
    3 => 
    array (
      'name' => 'idx_cont_pmt_assigned_status',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'status',
        1 => 'assigned_user_id',
      ),
    ),
  ),
  'relationships' => 
  array (
    'contracts_payments_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Contracts_Payments',
      'rhs_table' => 'contracts_payments',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'contracts_payments_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Contracts_Payments',
      'rhs_table' => 'contracts_payments',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
    'contracts_payments_assigned_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Contracts_Payments',
      'rhs_table' => 'contracts_payments',
      'rhs_key' => 'assigned_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'FK_PAYMENT_CONTRACT_ID' => 
    array (
      'lhs_module' => 'Contracts',
      'lhs_table' => 'contracts',
      'lhs_key' => 'id',
      'rhs_module' => 'Contracts_Payments',
      'rhs_table' => 'contracts_payments',
      'rhs_key' => 'contract_id',
      'relationship_type' => 'one-to-many',
    ),
    'FK_ACCOUNTING_USER_ID' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Contracts_Payments',
      'rhs_table' => 'contracts_payments',
      'rhs_key' => 'accounting_user_id',
      'relationship_type' => 'one-to-many',
    ),
  ),
  'templates' => 
  array (
    'assignable' => 'assignable',
    'groups' => 'groups',
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
