<?php
// created: 2025-02-27 10:55:34
$<PERSON><PERSON><PERSON><PERSON><PERSON>["dictionary"]["Cash_Reason"] = array (
  'table' => 'cash_reasons',
  'audited' => true,
  'unified_search' => false,
  'duplicate_merge' => false,
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'name',
      'dbType' => 'varchar',
      'len' => '250',
      'required' => true,
      'audited' => true,
      'acl' => true,
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'cash_reasons_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'cash_reasons_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'reason_type' => 
    array (
      'name' => 'reason_type',
      'vname' => 'LBL_REASON_TYPE',
      'type' => 'enum',
      'options' => 'cash_reasons_type_dom',
      'len' => '2',
      'audited' => true,
      'massupdate' => false,
    ),
    'accounting_type' => 
    array (
      'name' => 'accounting_type',
      'vname' => 'LBL_ACCOUNTING_TYPE',
      'type' => 'enum',
      'options' => 'cash_accounting_type_dom',
      'len' => '5',
      'audited' => true,
      'massupdate' => false,
    ),
    'list_expense' => 
    array (
      'name' => 'list_expense',
      'vname' => 'LBL_LIST_EXPENSE',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => false,
      'default' => 0,
    ),
    'ignore_summary' => 
    array (
      'name' => 'ignore_summary',
      'vname' => 'LBL_IGNORE_SUMMARY',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => true,
      'default' => 0,
    ),
    'ignore_banks' => 
    array (
      'name' => 'ignore_banks',
      'vname' => 'LBL_IGNORE_BANKS',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => true,
      'default' => 0,
    ),
    'reason_opp_type' => 
    array (
      'name' => 'reason_opp_type',
      'vname' => 'LBL_REASON_OPP_TYPE',
      'type' => 'enum',
      'options' => 'opportunity_type_dom',
      'dbType' => 'char',
      'len' => '1',
      'audited' => true,
      'massupdate' => false,
    ),
    'cash_group' => 
    array (
      'name' => 'cash_group',
      'vname' => 'LBL_CASH_GROUP',
      'type' => 'enum',
      'options' => 'cash_group_dom',
      'len' => 10,
      'audited' => true,
      'massupdate' => false,
    ),
    'cash_project_group' => 
    array (
      'name' => 'cash_project_group',
      'vname' => 'LBL_CASH_PROJECT_GROUP',
      'type' => 'enum',
      'options' => 'cash_project_group_dom',
      'len' => 10,
      'audited' => true,
      'massupdate' => false,
    ),
    'debt_type' => 
    array (
      'name' => 'debt_type',
      'vname' => 'LBL_DEBT_TYPE',
      'type' => 'enum',
      'options' => 'debt_type_dom',
      'len' => 10,
      'audited' => true,
      'massupdate' => false,
    ),
    'is_net_cost' => 
    array (
      'name' => 'is_net_cost',
      'vname' => 'LBL_IS_NET_COST',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => false,
      'default' => 0,
    ),
  ),
  'indices' => 
  array (
    'id' => 
    array (
      'name' => 'cash_reasonspk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    0 => 
    array (
      'name' => 'idx_cash_rs_id_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'id',
        1 => 'deleted',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_cash_rs_del_type',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'reason_type',
      ),
    ),
    2 => 
    array (
      'name' => 'idx_cash_rs_del_acc_type',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'accounting_type',
      ),
    ),
    3 => 
    array (
      'name' => 'idx_cash_rs_del_ig_summ',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'ignore_summary',
      ),
    ),
    4 => 
    array (
      'name' => 'idx_cash_rs_del_ig_bank',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'ignore_banks',
      ),
    ),
    5 => 
    array (
      'name' => 'idx_cash_rs_del_cash_group',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'cash_group',
      ),
    ),
    6 => 
    array (
      'name' => 'idx_cash_rs_del_debt_type',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'debt_type',
      ),
    ),
    7 => 
    array (
      'name' => 'idx_cash_rs_del_is_net',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'is_net_cost',
      ),
    ),
  ),
  'relationships' => 
  array (
    'cash_reasons_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Cash_Reasons',
      'rhs_table' => 'cash_reasons',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'cash_reasons_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Cash_Reasons',
      'rhs_table' => 'cash_reasons',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
  ),
  'optimistic_lock' => true,
  'templates' => 
  array (
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
