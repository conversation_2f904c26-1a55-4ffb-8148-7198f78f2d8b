
<section id="EditViewViewBody" class="view-module-{$module|strtolower}">

<form action="index.php" method="POST" name="{$form_name}" id="{$form_id}" class="_edit-{$instanceName}" {$enctype}>
<input type="hidden" name="module" value="{$module}" />
<input type="hidden" name="action" id="action" />
{if isset($smarty.request.isDuplicate) && $smarty.request.isDuplicate eq "true"}
<input type="hidden" name="record" id="record" value="" />
<input type="hidden" name="duplicateSave" value="true" />
{else}
<input type="hidden" name="record" id="record" value="{$fields.id.value}" />
{/if}
<input type="hidden" name="return_module" value="{$smarty.request.return_module}" />
<input type="hidden" name="return_action" value="{if $smarty.request.return_action eq 'LoadTabSubpanels'}DetailView{else}{$smarty.request.return_action}{/if}" />
<input type="hidden" name="return_id" value="{$smarty.request.return_id}" />
<input type="hidden" name="isDuplicate" value="false" />
<input type="hidden" name="module_tab" /> 
<input type="hidden" name="contact_role" />
{if !empty($smarty.request.return_module)}
<input type="hidden" name="relate_to" value="{if $smarty.request.return_relationship}{$smarty.request.return_relationship}{else}{$smarty.request.return_module}{/if}" />
<input type="hidden" name="relate_id" value="{$smarty.request.return_id}" />
{/if}
<input type="hidden" name="offset" value="{$offset}" />
{if empty($fields.id.value)}
<input type="hidden" name="_notify_random" value="{php}echo create_guid();{/php}" />
{/if}
<input type="hidden" name="isFromEditView" value="{$module}" />
<input type="hidden" name="old_id" value="{$fields.document_revision_id.value}" />
<table width="100%" cellpadding="0" cellspacing="0" border="0">
<tr>
<td class="buttons">
{if $bean->aclAccess("save")}<input title="{$APP.LBL_SAVE_BUTTON_TITLE}" accessKey="{$APP.LBL_SAVE_BUTTON_KEY}" class="button save" onclick="{if $isDuplicate}this.form.return_id.value=''; {/if}this.form.action.value='Save'; return check_form('EditView');" type="submit" name="button" value="{$APP.LBL_SAVE_BUTTON_LABEL}" />{/if} 
{if !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($fields.id.value))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {elseif !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($smarty.request.return_id))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {else}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='index'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {/if}
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=Documents", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</td>
</tr>
</table>
{incomCRM_include include=$includes}
<div id="DEFAULT" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="DEFAULT_GROUP" style="display:block">
<table width="100%" border="0" cellspacing="1" cellpadding="0">
<tr>
<td valign="top" id='document_name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_NAME' module='Documents'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_document_name_field' >
{counter name="panelFieldCount"}

{if strlen($fields.document_name.value) <= 0}
{assign var="value" value=$fields.document_name.default_value }
{else}
{assign var="value" value=$fields.document_name.value }
{/if}
{if isTypeNumber($fields.document_name.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.document_name.name}' id='{$fields.document_name.name}' size='30' maxlength='255' value='{$value}' title='' tabindex='100'  /> 

</td>
<td valign="top" id='active_date_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DOC_ACTIVE_DATE' module='Documents'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_active_date_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.active_date.value }
<input autocomplete="off" type="text" name="{$fields.active_date.name}" id="{$fields.active_date.name}" value="{$date_value}" title=''  tabindex='101' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.active_date.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.active_date.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.active_date.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
</tr>
<tr>
<td valign="top" id='uploadfile_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_FILENAME' module='Documents'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_uploadfile_field' >
{counter name="panelFieldCount"}
<input tabindex="102"  type="hidden" name="escaped_document_name" /><input tabindex="102"  name="uploadfile" type="{$FILE_OR_HIDDEN}" size="30" maxlength="" onchange="setvalue(this);" value="{$fields.filename.value}" />{$fields.filename.value}
</td>
<td valign="top" id='exp_date_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DOC_EXP_DATE' module='Documents'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_exp_date_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.exp_date.value }
<input autocomplete="off" type="text" name="{$fields.exp_date.name}" id="{$fields.exp_date.name}" value="{$date_value}" title=''  tabindex='103' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.exp_date.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.exp_date.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.exp_date.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
</tr>
<tr>
<td valign="top" id='revision_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DOC_VERSION' module='Documents'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_revision_field' >
{counter name="panelFieldCount"}
<input tabindex="104"  name="revision" type="text" value="{$fields.revision.value}" {$DISABLED} />
</td>
<td valign="top" id='status_id_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DOC_STATUS' module='Documents'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_status_id_field' >
{counter name="panelFieldCount"}

<select name="{$fields.status_id.name}" id="{$fields.status_id.name}" title='' tabindex="105"  >
{if isset($fields.status_id.value) && $fields.status_id.value != ''}
{html_options options=$fields.status_id.options selected=$fields.status_id.value}
{else}
{html_options options=$fields.status_id.options selected=$fields.status_id.default}
{/if}
</select>
</td>
</tr>
<tr>
<td valign="top" id='is_template_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DET_IS_TEMPLATE' module='Documents'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_is_template_field' >
{counter name="panelFieldCount"}

{if strval($fields.is_template.value) == "1" || strval($fields.is_template.value) == "yes" || strval($fields.is_template.value) == "on"} 
{assign var="checked" value="CHECKED"}
{else}
{assign var="checked" value=""}
{/if}
<input type="hidden" name="{$fields.is_template.name}" value="0" /> 
<input type="checkbox" id="{$fields.is_template.name}" name="{$fields.is_template.name}" value="1" title='' tabindex="106" {$checked}  />

</td>
<td valign="top" id='template_type_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DET_TEMPLATE_TYPE' module='Documents'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_template_type_field' >
{counter name="panelFieldCount"}

<select name="{$fields.template_type.name}" id="{$fields.template_type.name}" title='' tabindex="107"  >
{if isset($fields.template_type.value) && $fields.template_type.value != ''}
{html_options options=$fields.template_type.options selected=$fields.template_type.value}
{else}
{html_options options=$fields.template_type.options selected=$fields.template_type.default}
{/if}
</select>
</td>
</tr>
<tr>
<td valign="top" id='area_code_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_AREA_CODE' module='Documents'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_area_code_field' >
{counter name="panelFieldCount"}

{if strlen($fields.area_code.value) <= 0}
{assign var="value" value=$fields.area_code.default_value }
{else}
{assign var="value" value=$fields.area_code.value }
{/if}
{if isTypeNumber($fields.area_code.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.area_code.name}' id='{$fields.area_code.name}' size='30' maxlength='15' value='{$value}' title='' tabindex='108'  /> 

</td>
<td valign="top" id='storage_location_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_STORAGE' module='Documents'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_storage_location_field' >
{counter name="panelFieldCount"}

<select name="{$fields.storage_location.name}" id="{$fields.storage_location.name}" title='' tabindex="109"  >
{if isset($fields.storage_location.value) && $fields.storage_location.value != ''}
{html_options options=$fields.storage_location.options selected=$fields.storage_location.value}
{else}
{html_options options=$fields.storage_location.options selected=$fields.storage_location.default}
{/if}
</select>
</td>
</tr>
<tr>
<td valign="top" id='no_dossier_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_NO_DOSSIER' module='Documents'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_no_dossier_field' >
{counter name="panelFieldCount"}

{if strlen($fields.no_dossier.value) <= 0}
{assign var="value" value=$fields.no_dossier.default_value }
{else}
{assign var="value" value=$fields.no_dossier.value }
{/if}
{if isTypeNumber($fields.no_dossier.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.no_dossier.name}' id='{$fields.no_dossier.name}' size='30' maxlength='20' value='{$value}' title='' tabindex='110'  /> 

</td>
<td valign="top" id='assigned_user_name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ASSIGNED_USER_NAME' module='Documents'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_assigned_user_name_field' >
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{/if}<input type="hidden" name="{$fields.assigned_user_name.id_name}" id="{$fields.assigned_user_name.id_name}" value="{$fields.assigned_user_id.value}" />
<input type="text" name="{$fields.assigned_user_name.name}" class="sqsEnabled" tabindex="111" id="{$fields.assigned_user_name.name}" size="16" value="{$fields.assigned_user_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{/if}
<input type="button" name="btn_{$fields.assigned_user_name.name}" id="btn_{$fields.assigned_user_name.name}" tabindex="111" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.assigned_user_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"EditView","field_to_name_array":{"id":"assigned_user_id","user_name":"assigned_user_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.assigned_user_name.name}" id="btn_clr_{$fields.assigned_user_name.name}" tabindex="111" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.assigned_user_name.name}.value=""; this.form.{$fields.assigned_user_name.id_name}.value=""; this.form.{$fields.assigned_user_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
{if !empty($isSmartView)}</div>{/if}<script type="text/javascript">enableQS(false);</script>
</td>
</tr>
<tr>
<td valign="top" id='description_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DESCRIPTION' module='Documents'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_description_field' >
{counter name="panelFieldCount"}

{if empty($fields.description.value)}
{assign var="value" value=$fields.description.default_value }
{else}
{assign var="value" value=$fields.description.value }
{/if}
<textarea id="{$fields.description.name}" name="{$fields.description.name}" rows="4" cols="45" title='' tabindex="112"  >{$value}</textarea>
</td>
<td valign="top" id='department_all_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DEPARTMENT_ALL' module='Documents'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_department_all_field' >
{counter name="panelFieldCount"}
<input type="hidden" id="{$fields.department_all.name}_multiselect" name="{$fields.department_all.name}_multiselect" value="true" />
{multienum_to_array string=$fields.department_all.value default=$fields.department_all.default assign="values"}
<div style="height:80px;" id="{$fields.department_all.name}_multi_grp" class="multi-enum-grp multi-enum-grp-default">
{counter name="rowCount" start=0 print=false assign="rowCount"}
{foreach from=$fields.department_all.options item=option key=value}
{if $value}
<label id="_grp_{if isset($fields.department_all.groupBy) and isset($fields.department_all.groupBy[$value])}{ $fields.department_all.groupBy[$value]}{/if}" style="white-space: nowrap">
<input type="checkbox" id="{$fields.department_all.name}_checkbox{$rowCount}" name="{$fields.department_all.name}[]" value="{$value}" {if in_array(strval($value), $values)}checked="checked"{/if}  /> {$option}
</label>
{counter name="rowCount"}
{/if}
{/foreach}
</div>
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("DEFAULT").style.display='none';</script>
{/if}

<div class="buttons" style="margin-top:15px;">
{if $bean->aclAccess("save")}<input title="{$APP.LBL_SAVE_BUTTON_TITLE}" accessKey="{$APP.LBL_SAVE_BUTTON_KEY}" class="button save" onclick="{if $isDuplicate}this.form.return_id.value=''; {/if}this.form.action.value='Save'; return check_form('EditView');" type="submit" name="button" value="{$APP.LBL_SAVE_BUTTON_LABEL}" />{/if} 
{if !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($fields.id.value))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {elseif !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($smarty.request.return_id))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {else}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='index'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {/if}
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=Documents", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</div>
</form>
{$set_focus_block}
<!-- Begin Meta-Data Javascript -->
<script type="text/javascript" src="include/javascript/popup_parent_helper.js?s={$incomCRM_VERSION}&c={$JS_CUSTOM_VERSION}"></script>
<script type="text/javascript" src="include/jsolait/init.js?s={$incomCRM_VERSION}&c={$JS_CUSTOM_VERSION}"></script>
<script type="text/javascript" src="include/jsolait/lib/urllib.js?s={$incomCRM_VERSION}&c={$JS_CUSTOM_VERSION}"></script>
<script type="text/javascript" src="include/javascript/jsclass_base.js"></script>
<script type="text/javascript" src="include/javascript/jsclass_async.js"></script>
<script type="text/javascript" src="include/JSON.js?s={$incomCRM_VERSION}"></script>
<script type="text/javascript" src="modules/Documents/documents.js?s={$incomCRM_VERSION}&c={$JS_CUSTOM_VERSION}"></script>
<!-- End Meta-Data Javascript -->
</section>{literal}
<script type="text/javascript">
addToValidate('EditView', 'id', 'id', false, '{/literal}{incomCRM_translate label='LBL_ID' module='Documents'}{literal}' );
addToValidate('EditView', 'date_entered_date', 'date', false, 'Ngày nhập' );
addToValidate('EditView', 'date_modified_date', 'date', false, 'Ngày thay đổi' );
addToValidate('EditView', 'modified_user_id', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_ID' module='Documents'}{literal}' );
addToValidate('EditView', 'modified_by_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_NAME' module='Documents'}{literal}' );
addToValidate('EditView', 'created_by', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_CREATED_ID' module='Documents'}{literal}' );
addToValidate('EditView', 'created_by_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_CREATED' module='Documents'}{literal}' );
addToValidate('EditView', 'deleted', 'bool', false, '{/literal}{incomCRM_translate label='LBL_DELETED' module='Documents'}{literal}' );
addToValidate('EditView', 'description', 'text', false, '{/literal}{incomCRM_translate label='LBL_DESCRIPTION' module='Documents'}{literal}' );
addToValidate('EditView', 'branch_id', 'enum', false, '{/literal}{incomCRM_translate label='LBL_BRANCH_ID' module='Documents'}{literal}' );
addToValidate('EditView', 'department_id', 'enum', false, '{/literal}{incomCRM_translate label='LBL_DEPARTMENT_ID' module='Documents'}{literal}' );
addToValidate('EditView', 'assigned_user_id', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_ASSIGNED_USER_ID' module='Documents'}{literal}' );
addToValidate('EditView', 'assigned_user_name', 'relate', true, '{/literal}{incomCRM_translate label='LBL_ASSIGNED_USER_NAME' module='Documents'}{literal}' );
addToValidate('EditView', 'document_name', 'varchar', true, '{/literal}{incomCRM_translate label='LBL_NAME' module='Documents'}{literal}' );
addToValidate('EditView', 'filename', 'file', false, '{/literal}{incomCRM_translate label='LBL_FILENAME' module='Documents'}{literal}' );
addToValidate('EditView', 'uploadfile', 'file', false, '{/literal}{incomCRM_translate label='LBL_FILENAME' module='Documents'}{literal}' );
addToValidate('EditView', 'active_date', 'date', true, '{/literal}{incomCRM_translate label='LBL_DOC_ACTIVE_DATE' module='Documents'}{literal}' );
addToValidate('EditView', 'exp_date', 'date', false, '{/literal}{incomCRM_translate label='LBL_DOC_EXP_DATE' module='Documents'}{literal}' );
addToValidate('EditView', 'category_id', 'enum', false, '{/literal}{incomCRM_translate label='LBL_SF_CATEGORY' module='Documents'}{literal}' );
addToValidate('EditView', 'subcategory_id', 'enum', false, '{/literal}{incomCRM_translate label='LBL_SF_SUBCATEGORY' module='Documents'}{literal}' );
addToValidate('EditView', 'status_id', 'enum', true, '{/literal}{incomCRM_translate label='LBL_DOC_STATUS' module='Documents'}{literal}' );
addToValidate('EditView', 'status', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_DOC_STATUS' module='Documents'}{literal}' );
addToValidate('EditView', 'document_revision_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_LATEST_REVISION' module='Documents'}{literal}' );
addToValidate('EditView', 'revision', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_DOC_VERSION' module='Documents'}{literal}' );
addToValidate('EditView', 'last_rev_created_name', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_LAST_REV_CREATOR' module='Documents'}{literal}' );
addToValidate('EditView', 'last_rev_mime_type', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_LAST_REV_MIME_TYPE' module='Documents'}{literal}' );
addToValidate('EditView', 'latest_revision', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_LATEST_REVISION' module='Documents'}{literal}' );
addToValidate('EditView', 'last_rev_create_date', 'date', false, '{/literal}{incomCRM_translate label='LBL_LAST_REV_CREATE_DATE' module='Documents'}{literal}' );
addToValidate('EditView', 'related_doc_id', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_RELATED_DOCUMENT_ID' module='Documents'}{literal}' );
addToValidate('EditView', 'related_doc_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_DET_RELATED_DOCUMENT' module='Documents'}{literal}' );
addToValidate('EditView', 'related_doc_rev_id', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_RELATED_DOCUMENT_REVISION_ID' module='Documents'}{literal}' );
addToValidate('EditView', 'related_doc_rev_number', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_DET_RELATED_DOCUMENT_VERSION' module='Documents'}{literal}' );
addToValidate('EditView', 'is_template', 'bool', false, '{/literal}{incomCRM_translate label='LBL_DET_IS_TEMPLATE' module='Documents'}{literal}' );
addToValidate('EditView', 'template_type', 'enum', true, '{/literal}{incomCRM_translate label='LBL_DET_TEMPLATE_TYPE' module='Documents'}{literal}' );
addToValidate('EditView', 'storage', 'enum', false, '{/literal}{incomCRM_translate label='LBL_STORAGE' module='Documents'}{literal}' );
addToValidate('EditView', 'area_code', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_AREA_CODE' module='Documents'}{literal}' );
addToValidate('EditView', 'storage_location', 'enum', true, '{/literal}{incomCRM_translate label='LBL_STORAGE' module='Documents'}{literal}' );
addToValidate('EditView', 'no_dossier', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_NO_DOSSIER' module='Documents'}{literal}' );
addToValidate('EditView', 'department_all[]', 'enum', false, '{/literal}{incomCRM_translate label='LBL_DEPARTMENT_ALL' module='Documents'}{literal}' );
addToValidateBinaryDependency('EditView', 'assigned_user_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Documents'}{literal}{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO' module='Documents'}{literal}', 'assigned_user_id' );
</script>
<script type="text/javascript">if(typeof sqs_objects == 'undefined'){var sqs_objects = new Array;}
sqs_objects['EditView_assigned_user_name'] = {"form":"EditView","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["assigned_user_name","assigned_user_id"],"required_list":["assigned_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
</script>{/literal}
