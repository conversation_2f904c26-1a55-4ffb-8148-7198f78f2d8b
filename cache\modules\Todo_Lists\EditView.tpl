
<section id="EditViewViewBody" class="view-module-{$module|strtolower}">

<form action="index.php" method="POST" name="{$form_name}" id="{$form_id}" class="_edit-{$instanceName}" {$enctype}>
<input type="hidden" name="module" value="{$module}" />
<input type="hidden" name="action" id="action" />
{if isset($smarty.request.isDuplicate) && $smarty.request.isDuplicate eq "true"}
<input type="hidden" name="record" id="record" value="" />
<input type="hidden" name="duplicateSave" value="true" />
{else}
<input type="hidden" name="record" id="record" value="{$fields.id.value}" />
{/if}
<input type="hidden" name="return_module" value="{$smarty.request.return_module}" />
<input type="hidden" name="return_action" value="{if $smarty.request.return_action eq 'LoadTabSubpanels'}DetailView{else}{$smarty.request.return_action}{/if}" />
<input type="hidden" name="return_id" value="{$smarty.request.return_id}" />
<input type="hidden" name="isDuplicate" value="false" />
<input type="hidden" name="module_tab" /> 
<input type="hidden" name="contact_role" />
{if !empty($smarty.request.return_module)}
<input type="hidden" name="relate_to" value="{if $smarty.request.return_relationship}{$smarty.request.return_relationship}{else}{$smarty.request.return_module}{/if}" />
<input type="hidden" name="relate_id" value="{$smarty.request.return_id}" />
{/if}
<input type="hidden" name="offset" value="{$offset}" />
{if empty($fields.id.value)}
<input type="hidden" name="_notify_random" value="{php}echo create_guid();{/php}" />
{/if}
<input type="hidden" name="isFromEditView" value="{$module}" />
<input type="hidden" name="assigned_user_id" id="assigned_user_id" value="{$fields.assigned_user_id.value}" />
<table width="100%" cellpadding="0" cellspacing="0" border="0">
<tr>
<td class="buttons">
{if $bean->aclAccess("save")}<input title="{$APP.LBL_SAVE_BUTTON_TITLE}" accessKey="{$APP.LBL_SAVE_BUTTON_KEY}" class="button save" onclick="{if $isDuplicate}this.form.return_id.value=''; {/if}this.form.action.value='Save'; return check_form('EditView');" type="submit" name="button" value="{$APP.LBL_SAVE_BUTTON_LABEL}" />{/if} 
{if !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($fields.id.value))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {elseif !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($smarty.request.return_id))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {else}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='index'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {/if}
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=Todo_Lists", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</td>
</tr>
</table>
{incomCRM_include include=$includes}
<div id="DEFAULT" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="DEFAULT_GROUP" style="display:block">
<table width="100%" border="0" cellspacing="1" cellpadding="0">
<tr>
<td valign="top" id='code_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CODE' module='Todo_Lists'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_code_field' >
{counter name="panelFieldCount"}

{if strlen($fields.code.value) <= 0}
{assign var="value" value=$fields.code.default_value }
{else}
{assign var="value" value=$fields.code.value }
{/if}
{if isTypeNumber($fields.code.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.code.name}' id='{$fields.code.name}' size='30' maxlength='30' value='{$value}' title='' tabindex='100'  /> 

</td>
<td valign="top" id='processing_time_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_PROCESSING_TIME' module='Todo_Lists'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_processing_time_field' >
{counter name="panelFieldCount"}

{if strlen($fields.processing_time.value) <= 0}
{assign var="value" value=$fields.processing_time.default_value }
{else}
{assign var="value" value=$fields.processing_time.value }
{/if}
{if isTypeNumber($fields.processing_time.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.processing_time.name}' id='{$fields.processing_time.name}' size='30' maxlength='4' value='{$value}' title='' tabindex='101'  class="input-cal" onkeypress="return blockNonNumbers(this, event);" /> 

{$MOD.LBL_TIME_UNIT}
</td>
</tr>
<tr>
<td valign="top" id='name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_NAME' module='Todo_Lists'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_name_field' >
{counter name="panelFieldCount"}

{if strlen($fields.name.value) <= 0}
{assign var="value" value=$fields.name.default_value }
{else}
{assign var="value" value=$fields.name.value }
{/if}
{if isTypeNumber($fields.name.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.name.name}' id='{$fields.name.name}' size='30' maxlength='250' value='{$value}' title='' tabindex='102'  /> 

</td>
<td valign="top" id='cycle_type_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CYCLE_TYPE' module='Todo_Lists'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_cycle_type_field' >
{counter name="panelFieldCount"}

<select name="{$fields.cycle_type.name}" id="{$fields.cycle_type.name}" title='' tabindex="103"  >
{if isset($fields.cycle_type.value) && $fields.cycle_type.value != ''}
{html_options options=$fields.cycle_type.options selected=$fields.cycle_type.value}
{else}
{html_options options=$fields.cycle_type.options selected=$fields.cycle_type.default}
{/if}
</select>
</td>
</tr>
<tr>
<td valign="top" id='description_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DESCRIPTION' module='Todo_Lists'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_description_field' >
{counter name="panelFieldCount"}

{if empty($fields.description.value)}
{assign var="value" value=$fields.description.default_value }
{else}
{assign var="value" value=$fields.description.value }
{/if}
<textarea id="{$fields.description.name}" name="{$fields.description.name}" rows="2" cols="45" title='' tabindex="104"  >{$value}</textarea>
</td>
<td valign="top" id='job_type_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_JOB_TYPE' module='Todo_Lists'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_job_type_field' >
{counter name="panelFieldCount"}

<select name="{$fields.job_type.name}" id="{$fields.job_type.name}" title='' tabindex="105"  >
{if isset($fields.job_type.value) && $fields.job_type.value != ''}
{html_options options=$fields.job_type.options selected=$fields.job_type.value}
{else}
{html_options options=$fields.job_type.options selected=$fields.job_type.default}
{/if}
</select>
</td>
</tr>
<tr>
<td valign="top" id='department_all_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DEPARTMENT_ALL' module='Todo_Lists'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" colspan='3' id='_department_all_field' >
{counter name="panelFieldCount"}
<input type="hidden" id="{$fields.department_all.name}_multiselect" name="{$fields.department_all.name}_multiselect" value="true" />
{multienum_to_array string=$fields.department_all.value default=$fields.department_all.default assign="values"}
<div style="" id="{$fields.department_all.name}_multi_grp" class="multi-enum-grp multi-enum-grp-default">
{counter name="rowCount" start=0 print=false assign="rowCount"}
{foreach from=$fields.department_all.options item=option key=value}
{if $value}
<label id="_grp_{if isset($fields.department_all.groupBy) and isset($fields.department_all.groupBy[$value])}{ $fields.department_all.groupBy[$value]}{/if}" style="white-space: nowrap">
<input type="checkbox" id="{$fields.department_all.name}_checkbox{$rowCount}" name="{$fields.department_all.name}[]" value="{$value}" {if in_array(strval($value), $values)}checked="checked"{/if}  /> {$option}
</label>
{counter name="rowCount"}
{/if}
{/foreach}
</div>
</td>
</tr>
<tr>
<td valign="top" id='teams_all_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_TEAMS_ALL' module='Todo_Lists'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" colspan='3' id='_teams_all_field' >
{counter name="panelFieldCount"}
<input type="hidden" id="{$fields.teams_all.name}_multiselect" name="{$fields.teams_all.name}_multiselect" value="true" />
{multienum_to_array string=$fields.teams_all.value default=$fields.teams_all.default assign="values"}
<div style="" id="{$fields.teams_all.name}_multi_grp" class="multi-enum-grp multi-enum-grp-default">
{counter name="rowCount" start=0 print=false assign="rowCount"}
{foreach from=$fields.teams_all.options item=option key=value}
{if $value}
<label id="_grp_{if isset($fields.teams_all.groupBy) and isset($fields.teams_all.groupBy[$value])}{ $fields.teams_all.groupBy[$value]}{/if}" style="white-space: nowrap">
<input type="checkbox" id="{$fields.teams_all.name}_checkbox{$rowCount}" name="{$fields.teams_all.name}[]" value="{$value}" {if in_array(strval($value), $values)}checked="checked"{/if}  /> {$option}
</label>
{counter name="rowCount"}
{/if}
{/foreach}
</div>
</td>
</tr>
<tr>
<td valign="top" id='users_all_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_USERS_ALL' module='Todo_Lists'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" colspan='3' id='_users_all_field' >
{counter name="panelFieldCount"}

{incomCRM_peoplebox options=$fields.users_all.options default=$fields.users_all.value module="Users" assign="values"}
<input type="hidden" id="{$fields.users_all.name}_multiselect" name="{$fields.users_all.name}_multiselect" value="true" />
<div class="people"></div>
<script type="text/javascript">
LULO.peopleBox.add( "{$fields.users_all.name}", {ldelim}form:'{$form_name}', module:'Users', value:{$values|default:'null'}, data:{literal}{"method":"peopleBox","modules":["Users"],"group":"or","field_list":["user_name","id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"last_name","limit":"30"}{/literal} {rdelim});
</script>
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("DEFAULT").style.display='none';</script>
{/if}

<div class="buttons" style="margin-top:15px;">
{if $bean->aclAccess("save")}<input title="{$APP.LBL_SAVE_BUTTON_TITLE}" accessKey="{$APP.LBL_SAVE_BUTTON_KEY}" class="button save" onclick="{if $isDuplicate}this.form.return_id.value=''; {/if}this.form.action.value='Save'; return check_form('EditView');" type="submit" name="button" value="{$APP.LBL_SAVE_BUTTON_LABEL}" />{/if} 
{if !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($fields.id.value))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {elseif !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($smarty.request.return_id))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {else}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='index'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {/if}
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=Todo_Lists", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</div>
</form>
{$set_focus_block}
</section>{literal}
<script type="text/javascript">
addToValidate('EditView', 'id', 'id', false, '{/literal}{incomCRM_translate label='LBL_ID' module='Todo_Lists'}{literal}' );
addToValidate('EditView', 'date_entered_date', 'date', false, 'Ngày tạo' );
addToValidate('EditView', 'date_modified_date', 'date', false, 'Ngày cập nhật' );
addToValidate('EditView', 'modified_user_id', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_ID' module='Todo_Lists'}{literal}' );
addToValidate('EditView', 'modified_by_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_NAME' module='Todo_Lists'}{literal}' );
addToValidate('EditView', 'created_by', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_CREATED_ID' module='Todo_Lists'}{literal}' );
addToValidate('EditView', 'created_by_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_CREATED' module='Todo_Lists'}{literal}' );
addToValidate('EditView', 'deleted', 'bool', false, '{/literal}{incomCRM_translate label='LBL_DELETED' module='Todo_Lists'}{literal}' );
addToValidate('EditView', 'name', 'name', true, '{/literal}{incomCRM_translate label='LBL_NAME' module='Todo_Lists'}{literal}' );
addToValidate('EditView', 'description', 'text', false, '{/literal}{incomCRM_translate label='LBL_DESCRIPTION' module='Todo_Lists'}{literal}' );
addToValidate('EditView', 'branch_id', 'enum', false, '{/literal}{incomCRM_translate label='LBL_BRANCH_ID' module='Todo_Lists'}{literal}' );
addToValidate('EditView', 'department_id', 'enum', false, '{/literal}{incomCRM_translate label='LBL_DEPARTMENT_ID' module='Todo_Lists'}{literal}' );
addToValidate('EditView', 'assigned_user_id', 'relate', false, '{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO_ID' module='Todo_Lists'}{literal}' );
addToValidate('EditView', 'assigned_user_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='Todo_Lists'}{literal}' );
addToValidate('EditView', 'code', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_CODE' module='Todo_Lists'}{literal}' );
addToValidate('EditView', 'processing_time', 'int', true, '{/literal}{incomCRM_translate label='LBL_PROCESSING_TIME' module='Todo_Lists'}{literal}' );
addToValidate('EditView', 'cycle_type', 'enum', true, '{/literal}{incomCRM_translate label='LBL_CYCLE_TYPE' module='Todo_Lists'}{literal}' );
addToValidate('EditView', 'job_type', 'enum', true, '{/literal}{incomCRM_translate label='LBL_JOB_TYPE' module='Todo_Lists'}{literal}' );
addToValidate('EditView', 'department_all[]', 'enum', false, '{/literal}{incomCRM_translate label='LBL_DEPARTMENT_ALL' module='Todo_Lists'}{literal}' );
addToValidate('EditView', 'teams_all[]', 'enum', false, '{/literal}{incomCRM_translate label='LBL_TEAMS_ALL' module='Todo_Lists'}{literal}' );
addToValidate('EditView', 'users_all[]', 'enum', false, '{/literal}{incomCRM_translate label='LBL_USERS_ALL' module='Todo_Lists'}{literal}' );
addToValidate('EditView', 'group_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_GROUP_ID' module='Todo_Lists'}{literal}' );
addToValidate('EditView', 'group_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_GROUP_NAME' module='Todo_Lists'}{literal}' );
addToValidate('EditView', 'group_steps', 'text', false, '{/literal}{incomCRM_translate label='LBL_GROUP_STEPS' module='Todo_Lists'}{literal}' );
addToValidate('EditView', 'workflow_code', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_WORKFLOW_CODE' module='Todo_Lists'}{literal}' );
addToValidateBinaryDependency('EditView', 'assigned_user_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Todo_Lists'}{literal}{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO' module='Todo_Lists'}{literal}', 'assigned_user_id' );
</script>
{/literal}
