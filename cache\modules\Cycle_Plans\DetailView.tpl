{literal}
<style type="text/css">
#subpanel_title_accounts h3 { color:#F00 !important; }
a.portal { color:#000 !important; }
#list_subpanel_activities table.list, #list_subpanel_opportunities table.list { max-width:1300px; }
a.ql, #daily_cal_table .cell-day #cal_add_act { display: none !important; }
.care .yes { color:green !important; font-weight:bold; }
.care .no, .red { color:red !important; font-weight:bold; }
.care .all { color:black !important; font-weight:bold; }
.care .tip { color:black; font-style:italic; }
</style>
{/literal}

<section id="DetailViewBody" class="view-module-{$module|strtolower}">

<form action="index.php" method="post" name="{$form_name|default:'DetailView'}" id="form" class="_view-{$instanceName}">
<table cellpadding="1" cellspacing="0" border="0" width="100%">
<tr>
<td class="buttons" align="left" valign="top" nowrap>
<input type="hidden" name="module" value="{$module}" />
<input type="hidden" name="action" value="EditView" />
<input type="hidden" name="record" value="{$fields.id.value}" />
<input type="hidden" name="return_action" />
<input type="hidden" name="return_module" />
<input type="hidden" name="return_id" />
<input type="hidden" name="module_tab" /> 
<input type="hidden" name="isDuplicate" value="false" />
<input type="hidden" name="offset" value="{$offset}" />
{if !isset($isEditEnabled) || (isset($isEditEnabled) && $isEditEnabled)}
<input title="{$APP.LBL_EDIT_BUTTON_TITLE}" accessKey="{$APP.LBL_EDIT_BUTTON_KEY}" class="button bedit" onclick="this.form.return_module.value='{$module}'; this.form.return_action.value='DetailView'; this.form.return_id.value='{$id}'; this.form.action.value='EditView'" type="submit" name="Edit" id='edit_button' value="  {$APP.LBL_EDIT_BUTTON_LABEL}  " />
<input title="{$APP.LBL_DUPLICATE_BUTTON_TITLE}" accessKey="{$APP.LBL_DUPLICATE_BUTTON_KEY}" class="button duplicate" onclick="this.form.return_module.value='{$module}'; this.form.return_action.value='index'; this.form.isDuplicate.value=true; this.form.action.value='EditView'" type="submit" name="Duplicate" value=" {$APP.LBL_DUPLICATE_BUTTON_LABEL} " id='duplicate_button' />
{/if}
{if !isset($isDeleteEnabled) || (isset($isDeleteEnabled) && $isDeleteEnabled)}
<input title="{$APP.LBL_DELETE_BUTTON_TITLE}" accessKey="{$APP.LBL_DELETE_BUTTON_KEY}" class="button delete" onclick="this.form.return_module.value='{$module}'; this.form.return_action.value='ListView'; this.form.action.value='Delete'; return confirm('{$APP.NTC_DELETE_CONFIRMATION}')" type="submit" name="Delete" value=" {$APP.LBL_DELETE_BUTTON_LABEL} " />
{/if}
</td>
<td class="buttons" align="left" valign="top" nowrap>
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=Cycle_Plans", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</td>
<td align="right" width="90%">{$ADMIN_EDIT}</td>
</tr>
</table>
</form>
{incomCRM_include include=$includes}
<div id="DEFAULT" class="detailview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<div class="detail view {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="DEFAULT_GROUP" style="display:block">
<table id='detailpanel_1' cellspacing='{$gridline}'>
{$PAGINATION}
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_NAME' module='Cycle_Plans'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_name_field' >
{counter name="panelFieldCount"}

<span id='{$fields.name.name}' >
{$fields.name.value}
</span>
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CODE' module='Cycle_Plans'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_code_field' >
{counter name="panelFieldCount"}

<span id='{$fields.code.name}' >
{$fields.code.value}
</span>
</td>
</tr>
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_PLAN_MONTH' module='Cycle_Plans'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_plan_month_field' >
{counter name="panelFieldCount"}

{ $fields.plan_month.options[$fields.plan_month.value]}
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='Cycle_Plans'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_assigned_user_name_field' >
{counter name="panelFieldCount"}

{$fields.assigned_user_name.value}
</td>
</tr>
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_PLAN_YEAR' module='Cycle_Plans'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_plan_year_field' >
{counter name="panelFieldCount"}

{ $fields.plan_year.options[$fields.plan_year.value]}
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DATE_ENTERED' module='Cycle_Plans'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_date_entered_field' >
{counter name="panelFieldCount"}
{$fields.date_entered.value} {$APP.LBL_BY} {$fields.created_by_name.value}	
</td>
</tr>
<tr>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DESCRIPTION' module='Cycle_Plans'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_description_field' >
{counter name="panelFieldCount"}

{$fields.description.value|url2html|nl2br}
</td>
<td width='20%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DATE_MODIFIED' module='Cycle_Plans'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='30%' id='_date_modified_field' >
{counter name="panelFieldCount"}
{$fields.date_modified.value} {$APP.LBL_BY} {$fields.modified_by_name.value}	
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("DEFAULT").style.display='none';</script>
{/if}

</section>
<div id="assigned_user_name_txt" style="display:none;">
<span style="color:red">{$fields.assigned_user_name.value}</span>
</div>
<div style="width:1000px;">
<table cellspacing="0" cellpadding="0" border="0" class="list view gird" width="100%">
<tr>
<th scope="col">&nbsp;</th>
<th scope="col" style="text-align:center;">{incomCRM_translate label='LBL_MONTHLY_TARGET' module='Opportunities'}</th>
<th scope="col" style="text-align:center;">{$MOD.LBL_PERCENT_COMPLETED}<br/>(Thực tế/Chỉ tiêu)</th>
<th scope="col" style="text-align:center;">{$MOD.LBL_EXPECTED}</th>
<th scope="col" style="text-align:center;">{$MOD.LBL_ACTUAL}</th>
<th scope="col" style="text-align:center;">{$MOD.LBL_PERCENT_COMPLETED}<br/>(Thực tế/Dự kiến)</th>
<th scope="col" style="text-align:center;">{incomCRM_translate label='LBL_PLAN_SAME_LAST_MONTH' module='Accounts'}</th>
<th scope="col" style="text-align:center;">{incomCRM_translate label='LBL_PLAN_SAME_LAST_YEAR' module='Accounts'}</th>
</tr>
<tr class="oddListRowS1">
<td scope="row" align="right">{$MOD.LBL_TOTAL_AMOUNT}:</td>
<td align="center">
{if $opp.TargetRevenue && $opp.TargetRevenue > 0.00}
{incomCRM_currency_format var=$opp.TargetRevenue convert=false}
{else}
0
{/if}
</td>
<td align="center">{$opp.PercentTargetRevenue}%</td>
<td align="center">
{if $opp.ExpectedAmount && $opp.ExpectedAmount > 0.00}
{incomCRM_currency_format var=$opp.ExpectedAmount convert=false}
{else}
0
{/if}
</td>
<td align="center">
{if $opp.ActualAmount && $opp.ActualAmount > 0.00}
{incomCRM_currency_format var=$opp.ActualAmount convert=false}
{else}
0
{/if}
</td>
<td align="center">{$opp.PercentAmount}%</td>
<td align="center">
{if $opp.LM_ActualAmount && $opp.LM_ActualAmount > 0.00}
{incomCRM_currency_format var=$opp.LM_ActualAmount convert=false}
{else}
0
{/if}
</td>
<td align="center">
{if $opp.LY_ActualAmount && $opp.LY_ActualAmount > 0.00}
{incomCRM_currency_format var=$opp.LY_ActualAmount convert=false}
{else}
0
{/if}
</td>
</tr>
<tr class="evenListRowS1">
<td scope="row" align="right">{$MOD.LBL_TOTAL_OUTPUT}:</td>
<td align="center">{$opp.TargetOutput}</td>
<td align="center">{$opp.PercentTargetOutput}%</td>
<td align="center">{$opp.ExpectedOutput}</td>
<td align="center">{$opp.ActualOutput}</td>
<td align="center">{$opp.PercentOutput}%</td>
<td align="center">{$opp.LM_ActualOutput}</td>
<td align="center">{$opp.LY_ActualOutput}</td>
</tr>
</table>
<table width="100%" border="0" cellspacing="0" cellpadding="0" class="formHeader h3Row">
<tr>
<td valign="top">
<h3>
<a href="index.php?module={$module}&action=SalesLines&record={$fields.id.value}&to_pdf=1&is_ajax_call=1" class="tabFormAdvLink portal" id="sales_lines_today" style="font-size: 13px; color:#000;">
<img border="0" id="up_down_img" src="themes/default/images/advanced_search.gif">&nbsp;
{$MOD.LBL_SALES_LINES_TITLE} [{$date_line}]</a>
</h3>
</td>
</tr>
</table>
<div id="sales_lines_today_cont" style="display:none;"></div>
<table width="100%" border="0" cellspacing="0" cellpadding="0" class="formHeader h3Row">
<tr>
<td valign="top">
<h3>
<a href="#" class="tabFormAdvLink portal" id="search_detail_plan" style="font-size: 13px; color:#000;">
<img border="0" id="up_down_img" src="themes/default/images/advanced_search.gif">&nbsp;
{$MOD.LBL_SEARCH_DETAIL_PLAN}</a>
</h3>
</td>
</tr>
</table>
<div class="detail view" id="search_detail_plan_cont" style="display:none;">
<form name="formSearchSubpanel" id="formSearchSubpanel" method="get" action="index.php">
<input type="hidden" name="subpanel_search" value="accounts" />
<input type="hidden" name="subpanel_search_accounts" value="left" />
<table border="0" cellspacing="0" cellpadding="0" width="100%">
<tr>
<td style="padding-left:0px !important; text-align:center;" nowrap>
<input type="button" name="btnSearch" class="button search2" value=" {$APP.LBL_SEARCH_BUTTON_LABEL} " onclick="searchSubpanel( 'accounts', this.form );" />
</td>
<td scope="row" nowrap>{incomCRM_translate label='LBL_ACCOUNT_NAME' module='Cycle_Plans' trimColon=true}: </td>
<td>
<input type="text" name="search_name" value="{$smarty.request.search_name}" size="15" />
</td>
<td>
<select name="search_transaction_level[]" size="1" style="width:150px;">
{html_options options=$transaction_level_options selected=$smarty.request.search_transaction_level}
</select>
</td>
<td>
<select name="search_account_type[]" size="1" style="width:100px;">
{html_options options=$account_type_options selected=$smarty.request.search_account_type}
</select>
</td>
<td>
<select name="search_location_city[]" id="search_location_city" size="1" style="width:130px;" onchange="changeParentSelectedOption(this, this.form.search_location_district, 'location_district_dom');">
{html_options options=$location_cities_dom selected=$smarty.request.search_location_city}
</select>
</td>
<td>
<select name="search_location_district[]" id="search_location_district" size="1" style="width:150px;">
{html_options options=$location_district_dom selected=$smarty.request.search_location_district}
</select>
</td>
</tr>
<tr>
<td style="padding-left:0px !important; text-align:center;" nowrap>
<input type="reset" name="btnReset" class="button search2" value=" {$APP.LBL_CLEAR_BUTTON_LABEL} " />
</td>
<td scope="row" nowrap>{incomCRM_translate label='LBL_MEMBER_OF' module='Accounts' trimColon=true}: </td>
<td colspan="2" nowrap>
<input type="text" name="account_parent_name" id="account_parent_name" value="{$smarty.request.account_parent_name}" size="15" readonly="readonly" />
<input type="hidden" name="search_parent_id" id="search_parent_id" value="{$smarty.request.search_parent_id}" />
<input type="button" name="btn_account_parent_name" id="btn_account_parent_name" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("Accounts", 600, 400, "", true, false, {literal}{"call_back_function":"set_return","form_name":"formSearchSubpanel","field_to_name_array":{"id":"search_parent_id","name":"account_parent_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_account_parent_name" id="btn_clr_account_parent_name" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button" onclick='this.form.account_parent_name.value=""; this.form.search_parent_id.value="";' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
</td>
<td scope="row" nowrap>{incomCRM_translate label='LBL_ANY_PHONE' module='Accounts' trimColon=true}: </td>
<td>
<input type="text" name="search_phone" value="{$smarty.request.search_phone}" size="20" />
</td>
<td>
<select name="search_opportunity_sales_stage[]" size="1" style="width:150px;">
{html_options options=$sales_stage_dom selected=$smarty.request.search_opportunity_sales_stage}
</select>
</td>
</tr>
</table>
</form>
</div>
<div style="display:none;" id="config_list_subpanel_accounts">
<form name="formDisplaySubpanel" action="index.php?module={$module}&action=DetailView&record={$fields.id.value}" onsubmit="return changeSubpanelFields('accounts', this);">
<table border="0" cellspacing="0" cellpadding="2" width="100%" class="detail view" style="width:600px;">
<tr>
<td colspan="2" nowrap>
{$MOD.LBL_SALES_PLAN_DISPLAY_BY}:
&nbsp;
<select name="cycle_plans_display" size="1">
{html_options options=$cycle_plans_display_options selected=$cycle_plans_display}
</select>
</td>
</tr>
<tr>
<td nowrap>
<input type="checkbox" name="subpanel_fields_accounts[]" value="phone_office" id="subpanel_fields_phone_office" />
<label for="subpanel_fields_phone_office">{incomCRM_translate label='LBL_PHONE_OFFICE' module='Accounts' trimColon=true}</label>
</td>
<td nowrap>
<input type="checkbox" name="subpanel_fields_accounts[]" value="email1" id="subpanel_fields_email1" />
<label for="subpanel_fields_email1">{incomCRM_translate label='LBL_EMAIL1' module='Accounts' trimColon=true}</label>
</td>
</tr>
<tr>
<td nowrap>
<input type="checkbox" name="subpanel_fields_accounts[]" value="primary_contact_email" id="subpanel_fields_primary_contact_email" />
<label for="subpanel_fields_primary_contact_email">{incomCRM_translate label='LBL_PRIMARY_CONTACT_EMAIL' module='Accounts' trimColon=true}</label>
</td>
<td nowrap>
<input type="checkbox" name="subpanel_fields_accounts[]" value="secondary_contact_email" id="subpanel_fields_secondary_contact_email" />
<label for="subpanel_fields_secondary_contact_email">{incomCRM_translate label='LBL_SECONDARY_CONTACT_EMAIL' module='Accounts' trimColon=true}</label>
</td>
</tr>
<tr>
<td nowrap>
<input type="checkbox" name="subpanel_fields_accounts[]" value="primary_contact_phone_work" id="subpanel_fields_primary_contact_phone_work" />
<label for="subpanel_fields_primary_contact_phone_work">{incomCRM_translate label='LBL_PRIMARY_CONTACT_PHONE_WORK' module='Accounts' trimColon=true}</label>
</td>
<td nowrap>
<input type="checkbox" name="subpanel_fields_accounts[]" value="secondary_contact_phone_work" id="subpanel_fields_secondary_contact_phone_work" />
<label for="subpanel_fields_secondary_contact_phone_work">{incomCRM_translate label='LBL_SECONDARY_CONTACT_PHONE_WORK' module='Accounts' trimColon=true}</label>
</td>
</tr>
<tr>
<td nowrap>
<input type="checkbox" name="subpanel_fields_accounts[]" value="primary_contact_phone_mobile" id="subpanel_fields_primary_contact_phone_mobile" />
<label for="subpanel_fields_primary_contact_phone_mobile">{incomCRM_translate label='LBL_PRIMARY_CONTACT_PHONE_MOBILE' module='Accounts' trimColon=true}</label>
</td>
<td nowrap>
<input type="checkbox" name="subpanel_fields_accounts[]" value="secondary_contact_phone_mobile" id="subpanel_fields_secondary_contact_phone_mobile" />
<label for="subpanel_fields_secondary_contact_phone_mobile">{incomCRM_translate label='LBL_SECONDARY_CONTACT_PHONE_MOBILE' module='Accounts' trimColon=true}</label>
</td>
</tr>
</table>
<div style="padding:2px 0;">
<input type="submit" name="btnShow" class="button save" value=" {$APP.LBL_SAVE_BUTTON_LABEL} " title="{$APP.LBL_SAVE_BUTTON_TITLE}" accessKey="{$APP.LBL_SAVE_BUTTON_KEY}" />
</div>
</form>
</div>
</div>