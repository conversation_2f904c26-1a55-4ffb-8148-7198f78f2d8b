<?php
// created: 2025-02-26 16:10:46
$GLOBALS["dictionary"]["Currency"] = array (
  'table' => 'currencies',
  'comment' => 'Currencies allow incomCRM to store and display monetary values in various denominations',
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_NAME',
      'type' => 'id',
      'required' => true,
      'reportable' => false,
      'comment' => 'Unique identifer',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_LIST_NAME',
      'type' => 'varchar',
      'len' => '50',
      'required' => true,
      'comment' => 'Name of the currency',
      'importable' => 'required',
    ),
    'symbol' => 
    array (
      'name' => 'symbol',
      'vname' => 'LBL_LIST_SYMBOL',
      'type' => 'varchar',
      'len' => '20',
      'required' => true,
      'comment' => 'Symbol representing the currency',
      'importable' => 'required',
    ),
    'iso4217' => 
    array (
      'name' => 'iso4217',
      'vname' => 'LBL_LIST_ISO4217',
      'type' => 'varchar',
      'len' => '3',
      'required' => true,
      'comment' => '3-letter identifier specified by ISO 4217 (ex: USD)',
      'importable' => 'required',
    ),
    'conversion_rate' => 
    array (
      'name' => 'conversion_rate',
      'vname' => 'LBL_LIST_RATE',
      'type' => 'float',
      'dbType' => 'double',
      'default' => '0',
      'required' => true,
      'comment' => 'Conversion rate factor (relative to stored value)',
      'importable' => 'required',
    ),
    'status' => 
    array (
      'name' => 'status',
      'vname' => 'LBL_STATUS',
      'type' => 'enum',
      'dbType' => 'varchar',
      'options' => 'currency_status_dom',
      'len' => '25',
      'comment' => 'Currency status',
      'importable' => 'required',
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'required' => true,
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'required' => true,
      'comment' => 'Date record created',
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'required' => true,
      'comment' => 'Date record last modified',
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'reportable' => false,
      'vname' => 'LBL_CREATED_BY',
      'type' => 'id',
      'len' => '36',
      'required' => true,
      'comment' => 'User ID who created record',
    ),
    'text_read' => 
    array (
      'name' => 'text_read',
      'vname' => 'LBL_TEXT_READ',
      'type' => 'varchar',
      'len' => '150',
      'comment' => 'Read the currency with text',
    ),
  ),
  'indices' => 
  array (
    0 => 
    array (
      'name' => 'currenciespk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_currency_name',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'name',
        1 => 'deleted',
      ),
    ),
  ),
  'custom_fields' => false,
);
?>
