<?php
// created: 2025-02-27 14:59:04
$<PERSON><PERSON><PERSON><PERSON><PERSON>["dictionary"]["Product_Promotion"] = array (
  'table' => 'product_promotions',
  'audited' => true,
  'unified_search' => false,
  'duplicate_merge' => false,
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'varchar',
      'len' => '200',
      'acl' => true,
      'audited' => true,
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
      'comment' => 'Full text of the note',
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'product_promotions_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'product_promotions_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'branch_id' => 
    array (
      'name' => 'branch_id',
      'vname' => 'LBL_BRANCH_ID',
      'type' => 'enum',
      'options' => 'users_branch_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'BranchID of assigned user',
    ),
    'department_id' => 
    array (
      'name' => 'department_id',
      'vname' => 'LBL_DEPARTMENT_ID',
      'type' => 'enum',
      'options' => 'users_department_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'DepartmentID of assigned user',
    ),
    'assigned_user_id' => 
    array (
      'name' => 'assigned_user_id',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'vname' => 'LBL_ASSIGNED_TO_ID',
      'group' => 'assigned_user_name',
      'type' => 'relate',
      'table' => 'users',
      'module' => 'Users',
      'isnull' => 'false',
      'dbType' => 'id',
      'audited' => true,
      'reportable' => true,
      'duplicate_merge' => 'disabled',
      'comment' => 'User ID assigned to record',
    ),
    'assigned_user_name' => 
    array (
      'name' => 'assigned_user_name',
      'link' => 'assigned_user_link',
      'vname' => 'LBL_ASSIGNED_TO_NAME',
      'rname' => 'user_name',
      'type' => 'relate',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'assigned_user_id',
      'module' => 'Users',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'assigned_user_link' => 
    array (
      'name' => 'assigned_user_link',
      'type' => 'link',
      'relationship' => 'product_promotions_assigned_user',
      'vname' => 'LBL_ASSIGNED_TO_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
      'duplicate_merge' => 'enabled',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'table' => 'users',
    ),
    'code' => 
    array (
      'name' => 'code',
      'vname' => 'LBL_CODE',
      'type' => 'varchar',
      'len' => '30',
    ),
    'status' => 
    array (
      'name' => 'status',
      'vname' => 'LBL_STATUS',
      'type' => 'enum',
      'options' => 'product_promotion_status_dom',
      'len' => '1',
      'dbType' => 'char',
      'audited' => true,
      'massupdate' => true,
      'display_default' => '1',
    ),
    'promotion_type' => 
    array (
      'name' => 'promotion_type',
      'vname' => 'LBL_PROMOTION_TYPE',
      'type' => 'enum',
      'options' => 'product_promotion_type_dom',
      'len' => '1',
      'dbType' => 'char',
      'audited' => true,
      'massupdate' => false,
      'default' => '1',
    ),
    'apply_all' => 
    array (
      'name' => 'apply_all',
      'vname' => 'LBL_APPLY_ALL',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => false,
      'default' => '0',
    ),
    'date_valid_from' => 
    array (
      'name' => 'date_valid_from',
      'vname' => 'LBL_DATE_VALID_FROM',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'date_valid_to' => 
    array (
      'name' => 'date_valid_to',
      'vname' => 'LBL_DATE_VALID_TO',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'all_discount' => 
    array (
      'name' => 'all_discount',
      'vname' => 'LBL_ALL_DISCOUNT',
      'type' => 'double',
      'audited' => true,
      'display_default' => '0',
    ),
    'branch_all' => 
    array (
      'name' => 'branch_all',
      'vname' => 'LBL_BRANCH_ALL',
      'type' => 'enum',
      'options' => 'branch_all_dom',
      'dbType' => 'text',
      'isMultiSelect' => true,
      'isCheckBoxList' => true,
      'audited' => true,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
    ),
    'department_all' => 
    array (
      'name' => 'department_all',
      'vname' => 'LBL_DEPARTMENT_ALL',
      'type' => 'enum',
      'options' => 'department_all_dom',
      'dbType' => 'text',
      'isMultiSelect' => true,
      'isCheckBoxList' => true,
      'audited' => true,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
    ),
    'membership_all' => 
    array (
      'name' => 'membership_all',
      'vname' => 'LBL_MEMBERSHIP_ALL',
      'type' => 'enum',
      'options' => 'membership_card_dom',
      'dbType' => 'varchar',
      'len' => 250,
      'isMultiSelect' => true,
      'isCheckBoxList' => true,
      'audited' => true,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
    ),
    'tqt_products' => 
    array (
      'name' => 'tqt_products',
      'type' => 'link',
      'relationship' => 'product_promotions_discounts',
      'vname' => 'LBL_TQT_PRODUCTS',
      'source' => 'non-db',
    ),
    'discount' => 
    array (
      'name' => 'discount',
      'vname' => 'LBL_ALL_DISCOUNT',
      'type' => 'float',
      'source' => 'non-db',
    ),
    'SecurityGroups' => 
    array (
      'name' => 'SecurityGroups',
      'type' => 'link',
      'relationship' => 'securitygroups_product_promotions',
      'module' => 'SecurityGroups',
      'bean_name' => 'SecurityGroup',
      'source' => 'non-db',
      'vname' => 'LBL_SECURITYGROUPS',
    ),
  ),
  'indices' => 
  array (
    'id' => 
    array (
      'name' => 'product_promotionspk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    'idx_product_promotions_branch_id' => 
    array (
      'name' => 'idx_product_promotions_branch_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
      ),
    ),
    'idx_product_promotions_department_id' => 
    array (
      'name' => 'idx_product_promotions_department_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'department_id',
      ),
    ),
    'idx_product_promotions_branch_dept' => 
    array (
      'name' => 'idx_product_promotions_branch_dept',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
        1 => 'department_id',
      ),
    ),
    'idx_product_promotions_assigned' => 
    array (
      'name' => 'idx_product_promotions_assigned',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'assigned_user_id',
      ),
    ),
    0 => 
    array (
      'name' => 'idx_prod_prom_id_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'id',
        1 => 'deleted',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_prod_prom_status',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'status',
      ),
    ),
    2 => 
    array (
      'name' => 'idx_prod_prom_apply_all',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'apply_all',
      ),
    ),
    3 => 
    array (
      'name' => 'idx_prod_prom_type_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'promotion_type',
      ),
    ),
    4 => 
    array (
      'name' => 'idx_prod_prom_assigned_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'assigned_user_id',
      ),
    ),
  ),
  'relationships' => 
  array (
    'product_promotions_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Product_Promotions',
      'rhs_table' => 'product_promotions',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'product_promotions_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Product_Promotions',
      'rhs_table' => 'product_promotions',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
    'product_promotions_assigned_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Product_Promotions',
      'rhs_table' => 'product_promotions',
      'rhs_key' => 'assigned_user_id',
      'relationship_type' => 'one-to-many',
    ),
  ),
  'optimistic_lock' => true,
  'templates' => 
  array (
    'assignable' => 'assignable',
    'groups' => 'groups',
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
