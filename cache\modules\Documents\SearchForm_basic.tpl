

{math equation="floor(left / right)"
	left=12
		right=$templateMeta.maxColumns
			assign=colClass
	}

<section class="container2 search-form ml-0 search_form_basic">
	<div class="row">
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12">
			<div class="row">
				<label class="col-5 pr-0" for="document_name_basic">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_NAME' module='Documents'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.document_name_basic.value) <= 0}
	{assign var="value" value=$fields.document_name_basic.default_value }
{else}
	{assign var="value" value=$fields.document_name_basic.value }
{/if}
{if isTypeNumber($fields.document_name_basic.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.document_name_basic.name}' id='{$fields.document_name_basic.name}' size='30' maxlength='255' value='{$value}' title='' tabindex=''  /> 
									</div>
			</div>
		</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12">
			<div class="row">
				<label class="col-5 pr-0" for="no_dossier_basic">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_NO_DOSSIER' module='Documents'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.no_dossier_basic.value) <= 0}
	{assign var="value" value=$fields.no_dossier_basic.default_value }
{else}
	{assign var="value" value=$fields.no_dossier_basic.value }
{/if}
{if isTypeNumber($fields.no_dossier_basic.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.no_dossier_basic.name}' id='{$fields.no_dossier_basic.name}' size='30' maxlength='20' value='{$value}' title='' tabindex=''  /> 
									</div>
			</div>
		</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12">
			<div class="row">
				<label class="col-5 pr-0" for="template_type_basic">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_TEMPLATE_TYPE' module='Documents'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="template_type_basic[]" id="{$fields.template_type_basic.name}" size="1"   >
{html_options options=$fields.template_type_basic.options selected=$fields.template_type_basic.value}
</select>
									</div>
			</div>
		</div>
	</div>
</section>

{if $HAS_ADVANCED_SEARCH}
<table cellspacing="0" cellpadding="0" border="0" class="mt-10">
<tr>
	<td scope="row" nowrap="nowrap" class="pt-10">
	<a style="font-size:10pt; text-decoration:none;" onclick="incomCRM.searchForm.searchFormSelect('{$module}|advanced_search','{$module}|basic_search')" href="#">[ {$APP.LNK_ADVANCED_SEARCH} ]</a>
	</td>
</tr>
</table>
{/if}

{literal}<script type="text/javascript">if(typeof sqs_objects == 'undefined'){var sqs_objects = new Array;}
sqs_objects['search_form_modified_by_name_basic'] = {"form":"search_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["modified_by_name_basic","modified_user_id_basic"],"required_list":["modified_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['search_form_created_by_name_basic'] = {"form":"search_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["created_by_name_basic","created_by_basic"],"required_list":["created_by"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['search_form_assigned_user_name_basic'] = {"form":"search_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["assigned_user_name_basic","assigned_user_id_basic"],"required_list":["assigned_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['search_form_related_doc_name_basic'] = {"form":"search_form","method":"query","modules":["Documents"],"group":"or","field_list":["name","id"],"populate_list":["related_doc_name_basic","related_doc_id_basic"],"required_list":["parent_id"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
</script>{/literal}