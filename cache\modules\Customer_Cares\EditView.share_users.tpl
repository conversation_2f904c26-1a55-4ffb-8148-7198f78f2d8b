<input type="hidden" id="{$fields.share_users.name}_multiselect" name="{$fields.share_users.name}_multiselect" value="true" />
{multienum_to_array string=$fields.share_users.value default=$fields.share_users.default assign="values"}
<div style="height:110px;" id="{$fields.share_users.name}_multi_grp" class="multi-enum-grp multi-enum-grp-default">
{if isset($fields.share_users.groupBy) and isset($fields.share_users.groupDom)}
		<label id="_grp_choose">
			<select name="_{$fields.share_users.name}_grp" size="1" onchange="changeFilterSelectedGroup(this.value, '{$fields.share_users.name}_multi_grp' )">
				{html_options options=$fields.share_users.groupDom}
			</select>
		</label>
{/if}
		<label id="_grp_all_" class="_grp_all_">
			<input type="checkbox" value="__all__" onclick="checkedChangeSel(this, '{$fields.share_users.name}', true)" />
			-- {$APP.LBL_LINK_ALL} --
		</label>
{counter name="rowCount" start=0 print=false assign="rowCount"}
{foreach from=$fields.share_users.options item=option key=value}
	{if $value}
		<label id="_grp_{if isset($fields.share_users.groupBy) and isset($fields.share_users.groupBy[$value])}{ $fields.share_users.groupBy[$value]}{/if}" style="white-space: nowrap">
			<input type="checkbox" id="{$fields.share_users.name}_checkbox{$rowCount}" name="{$fields.share_users.name}[]" value="{$value}" {if in_array(strval($value), $values)}checked="checked"{/if}  /> {$option}
		</label>
		{counter name="rowCount"}
	{/if}
{/foreach}
</div>
