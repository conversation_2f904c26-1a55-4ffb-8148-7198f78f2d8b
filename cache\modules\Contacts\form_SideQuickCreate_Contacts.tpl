
<section id="EditViewViewBody" class="view-module-{$module|strtolower}">

<form action="index.php" method="POST" name="{$form_name}" id="{$form_id}" class="_edit-{$instanceName}" {$enctype}>
<input type="hidden" name="module" value="{$module}" />
<input type="hidden" name="action" id="action" />
{if isset($smarty.request.isDuplicate) && $smarty.request.isDuplicate eq "true"}
<input type="hidden" name="record" id="record" value="" />
<input type="hidden" name="duplicateSave" value="true" />
{else}
<input type="hidden" name="record" id="record" value="{$fields.id.value}" />
{/if}
<input type="hidden" name="return_module" value="{$smarty.request.return_module}" />
<input type="hidden" name="return_action" value="{if $smarty.request.return_action eq 'LoadTabSubpanels'}DetailView{else}{$smarty.request.return_action}{/if}" />
<input type="hidden" name="return_id" value="{$smarty.request.return_id}" />
<input type="hidden" name="isDuplicate" value="false" />
<input type="hidden" name="module_tab" /> 
<input type="hidden" name="contact_role" />
{if !empty($smarty.request.return_module)}
<input type="hidden" name="relate_to" value="{if $smarty.request.return_relationship}{$smarty.request.return_relationship}{else}{$smarty.request.return_module}{/if}" />
<input type="hidden" name="relate_id" value="{$smarty.request.return_id}" />
{/if}
<input type="hidden" name="offset" value="{$offset}" />
{if empty($fields.id.value)}
<input type="hidden" name="_notify_random" value="{php}echo create_guid();{/php}" />
{/if}
<input type="hidden" name="isFromEditView" value="{$module}" />
<input type="hidden" name="opportunity_id" value="{$smarty.request.opportunity_id}">
<input type="hidden" name="case_id" value="{$smarty.request.case_id}">
<input type="hidden" name="bug_id" value="{$smarty.request.bug_id}">
<input type="hidden" name="email_id" value="{$smarty.request.email_id}">
<input type="hidden" name="inbound_email_id" value="{$smarty.request.inbound_email_id}">
{incomCRM_include include=$includes}
<div id="DEFAULT" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="DEFAULT_GROUP" style="display:block">
<table width="100%" border="0" cellspacing="1" cellpadding="0">
<tr>
<td valign="top" width='75%' id='_last_name_field' >
<div class="labelsOnTop _last_name_label">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_LAST_NAME' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</div>
{counter name="panelFieldCount"}

{if strlen($fields.last_name.value) <= 0}
{assign var="value" value=$fields.last_name.default_value }
{else}
{assign var="value" value=$fields.last_name.value }
{/if}
{if isTypeNumber($fields.last_name.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.last_name.name}' id='{$fields.last_name.name}' size='25' maxlength='100' value='{$value}' title='' tabindex='100'  /> 

</td>
</tr>
<tr>
<td valign="top" width='75%' id='_phone_work_field' >
<div class="labelsOnTop _phone_work_label">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_OFFICE_PHONE' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</div>
{counter name="panelFieldCount"}

{if strlen($fields.phone_work.value) <= 0}
{assign var="value" value=$fields.phone_work.default_value }
{else}
{assign var="value" value=$fields.phone_work.value }
{/if}
{if isTypeNumber($fields.phone_work.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.phone_work.name}' id='{$fields.phone_work.name}' size='25' maxlength='100' value='{$value}' title='' tabindex='101'  /> 

</td>
</tr>
<tr>
<td valign="top" width='75%' id='_email1_field' >
<div class="labelsOnTop _email1_label">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_EMAIL_ADDRESS' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</div>
{counter name="panelFieldCount"}

{$fields.email1.value}
</td>
</tr>
<tr>
<td valign="top" width='75%' id='_account_name_field' >
<div class="labelsOnTop _account_name_label">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACCOUNT_NAME' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</div>
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{/if}<input type="hidden" name="{$fields.account_name.id_name}" id="{$fields.account_name.id_name}" value="{$fields.account_id.value}" />
<input type="text" name="{$fields.account_name.name}" class="sqsEnabled" tabindex="103" id="{$fields.account_name.name}" size="16" value="{$fields.account_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{/if}
<input type="button" name="btn_{$fields.account_name.name}" id="btn_{$fields.account_name.name}" tabindex="103" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.account_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"form_SideQuickCreate_Contacts","field_to_name_array":{"id":"account_id","name":"account_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.account_name.name}" id="btn_clr_{$fields.account_name.name}" tabindex="103" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.account_name.name}.value=""; this.form.{$fields.account_name.id_name}.value=""; this.form.{$fields.account_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
{if !empty($isSmartView)}</div>{/if}<script type="text/javascript">enableQS(false);</script>
</td>
</tr>
<tr>
<td valign="top" width='75%' id='_assigned_user_name_field' >
<div class="labelsOnTop _assigned_user_name_label">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</div>
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{/if}<input type="hidden" name="{$fields.assigned_user_name.id_name}" id="{$fields.assigned_user_name.id_name}" value="{$fields.assigned_user_id.value}" />
<input type="text" name="{$fields.assigned_user_name.name}" class="sqsEnabled" tabindex="104" id="{$fields.assigned_user_name.name}" size="11" value="{$fields.assigned_user_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{/if}
<input type="button" name="btn_{$fields.assigned_user_name.name}" id="btn_{$fields.assigned_user_name.name}" tabindex="104" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.assigned_user_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"form_SideQuickCreate_Contacts","field_to_name_array":{"id":"assigned_user_id","user_name":"assigned_user_name"}}{/literal}, "single", true);'  />
{if !empty($isSmartView)}</div>{/if}<script type="text/javascript">enableQS(false);</script>
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("DEFAULT").style.display='none';</script>
{/if}

<div class="buttons" style="margin-top:15px;">
{if $bean->aclAccess("save")}<input title="{$APP.LBL_SAVE_BUTTON_TITLE}" accessKey="{$APP.LBL_SAVE_BUTTON_KEY}" class="button save" onclick="{if $isDuplicate}this.form.return_id.value=''; {/if}this.form.action.value='Save'; return check_form('form_SideQuickCreate_Contacts');" type="submit" name="button" value="{$APP.LBL_SAVE_BUTTON_LABEL}" />{/if} 
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=Contacts", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</div>
</form>
{$set_focus_block}
</section>{literal}
<script type="text/javascript">
addToValidate('form_SideQuickCreate_Contacts', 'id', 'id', false, '{/literal}{incomCRM_translate label='LBL_ID' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'date_entered_date', 'date', false, 'Ngày nhập' );
addToValidate('form_SideQuickCreate_Contacts', 'date_modified_date', 'date', false, 'Ngày thay đổi' );
addToValidate('form_SideQuickCreate_Contacts', 'modified_user_id', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_ID' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'modified_by_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_NAME' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'created_by', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_CREATED_ID' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'created_by_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_CREATED' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'deleted', 'bool', false, '{/literal}{incomCRM_translate label='LBL_DELETED' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'name', 'name', false, '{/literal}{incomCRM_translate label='LBL_NAME' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'description', 'text', false, '{/literal}{incomCRM_translate label='LBL_DESCRIPTION' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'branch_id', 'enum', false, '{/literal}{incomCRM_translate label='LBL_BRANCH_ID' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'department_id', 'enum', false, '{/literal}{incomCRM_translate label='LBL_DEPARTMENT_ID' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'assigned_user_id', 'relate', false, '{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO_ID' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'assigned_user_name', 'relate', true, '{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'salutation', 'enum', false, '{/literal}{incomCRM_translate label='LBL_SALUTATION' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'first_name', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_FIRST_NAME' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'last_name', 'varchar', true, '{/literal}{incomCRM_translate label='LBL_LAST_NAME' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'full_name', 'fullname', false, '{/literal}{incomCRM_translate label='LBL_NAME' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'title', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TITLE' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'department', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_DEPARTMENT' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'do_not_call', 'bool', false, '{/literal}{incomCRM_translate label='LBL_DO_NOT_CALL' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'phone_home', 'phone', false, '{/literal}{incomCRM_translate label='LBL_HOME_PHONE' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'phone_mobile', 'phone', false, '{/literal}{incomCRM_translate label='LBL_MOBILE_PHONE' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'phone_work', 'phone', false, '{/literal}{incomCRM_translate label='LBL_OFFICE_PHONE' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'phone_other', 'phone', false, '{/literal}{incomCRM_translate label='LBL_OTHER_PHONE' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'phone_fax', 'phone', false, '{/literal}{incomCRM_translate label='LBL_FAX_PHONE' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'email1', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_EMAIL_ADDRESS' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'email2', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_OTHER_EMAIL_ADDRESS' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'invalid_email', 'bool', false, '{/literal}{incomCRM_translate label='LBL_INVALID_EMAIL' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'email_opt_out', 'bool', false, '{/literal}{incomCRM_translate label='LBL_EMAIL_OPT_OUT' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'primary_address_street', 'text', false, '{/literal}{incomCRM_translate label='LBL_PRIMARY_ADDRESS_STREET' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'primary_address_street_2', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_PRIMARY_ADDRESS_STREET_2' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'primary_address_street_3', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_PRIMARY_ADDRESS_STREET_3' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'primary_address_city', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_PRIMARY_ADDRESS_CITY' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'primary_address_state', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_PRIMARY_ADDRESS_STATE' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'primary_address_postalcode', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_PRIMARY_ADDRESS_POSTALCODE' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'primary_address_country', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_PRIMARY_ADDRESS_COUNTRY' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'alt_address_street', 'text', false, '{/literal}{incomCRM_translate label='LBL_ALT_ADDRESS_STREET' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'alt_address_street_2', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ALT_ADDRESS_STREET_2' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'alt_address_street_3', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ALT_ADDRESS_STREET_3' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'alt_address_city', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ALT_ADDRESS_CITY' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'alt_address_state', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ALT_ADDRESS_STATE' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'alt_address_postalcode', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ALT_ADDRESS_POSTALCODE' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'alt_address_country', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ALT_ADDRESS_COUNTRY' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'assistant', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ASSISTANT' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'assistant_phone', 'phone', false, '{/literal}{incomCRM_translate label='LBL_ASSISTANT_PHONE' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'gender', 'enum', false, '{/literal}{incomCRM_translate label='LBL_GENDER' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'mail_status', 'enum', false, '{/literal}{incomCRM_translate label='LBL_MAIL_STATUS' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'birthdate', 'date', false, '{/literal}{incomCRM_translate label='LBL_BIRTHDATE' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'email_and_name1', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_NAME' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'lead_source', 'enum', false, '{/literal}{incomCRM_translate label='LBL_LEAD_SOURCE' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'account_id', 'relate', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_ID' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'account_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_NAME' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'opportunity_role_fields', 'relate', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_NAME' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'opportunity_role_id', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_OPPORTUNITY_ROLE_ID' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'opportunity_role', 'enum', false, '{/literal}{incomCRM_translate label='LBL_OPPORTUNITY_ROLE' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'reports_to_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_REPORTS_TO_ID' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'report_to_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_REPORTS_TO' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'campaign_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_CAMPAIGN_ID' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'campaign_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_CAMPAIGN' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'c_accept_status_fields', 'relate', false, '{/literal}{incomCRM_translate label='LBL_LIST_ACCEPT_STATUS' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'm_accept_status_fields', 'relate', false, '{/literal}{incomCRM_translate label='LBL_LIST_ACCEPT_STATUS' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'accept_status_id', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_LIST_ACCEPT_STATUS' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'accept_status_name', 'enum', false, '{/literal}{incomCRM_translate label='LBL_LIST_ACCEPT_STATUS' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'sync_contact', 'bool', false, '{/literal}{incomCRM_translate label='LBL_SYNC_CONTACT' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'is_locked', 'bool', false, '{/literal}{incomCRM_translate label='LBL_IS_LOCKED' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'position', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_POSITION' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'specific_info', 'text', false, '{/literal}{incomCRM_translate label='LBL_SPECIFIC_INFO' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'reject_email', 'bool', false, '{/literal}{incomCRM_translate label='LBL_REJECT_EMAIL' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'notification', 'bool', false, '{/literal}{incomCRM_translate label='LBL_NOTIFICATION' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'team', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TEAM' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'reference', 'bool', false, '{/literal}{incomCRM_translate label='LBL_REFERENCE' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'transaction_level', 'enum', false, '{/literal}{incomCRM_translate label='LBL_TRANSACTION_LEVEL' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'transaction_comment', 'text', false, '{/literal}{incomCRM_translate label='LBL_TRANSACTION_COMMENT' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'commission_recipient', 'bool', false, '{/literal}{incomCRM_translate label='LBL_COMMISSION_RECIPIENT' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'account_holder', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_HOLDER' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'id_number', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ID_NUMBER' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'passport_number', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_PASSPORT_NUMBER' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'account_number', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_NUMBER' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'account_bank_name', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_BANK_NAME' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'hobbies', 'text', false, '{/literal}{incomCRM_translate label='LBL_HOBBIES' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'talent', 'text', false, '{/literal}{incomCRM_translate label='LBL_TALENT' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'sport', 'text', false, '{/literal}{incomCRM_translate label='LBL_SPORT' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'intimate_level', 'enum', false, '{/literal}{incomCRM_translate label='LBL_INTIMATE_LEVEL' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'intimate_note', 'text', false, '{/literal}{incomCRM_translate label='LBL_INTIMATE_NOTE' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'work_history', 'text', false, '{/literal}{incomCRM_translate label='LBL_WORK_HISTORY' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'independence_day', 'date', false, '{/literal}{incomCRM_translate label='LBL_INDEPENDENCE_DAY' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'foreigner_rep', 'bool', false, '{/literal}{incomCRM_translate label='LBL_FOREIGNER_REP' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'need_care', 'bool', false, '{/literal}{incomCRM_translate label='LBL_NEED_CARE' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'care_priority', 'enum', false, '{/literal}{incomCRM_translate label='LBL_CARE_PRIORITY' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'care_suggest', 'text', false, '{/literal}{incomCRM_translate label='LBL_CARE_SUGGEST' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'contact_position', 'enum', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_POSITION' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'import_mark', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_IMPORT_MARK' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'reference_code', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_REFERENCE_CODE' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'account_branch', 'enum', false, '{/literal}{incomCRM_translate label='Chi nhánh KH' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'account_department', 'enum', false, '{/literal}{incomCRM_translate label='Phòng ban KH' module='Contacts'}{literal}' );
addToValidate('form_SideQuickCreate_Contacts', 'account_user_id', 'id', false, '{/literal}{incomCRM_translate label='Nv.Qlý KH' module='Contacts'}{literal}' );
addToValidateBinaryDependency('form_SideQuickCreate_Contacts', 'assigned_user_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Contacts'}{literal}{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO' module='Contacts'}{literal}', 'assigned_user_id' );
addToValidateBinaryDependency('form_SideQuickCreate_Contacts', 'account_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Contacts'}{literal}{/literal}{incomCRM_translate label='LBL_ACCOUNT_NAME' module='Contacts'}{literal}', 'account_id' );
</script>
<script type="text/javascript">if(typeof sqs_objects == 'undefined'){var sqs_objects = new Array;}
sqs_objects['form_SideQuickCreate_Contacts_account_name'] = {"form":"form_SideQuickCreate_Contacts","method":"query","modules":["Accounts"],"group":"or","field_list":["name","id"],"populate_list":["form_SideQuickCreate_Contacts_account_name","account_id"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"required_list":["account_id"],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['form_SideQuickCreate_Contacts_assigned_user_name'] = {"form":"form_SideQuickCreate_Contacts","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["assigned_user_name","assigned_user_id"],"required_list":["assigned_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
</script>{/literal}
