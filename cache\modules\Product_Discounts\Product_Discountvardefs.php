<?php
// created: 2025-02-26 16:10:47
$<PERSON><PERSON><PERSON><PERSON><PERSON>["dictionary"]["Product_Discount"] = array (
  'table' => 'tqt_products',
  'audited' => true,
  'unified_search' => false,
  'duplicate_merge' => false,
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'type' => 'name',
      'dbType' => 'varchar',
      'vname' => 'LBL_NAME',
      'len' => 250,
      'acl' => true,
      'audited' => true,
      'unified_search' => true,
      'importable' => 'required',
      'merge_filter' => 'selected',
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
      'comment' => 'Full text of the note',
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'tqt_products_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'tqt_products_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'branch_id' => 
    array (
      'name' => 'branch_id',
      'vname' => 'LBL_BRANCH_ID',
      'type' => 'enum',
      'options' => 'users_branch_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'BranchID of assigned user',
    ),
    'department_id' => 
    array (
      'name' => 'department_id',
      'vname' => 'LBL_DEPARTMENT_ID',
      'type' => 'enum',
      'options' => 'users_department_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'DepartmentID of assigned user',
    ),
    'assigned_user_id' => 
    array (
      'name' => 'assigned_user_id',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'vname' => 'LBL_ASSIGNED_TO_ID',
      'group' => 'assigned_user_name',
      'type' => 'relate',
      'table' => 'users',
      'module' => 'Users',
      'isnull' => 'false',
      'dbType' => 'id',
      'audited' => true,
      'reportable' => true,
      'duplicate_merge' => 'disabled',
      'comment' => 'User ID assigned to record',
    ),
    'assigned_user_name' => 
    array (
      'name' => 'assigned_user_name',
      'link' => 'assigned_user_link',
      'vname' => 'LBL_ASSIGNED_TO_NAME',
      'rname' => 'user_name',
      'type' => 'relate',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'assigned_user_id',
      'module' => 'Users',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'assigned_user_link' => 
    array (
      'name' => 'assigned_user_link',
      'type' => 'link',
      'relationship' => 'tqt_products_assigned_user',
      'vname' => 'LBL_ASSIGNED_TO_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
      'duplicate_merge' => 'enabled',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'table' => 'users',
    ),
    'product_code' => 
    array (
      'name' => 'product_code',
      'vname' => 'LBL_PRODUCT_CODE',
      'type' => 'varchar',
      'len' => 30,
      'audited' => true,
      'unified_search' => true,
      'duplicate_merge' => false,
    ),
    'code' => 
    array (
      'name' => 'code',
      'vname' => 'LBL_CODE',
      'type' => 'varchar',
      'len' => 50,
      'audited' => true,
      'unified_search' => true,
    ),
    'reference_code' => 
    array (
      'name' => 'reference_code',
      'vname' => 'LBL_REFERENCE_CODE',
      'type' => 'varchar',
      'len' => 50,
      'audited' => true,
    ),
    'status' => 
    array (
      'name' => 'status',
      'vname' => 'LBL_STATUS',
      'type' => 'enum',
      'options' => 'products_status_dom',
      'len' => 10,
      'audited' => true,
      'default' => 'Active',
      'massupdate' => false,
    ),
    'price_orig' => 
    array (
      'name' => 'price_orig',
      'vname' => 'LBL_PRICE_ORIG',
      'type' => 'double',
      'audited' => true,
      'duplicate_merge' => false,
    ),
    'price_purchase' => 
    array (
      'name' => 'price_purchase',
      'vname' => 'LBL_PRICE_PURCHASE',
      'type' => 'double',
      'audited' => true,
      'duplicate_merge' => false,
    ),
    'price' => 
    array (
      'name' => 'price',
      'vname' => 'LBL_PRICE',
      'type' => 'double',
      'audited' => true,
    ),
    'price_c1' => 
    array (
      'name' => 'price_c1',
      'vname' => 'LBL_PRICE_C1',
      'type' => 'double',
      'audited' => true,
    ),
    'price_c2' => 
    array (
      'name' => 'price_c2',
      'vname' => 'LBL_PRICE_C2',
      'type' => 'double',
      'audited' => true,
    ),
    'price_c3' => 
    array (
      'name' => 'price_c3',
      'vname' => 'LBL_PRICE_C3',
      'type' => 'double',
      'audited' => true,
    ),
    'unit' => 
    array (
      'name' => 'unit',
      'vname' => 'LBL_UNIT',
      'type' => 'varchar',
      'len' => 30,
      'audited' => true,
      'massupdate' => false,
    ),
    'product_type' => 
    array (
      'name' => 'product_type',
      'vname' => 'LBL_PRODUCT_TYPE',
      'type' => 'enum',
      'options' => 'products_type_dom',
      'len' => 2,
      'audited' => true,
      'massupdate' => false,
      'duplicate_merge' => false,
      'display_default' => '0',
    ),
    'made_in' => 
    array (
      'name' => 'made_in',
      'vname' => 'LBL_MADE_IN',
      'type' => 'varchar',
      'len' => 50,
      'audited' => true,
      'massupdate' => false,
    ),
    'model' => 
    array (
      'name' => 'model',
      'vname' => 'LBL_MODEL',
      'type' => 'bool',
      'len' => 100,
      'audited' => true,
      'massupdate' => false,
      'dbType' => 'varchar',
    ),
    'manufacturer' => 
    array (
      'name' => 'manufacturer',
      'vname' => 'LBL_MANUFACTURER',
      'type' => 'varchar',
      'len' => 100,
      'audited' => true,
    ),
    'warranty' => 
    array (
      'name' => 'warranty',
      'vname' => 'LBL_WARRANTY',
      'type' => 'varchar',
      'len' => 100,
      'audited' => true,
    ),
    'quantity' => 
    array (
      'name' => 'quantity',
      'vname' => 'LBL_QUANTITY',
      'type' => 'float',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => false,
      'default' => 0,
    ),
    'in_stock' => 
    array (
      'name' => 'in_stock',
      'vname' => 'LBL_IN_STOCK',
      'type' => 'float',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => false,
      'default' => 0,
    ),
    'qty_waiting' => 
    array (
      'name' => 'qty_waiting',
      'vname' => 'LBL_QTY_WAITING',
      'type' => 'float',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => false,
      'default' => 0,
    ),
    'qty_repair' => 
    array (
      'name' => 'qty_repair',
      'vname' => 'LBL_QTY_REPAIR',
      'type' => 'float',
      'dbType' => 'double',
      'audited' => true,
      'duplicate_merge' => false,
      'default' => 0,
    ),
    'product_in_stocks' => 
    array (
      'name' => 'product_in_stocks',
      'vname' => 'LBL_PRODUCT_IN_STOCKS',
      'type' => 'text',
      'duplicate_merge' => false,
    ),
    'convert_rate' => 
    array (
      'name' => 'convert_rate',
      'vname' => 'LBL_CONVERT_RATE',
      'type' => 'float',
      'precision' => 6,
      'audited' => true,
    ),
    'hide_in_stock' => 
    array (
      'name' => 'hide_in_stock',
      'vname' => 'LBL_HIDE_IN_STOCK',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => false,
      'default' => 0,
    ),
    'tqt_productgroup_id' => 
    array (
      'name' => 'tqt_productgroup_id',
      'vname' => 'LBL_TQT_PRODUCTGROUP_ID',
      'type' => 'id',
      'function' => 
      array (
        'name' => 'getProductGroupOptions',
        'returns' => 'html',
        'include' => 'modules/TQT_Products/ProductOptions.php',
      ),
      'required' => false,
      'reportable' => false,
      'massupdate' => false,
    ),
    'tqt_productgroup_name' => 
    array (
      'name' => 'tqt_productgroup_name',
      'rname' => 'name',
      'id_name' => 'tqt_productgroup_id',
      'vname' => 'LBL_TQT_PRODUCTGROUP_NAME',
      'table' => 'tqt_productgroup',
      'type' => 'relate',
      'link' => 'tqt_productgroup',
      'join_name' => 'tqt_productgroup',
      'isnull' => 'true',
      'module' => 'TQT_ProductGroup',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'tqt_productgroup' => 
    array (
      'name' => 'tqt_productgroup',
      'type' => 'link',
      'relationship' => 'tqt_productgroup_tqt_products',
      'vname' => 'LBL_LIST_PRODUCTGROUP_NAME',
      'source' => 'non-db',
    ),
    'tqt_productseries_id' => 
    array (
      'name' => 'tqt_productseries_id',
      'vname' => 'LBL_TQT_PRODUCTSERIES_ID',
      'type' => 'id',
      'function' => 
      array (
        'name' => 'getProductSeriesOptions',
        'returns' => 'html',
        'include' => 'modules/TQT_Products/ProductOptions.php',
      ),
      'required' => false,
      'reportable' => false,
      'massupdate' => false,
    ),
    'tqt_productseries_name' => 
    array (
      'name' => 'tqt_productseries_name',
      'rname' => 'name',
      'id_name' => 'tqt_productseries_id',
      'vname' => 'LBL_TQT_PRODUCTSERIES_NAME',
      'table' => 'tqt_productseries',
      'type' => 'relate',
      'link' => 'tqt_productseries',
      'join_name' => 'tqt_productseries',
      'isnull' => 'true',
      'module' => 'TQT_ProductSeries',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'tqt_productseries' => 
    array (
      'name' => 'tqt_productseries',
      'type' => 'link',
      'relationship' => 'tqt_productseries_tqt_products',
      'vname' => 'LBL_LIST_PRODUCTSERIES_NAME',
      'source' => 'non-db',
    ),
    'filename' => 
    array (
      'name' => 'filename',
      'vname' => 'LBL_FILENAME',
      'type' => 'varchar',
      'len' => '200',
      'reportable' => true,
      'comment' => 'File name associated with the note (attachment)',
    ),
    'file_mime_type' => 
    array (
      'name' => 'file_mime_type',
      'vname' => 'LBL_FILE_MIME_TYPE',
      'type' => 'varchar',
      'len' => '100',
      'comment' => 'Attachment MIME type',
      'duplicate_merge' => false,
    ),
    'file_url' => 
    array (
      'name' => 'file_url',
      'vname' => 'LBL_FILE_URL',
      'type' => 'function',
      'function_require' => 'include/upload_file.php',
      'function_class' => 'UploadFile',
      'function_name' => 'get_url',
      'function_params' => 
      array (
        0 => 'filename',
        1 => 'id',
        2 => 'date_entered',
        3 => 'date_modified',
      ),
      'source' => 'function',
      'reportable' => false,
      'comment' => 'Path to file (can be URL)',
    ),
    'full_name' => 
    array (
      'name' => 'full_name',
      'vname' => 'LBL_FULL_NAME',
      'type' => 'varchar',
      'len' => 500,
      'source' => 'non-db',
    ),
    'discount' => 
    array (
      'name' => 'discount',
      'vname' => 'LBL_DISCOUNT',
      'type' => 'float',
      'source' => 'non-db',
    ),
    'available' => 
    array (
      'name' => 'available',
      'vname' => 'LBL_IN_STOCK',
      'type' => 'float',
      'source' => 'non-db',
    ),
    'waiting' => 
    array (
      'name' => 'waiting',
      'vname' => 'LBL_QTY_WAITING',
      'type' => 'float',
      'source' => 'non-db',
    ),
    'children_list' => 
    array (
      'name' => 'children_list',
      'vname' => 'LBL_MEMBERS',
      'type' => 'text',
      'source' => 'non-db',
    ),
    'product_vats' => 
    array (
      'name' => 'product_vats',
      'type' => 'link',
      'relationship' => 'vat_tqt_products',
      'module' => 'TQT_Products',
      'bean_name' => 'TQT_Product',
      'source' => 'non-db',
      'vname' => 'LBL_PRODUCT_VATS',
    ),
    'quantity_manufacture' => 
    array (
      'name' => 'quantity_manufacture',
      'vname' => 'LBL_QUANTITY_MANUFACTURE',
      'type' => 'float',
    ),
    'branch_prices' => 
    array (
      'name' => 'branch_prices',
      'type' => 'link',
      'relationship' => 'products_branches_prices',
      'module' => 'Branches',
      'bean_name' => 'Branch',
      'source' => 'non-db',
      'vname' => 'LBL_BRANCH_PRICES',
    ),
    'product_unit_id' => 
    array (
      'name' => 'product_unit_id',
      'vname' => 'LBL_PRODUCT_UNIT_ID',
      'type' => 'id',
      'function' => 
      array (
        'name' => 'getProductUnitOptions',
        'returns' => 'html',
        'include' => 'modules/TQT_Products/ProductOptions.php',
      ),
      'required' => false,
      'reportable' => false,
      'audited' => true,
      'massupdate' => false,
    ),
    'product_unit_name' => 
    array (
      'name' => 'product_unit_name',
      'rname' => 'name',
      'id_name' => 'product_unit_id',
      'vname' => 'LBL_PRODUCT_UNIT_NAME',
      'table' => 'product_units',
      'type' => 'relate',
      'link' => 'product_units',
      'join_name' => 'product_units',
      'isnull' => 'true',
      'module' => 'Product_Units',
      'source' => 'non-db',
      'massupdate' => false,
      'duplicate_merge' => false,
    ),
    'product_unit_code' => 
    array (
      'name' => 'product_unit_code',
      'rname' => 'code',
      'id_name' => 'product_unit_id',
      'vname' => 'LBL_PRODUCT_UNIT_CODE',
      'table' => 'product_units',
      'type' => 'relate',
      'link' => 'product_units',
      'join_name' => 'product_units',
      'isnull' => 'true',
      'module' => 'Product_Units',
      'source' => 'non-db',
      'massupdate' => false,
      'additionalFields' => 
      array (
        'convert_kg' => 'prod_convert_kg',
        'convert_m3' => 'prod_convert_m3',
      ),
    ),
    'prod_convert_kg' => 
    array (
      'name' => 'prod_convert_kg',
      'vname' => 'LBL_PROD_CONVERT_KG',
      'type' => 'float',
      'precision' => 6,
      'source' => 'non-db',
    ),
    'prod_convert_m3' => 
    array (
      'name' => 'prod_convert_m3',
      'vname' => 'LBL_PROD_CONVERT_M3',
      'type' => 'float',
      'precision' => 6,
      'source' => 'non-db',
    ),
    'product_units' => 
    array (
      'name' => 'product_units',
      'type' => 'link',
      'relationship' => 'product_units_related',
      'vname' => 'LBL_PRODUCT_UNITS',
      'source' => 'non-db',
    ),
    'SecurityGroups' => 
    array (
      'name' => 'SecurityGroups',
      'type' => 'link',
      'relationship' => 'securitygroups_tqt_products',
      'module' => 'SecurityGroups',
      'bean_name' => 'SecurityGroup',
      'source' => 'non-db',
      'vname' => 'LBL_SECURITYGROUPS',
    ),
    'product_gifts' => 
    array (
      'name' => 'product_gifts',
      'type' => 'link',
      'relationship' => 'product_gift_approved',
      'vname' => 'LBL_PRODUCT_GIFTS',
      'source' => 'non-db',
    ),
    'norm_min' => 
    array (
      'name' => 'norm_min',
      'vname' => 'LBL_NORM_MIN',
      'type' => 'int',
      'len' => 6,
      'audited' => true,
    ),
    'norm_max' => 
    array (
      'name' => 'norm_max',
      'vname' => 'LBL_NORM_MAX',
      'type' => 'int',
      'len' => 6,
      'audited' => true,
    ),
    'tqt_product_names' => 
    array (
      'name' => 'tqt_product_names',
      'type' => 'link',
      'relationship' => 'tqt_products_names_related',
      'source' => 'non-db',
      'vname' => 'LBL_TQT_PRODUCT_NAMES',
    ),
    'members' => 
    array (
      'name' => 'members',
      'type' => 'link',
      'relationship' => 'member_tqt_products',
      'module' => 'TQT_Products',
      'bean_name' => 'TQT_Product',
      'source' => 'non-db',
      'vname' => 'LBL_MEMBERS',
    ),
    'member_of' => 
    array (
      'name' => 'member_of',
      'type' => 'link',
      'relationship' => 'member_tqt_products',
      'module' => 'TQT_Products',
      'bean_name' => 'TQT_Product',
      'link_type' => 'one',
      'source' => 'non-db',
      'vname' => 'LBL_MEMBER_OF',
      'side' => 'left',
    ),
    'quantity_need' => 
    array (
      'name' => 'quantity_need',
      'vname' => 'LBL_QUANTITY_NEED',
      'type' => 'float',
      'source' => 'non-db',
    ),
    'quantity_to_buy' => 
    array (
      'name' => 'quantity_to_buy',
      'vname' => 'LBL_QUANTITY_TO_BUY',
      'type' => 'float',
      'source' => 'non-db',
    ),
    'member_stocks' => 
    array (
      'name' => 'member_stocks',
      'vname' => 'LBL_IN_STOCKS',
      'type' => 'float',
      'source' => 'non-db',
    ),
    'member_price_orig' => 
    array (
      'name' => 'member_price_orig',
      'vname' => 'LBL_PRICE_ORIG',
      'type' => 'float',
      'source' => 'non-db',
    ),
    'tqt_product_vats' => 
    array (
      'name' => 'tqt_product_vats',
      'type' => 'link',
      'relationship' => 'tqt_products_vats_related',
      'source' => 'non-db',
      'vname' => 'LBL_TQT_PRODUCT_VATS',
    ),
    'target_qty' => 
    array (
      'name' => 'target_qty',
      'vname' => 'LBL_TARGET_QTY',
      'type' => 'float',
      'source' => 'non-db',
    ),
    'target_price' => 
    array (
      'name' => 'target_price',
      'vname' => 'LBL_TARGET_PRICE',
      'type' => 'double',
      'source' => 'non-db',
    ),
    'target_amount' => 
    array (
      'name' => 'target_amount',
      'vname' => 'LBL_TARGET_AMOUNT',
      'type' => 'double',
      'source' => 'non-db',
    ),
    'mt_in_qty' => 
    array (
      'name' => 'mt_in_qty',
      'vname' => 'LBL_MT_IN_QTY',
      'type' => 'float',
      'source' => 'non-db',
    ),
    'mt_lm_qty' => 
    array (
      'name' => 'mt_lm_qty',
      'vname' => 'LBL_MT_LM_QTY',
      'type' => 'float',
      'source' => 'non-db',
    ),
    'mt_ly_qty' => 
    array (
      'name' => 'mt_ly_qty',
      'vname' => 'LBL_MT_LY_QTY',
      'type' => 'float',
      'source' => 'non-db',
    ),
    'tqt_product_prices' => 
    array (
      'name' => 'tqt_product_prices',
      'type' => 'link',
      'relationship' => 'tqt_products_tqt_product_prices',
      'source' => 'non-db',
      'vname' => 'LBL_TQT_PRODUCT_PRICES',
    ),
    'tqt_product_stocks' => 
    array (
      'name' => 'tqt_product_stocks',
      'type' => 'link',
      'relationship' => 'tqt_products_stocks_related',
      'source' => 'non-db',
      'vname' => 'LBL_TQT_PRODUCT_STOCKS',
    ),
    'warehouse_costs' => 
    array (
      'name' => 'warehouse_costs',
      'type' => 'link',
      'relationship' => 'warehouse_product_costs',
      'source' => 'non-db',
      'vname' => 'LBL_WAREHOUSE_COSTS',
    ),
    'product_bgn_costs' => 
    array (
      'name' => 'product_bgn_costs',
      'type' => 'link',
      'relationship' => 'product_beginning_costs',
      'source' => 'non-db',
      'vname' => 'LBL_PRODUCT_BGN_COSTS',
    ),
    'group_price_id' => 
    array (
      'name' => 'group_price_id',
      'vname' => 'LBL_GROUP_PRICE_ID',
      'type' => 'id',
      'audited' => true,
      'required' => false,
      'reportable' => false,
      'massupdate' => false,
    ),
    'group_price_name' => 
    array (
      'name' => 'group_price_name',
      'rname' => 'name',
      'id_name' => 'group_price_id',
      'vname' => 'LBL_GROUP_PRICE_NAME',
      'table' => 'group_prices',
      'type' => 'relate',
      'link' => 'group_prices',
      'join_name' => 'group_prices',
      'isnull' => 'true',
      'module' => 'Group_Prices',
      'source' => 'non-db',
      'massupdate' => false,
      'duplicate_merge' => false,
    ),
    'group_prices' => 
    array (
      'name' => 'group_prices',
      'type' => 'link',
      'relationship' => 'group_prices_products_related',
      'vname' => 'LBL_GROUP_PRICES',
      'source' => 'non-db',
    ),
    'fix_cost_begin' => 
    array (
      'name' => 'fix_cost_begin',
      'vname' => 'LBL_FIX_COST_BEGIN',
      'type' => 'double',
      'audited' => true,
      'massupdate' => false,
      'precision' => 4,
    ),
    'date_fix_cost' => 
    array (
      'name' => 'date_fix_cost',
      'vname' => 'LBL_DATE_FIX_COST',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
  ),
  'optimistic_lock' => true,
  'templates' => 
  array (
    'assignable' => 'assignable',
    'groups' => 'groups',
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
