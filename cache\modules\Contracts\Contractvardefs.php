<?php
// created: 2025-02-26 15:24:03
$G<PERSON><PERSON><PERSON><PERSON>["dictionary"]["Contract"] = array (
  'table' => 'contracts',
  'audited' => true,
  'unified_search' => true,
  'duplicate_merge' => false,
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'name',
      'dbType' => 'varchar',
      'len' => 255,
      'audited' => true,
      'required' => true,
      'unified_search' => true,
      'merge_filter' => 'selected',
      'acl' => true,
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
      'comment' => 'Full text of the note',
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'contracts_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'contracts_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'branch_id' => 
    array (
      'name' => 'branch_id',
      'vname' => 'LBL_BRANCH_ID',
      'type' => 'enum',
      'options' => 'users_branch_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'BranchID of assigned user',
    ),
    'department_id' => 
    array (
      'name' => 'department_id',
      'vname' => 'LBL_DEPARTMENT_ID',
      'type' => 'enum',
      'options' => 'users_department_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'DepartmentID of assigned user',
    ),
    'assigned_user_id' => 
    array (
      'name' => 'assigned_user_id',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'vname' => 'LBL_ASSIGNED_TO_ID',
      'group' => 'assigned_user_name',
      'type' => 'relate',
      'table' => 'users',
      'module' => 'Users',
      'isnull' => 'false',
      'dbType' => 'id',
      'audited' => true,
      'reportable' => true,
      'duplicate_merge' => 'disabled',
      'comment' => 'User ID assigned to record',
    ),
    'assigned_user_name' => 
    array (
      'name' => 'assigned_user_name',
      'link' => 'assigned_user_link',
      'vname' => 'LBL_ASSIGNED_TO_NAME',
      'rname' => 'user_name',
      'type' => 'relate',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'assigned_user_id',
      'module' => 'Users',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'assigned_user_link' => 
    array (
      'name' => 'assigned_user_link',
      'type' => 'link',
      'relationship' => 'contracts_assigned_user',
      'vname' => 'LBL_ASSIGNED_TO_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
      'duplicate_merge' => 'enabled',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'table' => 'users',
    ),
    'rowindex' => 
    array (
      'name' => 'rowindex',
      'vname' => 'LBL_ROW_INDEX',
      'type' => 'int',
      'audited' => true,
    ),
    'code' => 
    array (
      'name' => 'code',
      'vname' => 'LBL_CODE',
      'type' => 'varchar',
      'len' => '255',
      'massupdate' => false,
      'audited' => true,
      'unified_search' => true,
    ),
    'is_locked' => 
    array (
      'name' => 'is_locked',
      'vname' => 'LBL_IS_LOCKED',
      'type' => 'bool',
      'massupdate' => false,
      'audited' => true,
    ),
    'signed' => 
    array (
      'name' => 'signed',
      'vname' => 'LBL_SIGNED',
      'type' => 'bool',
      'massupdate' => false,
      'audited' => true,
    ),
    'date_signed' => 
    array (
      'name' => 'date_signed',
      'vname' => 'LBL_DATE_SIGNED',
      'type' => 'date',
      'massupdate' => false,
      'audited' => true,
    ),
    'date_signed_to' => 
    array (
      'name' => 'date_signed_to',
      'vname' => 'LBL_LIST_DATE_SIGNED_TO',
      'type' => 'date',
      'massupdate' => false,
      'source' => 'non-db',
    ),
    'date_start' => 
    array (
      'name' => 'date_start',
      'vname' => 'LBL_DATE_START',
      'type' => 'date',
      'massupdate' => false,
      'audited' => true,
    ),
    'date_end' => 
    array (
      'name' => 'date_end',
      'vname' => 'LBL_DATE_END',
      'type' => 'date',
      'massupdate' => false,
      'audited' => true,
    ),
    'date_expired' => 
    array (
      'name' => 'date_expired',
      'vname' => 'LBL_DATE_EXPIRED',
      'type' => 'date',
      'massupdate' => false,
      'audited' => true,
    ),
    'status' => 
    array (
      'name' => 'status',
      'vname' => 'LBL_STATUS',
      'type' => 'enum',
      'options' => 'contract_status_dom',
      'len' => '50',
      'massupdate' => false,
      'audited' => true,
      'default' => '1',
    ),
    'stage' => 
    array (
      'name' => 'stage',
      'vname' => 'LBL_STAGE',
      'type' => 'enum',
      'options' => 'contract_stage_dom',
      'len' => '50',
      'massupdate' => false,
      'audited' => true,
      'default' => '2',
    ),
    'type' => 
    array (
      'name' => 'type',
      'vname' => 'LBL_TYPE',
      'type' => 'enum',
      'options' => 'contract_type_dom',
      'len' => '50',
      'massupdate' => false,
      'audited' => true,
    ),
    'cnt_type' => 
    array (
      'name' => 'cnt_type',
      'vname' => 'LBL_CNT_TYPE',
      'type' => 'enum',
      'options' => 'contract_cnt_type_dom',
      'len' => '50',
      'massupdate' => false,
      'audited' => true,
    ),
    'market' => 
    array (
      'name' => 'market',
      'vname' => 'LBL_MARKET',
      'type' => 'enum',
      'options' => 'contract_market_dom',
      'len' => '50',
      'massupdate' => false,
      'audited' => true,
    ),
    'dept_warning' => 
    array (
      'name' => 'dept_warning',
      'vname' => 'LBL_DEPT_WARNING',
      'type' => 'bool',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'currency' => 
    array (
      'name' => 'currency',
      'vname' => 'LBL_CURRENCY',
      'type' => 'currency',
      'massupdate' => 0,
      'comments' => '',
      'help' => '',
      'importable' => 'true',
      'duplicate_merge' => 'disabled',
      'duplicate_merge_dom_value' => '0',
      'audited' => true,
      'reportable' => 0,
      'len' => 26,
    ),
    'currency_id' => 
    array (
      'audited' => 0,
      'name' => 'currency_id',
      'vname' => 'LBL_CURRENCY',
      'type' => 'id',
      'massupdate' => 0,
      'comments' => '',
      'help' => '',
      'importable' => 'true',
      'duplicate_merge' => 'disabled',
      'duplicate_merge_dom_value' => 0,
      'reportable' => 0,
      'len' => 36,
      'studio' => 'visible',
      'function' => 
      array (
        'name' => 'getCurrencyDropDown',
        'returns' => 'html',
      ),
    ),
    'payment_type' => 
    array (
      'name' => 'payment_type',
      'vname' => 'LBL_PAYMENT_TYPE',
      'type' => 'enum',
      'options' => 'contract_payment_dom',
      'massupdate' => false,
      'audited' => true,
    ),
    'payment_note' => 
    array (
      'name' => 'payment_note',
      'vname' => 'LBL_PAYMENT_NOTE',
      'type' => 'text',
      'audited' => true,
    ),
    'contract_amount' => 
    array (
      'name' => 'contract_amount',
      'vname' => 'LBL_CONTRACT_AMOUNT',
      'type' => 'currency',
      'dbType' => 'double',
      'massupdate' => false,
      'audited' => true,
    ),
    'contract_tax' => 
    array (
      'name' => 'contract_tax',
      'vname' => 'LBL_CONTRACT_TAX',
      'type' => 'enum',
      'options' => 'contract_tax_dom',
      'dbType' => 'float',
      'massupdate' => false,
      'audited' => true,
    ),
    'tax_amount' => 
    array (
      'name' => 'tax_amount',
      'vname' => 'LBL_TAX_AMOUNT',
      'type' => 'currency',
      'dbType' => 'double',
      'massupdate' => false,
      'audited' => true,
    ),
    'amount_before_tax' => 
    array (
      'name' => 'amount_before_tax',
      'vname' => 'LBL_AMOUNT_BEFORE_TAX',
      'type' => 'currency',
      'dbType' => 'double',
      'massupdate' => false,
      'audited' => true,
    ),
    'amount_after_tax' => 
    array (
      'name' => 'amount_after_tax',
      'vname' => 'LBL_AMOUNT_AFTER_TAX',
      'type' => 'currency',
      'dbType' => 'double',
      'massupdate' => false,
      'audited' => true,
    ),
    'amount_total' => 
    array (
      'name' => 'amount_total',
      'vname' => 'LBL_AMOUNT_TOTAL',
      'type' => 'currency',
      'dbType' => 'double',
      'massupdate' => false,
      'audited' => true,
    ),
    'amount_note' => 
    array (
      'name' => 'amount_note',
      'vname' => 'LBL_AMOUNT_NOTE',
      'type' => 'text',
      'massupdate' => false,
      'audited' => true,
    ),
    'discount_percent' => 
    array (
      'name' => 'discount_percent',
      'vname' => 'LBL_DISCOUNT_PERCENT',
      'type' => 'double',
      'disable_num_format' => true,
      'massupdate' => false,
      'audited' => true,
    ),
    'discount_amount' => 
    array (
      'name' => 'discount_amount',
      'vname' => 'LBL_DISCOUNT_AMOUNT',
      'type' => 'currency',
      'dbType' => 'double',
      'massupdate' => false,
      'audited' => true,
    ),
    'product_discount' => 
    array (
      'name' => 'product_discount',
      'vname' => 'LBL_PRODUCT_DISCOUNT',
      'type' => 'double',
      'disable_num_format' => true,
      'massupdate' => false,
      'audited' => true,
    ),
    'product_amt_discount' => 
    array (
      'name' => 'product_amt_discount',
      'vname' => 'LBL_PRODUCT_AMT_DISCOUNT',
      'type' => 'currency',
      'dbType' => 'double',
      'massupdate' => false,
      'audited' => true,
    ),
    'amt_before_discount' => 
    array (
      'name' => 'amt_before_discount',
      'vname' => 'LBL_AMT_BEFORE_DISCOUNT',
      'type' => 'currency',
      'dbType' => 'double',
      'massupdate' => false,
      'audited' => true,
    ),
    'amt_after_discount' => 
    array (
      'name' => 'amt_after_discount',
      'vname' => 'LBL_AMT_AFTER_DISCOUNT',
      'type' => 'currency',
      'dbType' => 'double',
      'massupdate' => false,
      'audited' => true,
    ),
    'refund_percentage' => 
    array (
      'name' => 'refund_percentage',
      'vname' => 'LBL_REFUND_PERCENTAGE',
      'type' => 'double',
      'disable_num_format' => true,
      'massupdate' => false,
      'audited' => true,
    ),
    'refund_amount' => 
    array (
      'name' => 'refund_amount',
      'vname' => 'LBL_REFUND_AMOUNT',
      'type' => 'currency',
      'dbType' => 'double',
      'massupdate' => false,
      'audited' => true,
    ),
    'reduce_price' => 
    array (
      'name' => 'reduce_price',
      'vname' => 'LBL_REDUCE_PRICE',
      'type' => 'currency',
      'dbType' => 'double',
      'massupdate' => false,
      'audited' => true,
    ),
    'commission' => 
    array (
      'name' => 'commission',
      'vname' => 'LBL_COMMISSION',
      'type' => 'currency',
      'dbType' => 'double',
      'massupdate' => false,
      'audited' => true,
    ),
    'industry' => 
    array (
      'name' => 'industry',
      'vname' => 'LBL_INDUSTRY',
      'type' => 'multienum',
      'options' => 'business_field_dom',
      'isMultiSelect' => true,
      'massupdate' => false,
      'isCheckBoxList' => true,
      'audited' => true,
    ),
    'responsible' => 
    array (
      'name' => 'responsible',
      'vname' => 'LBL_RESPONSIBLE',
      'type' => 'text',
    ),
    'account_representation' => 
    array (
      'name' => 'account_representation',
      'vname' => 'LBL_ACCOUNT_REPRESENTATION',
      'type' => 'varchar',
      'len' => '255',
      'audited' => true,
      'massupdate' => false,
    ),
    'account_position' => 
    array (
      'name' => 'account_position',
      'vname' => 'LBL_ACCOUNT_POSITION',
      'type' => 'varchar',
      'len' => '255',
      'audited' => true,
      'massupdate' => false,
    ),
    'contract_note' => 
    array (
      'name' => 'contract_note',
      'vname' => 'LBL_CONTRACT_NOTE',
      'type' => 'text',
      'audited' => true,
    ),
    'parent_id' => 
    array (
      'name' => 'parent_id',
      'vname' => 'LBL_RELATED_ID',
      'type' => 'id',
      'comment' => 'Contract ID of the parent of this contract',
      'audited' => true,
      'massupdate' => false,
    ),
    'parent_name' => 
    array (
      'name' => 'parent_name',
      'rname' => 'name',
      'id_name' => 'parent_id',
      'vname' => 'LBL_RELATED_NAME',
      'type' => 'relate',
      'table' => 'member_contracts',
      'module' => 'Contracts',
      'massupdate' => false,
      'source' => 'non-db',
      'link' => 'member_of',
      'unified_search' => true,
      'importable' => 'false',
    ),
    'parent_code' => 
    array (
      'name' => 'parent_code',
      'rname' => 'code',
      'id_name' => 'parent_id',
      'vname' => 'LBL_RELATED_CODE',
      'type' => 'relate',
      'module' => 'Contracts',
      'source' => 'non-db',
      'link' => 'member_of',
      'importable' => 'false',
      'massupdate' => false,
    ),
    'members' => 
    array (
      'name' => 'members',
      'type' => 'link',
      'relationship' => 'member_contracts',
      'module' => 'Contracts',
      'bean_name' => 'Contract',
      'source' => 'non-db',
      'vname' => 'LBL_MEMBERS',
    ),
    'member_of' => 
    array (
      'name' => 'member_of',
      'type' => 'link',
      'relationship' => 'member_contracts',
      'module' => 'Contracts',
      'bean_name' => 'Contract',
      'link_type' => 'one',
      'source' => 'non-db',
      'vname' => 'LBL_MEMBER_OF',
      'side' => 'left',
    ),
    'account_id' => 
    array (
      'name' => 'account_id',
      'vname' => 'LBL_ACCOUNT_ID',
      'type' => 'id',
      'audited' => true,
      'massupdate' => false,
    ),
    'account_name' => 
    array (
      'name' => 'account_name',
      'rname' => 'name',
      'id_name' => 'account_id',
      'vname' => 'LBL_ACCOUNT_NAME',
      'source' => 'non-db',
      'type' => 'relate',
      'link' => 'accounts',
      'module' => 'Accounts',
      'join_name' => 'accounts',
      'required' => true,
      'massupdate' => false,
      'field_list' => 
      array (
        0 => 'account_type',
        1 => 'account_region',
        2 => 'account_billing_address',
        3 => 'account_representation',
        4 => 'account_position',
        5 => 'account_fax',
        6 => 'account_phone',
        7 => 'account_status',
        8 => 'account_tax_code',
        9 => 'account_code',
        10 => 'account_name',
        11 => 'account_id',
      ),
      'populate_list' => 
      array (
        0 => 'account_type',
        1 => 'location_area',
        2 => 'billing_address_street',
        3 => 'billing_representation',
        4 => 'billing_position',
        5 => 'phone_fax',
        6 => 'phone_office',
        7 => 'account_status',
        8 => 'tax_code',
        9 => 'account_code',
        10 => 'name',
        11 => 'id',
      ),
    ),
    'accounts' => 
    array (
      'name' => 'accounts',
      'type' => 'link',
      'source' => 'non-db',
      'relationship' => 'accounts_contracts',
    ),
    'account_code' => 
    array (
      'name' => 'account_code',
      'vname' => 'LBL_ACCOUNT_CODE',
      'type' => 'varchar',
      'source' => 'non-db',
    ),
    'account_phone' => 
    array (
      'name' => 'account_phone',
      'vname' => 'LBL_ACCOUNT_PHONE',
      'type' => 'varchar',
      'source' => 'non-db',
    ),
    'account_fax' => 
    array (
      'name' => 'account_fax',
      'vname' => 'LBL_ACCOUNT_FAX',
      'type' => 'varchar',
      'source' => 'non-db',
    ),
    'account_status' => 
    array (
      'name' => 'account_status',
      'vname' => 'LBL_ACCOUNT_STATUS',
      'type' => 'varchar',
      'source' => 'non-db',
    ),
    'account_type' => 
    array (
      'name' => 'account_type',
      'vname' => 'LBL_ACCOUNT_TYPE',
      'type' => 'varchar',
      'source' => 'non-db',
    ),
    'account_type2' => 
    array (
      'name' => 'account_type2',
      'vname' => 'LBL_ACCOUNT_TYPE',
      'type' => 'enum',
      'options' => 'account_type_dom',
      'massupdate' => false,
      'source' => 'non-db',
    ),
    'account_tax_code' => 
    array (
      'name' => 'account_tax_code',
      'vname' => 'LBL_ACCOUNT_TAX_CODE',
      'type' => 'varchar',
      'source' => 'non-db',
    ),
    'account_region' => 
    array (
      'name' => 'account_region',
      'vname' => 'LBL_ACCOUNT_REGION',
      'type' => 'varchar',
      'source' => 'non-db',
    ),
    'account_billing_address' => 
    array (
      'name' => 'account_billing_address',
      'vname' => 'LBL_ACCOUNT_BILLING_ADDRESS',
      'type' => 'varchar',
      'source' => 'non-db',
    ),
    'debt_user_id' => 
    array (
      'name' => 'debt_user_id',
      'vname' => 'LBL_DEBT_USER_ID',
      'type' => 'id',
      'audited' => true,
      'massupdate' => false,
    ),
    'debt_user_name' => 
    array (
      'name' => 'debt_user_name',
      'rname' => 'user_name',
      'id_name' => 'debt_user_id',
      'vname' => 'LBL_DEBT_USER_NAME',
      'type' => 'relate',
      'link' => 'debt_users',
      'source' => 'non-db',
      'module' => 'Users',
      'massupdate' => false,
    ),
    'debt_users' => 
    array (
      'name' => 'debt_users',
      'vname' => 'LBL_DEBT_USERS',
      'type' => 'link',
      'source' => 'non-db',
      'relationship' => 'debt_users_contracts',
    ),
    'documents' => 
    array (
      'name' => 'documents',
      'type' => 'link',
      'relationship' => 'contracts_documents',
      'module' => 'Documents',
      'bean_name' => 'Document',
      'source' => 'non-db',
      'vname' => 'LBL_DOCUMENTS',
    ),
    'tasks' => 
    array (
      'name' => 'tasks',
      'type' => 'link',
      'relationship' => 'contracts_tasks',
      'source' => 'non-db',
      'vname' => 'LBL_TASKS',
    ),
    'calls' => 
    array (
      'name' => 'calls',
      'type' => 'link',
      'relationship' => 'contracts_calls',
      'source' => 'non-db',
      'vname' => 'LBL_CALLS',
    ),
    'meetings' => 
    array (
      'name' => 'meetings',
      'type' => 'link',
      'relationship' => 'contracts_meetings',
      'source' => 'non-db',
      'vname' => 'LBL_MEETINGS',
    ),
    'notes' => 
    array (
      'name' => 'notes',
      'type' => 'link',
      'relationship' => 'contracts_notes',
      'source' => 'non-db',
      'vname' => 'LBL_NOTES',
    ),
    'contracts_tqt_offlines' => 
    array (
      'name' => 'contracts_tqt_offlines',
      'type' => 'link',
      'relationship' => 'contracts_tqt_offlines',
      'source' => 'non-db',
      'vname' => 'LBL_CONTRACTS_CAMPAIGN_OFFLINES',
    ),
    'contracts_approves' => 
    array (
      'name' => 'contracts_approves',
      'type' => 'link',
      'relationship' => 'contracts_contracts_approves',
      'source' => 'non-db',
      'vname' => 'LBL_CONTRACTS_APPROVES',
    ),
    'contracts_payments' => 
    array (
      'name' => 'contracts_payments',
      'vname' => 'LBL_CONTRACTS_PAYMENTS',
      'relationship' => 'FK_PAYMENT_CONTRACT_ID',
      'source' => 'non-db',
      'type' => 'link',
    ),
    'contracts_invoice' => 
    array (
      'name' => 'contracts_invoice',
      'type' => 'link',
      'relationship' => 'contracts_contracts_invoice',
      'source' => 'non-db',
      'vname' => 'LBL_CONTRACTS_INVOICES',
    ),
    'contracts_revenuereduces' => 
    array (
      'name' => 'contracts_revenuereduces',
      'type' => 'link',
      'relationship' => 'contracts_contracts_revenuereduces',
      'source' => 'non-db',
      'vname' => 'LBL_CONTRACTS_REVENUE_REDUCES',
    ),
    'contracts_collections' => 
    array (
      'name' => 'contracts_collections',
      'type' => 'link',
      'relationship' => 'contracts_contracts_collections',
      'source' => 'non-db',
      'vname' => 'LBL_CONTRACTS_COLLECTIONS',
    ),
    'project_teams' => 
    array (
      'name' => 'project_teams',
      'type' => 'link',
      'relationship' => 'contracts_project_teams',
      'vname' => 'LBL_PROJECT_TEAMS',
      'source' => 'non-db',
    ),
    'SecurityGroups' => 
    array (
      'name' => 'SecurityGroups',
      'type' => 'link',
      'relationship' => 'securitygroups_contracts',
      'module' => 'SecurityGroups',
      'bean_name' => 'SecurityGroup',
      'source' => 'non-db',
      'vname' => 'LBL_SECURITYGROUPS',
    ),
    'tqt_subtasks' => 
    array (
      'name' => 'tqt_subtasks',
      'type' => 'link',
      'relationship' => 'contracts_tqt_subtasks',
      'source' => 'non-db',
      'vname' => 'LBL_TQT_SUBTASKS',
    ),
    'tqt_summaryweeks' => 
    array (
      'name' => 'tqt_summaryweeks',
      'type' => 'link',
      'relationship' => 'contracts_tqt_summaryweeks',
      'source' => 'non-db',
      'vname' => 'LBL_TQT_SUMMARYWEEKS',
    ),
    'sw_status' => 
    array (
      'name' => 'sw_status',
      'vname' => 'LBL_SW_STATUS',
      'type' => 'enum',
      'options' => 'maintask_status_dom',
      'len' => '100',
      'massupdate' => false,
      'audited' => true,
      'comment' => 'Use: Only for Search and Update from SummaryWeek module',
    ),
    'sw_date_start' => 
    array (
      'name' => 'sw_date_start',
      'vname' => 'LBL_SW_DATE_START',
      'type' => 'date',
      'massupdate' => false,
      'audited' => true,
      'comment' => 'Use: Only for Search and Update from SummaryWeek module',
    ),
    'sw_date_end' => 
    array (
      'name' => 'sw_date_end',
      'vname' => 'LBL_SW_DATE_END',
      'type' => 'date',
      'massupdate' => false,
      'audited' => true,
      'comment' => 'Use: Only for Search and Update from SummaryWeek module',
    ),
    'deadline' => 
    array (
      'name' => 'deadline',
      'vname' => 'LBL_DEADLINE',
      'type' => 'date',
      'massupdate' => false,
      'audited' => true,
    ),
    'deadline_late' => 
    array (
      'name' => 'deadline_late',
      'vname' => 'LBL_DEADLINE_LATE',
      'type' => 'bool',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'days_late' => 
    array (
      'name' => 'days_late',
      'vname' => 'LBL_DAYS_LATE',
      'type' => 'int',
      'len' => '6',
      'audited' => true,
    ),
    'eval_confirm' => 
    array (
      'name' => 'eval_confirm',
      'vname' => 'LBL_EVAL_CONFIRM',
      'type' => 'enum',
      'options' => 'maintask_eval_confirm_dom',
      'len' => '100',
      'massupdate' => false,
      'audited' => true,
    ),
    'eval_notes' => 
    array (
      'name' => 'eval_notes',
      'vname' => 'LBL_EVAL_NOTES',
      'type' => 'text',
    ),
    'handle_user_id' => 
    array (
      'name' => 'handle_user_id',
      'vname' => 'LBL_HANDLE_USER_ID',
      'type' => 'id',
      'massupdate' => false,
      'audited' => true,
    ),
    'handle_user_name' => 
    array (
      'name' => 'handle_user_name',
      'rname' => 'user_name',
      'id_name' => 'handle_user_id',
      'vname' => 'LBL_HANDLE_USER_NAME',
      'source' => 'non-db',
      'type' => 'relate',
      'link' => 'handle_users',
      'module' => 'Users',
      'massupdate' => false,
    ),
    'handle_users' => 
    array (
      'name' => 'handle_users',
      'vname' => 'LBL_HANDLE_USERS',
      'type' => 'link',
      'source' => 'non-db',
      'relationship' => 'handle_users_contracts',
    ),
    'engineer_user_id' => 
    array (
      'name' => 'engineer_user_id',
      'vname' => 'LBL_ENGINNEER_USER_ID',
      'type' => 'id',
      'massupdate' => false,
      'audited' => true,
    ),
    'engineer_user_name' => 
    array (
      'name' => 'engineer_user_name',
      'rname' => 'user_name',
      'id_name' => 'engineer_user_id',
      'vname' => 'LBL_ENGINNEER_USER_NAME',
      'source' => 'non-db',
      'type' => 'relate',
      'link' => 'engineer_users',
      'module' => 'Users',
      'massupdate' => false,
    ),
    'engineer_users' => 
    array (
      'name' => 'engineer_users',
      'vname' => 'LBL_ENGINNEER_USERS',
      'type' => 'link',
      'source' => 'non-db',
      'relationship' => 'engineer_users_contracts',
    ),
    'date_sent' => 
    array (
      'name' => 'date_sent',
      'vname' => 'LBL_DATE_SENT',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'date_receipt' => 
    array (
      'name' => 'date_receipt',
      'vname' => 'LBL_DATE_RECEIPT',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'opportunity_id' => 
    array (
      'name' => 'opportunity_id',
      'vname' => 'LBL_OPPORTUNITY_ID',
      'type' => 'id',
      'required' => false,
      'reportable' => false,
      'audited' => true,
      'massupdate' => false,
    ),
    'opportunity_name' => 
    array (
      'name' => 'opportunity_name',
      'rname' => 'name',
      'id_name' => 'opportunity_id',
      'vname' => 'LBL_OPPORTUNITY_NAME',
      'table' => 'opportunities',
      'type' => 'relate',
      'link' => 'opportunities',
      'join_name' => 'opportunities',
      'isnull' => 'true',
      'module' => 'Opportunities',
      'source' => 'non-db',
      'massupdate' => false,
      'additionalFields' => 
      array (
        'order_number' => 'opportunity_code',
      ),
    ),
    'opportunity_code' => 
    array (
      'name' => 'opportunity_code',
      'vname' => 'LBL_OPPORTUNITY_CODE',
      'type' => 'varchar',
      'len' => '50',
      'source' => 'non-db',
    ),
    'opportunities' => 
    array (
      'name' => 'opportunities',
      'type' => 'link',
      'relationship' => 'opportunities_contracts',
      'vname' => 'LBL_OPPORTUNITIES',
      'source' => 'non-db',
    ),
  ),
  'indices' => 
  array (
    'id' => 
    array (
      'name' => 'contractspk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    'idx_contracts_branch_id' => 
    array (
      'name' => 'idx_contracts_branch_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
      ),
    ),
    'idx_contracts_department_id' => 
    array (
      'name' => 'idx_contracts_department_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'department_id',
      ),
    ),
    'idx_contracts_branch_dept' => 
    array (
      'name' => 'idx_contracts_branch_dept',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
        1 => 'department_id',
      ),
    ),
    'idx_contracts_assigned' => 
    array (
      'name' => 'idx_contracts_assigned',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'assigned_user_id',
      ),
    ),
    0 => 
    array (
      'name' => 'idx_contracts_acc',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'account_id',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_contracts_type',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'type',
      ),
    ),
    2 => 
    array (
      'name' => 'idx_contracts_status',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'status',
      ),
    ),
    3 => 
    array (
      'name' => 'idx_contracts_stage',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'stage',
      ),
    ),
    4 => 
    array (
      'name' => 'idx_contracts_cnt_type',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'cnt_type',
      ),
    ),
    5 => 
    array (
      'name' => 'idx_contracts_market',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'market',
      ),
    ),
    6 => 
    array (
      'name' => 'idx_contracts_assigned_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'assigned_user_id',
      ),
    ),
    7 => 
    array (
      'name' => 'idx_contracts_assigned_sign',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'signed',
        1 => 'assigned_user_id',
      ),
    ),
    8 => 
    array (
      'name' => 'idx_contracts_assigned_type',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'type',
        1 => 'assigned_user_id',
      ),
    ),
    9 => 
    array (
      'name' => 'idx_contracts_assigned_status',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'status',
        1 => 'assigned_user_id',
      ),
    ),
    10 => 
    array (
      'name' => 'idx_contracts_assigned_stage',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'stage',
        1 => 'assigned_user_id',
      ),
    ),
    11 => 
    array (
      'name' => 'idx_contracts_assigned_cnt_type',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'cnt_type',
        1 => 'assigned_user_id',
      ),
    ),
    12 => 
    array (
      'name' => 'idx_contracts_assigned_market',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'market',
        1 => 'assigned_user_id',
      ),
    ),
    13 => 
    array (
      'name' => 'idx_contracts_stage_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'stage',
      ),
    ),
    'idx_contracts_del_opp_id' => 
    array (
      'name' => 'idx_contracts_del_opp_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'opportunity_id',
      ),
    ),
  ),
  'relationships' => 
  array (
    'contracts_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Contracts',
      'rhs_table' => 'contracts',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'contracts_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Contracts',
      'rhs_table' => 'contracts',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
    'contracts_assigned_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Contracts',
      'rhs_table' => 'contracts',
      'rhs_key' => 'assigned_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'member_contracts' => 
    array (
      'lhs_module' => 'Contracts',
      'lhs_table' => 'contracts',
      'lhs_key' => 'id',
      'rhs_module' => 'Contracts',
      'rhs_table' => 'contracts',
      'rhs_key' => 'parent_id',
      'relationship_type' => 'one-to-many',
    ),
    'accounts_contracts' => 
    array (
      'lhs_module' => 'Accounts',
      'lhs_table' => 'accounts',
      'lhs_key' => 'id',
      'rhs_module' => 'Contracts',
      'rhs_table' => 'contracts',
      'rhs_key' => 'account_id',
      'relationship_type' => 'one-to-many',
    ),
    'debt_users_contracts' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Contracts',
      'rhs_table' => 'contracts',
      'rhs_key' => 'debt_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'contracts_tasks' => 
    array (
      'lhs_module' => 'Contracts',
      'lhs_table' => 'contracts',
      'lhs_key' => 'id',
      'rhs_module' => 'Tasks',
      'rhs_table' => 'tasks',
      'rhs_key' => 'parent_id',
      'relationship_type' => 'one-to-many',
      'relationship_role_column' => 'parent_type',
      'relationship_role_column_value' => 'Contracts',
    ),
    'contracts_calls' => 
    array (
      'lhs_module' => 'Contracts',
      'lhs_table' => 'contracts',
      'lhs_key' => 'id',
      'rhs_module' => 'Calls',
      'rhs_table' => 'calls',
      'rhs_key' => 'parent_id',
      'relationship_type' => 'one-to-many',
      'relationship_role_column' => 'parent_type',
      'relationship_role_column_value' => 'Contracts',
    ),
    'contracts_meetings' => 
    array (
      'lhs_module' => 'Contracts',
      'lhs_table' => 'contracts',
      'lhs_key' => 'id',
      'rhs_module' => 'Meetings',
      'rhs_table' => 'meetings',
      'rhs_key' => 'parent_id',
      'relationship_type' => 'one-to-many',
      'relationship_role_column' => 'parent_type',
      'relationship_role_column_value' => 'Contracts',
    ),
    'contracts_notes' => 
    array (
      'lhs_module' => 'Contracts',
      'lhs_table' => 'contracts',
      'lhs_key' => 'id',
      'rhs_module' => 'Notes',
      'rhs_table' => 'notes',
      'rhs_key' => 'parent_id',
      'relationship_type' => 'one-to-many',
      'relationship_role_column' => 'parent_type',
      'relationship_role_column_value' => 'Contracts',
    ),
    'contracts_documents' => 
    array (
      'lhs_module' => 'Contracts',
      'lhs_table' => 'contracts',
      'lhs_key' => 'id',
      'rhs_module' => 'Documents',
      'rhs_table' => 'documents',
      'rhs_key' => 'id',
      'relationship_type' => 'many-to-many',
      'join_table' => 'linked_documents',
      'join_key_lhs' => 'parent_id',
      'join_key_rhs' => 'document_id',
      'relationship_role_column' => 'parent_type',
      'relationship_role_column_value' => 'Contracts',
    ),
    'handle_users_contracts' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Contracts',
      'rhs_table' => 'contracts',
      'rhs_key' => 'handle_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'engineer_users_contracts' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Contracts',
      'rhs_table' => 'contracts',
      'rhs_key' => 'engineer_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'opportunities_contracts' => 
    array (
      'lhs_module' => 'Opportunities',
      'lhs_table' => 'opportunities',
      'lhs_key' => 'id',
      'rhs_module' => 'Contracts',
      'rhs_table' => 'contracts',
      'rhs_key' => 'opportunity_id',
      'relationship_type' => 'one-to-many',
    ),
  ),
  'templates' => 
  array (
    'assignable' => 'assignable',
    'groups' => 'groups',
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
