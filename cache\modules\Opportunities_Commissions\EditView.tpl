
<section id="EditViewViewBody" class="view-module-{$module|strtolower}">

<form action="index.php" method="POST" name="{$form_name}" id="{$form_id}" class="_edit-{$instanceName}" {$enctype}>
<input type="hidden" name="module" value="{$module}" />
<input type="hidden" name="action" id="action" />
{if isset($smarty.request.isDuplicate) && $smarty.request.isDuplicate eq "true"}
<input type="hidden" name="record" id="record" value="" />
<input type="hidden" name="duplicateSave" value="true" />
{else}
<input type="hidden" name="record" id="record" value="{$fields.id.value}" />
{/if}
<input type="hidden" name="return_module" value="{$smarty.request.return_module}" />
<input type="hidden" name="return_action" value="{if $smarty.request.return_action eq 'LoadTabSubpanels'}DetailView{else}{$smarty.request.return_action}{/if}" />
<input type="hidden" name="return_id" value="{$smarty.request.return_id}" />
<input type="hidden" name="isDuplicate" value="false" />
<input type="hidden" name="module_tab" /> 
<input type="hidden" name="contact_role" />
{if !empty($smarty.request.return_module)}
<input type="hidden" name="relate_to" value="{if $smarty.request.return_relationship}{$smarty.request.return_relationship}{else}{$smarty.request.return_module}{/if}" />
<input type="hidden" name="relate_id" value="{$smarty.request.return_id}" />
{/if}
<input type="hidden" name="offset" value="{$offset}" />
{if empty($fields.id.value)}
<input type="hidden" name="_notify_random" value="{php}echo create_guid();{/php}" />
{/if}
<input type="hidden" name="isFromEditView" value="{$module}" />
<input type="hidden" id="is_amount" name="is_amount" value="{$fields.is_amount.value}" />
<table width="100%" cellpadding="0" cellspacing="0" border="0">
<tr>
<td class="buttons">
{if $bean->aclAccess("save")}<input title="{$APP.LBL_SAVE_BUTTON_TITLE}" accessKey="{$APP.LBL_SAVE_BUTTON_KEY}" class="button save" onclick="{if $isDuplicate}this.form.return_id.value=''; {/if}this.form.action.value='Save'; return check_form('EditView');" type="submit" name="button" value="{$APP.LBL_SAVE_BUTTON_LABEL}" />{/if} 
{if !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($fields.id.value))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {elseif !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($smarty.request.return_id))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {else}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='index'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {/if}
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=Opportunities_Commissions", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</td>
</tr>
</table>
{incomCRM_include include=$includes}
<table width="100%" cellspacing="0" cellpadding="0" class='detail view' id='tabFormPagination'>
{$PAGINATION}
</table>
<div id="LBL_OPPORTUNITY_INFO" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_OPPORTUNITY_INFO');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_OPPORTUNITY_INFO_IMG" border="0" />
{incomCRM_translate label='LBL_OPPORTUNITY_INFO' module='Opportunities_Commissions'}
</a>
</h4>
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_OPPORTUNITY_INFO_GROUP" style="display:block">
<table width="100%" border="0" cellspacing="1" cellpadding="0">
<tr>
<td valign="top" id='opportunity_name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_OPPORTUNITY_NAME' module='Opportunities_Commissions'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_opportunity_name_field' >
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{else}{/if}<input type="hidden" name="{$fields.opportunity_name.id_name}" id="{$fields.opportunity_name.id_name}" value="{$fields.opportunity_id.value}" />
<input type="text" name="{$fields.opportunity_name.name}" class="sqsEnabled" tabindex="100" id="{$fields.opportunity_name.name}" size="25" value="{$fields.opportunity_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{else}{/if}
<input type="button" name="btn_{$fields.opportunity_name.name}" id="btn_{$fields.opportunity_name.name}" tabindex="100" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.opportunity_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return_focus","form_name":"EditView","field_to_name_array":{"id":"opportunity_id","name":"opportunity_name","amount":"opp_amount","actual_output":"opp_quantity","account_name":"account_name"},"passthru_data":{"callback":"calcCommissionAmount"}}{/literal}, "single", true);'  />
{if !empty($isSmartView)}</div>{else}{/if}<script type="text/javascript">enableQS(false);</script>
</td>
<td valign="top" id='opp_amount_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_OPP_AMOUNT' module='Opportunities_Commissions'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_opp_amount_field' >
{counter name="panelFieldCount"}

{if strlen($fields.opp_amount.value) <= 0}
{assign var="value" value=$fields.opp_amount.default_value }
{else}
{assign var="value" value=$fields.opp_amount.value }
{/if}
{if isTypeNumber($fields.opp_amount.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.opp_amount.name}' id='{$fields.opp_amount.name}' size='30'  value='{$value}' title='' tabindex='101'  readonly="readonly" /> 

</td>
</tr>
<tr>
<td valign="top" id='account_name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACCOUNT_NAME' module='Opportunities_Commissions'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_account_name_field' >
{counter name="panelFieldCount"}

{if strlen($fields.account_name.value) <= 0}
{assign var="value" value=$fields.account_name.default_value }
{else}
{assign var="value" value=$fields.account_name.value }
{/if}
{if isTypeNumber($fields.account_name.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.account_name.name}' id='{$fields.account_name.name}' size='30' maxlength='250' value='{$value}' title='' tabindex='102'  readonly="readonly" /> 

</td>
<td valign="top" id='opp_quantity_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_OPP_QUANTITY' module='Opportunities_Commissions'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_opp_quantity_field' >
{counter name="panelFieldCount"}

{if strlen($fields.opp_quantity.value) <= 0}
{assign var="value" value=$fields.opp_quantity.default_value }
{else}
{assign var="value" value=$fields.opp_quantity.value }
{/if}
{if isTypeNumber($fields.opp_quantity.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.opp_quantity.name}' id='{$fields.opp_quantity.name}' size='30'  value='{$value}' title='' tabindex='103'  readonly="readonly" /> 

</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_OPPORTUNITY_INFO").style.display='none';</script>
{/if}
<div id="LBL_COMMISSION_INFO" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_COMMISSION_INFO');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_COMMISSION_INFO_IMG" border="0" />
{incomCRM_translate label='LBL_COMMISSION_INFO' module='Opportunities_Commissions'}
</a>
</h4>
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_COMMISSION_INFO_GROUP" style="display:block">
<table width="100%" border="0" cellspacing="1" cellpadding="0">
<tr>
<td valign="top" id='commission_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_COMMISSION' module='Opportunities_Commissions'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_commission_field' >
{counter name="panelFieldCount"}

{if strlen($fields.commission.value) <= 0}
{assign var="value" value=$fields.commission.default_value }
{else}
{assign var="value" value=$fields.commission.value }
{/if}
{if isTypeNumber($fields.commission.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.commission.name}' id='{$fields.commission.name}' size='30'  value='{$value}' title='' tabindex='104'  onblur="calcCommissionAmount();" /> 

</td>
<td valign="top" id='name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_NAME' module='Opportunities_Commissions'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_name_field' >
{counter name="panelFieldCount"}

{if strlen($fields.name.value) <= 0}
{assign var="value" value=$fields.name.default_value }
{else}
{assign var="value" value=$fields.name.value }
{/if}
{if isTypeNumber($fields.name.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.name.name}' id='{$fields.name.name}' size='30' maxlength='150' value='{$value}' title='' tabindex='105'  /> 

</td>
</tr>
<tr>
<td valign="top" id='amount_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_AMOUNT' module='Opportunities_Commissions'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_amount_field' >
{counter name="panelFieldCount"}

{if strlen($fields.amount.value) <= 0}
{assign var="value" value=$fields.amount.default_value }
{else}
{assign var="value" value=$fields.amount.value }
{/if}
{if isTypeNumber($fields.amount.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.amount.name}' id='{$fields.amount.name}' size='30'  value='{$value}' title='' tabindex='106'  onblur="calcCommissionAmount(true);" /> 

</td>
<td valign="top" id='assigned_user_name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='Opportunities_Commissions'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_assigned_user_name_field' >
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{else}{/if}<input type="hidden" name="{$fields.assigned_user_name.id_name}" id="{$fields.assigned_user_name.id_name}" value="{$fields.assigned_user_id.value}" />
<input type="text" name="{$fields.assigned_user_name.name}" class="sqsEnabled" tabindex="107" id="{$fields.assigned_user_name.name}" size="16" value="{$fields.assigned_user_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{else}{/if}
<input type="button" name="btn_{$fields.assigned_user_name.name}" id="btn_{$fields.assigned_user_name.name}" tabindex="107" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.assigned_user_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"EditView","field_to_name_array":{"id":"assigned_user_id","user_name":"assigned_user_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.assigned_user_name.name}" id="btn_clr_{$fields.assigned_user_name.name}" tabindex="107" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.assigned_user_name.name}.value=""; this.form.{$fields.assigned_user_name.id_name}.value=""; this.form.{$fields.assigned_user_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
{if !empty($isSmartView)}</div>{else}{/if}<script type="text/javascript">enableQS(false);</script>
</td>
</tr>
<tr>
<td valign="top" id='description_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DESCRIPTION' module='Opportunities_Commissions'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" colspan='3' id='_description_field' >
{counter name="panelFieldCount"}

{if empty($fields.description.value)}
{assign var="value" value=$fields.description.default_value }
{else}
{assign var="value" value=$fields.description.value }
{/if}
<textarea id="{$fields.description.name}" name="{$fields.description.name}" rows="2" cols="45" title='' tabindex="108"   style="width:95%">{$value}</textarea>
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_COMMISSION_INFO").style.display='none';</script>
{/if}

<div class="buttons" style="margin-top:15px;">
{if $bean->aclAccess("save")}<input title="{$APP.LBL_SAVE_BUTTON_TITLE}" accessKey="{$APP.LBL_SAVE_BUTTON_KEY}" class="button save" onclick="{if $isDuplicate}this.form.return_id.value=''; {/if}this.form.action.value='Save'; return check_form('EditView');" type="submit" name="button" value="{$APP.LBL_SAVE_BUTTON_LABEL}" />{/if} 
{if !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($fields.id.value))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {elseif !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($smarty.request.return_id))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {else}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='index'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {/if}
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=Opportunities_Commissions", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</div>
</form>
{$set_focus_block}
<!-- Begin Meta-Data Javascript -->
<script type="text/javascript">
function calcCommissionAmount(isAmt) {ldelim}
	if( typeof(isAmt) == "undefined" ) isAmt = false;
	var form = document.{$form_name};
	var comm = luloParseValue(form.commission.value);
	var amount = luloParseValue(form.amount.value);
	var opp_amt = luloParseValue(form.opp_amount.value);
	
	if( isAmt ) comm = (opp_amt>0)? roundUpValue( (amount * 100)/opp_amt ) : 0;
	else amount = (comm * opp_amt)/100;
	
	form.opp_amount.value = formatDisplayNumber( opp_amt );
	form.amount.value = formatDisplayNumber( amount );
	form.commission.value = comm;
	form.is_amount.value = isAmt? 1 : 0;
{rdelim}
</script>
<!-- End Meta-Data Javascript -->
</section>{literal}
<script type="text/javascript">
addToValidate('EditView', 'id', 'id', false, '{/literal}{incomCRM_translate label='LBL_ID' module='Opportunities_Commissions'}{literal}' );
addToValidate('EditView', 'date_entered_date', 'date', false, 'Ngày tạo' );
addToValidate('EditView', 'date_modified_date', 'date', false, 'Ngày cập nhật' );
addToValidate('EditView', 'modified_user_id', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_ID' module='Opportunities_Commissions'}{literal}' );
addToValidate('EditView', 'modified_by_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_NAME' module='Opportunities_Commissions'}{literal}' );
addToValidate('EditView', 'created_by', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_CREATED_ID' module='Opportunities_Commissions'}{literal}' );
addToValidate('EditView', 'created_by_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_CREATED' module='Opportunities_Commissions'}{literal}' );
addToValidate('EditView', 'deleted', 'bool', false, '{/literal}{incomCRM_translate label='LBL_DELETED' module='Opportunities_Commissions'}{literal}' );
addToValidate('EditView', 'name', 'varchar', true, '{/literal}{incomCRM_translate label='LBL_NAME' module='Opportunities_Commissions'}{literal}' );
addToValidate('EditView', 'description', 'text', false, '{/literal}{incomCRM_translate label='LBL_DESCRIPTION' module='Opportunities_Commissions'}{literal}' );
addToValidate('EditView', 'branch_id', 'enum', false, '{/literal}{incomCRM_translate label='LBL_BRANCH_ID' module='Opportunities_Commissions'}{literal}' );
addToValidate('EditView', 'department_id', 'enum', false, '{/literal}{incomCRM_translate label='LBL_DEPARTMENT_ID' module='Opportunities_Commissions'}{literal}' );
addToValidate('EditView', 'assigned_user_id', 'relate', false, '{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO_ID' module='Opportunities_Commissions'}{literal}' );
addToValidate('EditView', 'assigned_user_name', 'relate', true, '{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='Opportunities_Commissions'}{literal}' );
addToValidate('EditView', 'commission', 'float', true, '{/literal}{incomCRM_translate label='LBL_COMMISSION' module='Opportunities_Commissions'}{literal}' );
addToValidate('EditView', 'amount', 'double', true, '{/literal}{incomCRM_translate label='LBL_AMOUNT' module='Opportunities_Commissions'}{literal}' );
addToValidate('EditView', 'is_amount', 'bool', false, '{/literal}{incomCRM_translate label='LBL_IS_AMOUNT' module='Opportunities_Commissions'}{literal}' );
addToValidate('EditView', 'opportunity_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_OPPORTUNITY_ID' module='Opportunities_Commissions'}{literal}' );
addToValidate('EditView', 'opportunity_name', 'relate', true, '{/literal}{incomCRM_translate label='LBL_OPPORTUNITY_NAME' module='Opportunities_Commissions'}{literal}' );
addToValidate('EditView', 'account_name', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_NAME' module='Opportunities_Commissions'}{literal}' );
addToValidate('EditView', 'opp_amount', 'double', false, '{/literal}{incomCRM_translate label='LBL_OPP_AMOUNT' module='Opportunities_Commissions'}{literal}' );
addToValidate('EditView', 'opp_quantity', 'float', false, '{/literal}{incomCRM_translate label='LBL_OPP_QUANTITY' module='Opportunities_Commissions'}{literal}' );
addToValidateBinaryDependency('EditView', 'assigned_user_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Opportunities_Commissions'}{literal}{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO' module='Opportunities_Commissions'}{literal}', 'assigned_user_id' );
addToValidateBinaryDependency('EditView', 'opportunity_name', 'alpha', true, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Opportunities_Commissions'}{literal}{/literal}{incomCRM_translate label='LBL_OPPORTUNITY_NAME' module='Opportunities_Commissions'}{literal}', 'opportunity_id' );
</script>
<script type="text/javascript">if(typeof sqs_objects == 'undefined'){var sqs_objects = new Array;}
sqs_objects['EditView_opportunity_name'] = {"form":"EditView","method":"query","modules":["Opportunities"],"group":"or","field_list":["name","id","amount","actual_output","account_name"],"populate_list":["opportunity_name","opportunity_id","opp_amount","opp_quantity","account_name"],"required_list":["parent_id"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p","post_onblur_function":"calcCommissionAmount"};
sqs_objects['EditView_assigned_user_name'] = {"form":"EditView","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["assigned_user_name","assigned_user_id"],"required_list":["assigned_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
</script>{/literal}
