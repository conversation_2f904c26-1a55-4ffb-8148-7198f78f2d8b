<?php
// created: 2025-03-03 22:47:48
$G<PERSON><PERSON><PERSON><PERSON>["dictionary"]["EmailMan"] = array (
  'table' => 'emailman',
  'comment' => 'Email campaign queue',
  'fields' => 
  array (
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'comment' => 'Date record created',
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'comment' => 'Date record last modified',
    ),
    'user_id' => 
    array (
      'name' => 'user_id',
      'vname' => 'LBL_USER_ID',
      'type' => 'id',
      'len' => '36',
      'reportable' => false,
      'comment' => 'User ID representing assigned-to user',
    ),
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'int',
      'len' => '11',
      'auto_increment' => true,
      'comment' => 'Unique identifier',
    ),
    'campaign_id' => 
    array (
      'name' => 'campaign_id',
      'vname' => 'LBL_CAMPAIGN_ID',
      'type' => 'id',
      'reportable' => false,
      'comment' => 'ID of related campaign',
    ),
    'marketing_id' => 
    array (
      'name' => 'marketing_id',
      'vname' => 'LBL_MARKETING_ID',
      'type' => 'id',
      'reportable' => false,
      'comment' => '',
    ),
    'list_id' => 
    array (
      'name' => 'list_id',
      'vname' => 'LBL_LIST_ID',
      'type' => 'id',
      'reportable' => false,
      'len' => '36',
      'comment' => 'Associated list',
    ),
    'send_date_time' => 
    array (
      'name' => 'send_date_time',
      'vname' => 'LBL_SEND_DATE_TIME',
      'type' => 'datetime',
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_USER_ID',
      'type' => 'id',
      'reportable' => false,
      'len' => '36',
      'comment' => 'User ID who last modified record',
    ),
    'in_queue' => 
    array (
      'name' => 'in_queue',
      'vname' => 'LBL_IN_QUEUE',
      'type' => 'bool',
      'comment' => 'Flag indicating if item still in queue',
    ),
    'in_queue_date' => 
    array (
      'name' => 'in_queue_date',
      'vname' => 'LBL_IN_QUEUE_DATE',
      'type' => 'datetime',
      'comment' => 'Datetime in which item entered queue',
    ),
    'send_attempts' => 
    array (
      'name' => 'send_attempts',
      'vname' => 'LBL_SEND_ATTEMPTS',
      'type' => 'int',
      'default' => '0',
      'comment' => 'Number of attempts made to send this item',
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'related_id' => 
    array (
      'name' => 'related_id',
      'vname' => 'LBL_RELATED_ID',
      'type' => 'id',
      'reportable' => false,
      'comment' => 'ID of incomCRM object to which this item is related',
    ),
    'related_type' => 
    array (
      'name' => 'related_type',
      'vname' => 'LBL_RELATED_TYPE',
      'type' => 'varchar',
      'len' => '100',
      'comment' => 'Descriptor of the incomCRM object indicated by related_id',
    ),
    'recipient_name' => 
    array (
      'name' => 'recipient_name',
      'type' => 'varchar',
      'len' => '255',
      'source' => 'non-db',
    ),
    'recipient_email' => 
    array (
      'name' => 'recipient_email',
      'type' => 'varchar',
      'len' => '255',
      'source' => 'non-db',
    ),
    'message_name' => 
    array (
      'name' => 'message_name',
      'type' => 'varchar',
      'len' => '255',
      'source' => 'non-db',
    ),
    'campaign_name' => 
    array (
      'name' => 'campaign_name',
      'vname' => 'LBL_LIST_CAMPAIGN',
      'type' => 'varchar',
      'len' => '50',
      'source' => 'non-db',
    ),
  ),
  'indices' => 
  array (
    0 => 
    array (
      'name' => 'emailmanpk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_eman_list',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'list_id',
        1 => 'user_id',
        2 => 'deleted',
      ),
    ),
    2 => 
    array (
      'name' => 'idx_eman_campaign_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'campaign_id',
      ),
    ),
    3 => 
    array (
      'name' => 'idx_eman_relid_reltype_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'related_id',
        1 => 'related_type',
        2 => 'campaign_id',
      ),
    ),
  ),
  'custom_fields' => false,
);
?>
