<?php
// created: 2025-02-26 16:22:31
$<PERSON><PERSON><PERSON><PERSON><PERSON>["dictionary"]["Todo_List"] = array (
  'table' => 'todo_lists',
  'audited' => true,
  'unified_search' => false,
  'duplicate_merge' => false,
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'name',
      'dbType' => 'varchar',
      'len' => 250,
      'audited' => true,
      'required' => true,
      'acl' => true,
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'todo_lists_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'todo_lists_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'branch_id' => 
    array (
      'name' => 'branch_id',
      'vname' => 'LBL_BRANCH_ID',
      'type' => 'enum',
      'options' => 'users_branch_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'BranchID of assigned user',
    ),
    'department_id' => 
    array (
      'name' => 'department_id',
      'vname' => 'LBL_DEPARTMENT_ID',
      'type' => 'enum',
      'options' => 'users_department_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'DepartmentID of assigned user',
    ),
    'assigned_user_id' => 
    array (
      'name' => 'assigned_user_id',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'vname' => 'LBL_ASSIGNED_TO_ID',
      'group' => 'assigned_user_name',
      'type' => 'relate',
      'table' => 'users',
      'module' => 'Users',
      'isnull' => 'false',
      'dbType' => 'id',
      'audited' => true,
      'reportable' => true,
      'duplicate_merge' => 'disabled',
      'comment' => 'User ID assigned to record',
    ),
    'assigned_user_name' => 
    array (
      'name' => 'assigned_user_name',
      'link' => 'assigned_user_link',
      'vname' => 'LBL_ASSIGNED_TO_NAME',
      'rname' => 'user_name',
      'type' => 'relate',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'assigned_user_id',
      'module' => 'Users',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'assigned_user_link' => 
    array (
      'name' => 'assigned_user_link',
      'type' => 'link',
      'relationship' => 'todo_lists_assigned_user',
      'vname' => 'LBL_ASSIGNED_TO_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
      'duplicate_merge' => 'enabled',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'table' => 'users',
    ),
    'code' => 
    array (
      'name' => 'code',
      'vname' => 'LBL_CODE',
      'type' => 'varchar',
      'len' => 30,
      'audited' => true,
      'massupdate' => false,
    ),
    'processing_time' => 
    array (
      'name' => 'processing_time',
      'vname' => 'LBL_PROCESSING_TIME',
      'type' => 'int',
      'len' => 4,
      'audited' => true,
      'massupdate' => false,
    ),
    'cycle_type' => 
    array (
      'name' => 'cycle_type',
      'vname' => 'LBL_CYCLE_TYPE',
      'type' => 'enum',
      'options' => 'todo_list_cycle_type_dom',
      'dbType' => 'char',
      'len' => 1,
      'audited' => true,
      'massupdate' => false,
    ),
    'job_type' => 
    array (
      'name' => 'job_type',
      'vname' => 'LBL_JOB_TYPE',
      'type' => 'enum',
      'options' => 'todo_list_job_type_dom',
      'dbType' => 'char',
      'len' => 1,
      'audited' => true,
      'massupdate' => false,
      'display_default' => '1',
    ),
    'department_all' => 
    array (
      'name' => 'department_all',
      'vname' => 'LBL_DEPARTMENT_ALL',
      'type' => 'enum',
      'dbType' => 'text',
      'options' => 'department_all_dom',
      'audited' => true,
      'massupdate' => false,
      'isMultiSelect' => true,
      'isCheckBoxList' => true,
    ),
    'teams_all' => 
    array (
      'name' => 'teams_all',
      'vname' => 'LBL_TEAMS_ALL',
      'type' => 'enum',
      'dbType' => 'text',
      'options' => 'teams_all_dom',
      'audited' => true,
      'massupdate' => false,
      'isMultiSelect' => true,
      'isCheckBoxList' => true,
    ),
    'users_all' => 
    array (
      'name' => 'users_all',
      'vname' => 'LBL_USERS_ALL',
      'type' => 'enum',
      'dbType' => 'text',
      'function' => 'getAllUserOptions',
      'audited' => true,
      'massupdate' => false,
      'isMultiSelect' => true,
      'isCheckBoxList' => true,
    ),
    'group_id' => 
    array (
      'name' => 'group_id',
      'vname' => 'LBL_GROUP_ID',
      'type' => 'id',
      'source' => 'non-db',
      'audited' => false,
      'massupdate' => false,
    ),
    'group_name' => 
    array (
      'name' => 'group_name',
      'rname' => 'name',
      'id_name' => 'code',
      'vname' => 'LBL_GROUP_NAME',
      'table' => 'tasks_groups',
      'type' => 'relate',
      'link' => 'tasks_groups',
      'join_name' => 'tasks_groups',
      'isnull' => 'true',
      'module' => 'Tasks_Groups',
      'source' => 'non-db',
      'massupdate' => false,
      'additionalFields' => 
      array (
        'id' => 'group_id',
        'steps' => 'group_steps',
      ),
    ),
    'group_steps' => 
    array (
      'name' => 'group_steps',
      'vname' => 'LBL_GROUP_STEPS',
      'type' => 'text',
      'source' => 'non-db',
      'audited' => false,
      'massupdate' => false,
    ),
    'tasks_groups' => 
    array (
      'name' => 'tasks_groups',
      'type' => 'link',
      'relationship' => 'tasks_groups_todos',
      'vname' => 'LBL_TASKS_GROUPS',
      'source' => 'non-db',
    ),
    'workflow_code' => 
    array (
      'name' => 'workflow_code',
      'vname' => 'LBL_WORKFLOW_CODE',
      'type' => 'varchar',
      'len' => 30,
      'source' => 'non-db',
      'audited' => false,
      'massupdate' => false,
    ),
  ),
  'indices' => 
  array (
    'id' => 
    array (
      'name' => 'todo_listspk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    'idx_todo_lists_branch_id' => 
    array (
      'name' => 'idx_todo_lists_branch_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
      ),
    ),
    'idx_todo_lists_department_id' => 
    array (
      'name' => 'idx_todo_lists_department_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'department_id',
      ),
    ),
    'idx_todo_lists_branch_dept' => 
    array (
      'name' => 'idx_todo_lists_branch_dept',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
        1 => 'department_id',
      ),
    ),
    'idx_todo_lists_assigned' => 
    array (
      'name' => 'idx_todo_lists_assigned',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'assigned_user_id',
      ),
    ),
    0 => 
    array (
      'name' => 'idx_todo_list_id_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'id',
        1 => 'deleted',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_todo_list_name_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'name',
      ),
    ),
    2 => 
    array (
      'name' => 'idx_todo_list_code_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'code',
      ),
    ),
    3 => 
    array (
      'name' => 'idx_todo_list_cycle_type',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'cycle_type',
      ),
    ),
    4 => 
    array (
      'name' => 'idx_todo_list_job_type',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'job_type',
      ),
    ),
  ),
  'relationships' => 
  array (
    'todo_lists_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Todo_Lists',
      'rhs_table' => 'todo_lists',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'todo_lists_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Todo_Lists',
      'rhs_table' => 'todo_lists',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
    'todo_lists_assigned_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Todo_Lists',
      'rhs_table' => 'todo_lists',
      'rhs_key' => 'assigned_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'tasks_groups_todos' => 
    array (
      'lhs_module' => 'Tasks_Groups',
      'lhs_table' => 'tasks_groups',
      'lhs_key' => 'code',
      'rhs_module' => 'Todo_Lists',
      'rhs_table' => 'todo_lists',
      'rhs_key' => 'code',
      'relationship_type' => 'one-to-one',
    ),
  ),
  'optimistic_lock' => true,
  'templates' => 
  array (
    'assignable' => 'assignable',
    'groups' => 'groups',
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
