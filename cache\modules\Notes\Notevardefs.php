<?php
// created: 2025-02-26 15:23:55
$<PERSON><PERSON><PERSON><PERSON><PERSON>["dictionary"]["Note"] = array (
  'table' => 'notes',
  'comment' => 'Notes and Attachments',
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NOTE_SUBJECT',
      'dbType' => 'varchar',
      'type' => 'name',
      'len' => '255',
      'comment' => 'Name of the note',
      'importable' => 'required',
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
      'comment' => 'Full text of the note',
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'notes_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'notes_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'filename' => 
    array (
      'name' => 'filename',
      'vname' => 'LBL_FILENAME',
      'type' => 'varchar',
      'len' => '255',
      'reportable' => true,
      'comment' => 'File name associated with the note (attachment)',
    ),
    'file_mime_type' => 
    array (
      'name' => 'file_mime_type',
      'vname' => 'LBL_FILE_MIME_TYPE',
      'type' => 'varchar',
      'len' => '100',
      'comment' => 'Attachment MIME type',
    ),
    'file_url' => 
    array (
      'name' => 'file_url',
      'vname' => 'LBL_FILE_URL',
      'type' => 'function',
      'function_require' => 'include/upload_file.php',
      'function_class' => 'UploadFile',
      'function_name' => 'get_url',
      'function_params' => 
      array (
        0 => 'filename',
        1 => 'id',
        2 => 'date_entered',
      ),
      'source' => 'function',
      'reportable' => false,
      'comment' => 'Path to file (can be URL)',
    ),
    'portal_flag' => 
    array (
      'name' => 'portal_flag',
      'vname' => 'LBL_PORTAL_FLAG',
      'type' => 'bool',
      'required' => true,
      'massupdate' => false,
      'comment' => 'Portal flag indicator determines if note created via portal',
    ),
    'embed_flag' => 
    array (
      'name' => 'embed_flag',
      'vname' => 'LBL_EMBED_FLAG',
      'type' => 'bool',
      'default' => 0,
      'required' => true,
      'massupdate' => false,
      'comment' => 'Embed flag indicator determines if note embedded in email',
    ),
    'parent_type' => 
    array (
      'name' => 'parent_type',
      'vname' => 'LBL_PARENT_TYPE',
      'type' => 'parent_type',
      'dbType' => 'varchar',
      'group' => 'parent_name',
      'len' => '25',
      'comment' => 'incomCRM module the Note is associated with',
    ),
    'parent_id' => 
    array (
      'name' => 'parent_id',
      'vname' => 'LBL_PARENT_ID',
      'type' => 'id',
      'required' => false,
      'reportable' => true,
      'comment' => 'The ID of the incomCRM item specified in parent_type',
    ),
    'parent_name' => 
    array (
      'name' => 'parent_name',
      'parent_type' => 'record_type_display',
      'type_name' => 'parent_type',
      'id_name' => 'parent_id',
      'vname' => 'LBL_RELATED_TO',
      'type' => 'parent',
      'source' => 'non-db',
      'options' => 'record_type_display_notes',
    ),
    'contact_id' => 
    array (
      'name' => 'contact_id',
      'vname' => 'LBL_CONTACT_ID',
      'type' => 'id',
      'required' => false,
      'reportable' => false,
      'comment' => 'Contact ID note is associated with',
    ),
    'contact_name' => 
    array (
      'name' => 'contact_name',
      'rname' => 'last_name',
      'id_name' => 'contact_id',
      'vname' => 'LBL_CONTACT_NAME',
      'table' => 'contacts',
      'type' => 'relate',
      'link' => 'contact',
      'join_name' => 'contacts',
      'db_concat_fields' => 
      array (
        0 => 'first_name',
        1 => 'last_name',
      ),
      'isnull' => 'true',
      'module' => 'Contacts',
      'source' => 'non-db',
    ),
    'contact_phone' => 
    array (
      'name' => 'contact_phone',
      'vname' => 'LBL_PHONE',
      'type' => 'phone',
      'source' => 'non-db',
    ),
    'contact_email' => 
    array (
      'name' => 'contact_email',
      'type' => 'varchar',
      'vname' => 'LBL_EMAIL_ADDRESS',
      'source' => 'non-db',
    ),
    'account_id' => 
    array (
      'name' => 'account_id',
      'vname' => 'LBL_ACCOUNT_ID',
      'type' => 'id',
      'reportable' => false,
      'source' => 'non-db',
    ),
    'opportunity_id' => 
    array (
      'name' => 'opportunity_id',
      'vname' => 'LBL_OPPORTUNITY_ID',
      'type' => 'id',
      'reportable' => false,
      'source' => 'non-db',
    ),
    'acase_id' => 
    array (
      'name' => 'acase_id',
      'vname' => 'LBL_CASE_ID',
      'type' => 'id',
      'reportable' => false,
      'source' => 'non-db',
    ),
    'lead_id' => 
    array (
      'name' => 'lead_id',
      'vname' => 'LBL_LEAD_ID',
      'type' => 'id',
      'reportable' => false,
      'source' => 'non-db',
    ),
    'contact' => 
    array (
      'name' => 'contact',
      'type' => 'link',
      'relationship' => 'contact_notes',
      'vname' => 'LBL_LIST_CONTACT_NAME',
      'source' => 'non-db',
    ),
    'cases' => 
    array (
      'name' => 'cases',
      'type' => 'link',
      'relationship' => 'case_notes',
      'vname' => 'LBL_CASES',
      'source' => 'non-db',
    ),
    'accounts' => 
    array (
      'name' => 'accounts',
      'type' => 'link',
      'relationship' => 'account_notes',
      'source' => 'non-db',
      'vname' => 'LBL_ACCOUNTS',
    ),
    'opportunities' => 
    array (
      'name' => 'opportunities',
      'type' => 'link',
      'relationship' => 'opportunity_notes',
      'source' => 'non-db',
      'vname' => 'LBL_OPPORTUNITIES',
    ),
    'leads' => 
    array (
      'name' => 'leads',
      'type' => 'link',
      'relationship' => 'lead_notes',
      'source' => 'non-db',
      'vname' => 'LBL_LEADS',
    ),
    'bugs' => 
    array (
      'name' => 'bugs',
      'type' => 'link',
      'relationship' => 'bug_notes',
      'source' => 'non-db',
      'vname' => 'LBL_BUGS',
    ),
    'emails' => 
    array (
      'name' => 'emails',
      'vname' => 'LBL_EMAILS',
      'type' => 'link',
      'relationship' => 'emails_notes_rel',
      'source' => 'non-db',
    ),
    'projects' => 
    array (
      'name' => 'projects',
      'type' => 'link',
      'relationship' => 'projects_notes',
      'source' => 'non-db',
      'vname' => 'LBL_PROJECTS',
    ),
    'project_tasks' => 
    array (
      'name' => 'project_tasks',
      'type' => 'link',
      'relationship' => 'project_tasks_notes',
      'source' => 'non-db',
      'vname' => 'LBL_PROJECT_TASKS',
    ),
    'meetings' => 
    array (
      'name' => 'meetings',
      'type' => 'link',
      'relationship' => 'meetings_notes',
      'source' => 'non-db',
      'vname' => 'LBL_MEETINGS',
    ),
    'calls' => 
    array (
      'name' => 'calls',
      'type' => 'link',
      'relationship' => 'calls_notes',
      'source' => 'non-db',
      'vname' => 'LBL_CALLS',
    ),
    'tasks' => 
    array (
      'name' => 'tasks',
      'type' => 'link',
      'relationship' => 'tasks_notes',
      'source' => 'non-db',
      'vname' => 'LBL_TASKS',
    ),
    'SecurityGroups' => 
    array (
      'name' => 'SecurityGroups',
      'type' => 'link',
      'relationship' => 'securitygroups_notes',
      'module' => 'SecurityGroups',
      'bean_name' => 'SecurityGroup',
      'source' => 'non-db',
      'vname' => 'LBL_SECURITYGROUPS',
    ),
    'approved' => 
    array (
      'name' => 'approved',
      'vname' => 'LBL_APPROVED',
      'type' => 'enum',
      'options' => 'notes_approved_dom',
      'dbType' => 'char',
      'len' => 1,
      'massupdate' => false,
    ),
    'date_approved' => 
    array (
      'name' => 'date_approved',
      'vname' => 'LBL_DATE_APPROVED',
      'type' => 'datetime',
      'massupdate' => false,
    ),
    'approve_user_id' => 
    array (
      'name' => 'approve_user_id',
      'vname' => 'LBL_APPROVE_USER_ID',
      'type' => 'id',
      'audited' => true,
      'massupdate' => false,
    ),
    'approve_user_name' => 
    array (
      'name' => 'approve_user_name',
      'rname' => 'user_name',
      'id_name' => 'approve_user_id',
      'vname' => 'LBL_APPROVE_USER_NAME',
      'source' => 'non-db',
      'type' => 'relate',
      'link' => 'approve_users',
      'join_name' => 'approves',
      'table' => 'users',
      'module' => 'Users',
      'massupdate' => false,
    ),
    'approve_users' => 
    array (
      'name' => 'approve_users',
      'vname' => 'LBL_APPROVE_USERS',
      'type' => 'link',
      'source' => 'non-db',
      'relationship' => 'approve_users_notes',
    ),
  ),
  'relationships' => 
  array (
    'notes_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Notes',
      'rhs_table' => 'notes',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'notes_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Notes',
      'rhs_table' => 'notes',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
    'approve_users_notes' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Notes',
      'rhs_table' => 'notes',
      'rhs_key' => 'approve_user_id',
      'relationship_type' => 'one-to-many',
    ),
  ),
  'indices' => 
  array (
    'id' => 
    array (
      'name' => 'notespk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    0 => 
    array (
      'name' => 'idx_note_name',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'name',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_notes_parent',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'parent_id',
        1 => 'parent_type',
      ),
    ),
    2 => 
    array (
      'name' => 'idx_note_contact',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'contact_id',
      ),
    ),
    3 => 
    array (
      'name' => 'idx_note_del_approved',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'approved',
      ),
    ),
    4 => 
    array (
      'name' => 'idx_note_del_appr_user',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'approve_user_id',
      ),
    ),
  ),
  'optimistic_locking' => true,
  'templates' => 
  array (
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
