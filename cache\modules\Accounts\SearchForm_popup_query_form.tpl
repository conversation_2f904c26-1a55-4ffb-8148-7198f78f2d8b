

{math equation="floor(left / right)"
	left=12
		right=$templateMeta.maxColumns
			assign=colClass
	}

<section class="container2 search-form ml-0 search_form_adv">
	<div class="row">
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="name_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_NAME' module='Accounts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.name_advanced.value) <= 0}
	{assign var="value" value=$fields.name_advanced.default_value }
{else}
	{assign var="value" value=$fields.name_advanced.value }
{/if}
{if isTypeNumber($fields.name_advanced.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.name_advanced.name}' id='{$fields.name_advanced.name}' size='30' maxlength='200' value='{$value}' title='' tabindex=''  /> 

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="address_street_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_ANY_ADDRESS' module='Accounts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.address_street_advanced.value) <= 0}
	{assign var="value" value=$fields.address_street_advanced.default_value }
{else}
	{assign var="value" value=$fields.address_street_advanced.value }
{/if}
{if isTypeNumber($fields.address_street_advanced.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.address_street_advanced.name}' id='{$fields.address_street_advanced.name}' size='30'  value='{$value}' title='' tabindex=''  /> 

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="keywords_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_KEYWORDS' module='Accounts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.keywords_advanced.value) <= 0}
	{assign var="value" value=$fields.keywords_advanced.default_value }
{else}
	{assign var="value" value=$fields.keywords_advanced.value }
{/if}
{if isTypeNumber($fields.keywords_advanced.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.keywords_advanced.name}' id='{$fields.keywords_advanced.name}' size='30'  value='{$value}' title='' tabindex=''  /> 

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="phone_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_ANY_PHONE' module='Accounts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.phone_advanced.value) <= 0}
	{assign var="value" value=$fields.phone_advanced.default_value }
{else}
	{assign var="value" value=$fields.phone_advanced.value }
{/if}
{if isTypeNumber($fields.phone_advanced.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.phone_advanced.name}' id='{$fields.phone_advanced.name}' size='30'  value='{$value}' title='' tabindex=''  /> 

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="account_type_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_TYPE' module='Accounts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="account_type_advanced[]" id="{$fields.account_type_advanced.name}" size="4" multiple="1"  >
{html_options options=$fields.account_type_advanced.options selected=$fields.account_type_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="location_city_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_LOCATION_CITY' module='Accounts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="location_city_advanced[]" id="{$fields.location_city_advanced.name}" size="4" multiple="1"  onchange="changeParentSelectedOption(this, this.form.location_district_advanced, 'location_district_dom')" >
{html_options options=$fields.location_city_advanced.options selected=$fields.location_city_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="department_manager_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_DEPARTMENT_MANAGER' module='Accounts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="department_manager_advanced[]" id="{$fields.department_manager_advanced.name}" size="4" multiple="1"  >
{html_options options=$fields.department_manager_advanced.options selected=$fields.department_manager_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="location_district_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_LOCATION_DISTRICT' module='Accounts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="location_district_advanced[]" id="{$fields.location_district_advanced.name}" size="4" multiple="1"  >
{html_options options=$fields.location_district_advanced.options selected=$fields.location_district_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="department_id_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_DEPARTMENT_ID' module='Accounts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="department_id_advanced[]" id="{$fields.department_id_advanced.name}" size="4" multiple="1"  onchange="changeDepartmentUsers(this, this.form.assigned_user_id_advanced)" >
{html_options options=$fields.department_id_advanced.options selected=$fields.department_id_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="assigned_user_id_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_ASSIGNED_TO' module='Accounts'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="assigned_user_id_advanced[]" id="{$fields.assigned_user_id_advanced.name}" size="4" multiple="1"  >
{html_options options=$fields.assigned_user_id_advanced.options selected=$fields.assigned_user_id_advanced.value}
</select>

									</div>
			</div>
				</div>
	</div>
</section>

{if $HAS_ADVANCED_SEARCH}
<table cellspacing="0" cellpadding="0" border="0" class="mt-10">
<tr>
	<td scope="row" nowrap="nowrap" class="pt-10">
		<a style="font-size:10pt; text-decoration:none;" onclick="incomCRM.searchForm.searchFormSelect('{$module}|basic_search','{$module}|advanced_search')" href="#">[ {$APP.LNK_BASIC_SEARCH} ]</a>
	</td>
</tr>
<tr>
	<td>&nbsp;</td>
</tr>
</table>
{/if}

{if $DISPLAY_SAVED_SEARCH}
<div class="saved-search-adv table-responsive mt-10 pt-10">
<table cellspacing="0" cellpadding="0" border="0">
<tr>
	<td rowspan="2" width="40%">
		<a class='tabFormAdvLink' onhover href='javascript:toggleInlineSearch()'>
		<img src='{incomCRM_getimagepath file="advanced_search.gif"}' id='up_down_img' border="0" />&nbsp;{$APP.LNK_SAVED_VIEWS}
		</a><br/>
		<input type='hidden' id='showSSDIV' name='showSSDIV' value='{$SHOWSSDIV}' />
	</td>
	<td scope='row' width="20%" nowrap="nowrap">
		{incomCRM_translate label='LBL_SAVE_SEARCH_AS' module='SavedSearch'}:
	</td>
	<td nowrap>
		<input type='text' name='saved_search_name' />
		<input type='hidden' name='search_module' value='' />
		<input type='hidden' name='saved_search_action' value='' />
		<input title='{$APP.LBL_SAVE_BUTTON_LABEL}' value='{$APP.LBL_SAVE_BUTTON_LABEL}' class='button' type='button' name='saved_search_submit' onclick='incomCRM.savedViews.setChooser(); return incomCRM.savedViews.saved_search_action("save");' />
	</td>
</tr>
<tr>
	<td scope='row' nowrap="nowrap">
		{incomCRM_translate label='LBL_MODIFY_CURRENT_SEARCH' module='SavedSearch'}:
	</td>
	<td nowrap>
		<input class='button' onclick='incomCRM.savedViews.setChooser(); return incomCRM.savedViews.saved_search_action("update")' value='{$APP.LBL_UPDATE}' title='{$APP.LBL_UPDATE}' name='ss_update' id='ss_update' type='button' />
		<input class='button' onclick='return incomCRM.savedViews.saved_search_action("delete", "{incomCRM_translate label='LBL_DELETE_CONFIRM' module='SavedSearch'}")' value='{$APP.LBL_DELETE}' title='{$APP.LBL_DELETE}' name='ss_delete' id='ss_delete' type='button' />
		<br/><span id='curr_search_name'></span>
	</td>
</tr>
<tr>
	<td colspan="3">
		<div style="{$DISPLAYSS}" id="inlineSavedSearch">{$SAVED_SEARCH}</div>
	</td>
</tr>
</table>
</div>
{/if}

<script type="text/javascript">
{literal}
if( typeof(loadSSL_Scripts) == 'function' ) {
	loadSSL_Scripts();
}
YAHOO.util.Event.onDOMReady(function(){
	var form = null;
	if( document.search_form ) form = document.search_form;
	else if( document.popup_query_form ) form = document.popup_query_form;
	else return;
	if( form ) {
		if( form.branch_id_advanced ) {
			if( form.department_id_advanced )
				changeBranchDepartments(form.branch_id_advanced, form.department_id_advanced);
			else if( form.department_advanced )
				changeBranchDepartments(form.branch_id_advanced, form.department_advanced);
		}
		if( form.department_id_advanced && form.assigned_user_id_advanced )
			changeDepartmentUsers(form.department_id_advanced, form.assigned_user_id_advanced);
		if( form.location_district_advanced && form.location_city_advanced )
			changeParentSelectedOption(form.location_city_advanced, form.location_district_advanced, 'location_district_dom');
	}
});
{/literal}
</script>

{literal}<script type="text/javascript">if(typeof sqs_objects == 'undefined'){var sqs_objects = new Array;}
sqs_objects['popup_query_form_modified_by_name_advanced'] = {"form":"popup_query_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["modified_by_name_advanced","modified_user_id_advanced"],"required_list":["modified_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['popup_query_form_created_by_name_advanced'] = {"form":"popup_query_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["created_by_name_advanced","created_by_advanced"],"required_list":["created_by"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['popup_query_form_assigned_user_name_advanced'] = {"form":"popup_query_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["assigned_user_name_advanced","assigned_user_id_advanced"],"required_list":["assigned_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['popup_query_form_parent_name_advanced'] = {"form":"popup_query_form","method":"query","modules":["Accounts"],"group":"or","field_list":["name","id"],"populate_list":["popup_query_form_parent_name_advanced","parent_id_advanced"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"required_list":["parent_id"],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['popup_query_form_campaign_name_advanced'] = {"form":"popup_query_form","method":"query","modules":["Campaigns"],"group":"or","field_list":["name","id"],"populate_list":["campaign_name_advanced","campaign_id_advanced"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"required_list":["campaign_id"],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['popup_query_form_account_group_name_advanced'] = {"form":"popup_query_form","method":"query","modules":["Account_Groups"],"group":"or","field_list":["name","id"],"populate_list":["account_group_name_advanced","account_group_id_advanced"],"required_list":["parent_id"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['popup_query_form_sales_line_name_advanced'] = {"form":"popup_query_form","method":"query","modules":["Sales_Lines"],"group":"or","field_list":["name","id"],"populate_list":["sales_line_name_advanced","sales_line_id_advanced"],"required_list":["parent_id"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['popup_query_form_primary_contact_name_advanced'] = {"form":"popup_query_form","method":"get_contact_array","modules":["Contacts"],"field_list":["salutation","first_name","last_name","id"],"populate_list":["primary_contact_name_advanced","primary_contact_id_advanced","primary_contact_id_advanced","primary_contact_id_advanced"],"required_list":["primary_contact_id"],"group":"or","conditions":[{"name":"first_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"last_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['popup_query_form_secondary_contact_name_advanced'] = {"form":"popup_query_form","method":"get_contact_array","modules":["Contacts"],"field_list":["salutation","first_name","last_name","id"],"populate_list":["secondary_contact_name_advanced","secondary_contact_id_advanced","secondary_contact_id_advanced","secondary_contact_id_advanced"],"required_list":["secondary_contact_id"],"group":"or","conditions":[{"name":"first_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"last_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['popup_query_form_debt_user_name_advanced'] = {"form":"popup_query_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["debt_user_name_advanced","debt_user_id_advanced"],"required_list":["debt_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['popup_query_form_support_user_name_advanced'] = {"form":"popup_query_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["support_user_name_advanced","support_user_id_advanced"],"required_list":["support_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
</script>{/literal}