
<section id="EditViewViewBody" class="view-module-{$module|strtolower}">

<form action="index.php" method="POST" name="{$form_name}" id="{$form_id}" class="_edit-{$instanceName}" {$enctype}>
<input type="hidden" name="module" value="{$module}" />
<input type="hidden" name="action" id="action" />
{if isset($smarty.request.isDuplicate) && $smarty.request.isDuplicate eq "true"}
<input type="hidden" name="record" id="record" value="" />
<input type="hidden" name="duplicateSave" value="true" />
{else}
<input type="hidden" name="record" id="record" value="{$fields.id.value}" />
{/if}
<input type="hidden" name="return_module" value="{$smarty.request.return_module}" />
<input type="hidden" name="return_action" value="{if $smarty.request.return_action eq 'LoadTabSubpanels'}DetailView{else}{$smarty.request.return_action}{/if}" />
<input type="hidden" name="return_id" value="{$smarty.request.return_id}" />
<input type="hidden" name="isDuplicate" value="false" />
<input type="hidden" name="module_tab" /> 
<input type="hidden" name="contact_role" />
{if !empty($smarty.request.return_module)}
<input type="hidden" name="relate_to" value="{if $smarty.request.return_relationship}{$smarty.request.return_relationship}{else}{$smarty.request.return_module}{/if}" />
<input type="hidden" name="relate_id" value="{$smarty.request.return_id}" />
{/if}
<input type="hidden" name="offset" value="{$offset}" />
{if empty($fields.id.value)}
<input type="hidden" name="_notify_random" value="{php}echo create_guid();{/php}" />
{/if}
<input type="hidden" name="isFromEditView" value="{$module}" />
<table width="100%" cellpadding="0" cellspacing="0" border="0">
<tr>
<td class="buttons">
{if $bean->aclAccess("save")}<input title="{$APP.LBL_SAVE_BUTTON_TITLE}" accessKey="{$APP.LBL_SAVE_BUTTON_KEY}" class="button save" onclick="{if $isDuplicate}this.form.return_id.value=''; {/if}this.form.action.value='Save'; return check_form('EditView');" type="submit" name="button" value="{$APP.LBL_SAVE_BUTTON_LABEL}" />{/if} 
{if !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($fields.id.value))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {elseif !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($smarty.request.return_id))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {else}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='index'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {/if}
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=Revenue_Promotions", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</td>
</tr>
</table>
{incomCRM_include include=$includes}
<table width="100%" cellspacing="0" cellpadding="0" class='detail view' id='tabFormPagination'>
{$PAGINATION}
</table>
<div id="DEFAULT" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="DEFAULT_GROUP" style="display:block">
<table width="100%" border="0" cellspacing="1" cellpadding="0">
<tr>
<td valign="top" id='name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_NAME' module='Revenue_Promotions'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_name_field' >
{counter name="panelFieldCount"}

{if strlen($fields.name.value) <= 0}
{assign var="value" value=$fields.name.default_value }
{else}
{assign var="value" value=$fields.name.value }
{/if}
{if isTypeNumber($fields.name.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.name.name}' id='{$fields.name.name}' size='30' maxlength='250' value='{$value}' title='' tabindex='100'  /> 

</td>
<td valign="top" id='month_amount_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_MONTH_AMOUNT' module='Revenue_Promotions'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_month_amount_field' >
{counter name="panelFieldCount"}

{if strlen($fields.month_amount.value) <= 0}
{assign var="value" value=$fields.month_amount.default_value }
{else}
{assign var="value" value=$fields.month_amount.value }
{/if}
{if isTypeNumber($fields.month_amount.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.month_amount.name}' id='{$fields.month_amount.name}' size='30'  value='{$value}' title='' tabindex='101'  onblur="formatNumberRound(this);" /> 

</td>
</tr>
<tr>
<td valign="top" id='date_valid_from_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DATE_VALID_FROM' module='Revenue_Promotions'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_date_valid_from_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.date_valid_from.value }
<input autocomplete="off" type="text" name="{$fields.date_valid_from.name}" id="{$fields.date_valid_from.name}" value="{$date_value}" title=''  tabindex='102' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.date_valid_from.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.date_valid_from.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.date_valid_from.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
<td valign="top" id='qt_amount_1_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_QT_AMOUNT_1' module='Revenue_Promotions'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_qt_amount_1_field' >
{counter name="panelFieldCount"}

{if strlen($fields.qt_amount_1.value) <= 0}
{assign var="value" value=$fields.qt_amount_1.default_value }
{else}
{assign var="value" value=$fields.qt_amount_1.value }
{/if}
{if isTypeNumber($fields.qt_amount_1.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.qt_amount_1.name}' id='{$fields.qt_amount_1.name}' size='30'  value='{$value}' title='' tabindex='103'  onblur="formatNumberRound(this);" /> 

</td>
</tr>
<tr>
<td valign="top" id='date_valid_to_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DATE_VALID_TO' module='Revenue_Promotions'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_date_valid_to_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.date_valid_to.value }
<input autocomplete="off" type="text" name="{$fields.date_valid_to.name}" id="{$fields.date_valid_to.name}" value="{$date_value}" title=''  tabindex='104' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.date_valid_to.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.date_valid_to.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.date_valid_to.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
<td valign="top" id='qt_amount_2_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_QT_AMOUNT_2' module='Revenue_Promotions'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_qt_amount_2_field' >
{counter name="panelFieldCount"}

{if strlen($fields.qt_amount_2.value) <= 0}
{assign var="value" value=$fields.qt_amount_2.default_value }
{else}
{assign var="value" value=$fields.qt_amount_2.value }
{/if}
{if isTypeNumber($fields.qt_amount_2.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.qt_amount_2.name}' id='{$fields.qt_amount_2.name}' size='30'  value='{$value}' title='' tabindex='105'  onblur="formatNumberRound(this);" /> 

</td>
</tr>
<tr>
<td valign="top" id='month_discount_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_MONTH_DISCOUNT' module='Revenue_Promotions'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_month_discount_field' >
{counter name="panelFieldCount"}

{if strlen($fields.month_discount.value) <= 0}
{assign var="value" value=$fields.month_discount.default_value }
{else}
{assign var="value" value=$fields.month_discount.value }
{/if}
{if isTypeNumber($fields.month_discount.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.month_discount.name}' id='{$fields.month_discount.name}' size='30'  value='{$value}' title='' tabindex='106'  /> 

</td>
<td valign="top" id='qt_amount_3_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_QT_AMOUNT_3' module='Revenue_Promotions'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_qt_amount_3_field' >
{counter name="panelFieldCount"}

{if strlen($fields.qt_amount_3.value) <= 0}
{assign var="value" value=$fields.qt_amount_3.default_value }
{else}
{assign var="value" value=$fields.qt_amount_3.value }
{/if}
{if isTypeNumber($fields.qt_amount_3.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.qt_amount_3.name}' id='{$fields.qt_amount_3.name}' size='30'  value='{$value}' title='' tabindex='107'  onblur="formatNumberRound(this);" /> 

</td>
</tr>
<tr>
<td valign="top" id='quarter_discount_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_QUARTER_DISCOUNT' module='Revenue_Promotions'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_quarter_discount_field' >
{counter name="panelFieldCount"}

{if strlen($fields.quarter_discount.value) <= 0}
{assign var="value" value=$fields.quarter_discount.default_value }
{else}
{assign var="value" value=$fields.quarter_discount.value }
{/if}
{if isTypeNumber($fields.quarter_discount.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.quarter_discount.name}' id='{$fields.quarter_discount.name}' size='30'  value='{$value}' title='' tabindex='108'  /> 

</td>
<td valign="top" id='qt_amount_4_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_QT_AMOUNT_4' module='Revenue_Promotions'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_qt_amount_4_field' >
{counter name="panelFieldCount"}

{if strlen($fields.qt_amount_4.value) <= 0}
{assign var="value" value=$fields.qt_amount_4.default_value }
{else}
{assign var="value" value=$fields.qt_amount_4.value }
{/if}
{if isTypeNumber($fields.qt_amount_4.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.qt_amount_4.name}' id='{$fields.qt_amount_4.name}' size='30'  value='{$value}' title='' tabindex='109'  onblur="formatNumberRound(this);" /> 

</td>
</tr>
<tr>
<td valign="top" id='year_discount_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_YEAR_DISCOUNT' module='Revenue_Promotions'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_year_discount_field' >
{counter name="panelFieldCount"}

{if strlen($fields.year_discount.value) <= 0}
{assign var="value" value=$fields.year_discount.default_value }
{else}
{assign var="value" value=$fields.year_discount.value }
{/if}
{if isTypeNumber($fields.year_discount.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.year_discount.name}' id='{$fields.year_discount.name}' size='30'  value='{$value}' title='' tabindex='110'  /> 

</td>
<td valign="top" id='year_amount_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_YEAR_AMOUNT' module='Revenue_Promotions'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_year_amount_field' >
{counter name="panelFieldCount"}

{if strlen($fields.year_amount.value) <= 0}
{assign var="value" value=$fields.year_amount.default_value }
{else}
{assign var="value" value=$fields.year_amount.value }
{/if}
{if isTypeNumber($fields.year_amount.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.year_amount.name}' id='{$fields.year_amount.name}' size='30'  value='{$value}' title='' tabindex='111'  onblur="formatNumberRound(this);" /> 

</td>
</tr>
<tr>
<td valign="top" id='status_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_STATUS' module='Revenue_Promotions'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_status_field' >
{counter name="panelFieldCount"}

<select name="{$fields.status.name}" id="{$fields.status.name}" title='' tabindex="112"  >
{if isset($fields.status.value) && $fields.status.value != ''}
{html_options options=$fields.status.options selected=$fields.status.value}
{else}
{html_options options=$fields.status.options selected=$fields.status.default}
{/if}
</select>
</td>
<td valign="top" id='qt_target_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_QT_TARGET' module='Revenue_Promotions'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_qt_target_field' >
{counter name="panelFieldCount"}

{if strlen($fields.qt_target.value) <= 0}
{assign var="value" value=$fields.qt_target.default_value }
{else}
{assign var="value" value=$fields.qt_target.value }
{/if}
{if isTypeNumber($fields.qt_target.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.qt_target.name}' id='{$fields.qt_target.name}' size='30'  value='{$value}' title='' tabindex='113'  /> 

</td>
</tr>
<tr>
<td valign="top" id='prod_series_teams_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_PROD_SERIES_TEAMS' module='Revenue_Promotions'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" colspan='3' id='_prod_series_teams_field' >
{counter name="panelFieldCount"}

{incomCRM_peoplebox options=$fields.prod_series_teams.options default=$fields.prod_series_teams.value module="TQT_ProductSeries" assign="values"}
<input type="hidden" id="{$fields.prod_series_teams.name}_multiselect" name="{$fields.prod_series_teams.name}_multiselect" value="true" />
<div class="people"></div>
<script type="text/javascript">
LULO.peopleBox.add( "{$fields.prod_series_teams.name}", {ldelim}form:'{$form_name}', module:'TQT_ProductSeries', value:{$values|default:'null'}, data:{literal}{"method":"peopleBox","modules":["TQT_ProductSeries"],"group":"or","field_list":["name","id"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"order":"name","limit":"30"}{/literal} {rdelim});
</script>
</td>
</tr>
<tr>
<td valign="top" id='account_promotion_all_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACCOUNT_PROMOTION_ALL' module='Revenue_Promotions'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_account_promotion_all_field' >
{counter name="panelFieldCount"}
<input type="hidden" id="{$fields.account_promotion_all.name}_multiselect" name="{$fields.account_promotion_all.name}_multiselect" value="true" />
{multienum_to_array string=$fields.account_promotion_all.value default=$fields.account_promotion_all.default assign="values"}
<div style="" id="{$fields.account_promotion_all.name}_multi_grp" class="multi-enum-grp multi-enum-grp-default">
{counter name="rowCount" start=0 print=false assign="rowCount"}
{foreach from=$fields.account_promotion_all.options item=option key=value}
{if $value}
<label id="_grp_{if isset($fields.account_promotion_all.groupBy) and isset($fields.account_promotion_all.groupBy[$value])}{ $fields.account_promotion_all.groupBy[$value]}{/if}" style="white-space: nowrap">
<input type="checkbox" id="{$fields.account_promotion_all.name}_checkbox{$rowCount}" name="{$fields.account_promotion_all.name}[]" value="{$value}" {if in_array(strval($value), $values)}checked="checked"{/if}  /> {$option}
</label>
{counter name="rowCount"}
{/if}
{/foreach}
</div>
</td>
<td valign="top" id='branch_all_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_BRANCH_ALL' module='Revenue_Promotions'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_branch_all_field' >
{counter name="panelFieldCount"}
<input type="hidden" id="{$fields.branch_all.name}_multiselect" name="{$fields.branch_all.name}_multiselect" value="true" />
{multienum_to_array string=$fields.branch_all.value default=$fields.branch_all.default assign="values"}
<div style="" id="{$fields.branch_all.name}_multi_grp" class="multi-enum-grp multi-enum-grp-default">
{counter name="rowCount" start=0 print=false assign="rowCount"}
{foreach from=$fields.branch_all.options item=option key=value}
{if $value}
<label id="_grp_{if isset($fields.branch_all.groupBy) and isset($fields.branch_all.groupBy[$value])}{ $fields.branch_all.groupBy[$value]}{/if}" style="white-space: nowrap">
<input type="checkbox" id="{$fields.branch_all.name}_checkbox{$rowCount}" name="{$fields.branch_all.name}[]" value="{$value}" {if in_array(strval($value), $values)}checked="checked"{/if}  /> {$option}
</label>
{counter name="rowCount"}
{/if}
{/foreach}
</div>
</td>
</tr>
<tr>
<td valign="top" id='description_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DESCRIPTION' module='Revenue_Promotions'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_description_field' >
{counter name="panelFieldCount"}

{if empty($fields.description.value)}
{assign var="value" value=$fields.description.default_value }
{else}
{assign var="value" value=$fields.description.value }
{/if}
<textarea id="{$fields.description.name}" name="{$fields.description.name}" rows="3" cols="45" title='' tabindex="117"  >{$value}</textarea>
</td>
<td valign="top" id='assigned_user_name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='Revenue_Promotions'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_assigned_user_name_field' >
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{/if}<input type="hidden" name="{$fields.assigned_user_name.id_name}" id="{$fields.assigned_user_name.id_name}" value="{$fields.assigned_user_id.value}" />
<input type="text" name="{$fields.assigned_user_name.name}" class="sqsEnabled" tabindex="118" id="{$fields.assigned_user_name.name}" size="16" value="{$fields.assigned_user_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{/if}
<input type="button" name="btn_{$fields.assigned_user_name.name}" id="btn_{$fields.assigned_user_name.name}" tabindex="118" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.assigned_user_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"EditView","field_to_name_array":{"id":"assigned_user_id","user_name":"assigned_user_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.assigned_user_name.name}" id="btn_clr_{$fields.assigned_user_name.name}" tabindex="118" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.assigned_user_name.name}.value=""; this.form.{$fields.assigned_user_name.id_name}.value=""; this.form.{$fields.assigned_user_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
{if !empty($isSmartView)}</div>{/if}<script type="text/javascript">enableQS(false);</script>
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("DEFAULT").style.display='none';</script>
{/if}

<div class="buttons" style="margin-top:15px;">
{if $bean->aclAccess("save")}<input title="{$APP.LBL_SAVE_BUTTON_TITLE}" accessKey="{$APP.LBL_SAVE_BUTTON_KEY}" class="button save" onclick="{if $isDuplicate}this.form.return_id.value=''; {/if}this.form.action.value='Save'; return check_form('EditView');" type="submit" name="button" value="{$APP.LBL_SAVE_BUTTON_LABEL}" />{/if} 
{if !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($fields.id.value))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {elseif !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($smarty.request.return_id))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {else}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='index'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {/if}
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=Revenue_Promotions", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</div>
</form>
{$set_focus_block}
</section>{literal}
<script type="text/javascript">
addToValidate('EditView', 'id', 'id', false, '{/literal}{incomCRM_translate label='LBL_ID' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'date_entered_date', 'date', false, 'Ngày tạo' );
addToValidate('EditView', 'date_modified_date', 'date', false, 'Ngày cập nhật' );
addToValidate('EditView', 'modified_user_id', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_ID' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'modified_by_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_NAME' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'created_by', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_CREATED_ID' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'created_by_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_CREATED' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'deleted', 'bool', false, '{/literal}{incomCRM_translate label='LBL_DELETED' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'name', 'name', true, '{/literal}{incomCRM_translate label='LBL_NAME' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'description', 'text', false, '{/literal}{incomCRM_translate label='LBL_DESCRIPTION' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'branch_id', 'enum', false, '{/literal}{incomCRM_translate label='LBL_BRANCH_ID' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'department_id', 'enum', false, '{/literal}{incomCRM_translate label='LBL_DEPARTMENT_ID' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'assigned_user_id', 'relate', false, '{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO_ID' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'assigned_user_name', 'relate', true, '{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'status', 'enum', true, '{/literal}{incomCRM_translate label='LBL_STATUS' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'date_valid_from', 'date', true, '{/literal}{incomCRM_translate label='LBL_DATE_VALID_FROM' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'date_valid_to', 'date', true, '{/literal}{incomCRM_translate label='LBL_DATE_VALID_TO' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'month_discount', 'double', true, '{/literal}{incomCRM_translate label='LBL_MONTH_DISCOUNT' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'month_amount', 'double', true, '{/literal}{incomCRM_translate label='LBL_MONTH_AMOUNT' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'quarter_discount', 'double', true, '{/literal}{incomCRM_translate label='LBL_QUARTER_DISCOUNT' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'quarter_amount', 'double', false, '{/literal}{incomCRM_translate label='LBL_QUARTER_AMOUNT' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'year_discount', 'double', true, '{/literal}{incomCRM_translate label='LBL_YEAR_DISCOUNT' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'year_amount', 'double', true, '{/literal}{incomCRM_translate label='LBL_YEAR_AMOUNT' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'qt_target', 'double', true, '{/literal}{incomCRM_translate label='LBL_QT_TARGET' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'qt_amount_1', 'double', true, '{/literal}{incomCRM_translate label='LBL_QT_AMOUNT_1' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'qt_amount_2', 'double', true, '{/literal}{incomCRM_translate label='LBL_QT_AMOUNT_2' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'qt_amount_3', 'double', true, '{/literal}{incomCRM_translate label='LBL_QT_AMOUNT_3' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'qt_amount_4', 'double', true, '{/literal}{incomCRM_translate label='LBL_QT_AMOUNT_4' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'branch_all[]', 'enum', false, '{/literal}{incomCRM_translate label='LBL_BRANCH_ALL' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'account_promotion_all[]', 'enum', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_PROMOTION_ALL' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'tqt_productseries_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_TQT_PRODUCTSERIES_ID' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'tqt_productseries_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_TQT_PRODUCTSERIES_NAME' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'prod_series_teams[]', 'enum', false, '{/literal}{incomCRM_translate label='LBL_PROD_SERIES_TEAMS' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'unit_type', 'enum', false, '{/literal}{incomCRM_translate label='LBL_UNIT_TYPE' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'period_type', 'enum', false, '{/literal}{incomCRM_translate label='LBL_PERIOD_TYPE' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'period_date', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_PERIOD_DATE' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'amount_sales', 'double', false, '{/literal}{incomCRM_translate label='LBL_AMOUNT_SALES' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'amount_total', 'double', false, '{/literal}{incomCRM_translate label='LBL_AMOUNT_TOTAL' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'amount_used', 'double', false, '{/literal}{incomCRM_translate label='LBL_AMOUNT_USED' module='Revenue_Promotions'}{literal}' );
addToValidate('EditView', 'amount_left', 'double', false, '{/literal}{incomCRM_translate label='LBL_AMOUNT_LEFT' module='Revenue_Promotions'}{literal}' );
addToValidateBinaryDependency('EditView', 'assigned_user_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Revenue_Promotions'}{literal}{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO' module='Revenue_Promotions'}{literal}', 'assigned_user_id' );
</script>
<script type="text/javascript">if(typeof sqs_objects == 'undefined'){var sqs_objects = new Array;}
sqs_objects['EditView_assigned_user_name'] = {"form":"EditView","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["assigned_user_name","assigned_user_id"],"required_list":["assigned_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
</script>{/literal}
