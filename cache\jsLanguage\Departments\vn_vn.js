incomCRM.language.setLanguage('Departments', {"LBL_ID":"ID","LBL_DATE_ENTERED":"Ng\u00e0y t\u1ea1o","LBL_DATE_MODIFIED":"Ng\u00e0y c\u1eadp nh\u1eadt","LBL_MODIFIED":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_MODIFIED_ID":"ID ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_MODIFIED_NAME":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_CREATED":"Ng\u01b0\u1eddi t\u1ea1o","LBL_CREATED_ID":"ID ng\u01b0\u1eddi t\u1ea1o","LBL_DESCRIPTION":"Di\u1ec5n gi\u1ea3i","LBL_DELETED":"\u0110\u00e3 x\u00f3a?","LBL_NAME":"T\u00ean ph\u00f2ng ban","LBL_CREATED_USER":"Ng\u01b0\u1eddi t\u1ea1o","LBL_MODIFIED_USER":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_MODULE_NAME":"Ph\u00f2ng ban\/NPP","LBL_MODULE_TITLE":"Ph\u00f2ng ban\/NPP: Trang ch\u1ee7","LNK_NEW_RECORD":"Th\u00eam ph\u00f2ng ban","LNK_LIST":"Danh s\u00e1ch ph\u00f2ng ban","LBL_NEW_FORM_TITLE":"Th\u00eam m\u1edbi","LBL_LIST_FORM_TITLE":"Danh s\u00e1ch ph\u00f2ng ban","LBL_SEARCH_FORM_TITLE":"T\u00ecm ki\u1ebfm","LBL_CODE":"M\u00e3","LBL_BRANCH_ID":"Chi nh\u00e1nh","LBL_BRANCHES":"Chi nh\u00e1nh","LBL_USERS":"Ng\u01b0\u1eddi d\u00f9ng","LBL_ALLOW_USERS":"Ng\u01b0\u1eddi d\u00f9ng xem \u0111\u01b0\u1ee3c d\u1eef li\u1ec7u c\u1ee7a ph\u00f2ng ban n\u00e0y","LBL_TEAM_GROUPS":"B\u1ed9 ph\u1eadn"});