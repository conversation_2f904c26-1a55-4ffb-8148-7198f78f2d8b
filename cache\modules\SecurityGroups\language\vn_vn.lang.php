<?php
// created: 2025-02-26 15:10:10
$mod_strings = array (
  'LBL_ID' => 'ID',
  'LBL_DATE_ENTERED' => 'Ng<PERSON>y tạo',
  'LBL_DATE_MODIFIED' => '<PERSON><PERSON><PERSON> cập nhật',
  'LBL_MODIFIED' => 'Người cập nhật',
  'LBL_MODIFIED_ID' => 'ID Người cập nhật',
  'LBL_MODIFIED_NAME' => 'Cập nhật bởi',
  'LBL_CREATED' => 'Người tạo',
  'LBL_CREATED_ID' => 'ID Người tạo',
  'LBL_DESCRIPTION' => 'Mô tả',
  'LBL_DELETED' => 'Đã xóa',
  'LBL_NAME' => 'Tên nhóm',
  'LBL_CREATED_USER' => 'Tạo bở',
  'LBL_MODIFIED_USER' => 'Cập nhật bởi',
  'LBL_ASSIGNED_TO' => 'Nv.Qlý',
  'LBL_ASSIGNED_TO_ID' => 'Nv.Qlý',
  'LBL_ASSIGNED_TO_NAME' => 'Nv.Qlý',
  'LBL_ASSIGNED_TO_USER' => 'Nv.Qlý',
  'LBL_ASSIGNED_USER' => 'Nv.Qlý',
  'LBL_NONINHERITABLE' => 'Không kế thừa?',
  'LBL_LIST_NONINHERITABLE' => 'Không kế thừa?',
  'LBL_LIST_FORM_TITLE' => 'Nhóm người dùng',
  'LBL_MODULE_NAME' => 'Quản lý Nhóm người dùng',
  'LBL_MODULE_TITLE' => 'Quản lý Nhóm người dùng',
  'LNK_NEW_RECORD' => 'Tạo mới nhóm',
  'LNK_LIST' => 'List View',
  'LBL_SEARCH_FORM_TITLE' => 'Tìm kiếm',
  'LBL_HISTORY_SUBPANEL_TITLE' => 'History',
  'LBL_ACTIVITIES_SUBPANEL_TITLE' => 'Activities',
  'LBL_SECURITYGROUPS_SUBPANEL_TITLE' => 'Nhóm người dùng',
  'LBL_USERS' => 'Users',
  'LBL_USERS_SUBPANEL_TITLE' => 'Người dùng',
  'LBL_ROLES_SUBPANEL_TITLE' => 'Vai trò người dùng',
  'LBL_CONFIGURE_SETTINGS' => 'Configure',
  'LBL_ADDITIVE' => 'Additive Rights',
  'LBL_ADDITIVE_DESC' => 'User gets greatest rights of all roles assigned to the user or the user\'s group(s)',
  'LBL_STRICT_RIGHTS' => 'Strict Rights',
  'LBL_STRICT_RIGHTS_DESC' => 'If a user is a member of several groups only the respective rights from the group assigned to the current record are used.',
  'LBL_USER_ROLE_PRECEDENCE' => 'User Role Precedence',
  'LBL_USER_ROLE_PRECEDENCE_DESC' => 'If any role is assigned directly to a user that role should take precedence over any group roles.',
  'LBL_INHERIT_TITLE' => 'Group Inheritance Rules',
  'LBL_INHERIT_CREATOR' => 'Inherit from Created By User',
  'LBL_INHERIT_CREATOR_DESC' => 'The record will inherit all the groups assigned to the user who created it.',
  'LBL_INHERIT_PARENT' => 'Inherit from Parent Record',
  'LBL_INHERIT_PARENT_DESC' => 'e.g. If a case is created for a contact the case will inherit the groups associated with the contact.',
  'LBL_USER_POPUP' => 'New User Group Popup',
  'LBL_USER_POPUP_DESC' => 'When creating a new user show the SecurityGroups popup to assign the user to a group(s).',
  'LBL_INHERIT_ASSIGNED' => 'Inherit from Assigned To User',
  'LBL_INHERIT_ASSIGNED_DESC' => 'The record will inherit all the groups of the user assigned to the record. Other groups assigned to the record will NOT be removed.',
  'LBL_POPUP_SELECT' => 'Use Popup Select',
  'LBL_POPUP_SELECT_DESC' => 'When a record is created by a user in more than one group popup a group selection screen otherwise inherit that one group. Inheritance rules will only be used for non-user created records (e.g. Workflows, etc).',
  'LBL_FILTER_USER_LIST' => 'Filter User List',
  'LBL_FILTER_USER_LIST_DESC' => 'Non-admin users can only assign to users in the same group(s)',
  'LBL_DEFAULT_GROUP_TITLE' => 'Default Groups for New Records',
  'LBL_ADD_BUTTON_LABEL' => 'Thêm',
  'LBL_REMOVE_BUTTON_LABEL' => 'Xóa',
  'LBL_GROUP' => 'Group:',
  'LBL_MODULE' => 'Module:',
  'LBL_MASS_ASSIGN' => 'Security Groups: Mass Assign',
  'LBL_ASSIGN' => 'Assign',
  'LBL_REMOVE' => 'Remove',
  'LBL_ASSIGN_CONFIRM' => 'Are you sure that you want to add this group to the ',
  'LBL_REMOVE_CONFIRM' => 'Are you sure that you want to remove this group from the ',
  'LBL_CONFIRM_END' => ' selected record(s)?',
  'LBL_SECURITYGROUP_USER_FORM_TITLE' => 'SecurityGroup/User',
  'LBL_USER_NAME' => 'User Name',
  'LBL_SECURITYGROUP_NAME' => 'SecurityGroup Name',
  'LBL_LIST_USER_NONINHERITABLE' => 'Không kế thừa?',
  'LBL_SINGLE_USER_ACTIVE' => 'Assign User Single Mode',
  'LBL_SINGLE_USER_ACTIVE_DESC' => 'Only current assign user group is active. Other groups will be remove.',
  'LBL_USER_OWNER' => 'Owner User',
  'LBL_PRIMARY_USER' => 'Primary User',
);
?>
