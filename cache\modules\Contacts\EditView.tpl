
<section id="EditViewViewBody" class="view-module-{$module|strtolower}">

<form action="index.php" method="POST" name="{$form_name}" id="{$form_id}" class="_edit-{$instanceName}" {$enctype}>
<input type="hidden" name="module" value="{$module}" />
<input type="hidden" name="action" id="action" />
{if isset($smarty.request.isDuplicate) && $smarty.request.isDuplicate eq "true"}
<input type="hidden" name="record" id="record" value="" />
<input type="hidden" name="duplicateSave" value="true" />
{else}
<input type="hidden" name="record" id="record" value="{$fields.id.value}" />
{/if}
<input type="hidden" name="return_module" value="{$smarty.request.return_module}" />
<input type="hidden" name="return_action" value="{if $smarty.request.return_action eq 'LoadTabSubpanels'}DetailView{else}{$smarty.request.return_action}{/if}" />
<input type="hidden" name="return_id" value="{$smarty.request.return_id}" />
<input type="hidden" name="isDuplicate" value="false" />
<input type="hidden" name="module_tab" /> 
<input type="hidden" name="contact_role" />
{if !empty($smarty.request.return_module)}
<input type="hidden" name="relate_to" value="{if $smarty.request.return_relationship}{$smarty.request.return_relationship}{else}{$smarty.request.return_module}{/if}" />
<input type="hidden" name="relate_id" value="{$smarty.request.return_id}" />
{/if}
<input type="hidden" name="offset" value="{$offset}" />
{if empty($fields.id.value)}
<input type="hidden" name="_notify_random" value="{php}echo create_guid();{/php}" />
{/if}
<input type="hidden" name="isFromEditView" value="{$module}" />
<input type="hidden" name="opportunity_id" value="{$smarty.request.opportunity_id}" />
<input type="hidden" name="case_id" value="{$smarty.request.case_id}" />
<input type="hidden" name="bug_id" value="{$smarty.request.bug_id}" />
<input type="hidden" name="email_id" value="{$smarty.request.email_id}" />
<input type="hidden" name="inbound_email_id" value="{$smarty.request.inbound_email_id}" />
<table width="100%" cellpadding="0" cellspacing="0" border="0">
<tr>
<td class="buttons">
{if $bean->aclAccess("save")}<input title="{$APP.LBL_SAVE_BUTTON_TITLE}" accessKey="{$APP.LBL_SAVE_BUTTON_KEY}" class="button save" onclick="{if $isDuplicate}this.form.return_id.value=''; {/if}this.form.action.value='Save'; return check_form('EditView');" type="submit" name="button" value="{$APP.LBL_SAVE_BUTTON_LABEL}" />{/if} 
{if !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($fields.id.value))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {elseif !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($smarty.request.return_id))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {else}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='index'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {/if}
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=Contacts", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</td>
</tr>
</table>
{incomCRM_include include=$includes}
<div id="LBL_CONTACT_INFORMATION" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_CONTACT_INFORMATION');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_CONTACT_INFORMATION_IMG" border="0" />
{incomCRM_translate label='LBL_CONTACT_INFORMATION' module='Contacts'}
</a>
</h4>
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_CONTACT_INFORMATION_GROUP" style="display:block">
<table width="100%" border="0" cellspacing="1" cellpadding="0">
<tr>
<td valign="top" id='account_name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACCOUNT_NAME' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" colspan='3' id='_account_name_field' class="yui-ac-format">
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{else}{/if}<input type="hidden" name="{$fields.account_name.id_name}" id="{$fields.account_name.id_name}" value="{$fields.account_id.value}" />
<input type="text" name="{$fields.account_name.name}" class="sqsEnabled" tabindex="100" id="{$fields.account_name.name}" size="70" value="{$fields.account_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{else}{/if}
<input type="button" name="btn_{$fields.account_name.name}" id="btn_{$fields.account_name.name}" tabindex="100" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.account_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"EditView","field_to_name_array":{"id":"account_id","name":"account_name","billing_address_street":"primary_address_street","billing_address_city":"primary_address_city","billing_address_state":"primary_address_state","billing_address_postalcode":"primary_address_postalcode","billing_address_country":"primary_address_country","phone_office":"phone_work"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.account_name.name}" id="btn_clr_{$fields.account_name.name}" tabindex="100" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.account_name.name}.value=""; this.form.{$fields.account_name.id_name}.value=""; this.form.{$fields.account_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
{if !empty($isSmartView)}</div>{else}{/if}<script type="text/javascript">enableQS(false);</script>
</td>
</tr>
<tr>
<td valign="top" id='last_name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_LAST_NAME' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_last_name_field' >
{counter name="panelFieldCount"}
{html_options name="salutation" options=$fields.salutation.options selected=$fields.salutation.value style="width:50px;"}&nbsp;<input tabindex="101"  name="last_name" size="20" maxlength="100" type="text" value="{$fields.last_name.value}" />
</td>
<td valign="top" id='phone_work_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_OFFICE_PHONE' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_phone_work_field' >
{counter name="panelFieldCount"}

{if strlen($fields.phone_work.value) <= 0}
{assign var="value" value=$fields.phone_work.default_value }
{else}
{assign var="value" value=$fields.phone_work.value }
{/if}
{if isTypeNumber($fields.phone_work.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.phone_work.name}' id='{$fields.phone_work.name}' size='30' maxlength='100' value='{$value}' title='' tabindex='102'  /> 

</td>
</tr>
<tr>
<td valign="top" id='contact_position_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTACT_POSITION' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_contact_position_field' >
{counter name="panelFieldCount"}

<select name="{$fields.contact_position.name}" id="{$fields.contact_position.name}" title='' tabindex="103"  >
{if isset($fields.contact_position.value) && $fields.contact_position.value != ''}
{html_options options=$fields.contact_position.options selected=$fields.contact_position.value}
{else}
{html_options options=$fields.contact_position.options selected=$fields.contact_position.default}
{/if}
</select>
</td>
<td valign="top" id='phone_other_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_OTHER_PHONE' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_phone_other_field' >
{counter name="panelFieldCount"}

{if strlen($fields.phone_other.value) <= 0}
{assign var="value" value=$fields.phone_other.default_value }
{else}
{assign var="value" value=$fields.phone_other.value }
{/if}
{if isTypeNumber($fields.phone_other.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.phone_other.name}' id='{$fields.phone_other.name}' size='30' maxlength='100' value='{$value}' title='' tabindex='104'  /> 

</td>
</tr>
<tr>
<td valign="top" id='gender_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_GENDER' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_gender_field' >
{counter name="panelFieldCount"}

<select name="{$fields.gender.name}" id="{$fields.gender.name}" title='' tabindex="105"  >
{if isset($fields.gender.value) && $fields.gender.value != ''}
{html_options options=$fields.gender.options selected=$fields.gender.value}
{else}
{html_options options=$fields.gender.options selected=$fields.gender.default}
{/if}
</select>
</td>
<td valign="top" id='primary_address_street_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_PRIMARY_ADDRESS_STREET' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_primary_address_street_field' >
{counter name="panelFieldCount"}

{if empty($fields.primary_address_street.value)}
{assign var="value" value=$fields.primary_address_street.default_value }
{else}
{assign var="value" value=$fields.primary_address_street.value }
{/if}
<textarea id="{$fields.primary_address_street.name}" name="{$fields.primary_address_street.name}" rows="2" cols="40" title='' tabindex="106"  >{$value}</textarea>
</td>
</tr>
<tr>
<td valign="top" id='position_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_POSITION' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_position_field' >
{counter name="panelFieldCount"}

{if strlen($fields.position.value) <= 0}
{assign var="value" value=$fields.position.default_value }
{else}
{assign var="value" value=$fields.position.value }
{/if}
{if isTypeNumber($fields.position.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.position.name}' id='{$fields.position.name}' size='30' maxlength='200' value='{$value}' title='' tabindex='107'  /> 

</td>
<td valign="top" id='independence_day_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_INDEPENDENCE_DAY' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_independence_day_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.independence_day.value }
<input autocomplete="off" type="text" name="{$fields.independence_day.name}" id="{$fields.independence_day.name}" value="{$date_value}" title=''  tabindex='108' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.independence_day.name}_trigger" align="absmiddle" />
&nbsp;(<span class="dateFormat">{$USER_DATEFORMAT}</span>)
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.independence_day.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.independence_day.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
</tr>
<tr>
<td valign="top" id='department_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DEPARTMENT' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_department_field' >
{counter name="panelFieldCount"}

{if strlen($fields.department.value) <= 0}
{assign var="value" value=$fields.department.default_value }
{else}
{assign var="value" value=$fields.department.value }
{/if}
{if isTypeNumber($fields.department.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.department.name}' id='{$fields.department.name}' size='30' maxlength='255' value='{$value}' title='' tabindex='109'  /> 

</td>
<td valign="top" id='assistant_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ASSISTANT' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_assistant_field' >
{counter name="panelFieldCount"}

{if strlen($fields.assistant.value) <= 0}
{assign var="value" value=$fields.assistant.default_value }
{else}
{assign var="value" value=$fields.assistant.value }
{/if}
{if isTypeNumber($fields.assistant.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.assistant.name}' id='{$fields.assistant.name}' size='30' maxlength='75' value='{$value}' title='' tabindex='110'  /> 

</td>
</tr>
<tr>
<td valign="top" id='birthdate_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_BIRTHDATE' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_birthdate_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.birthdate.value }
<input autocomplete="off" type="text" name="{$fields.birthdate.name}" id="{$fields.birthdate.name}" value="{$date_value}" title=''  tabindex='111' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.birthdate.name}_trigger" align="absmiddle" />
&nbsp;(<span class="dateFormat">{$USER_DATEFORMAT}</span>)
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.birthdate.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.birthdate.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
<td valign="top" id='assistant_phone_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ASSISTANT_PHONE' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_assistant_phone_field' >
{counter name="panelFieldCount"}

{if strlen($fields.assistant_phone.value) <= 0}
{assign var="value" value=$fields.assistant_phone.default_value }
{else}
{assign var="value" value=$fields.assistant_phone.value }
{/if}
{if isTypeNumber($fields.assistant_phone.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.assistant_phone.name}' id='{$fields.assistant_phone.name}' size='30' maxlength='25' value='{$value}' title='' tabindex='112'  /> 

</td>
</tr>
<tr>
<td valign="top" id='phone_mobile_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_MOBILE_PHONE' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_phone_mobile_field' >
{counter name="panelFieldCount"}

{if strlen($fields.phone_mobile.value) <= 0}
{assign var="value" value=$fields.phone_mobile.default_value }
{else}
{assign var="value" value=$fields.phone_mobile.value }
{/if}
{if isTypeNumber($fields.phone_mobile.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.phone_mobile.name}' id='{$fields.phone_mobile.name}' size='30' maxlength='100' value='{$value}' title='' tabindex='113'  /> 

</td>
<td valign="top" id='report_to_name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_REPORTS_TO' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_report_to_name_field' >
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{else}{/if}<input type="hidden" name="{$fields.report_to_name.id_name}" id="{$fields.report_to_name.id_name}" value="{$fields.reports_to_id.value}" />
<input type="text" name="{$fields.report_to_name.name}" class="sqsEnabled" tabindex="114" id="{$fields.report_to_name.name}" size="16" value="{$fields.report_to_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{else}{/if}
<input type="button" name="btn_{$fields.report_to_name.name}" id="btn_{$fields.report_to_name.name}" tabindex="114" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.report_to_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"EditView","field_to_name_array":{"id":"reports_to_id","last_name":"report_to_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.report_to_name.name}" id="btn_clr_{$fields.report_to_name.name}" tabindex="114" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.report_to_name.name}.value=""; this.form.{$fields.report_to_name.id_name}.value=""; this.form.{$fields.report_to_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
{if !empty($isSmartView)}</div>{else}{/if}<script type="text/javascript">enableQS(false);</script>
</td>
</tr>
<tr>
<td valign="top" id='phone_home_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_HOME_PHONE' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_phone_home_field' >
{counter name="panelFieldCount"}

{if strlen($fields.phone_home.value) <= 0}
{assign var="value" value=$fields.phone_home.default_value }
{else}
{assign var="value" value=$fields.phone_home.value }
{/if}
{if isTypeNumber($fields.phone_home.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.phone_home.name}' id='{$fields.phone_home.name}' size='30' maxlength='100' value='{$value}' title='' tabindex='115'  /> 

</td>
<td valign="top" id='transaction_level_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_TRANSACTION_LEVEL' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_transaction_level_field' >
{counter name="panelFieldCount"}

<select name="{$fields.transaction_level.name}" id="{$fields.transaction_level.name}" title='' tabindex="116"  >
{if isset($fields.transaction_level.value) && $fields.transaction_level.value != ''}
{html_options options=$fields.transaction_level.options selected=$fields.transaction_level.value}
{else}
{html_options options=$fields.transaction_level.options selected=$fields.transaction_level.default}
{/if}
</select>
</td>
</tr>
<tr>
<td valign="top" id='alt_address_street_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ALT_ADDRESS_STREET' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_alt_address_street_field' >
{counter name="panelFieldCount"}

{if empty($fields.alt_address_street.value)}
{assign var="value" value=$fields.alt_address_street.default_value }
{else}
{assign var="value" value=$fields.alt_address_street.value }
{/if}
<textarea id="{$fields.alt_address_street.name}" name="{$fields.alt_address_street.name}" rows="2" cols="40" title='' tabindex="117"  >{$value}</textarea>
</td>
<td valign="top" id='transaction_comment_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_TRANSACTION_COMMENT' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_transaction_comment_field' >
{counter name="panelFieldCount"}

{if empty($fields.transaction_comment.value)}
{assign var="value" value=$fields.transaction_comment.default_value }
{else}
{assign var="value" value=$fields.transaction_comment.value }
{/if}
<textarea id="{$fields.transaction_comment.name}" name="{$fields.transaction_comment.name}" rows="2" cols="40" title='' tabindex="118"  >{$value}</textarea>
</td>
</tr>
<tr>
<td valign="top" id='assigned_user_name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_assigned_user_name_field' >
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{else}{/if}<input type="hidden" name="{$fields.assigned_user_name.id_name}" id="{$fields.assigned_user_name.id_name}" value="{$fields.assigned_user_id.value}" />
<input type="text" name="{$fields.assigned_user_name.name}" class="sqsEnabled" tabindex="119" id="{$fields.assigned_user_name.name}" size="16" value="{$fields.assigned_user_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{else}{/if}
<input type="button" name="btn_{$fields.assigned_user_name.name}" id="btn_{$fields.assigned_user_name.name}" tabindex="119" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.assigned_user_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"EditView","field_to_name_array":{"id":"assigned_user_id","user_name":"assigned_user_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.assigned_user_name.name}" id="btn_clr_{$fields.assigned_user_name.name}" tabindex="119" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.assigned_user_name.name}.value=""; this.form.{$fields.assigned_user_name.id_name}.value=""; this.form.{$fields.assigned_user_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
{if !empty($isSmartView)}</div>{else}{/if}<script type="text/javascript">enableQS(false);</script>
</td>
<td valign="top" id='foreigner_rep_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_FOREIGNER_REP' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_foreigner_rep_field' >
{counter name="panelFieldCount"}

{if strval($fields.foreigner_rep.value) == "1" || strval($fields.foreigner_rep.value) == "yes" || strval($fields.foreigner_rep.value) == "on"} 
{assign var="checked" value="CHECKED"}
{else}
{assign var="checked" value=""}
{/if}
<input type="hidden" name="{$fields.foreigner_rep.name}" value="0" /> 
<input type="checkbox" id="{$fields.foreigner_rep.name}" name="{$fields.foreigner_rep.name}" value="1" title='' tabindex="120" {$checked}  />

</td>
</tr>
<tr>
<td valign="top" id='email1_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_EMAIL_ADDRESS' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" colspan='3' id='_email1_field' >
{counter name="panelFieldCount"}

{$fields.email1.value}
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_CONTACT_INFORMATION").style.display='none';</script>
{/if}
<div id="LBL_BANK_ACCOUNT_INFORMATION" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_BANK_ACCOUNT_INFORMATION');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_BANK_ACCOUNT_INFORMATION_IMG" border="0" />
{incomCRM_translate label='LBL_BANK_ACCOUNT_INFORMATION' module='Contacts'}
</a>
</h4>
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_BANK_ACCOUNT_INFORMATION_GROUP" style="display:none">
<table width="100%" border="0" cellspacing="1" cellpadding="0">
<tr>
<td valign="top" id='account_holder_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACCOUNT_HOLDER' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_account_holder_field' >
{counter name="panelFieldCount"}

{if strlen($fields.account_holder.value) <= 0}
{assign var="value" value=$fields.account_holder.default_value }
{else}
{assign var="value" value=$fields.account_holder.value }
{/if}
{if isTypeNumber($fields.account_holder.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.account_holder.name}' id='{$fields.account_holder.name}' size='30' maxlength='100' value='{$value}' title='' tabindex='122'  /> 

</td>
<td valign="top" id='id_number_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ID_NUMBER' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_id_number_field' >
{counter name="panelFieldCount"}

{if strlen($fields.id_number.value) <= 0}
{assign var="value" value=$fields.id_number.default_value }
{else}
{assign var="value" value=$fields.id_number.value }
{/if}
{if isTypeNumber($fields.id_number.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.id_number.name}' id='{$fields.id_number.name}' size='30' maxlength='30' value='{$value}' title='' tabindex='123'  /> 

</td>
</tr>
<tr>
<td valign="top" id='account_number_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACCOUNT_NUMBER' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_account_number_field' >
{counter name="panelFieldCount"}

{if strlen($fields.account_number.value) <= 0}
{assign var="value" value=$fields.account_number.default_value }
{else}
{assign var="value" value=$fields.account_number.value }
{/if}
{if isTypeNumber($fields.account_number.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.account_number.name}' id='{$fields.account_number.name}' size='30' maxlength='30' value='{$value}' title='' tabindex='124'  /> 

</td>
<td valign="top" id='passport_number_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_PASSPORT_NUMBER' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_passport_number_field' >
{counter name="panelFieldCount"}

{if strlen($fields.passport_number.value) <= 0}
{assign var="value" value=$fields.passport_number.default_value }
{else}
{assign var="value" value=$fields.passport_number.value }
{/if}
{if isTypeNumber($fields.passport_number.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.passport_number.name}' id='{$fields.passport_number.name}' size='30' maxlength='30' value='{$value}' title='' tabindex='125'  /> 

</td>
</tr>
<tr>
<td valign="top" id='account_bank_name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACCOUNT_BANK_NAME' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_account_bank_name_field' >
{counter name="panelFieldCount"}

{if strlen($fields.account_bank_name.value) <= 0}
{assign var="value" value=$fields.account_bank_name.default_value }
{else}
{assign var="value" value=$fields.account_bank_name.value }
{/if}
{if isTypeNumber($fields.account_bank_name.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.account_bank_name.name}' id='{$fields.account_bank_name.name}' size='30' maxlength='200' value='{$value}' title='' tabindex='126'  /> 

</td>
<td valign="top" id='commission_recipient_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_COMMISSION_RECIPIENT' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_commission_recipient_field' >
{counter name="panelFieldCount"}

{if strval($fields.commission_recipient.value) == "1" || strval($fields.commission_recipient.value) == "yes" || strval($fields.commission_recipient.value) == "on"} 
{assign var="checked" value="CHECKED"}
{else}
{assign var="checked" value=""}
{/if}
<input type="hidden" name="{$fields.commission_recipient.name}" value="0" /> 
<input type="checkbox" id="{$fields.commission_recipient.name}" name="{$fields.commission_recipient.name}" value="1" title='' tabindex="127" {$checked}  />

</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_BANK_ACCOUNT_INFORMATION").style.display='none';</script>
{/if}
<div id="LBL_MORE_INFO_CARE" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_MORE_INFO_CARE');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_MORE_INFO_CARE_IMG" border="0" />
{incomCRM_translate label='LBL_MORE_INFO_CARE' module='Contacts'}
</a>
</h4>
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_MORE_INFO_CARE_GROUP" style="display:none">
<table width="100%" border="0" cellspacing="1" cellpadding="0">
<tr>
<td valign="top" id='need_care_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_NEED_CARE' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_need_care_field' >
{counter name="panelFieldCount"}

{if strval($fields.need_care.value) == "1" || strval($fields.need_care.value) == "yes" || strval($fields.need_care.value) == "on"} 
{assign var="checked" value="CHECKED"}
{else}
{assign var="checked" value=""}
{/if}
<input type="hidden" name="{$fields.need_care.name}" value="0" /> 
<input type="checkbox" id="{$fields.need_care.name}" name="{$fields.need_care.name}" value="1" title='' tabindex="128" {$checked}  />

</td>
<td valign="top" id='care_priority_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CARE_PRIORITY' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_care_priority_field' >
{counter name="panelFieldCount"}

<select name="{$fields.care_priority.name}" id="{$fields.care_priority.name}" title='' tabindex="129"  >
{if isset($fields.care_priority.value) && $fields.care_priority.value != ''}
{html_options options=$fields.care_priority.options selected=$fields.care_priority.value}
{else}
{html_options options=$fields.care_priority.options selected=$fields.care_priority.default}
{/if}
</select>
</td>
</tr>
<tr>
<td valign="top" id='care_suggest_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CARE_SUGGEST' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" colspan='3' id='_care_suggest_field' >
{counter name="panelFieldCount"}

{if empty($fields.care_suggest.value)}
{assign var="value" value=$fields.care_suggest.default_value }
{else}
{assign var="value" value=$fields.care_suggest.value }
{/if}
<textarea id="{$fields.care_suggest.name}" name="{$fields.care_suggest.name}" rows="4" cols="100" title='' tabindex="130"  >{$value}</textarea>
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_MORE_INFO_CARE").style.display='none';</script>
{/if}
<div id="LBL_OTHER_INFORMATION" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_OTHER_INFORMATION');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_OTHER_INFORMATION_IMG" border="0" />
{incomCRM_translate label='LBL_OTHER_INFORMATION' module='Contacts'}
</a>
</h4>
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_OTHER_INFORMATION_GROUP" style="display:none">
<table width="100%" border="0" cellspacing="1" cellpadding="0">
<tr>
<td valign="top" id='hobbies_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_HOBBIES' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_hobbies_field' >
{counter name="panelFieldCount"}

{if empty($fields.hobbies.value)}
{assign var="value" value=$fields.hobbies.default_value }
{else}
{assign var="value" value=$fields.hobbies.value }
{/if}
<textarea id="{$fields.hobbies.name}" name="{$fields.hobbies.name}" rows="3" cols="40" title='' tabindex="131"  >{$value}</textarea>
</td>
<td valign="top" id='intimate_level_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_INTIMATE_LEVEL' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_intimate_level_field' >
{counter name="panelFieldCount"}

<select name="{$fields.intimate_level.name}" id="{$fields.intimate_level.name}" title='' tabindex="132"  >
{if isset($fields.intimate_level.value) && $fields.intimate_level.value != ''}
{html_options options=$fields.intimate_level.options selected=$fields.intimate_level.value}
{else}
{html_options options=$fields.intimate_level.options selected=$fields.intimate_level.default}
{/if}
</select>
</td>
</tr>
<tr>
<td valign="top" id='talent_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_TALENT' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_talent_field' >
{counter name="panelFieldCount"}

{if empty($fields.talent.value)}
{assign var="value" value=$fields.talent.default_value }
{else}
{assign var="value" value=$fields.talent.value }
{/if}
<textarea id="{$fields.talent.name}" name="{$fields.talent.name}" rows="3" cols="40" title='' tabindex="133"  >{$value}</textarea>
</td>
<td valign="top" id='intimate_note_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_INTIMATE_NOTE' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_intimate_note_field' >
{counter name="panelFieldCount"}

{if empty($fields.intimate_note.value)}
{assign var="value" value=$fields.intimate_note.default_value }
{else}
{assign var="value" value=$fields.intimate_note.value }
{/if}
<textarea id="{$fields.intimate_note.name}" name="{$fields.intimate_note.name}" rows="3" cols="40" title='' tabindex="134"  >{$value}</textarea>
</td>
</tr>
<tr>
<td valign="top" id='sport_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_SPORT' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_sport_field' >
{counter name="panelFieldCount"}

{if empty($fields.sport.value)}
{assign var="value" value=$fields.sport.default_value }
{else}
{assign var="value" value=$fields.sport.value }
{/if}
<textarea id="{$fields.sport.name}" name="{$fields.sport.name}" rows="3" cols="40" title='' tabindex="135"  >{$value}</textarea>
</td>
<td valign="top" id='work_history_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_WORK_HISTORY' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_work_history_field' >
{counter name="panelFieldCount"}

{if empty($fields.work_history.value)}
{assign var="value" value=$fields.work_history.default_value }
{else}
{assign var="value" value=$fields.work_history.value }
{/if}
<textarea id="{$fields.work_history.name}" name="{$fields.work_history.name}" rows="3" cols="40" title='' tabindex="136"  >{$value}</textarea>
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_OTHER_INFORMATION").style.display='none';</script>
{/if}
<div id="LBL_DESCRIPTION_INFORMATION" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_DESCRIPTION_INFORMATION');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_DESCRIPTION_INFORMATION_IMG" border="0" />
{incomCRM_translate label='LBL_DESCRIPTION_INFORMATION' module='Contacts'}
</a>
</h4>
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_DESCRIPTION_INFORMATION_GROUP" style="display:none">
<table width="100%" border="0" cellspacing="1" cellpadding="0">
<tr>
<td valign="top" id='description_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DESCRIPTION' module='Contacts'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" colspan='3' id='_description_field' >
{counter name="panelFieldCount"}

{if empty($fields.description.value)}
{assign var="value" value=$fields.description.default_value }
{else}
{assign var="value" value=$fields.description.value }
{/if}
<textarea id="{$fields.description.name}" name="{$fields.description.name}" rows="4" cols="100" title='' tabindex="137"  >{$value}</textarea>
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_DESCRIPTION_INFORMATION").style.display='none';</script>
{/if}

<div class="buttons" style="margin-top:15px;">
{if $bean->aclAccess("save")}<input title="{$APP.LBL_SAVE_BUTTON_TITLE}" accessKey="{$APP.LBL_SAVE_BUTTON_KEY}" class="button save" onclick="{if $isDuplicate}this.form.return_id.value=''; {/if}this.form.action.value='Save'; return check_form('EditView');" type="submit" name="button" value="{$APP.LBL_SAVE_BUTTON_LABEL}" />{/if} 
{if !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($fields.id.value))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {elseif !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($smarty.request.return_id))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {else}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='index'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {/if}
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=Contacts", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</div>
</form>
{$set_focus_block}
</section>{literal}
<script type="text/javascript">
addToValidate('EditView', 'id', 'id', false, '{/literal}{incomCRM_translate label='LBL_ID' module='Contacts'}{literal}' );
addToValidate('EditView', 'date_entered_date', 'date', false, 'Ngày nhập' );
addToValidate('EditView', 'date_modified_date', 'date', false, 'Ngày thay đổi' );
addToValidate('EditView', 'modified_user_id', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_ID' module='Contacts'}{literal}' );
addToValidate('EditView', 'modified_by_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_NAME' module='Contacts'}{literal}' );
addToValidate('EditView', 'created_by', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_CREATED_ID' module='Contacts'}{literal}' );
addToValidate('EditView', 'created_by_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_CREATED' module='Contacts'}{literal}' );
addToValidate('EditView', 'deleted', 'bool', false, '{/literal}{incomCRM_translate label='LBL_DELETED' module='Contacts'}{literal}' );
addToValidate('EditView', 'name', 'name', false, '{/literal}{incomCRM_translate label='LBL_NAME' module='Contacts'}{literal}' );
addToValidate('EditView', 'description', 'text', false, '{/literal}{incomCRM_translate label='LBL_DESCRIPTION' module='Contacts'}{literal}' );
addToValidate('EditView', 'branch_id', 'enum', false, '{/literal}{incomCRM_translate label='LBL_BRANCH_ID' module='Contacts'}{literal}' );
addToValidate('EditView', 'department_id', 'enum', false, '{/literal}{incomCRM_translate label='LBL_DEPARTMENT_ID' module='Contacts'}{literal}' );
addToValidate('EditView', 'assigned_user_id', 'relate', false, '{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO_ID' module='Contacts'}{literal}' );
addToValidate('EditView', 'assigned_user_name', 'relate', true, '{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='Contacts'}{literal}' );
addToValidate('EditView', 'salutation', 'enum', false, '{/literal}{incomCRM_translate label='LBL_SALUTATION' module='Contacts'}{literal}' );
addToValidate('EditView', 'first_name', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_FIRST_NAME' module='Contacts'}{literal}' );
addToValidate('EditView', 'last_name', 'varchar', true, '{/literal}{incomCRM_translate label='LBL_LAST_NAME' module='Contacts'}{literal}' );
addToValidate('EditView', 'full_name', 'fullname', false, '{/literal}{incomCRM_translate label='LBL_NAME' module='Contacts'}{literal}' );
addToValidate('EditView', 'title', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TITLE' module='Contacts'}{literal}' );
addToValidate('EditView', 'department', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_DEPARTMENT' module='Contacts'}{literal}' );
addToValidate('EditView', 'do_not_call', 'bool', false, '{/literal}{incomCRM_translate label='LBL_DO_NOT_CALL' module='Contacts'}{literal}' );
addToValidate('EditView', 'phone_home', 'phone', false, '{/literal}{incomCRM_translate label='LBL_HOME_PHONE' module='Contacts'}{literal}' );
addToValidate('EditView', 'phone_mobile', 'phone', false, '{/literal}{incomCRM_translate label='LBL_MOBILE_PHONE' module='Contacts'}{literal}' );
addToValidate('EditView', 'phone_work', 'phone', false, '{/literal}{incomCRM_translate label='LBL_OFFICE_PHONE' module='Contacts'}{literal}' );
addToValidate('EditView', 'phone_other', 'phone', false, '{/literal}{incomCRM_translate label='LBL_OTHER_PHONE' module='Contacts'}{literal}' );
addToValidate('EditView', 'phone_fax', 'phone', false, '{/literal}{incomCRM_translate label='LBL_FAX_PHONE' module='Contacts'}{literal}' );
addToValidate('EditView', 'email1', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_EMAIL_ADDRESS' module='Contacts'}{literal}' );
addToValidate('EditView', 'email2', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_OTHER_EMAIL_ADDRESS' module='Contacts'}{literal}' );
addToValidate('EditView', 'invalid_email', 'bool', false, '{/literal}{incomCRM_translate label='LBL_INVALID_EMAIL' module='Contacts'}{literal}' );
addToValidate('EditView', 'email_opt_out', 'bool', false, '{/literal}{incomCRM_translate label='LBL_EMAIL_OPT_OUT' module='Contacts'}{literal}' );
addToValidate('EditView', 'primary_address_street', 'text', false, '{/literal}{incomCRM_translate label='LBL_PRIMARY_ADDRESS_STREET' module='Contacts'}{literal}' );
addToValidate('EditView', 'primary_address_street_2', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_PRIMARY_ADDRESS_STREET_2' module='Contacts'}{literal}' );
addToValidate('EditView', 'primary_address_street_3', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_PRIMARY_ADDRESS_STREET_3' module='Contacts'}{literal}' );
addToValidate('EditView', 'primary_address_city', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_PRIMARY_ADDRESS_CITY' module='Contacts'}{literal}' );
addToValidate('EditView', 'primary_address_state', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_PRIMARY_ADDRESS_STATE' module='Contacts'}{literal}' );
addToValidate('EditView', 'primary_address_postalcode', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_PRIMARY_ADDRESS_POSTALCODE' module='Contacts'}{literal}' );
addToValidate('EditView', 'primary_address_country', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_PRIMARY_ADDRESS_COUNTRY' module='Contacts'}{literal}' );
addToValidate('EditView', 'alt_address_street', 'text', false, '{/literal}{incomCRM_translate label='LBL_ALT_ADDRESS_STREET' module='Contacts'}{literal}' );
addToValidate('EditView', 'alt_address_street_2', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ALT_ADDRESS_STREET_2' module='Contacts'}{literal}' );
addToValidate('EditView', 'alt_address_street_3', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ALT_ADDRESS_STREET_3' module='Contacts'}{literal}' );
addToValidate('EditView', 'alt_address_city', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ALT_ADDRESS_CITY' module='Contacts'}{literal}' );
addToValidate('EditView', 'alt_address_state', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ALT_ADDRESS_STATE' module='Contacts'}{literal}' );
addToValidate('EditView', 'alt_address_postalcode', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ALT_ADDRESS_POSTALCODE' module='Contacts'}{literal}' );
addToValidate('EditView', 'alt_address_country', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ALT_ADDRESS_COUNTRY' module='Contacts'}{literal}' );
addToValidate('EditView', 'assistant', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ASSISTANT' module='Contacts'}{literal}' );
addToValidate('EditView', 'assistant_phone', 'phone', false, '{/literal}{incomCRM_translate label='LBL_ASSISTANT_PHONE' module='Contacts'}{literal}' );
addToValidate('EditView', 'gender', 'enum', false, '{/literal}{incomCRM_translate label='LBL_GENDER' module='Contacts'}{literal}' );
addToValidate('EditView', 'mail_status', 'enum', false, '{/literal}{incomCRM_translate label='LBL_MAIL_STATUS' module='Contacts'}{literal}' );
addToValidate('EditView', 'birthdate', 'date', false, '{/literal}{incomCRM_translate label='LBL_BIRTHDATE' module='Contacts'}{literal}' );
addToValidate('EditView', 'email_and_name1', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_NAME' module='Contacts'}{literal}' );
addToValidate('EditView', 'lead_source', 'enum', false, '{/literal}{incomCRM_translate label='LBL_LEAD_SOURCE' module='Contacts'}{literal}' );
addToValidate('EditView', 'account_id', 'relate', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_ID' module='Contacts'}{literal}' );
addToValidate('EditView', 'account_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_NAME' module='Contacts'}{literal}' );
addToValidate('EditView', 'opportunity_role_fields', 'relate', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_NAME' module='Contacts'}{literal}' );
addToValidate('EditView', 'opportunity_role_id', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_OPPORTUNITY_ROLE_ID' module='Contacts'}{literal}' );
addToValidate('EditView', 'opportunity_role', 'enum', false, '{/literal}{incomCRM_translate label='LBL_OPPORTUNITY_ROLE' module='Contacts'}{literal}' );
addToValidate('EditView', 'reports_to_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_REPORTS_TO_ID' module='Contacts'}{literal}' );
addToValidate('EditView', 'report_to_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_REPORTS_TO' module='Contacts'}{literal}' );
addToValidate('EditView', 'campaign_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_CAMPAIGN_ID' module='Contacts'}{literal}' );
addToValidate('EditView', 'campaign_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_CAMPAIGN' module='Contacts'}{literal}' );
addToValidate('EditView', 'c_accept_status_fields', 'relate', false, '{/literal}{incomCRM_translate label='LBL_LIST_ACCEPT_STATUS' module='Contacts'}{literal}' );
addToValidate('EditView', 'm_accept_status_fields', 'relate', false, '{/literal}{incomCRM_translate label='LBL_LIST_ACCEPT_STATUS' module='Contacts'}{literal}' );
addToValidate('EditView', 'accept_status_id', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_LIST_ACCEPT_STATUS' module='Contacts'}{literal}' );
addToValidate('EditView', 'accept_status_name', 'enum', false, '{/literal}{incomCRM_translate label='LBL_LIST_ACCEPT_STATUS' module='Contacts'}{literal}' );
addToValidate('EditView', 'sync_contact', 'bool', false, '{/literal}{incomCRM_translate label='LBL_SYNC_CONTACT' module='Contacts'}{literal}' );
addToValidate('EditView', 'is_locked', 'bool', false, '{/literal}{incomCRM_translate label='LBL_IS_LOCKED' module='Contacts'}{literal}' );
addToValidate('EditView', 'position', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_POSITION' module='Contacts'}{literal}' );
addToValidate('EditView', 'specific_info', 'text', false, '{/literal}{incomCRM_translate label='LBL_SPECIFIC_INFO' module='Contacts'}{literal}' );
addToValidate('EditView', 'reject_email', 'bool', false, '{/literal}{incomCRM_translate label='LBL_REJECT_EMAIL' module='Contacts'}{literal}' );
addToValidate('EditView', 'notification', 'bool', false, '{/literal}{incomCRM_translate label='LBL_NOTIFICATION' module='Contacts'}{literal}' );
addToValidate('EditView', 'team', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TEAM' module='Contacts'}{literal}' );
addToValidate('EditView', 'reference', 'bool', false, '{/literal}{incomCRM_translate label='LBL_REFERENCE' module='Contacts'}{literal}' );
addToValidate('EditView', 'transaction_level', 'enum', false, '{/literal}{incomCRM_translate label='LBL_TRANSACTION_LEVEL' module='Contacts'}{literal}' );
addToValidate('EditView', 'transaction_comment', 'text', false, '{/literal}{incomCRM_translate label='LBL_TRANSACTION_COMMENT' module='Contacts'}{literal}' );
addToValidate('EditView', 'commission_recipient', 'bool', false, '{/literal}{incomCRM_translate label='LBL_COMMISSION_RECIPIENT' module='Contacts'}{literal}' );
addToValidate('EditView', 'account_holder', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_HOLDER' module='Contacts'}{literal}' );
addToValidate('EditView', 'id_number', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ID_NUMBER' module='Contacts'}{literal}' );
addToValidate('EditView', 'passport_number', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_PASSPORT_NUMBER' module='Contacts'}{literal}' );
addToValidate('EditView', 'account_number', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_NUMBER' module='Contacts'}{literal}' );
addToValidate('EditView', 'account_bank_name', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_BANK_NAME' module='Contacts'}{literal}' );
addToValidate('EditView', 'hobbies', 'text', false, '{/literal}{incomCRM_translate label='LBL_HOBBIES' module='Contacts'}{literal}' );
addToValidate('EditView', 'talent', 'text', false, '{/literal}{incomCRM_translate label='LBL_TALENT' module='Contacts'}{literal}' );
addToValidate('EditView', 'sport', 'text', false, '{/literal}{incomCRM_translate label='LBL_SPORT' module='Contacts'}{literal}' );
addToValidate('EditView', 'intimate_level', 'enum', false, '{/literal}{incomCRM_translate label='LBL_INTIMATE_LEVEL' module='Contacts'}{literal}' );
addToValidate('EditView', 'intimate_note', 'text', false, '{/literal}{incomCRM_translate label='LBL_INTIMATE_NOTE' module='Contacts'}{literal}' );
addToValidate('EditView', 'work_history', 'text', false, '{/literal}{incomCRM_translate label='LBL_WORK_HISTORY' module='Contacts'}{literal}' );
addToValidate('EditView', 'independence_day', 'date', false, '{/literal}{incomCRM_translate label='LBL_INDEPENDENCE_DAY' module='Contacts'}{literal}' );
addToValidate('EditView', 'foreigner_rep', 'bool', false, '{/literal}{incomCRM_translate label='LBL_FOREIGNER_REP' module='Contacts'}{literal}' );
addToValidate('EditView', 'need_care', 'bool', false, '{/literal}{incomCRM_translate label='LBL_NEED_CARE' module='Contacts'}{literal}' );
addToValidate('EditView', 'care_priority', 'enum', false, '{/literal}{incomCRM_translate label='LBL_CARE_PRIORITY' module='Contacts'}{literal}' );
addToValidate('EditView', 'care_suggest', 'text', false, '{/literal}{incomCRM_translate label='LBL_CARE_SUGGEST' module='Contacts'}{literal}' );
addToValidate('EditView', 'contact_position', 'enum', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_POSITION' module='Contacts'}{literal}' );
addToValidate('EditView', 'import_mark', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_IMPORT_MARK' module='Contacts'}{literal}' );
addToValidate('EditView', 'reference_code', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_REFERENCE_CODE' module='Contacts'}{literal}' );
addToValidate('EditView', 'account_branch', 'enum', false, '{/literal}{incomCRM_translate label='Chi nhánh KH' module='Contacts'}{literal}' );
addToValidate('EditView', 'account_department', 'enum', false, '{/literal}{incomCRM_translate label='Phòng ban KH' module='Contacts'}{literal}' );
addToValidate('EditView', 'account_user_id', 'id', false, '{/literal}{incomCRM_translate label='Nv.Qlý KH' module='Contacts'}{literal}' );
addToValidateBinaryDependency('EditView', 'assigned_user_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Contacts'}{literal}{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO' module='Contacts'}{literal}', 'assigned_user_id' );
addToValidateBinaryDependency('EditView', 'account_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Contacts'}{literal}{/literal}{incomCRM_translate label='LBL_ACCOUNT_NAME' module='Contacts'}{literal}', 'account_id' );
addToValidateBinaryDependency('EditView', 'report_to_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Contacts'}{literal}{/literal}{incomCRM_translate label='LBL_REPORTS_TO' module='Contacts'}{literal}', 'reports_to_id' );
</script>
<script type="text/javascript">if(typeof sqs_objects == 'undefined'){var sqs_objects = new Array;}
sqs_objects['EditView_account_name'] = {"form":"EditView","method":"query","modules":["Accounts"],"group":"or","field_list":["name","id","billing_address_street","reference_code","phone_office","location_city","location_district"],"populate_list":["EditView_account_name","account_id","primary_address_street","reference_code_x","phone_work","location_city_x","location_district_x"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"required_list":["account_id"],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p","maxItems":30,"formatResult":"formatResultAccounts"};
sqs_objects['EditView_report_to_name'] = {"form":"EditView","method":"get_contact_array","modules":["Contacts"],"field_list":["salutation","first_name","last_name","id"],"populate_list":["report_to_name","reports_to_id","reports_to_id","reports_to_id"],"required_list":["reports_to_id"],"group":"or","conditions":[{"name":"first_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"last_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['EditView_assigned_user_name'] = {"form":"EditView","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["assigned_user_name","assigned_user_id"],"required_list":["assigned_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
</script>{/literal}
