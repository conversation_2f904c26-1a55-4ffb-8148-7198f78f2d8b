


<script type="text/javascript" src="{incomCRM_getjspath file='include/JSON.js'}"></script>
<script type="text/javascript" src="{incomCRM_getjspath file='include/javascript/popup_helper.js'}"></script>
<script type='text/javascript' src='{incomCRM_getjspath file='include/javascript/incomCRM_grp_overlib.js'}'></script>
<script type="text/javascript">
	{$ASSOCIATED_JAVASCRIPT_DATA}
</script>
{$SEARCH_FORM_HEADER}
<table cellpadding="0" cellspacing="0" border="0" width="100%" class="edit view">
<tr>
<td>
<form action="index.php" method="post" name="popup_query_form" id="popup_query_form">
<table width="100%" cellpadding="0" cellspacing="0" border="0">
<tr><td>
{$searchForm}
</td></tr>
<tr>
<td>
{$searchHidden}
<input type="hidden" name="module" value="{$module}" />
<input type="hidden" name="action" value="Popup" />
<input type="hidden" name="query" value="true" />
<input type="hidden" name="func_name" value="" />
<input type="hidden" name="request_data" value="{$request_data}" />
<input type="hidden" name="populate_parent" value="false" />
<input type="hidden" name="hide_clear_button" value="true" />
<input type="hidden" name="record_id" value="" />
{$MODE}
<input type="submit" name="button" class="button"
	title="{$APP.LBL_SEARCH_BUTTON_TITLE}"
	accessKey="{$APP.LBL_SEARCH_BUTTON_KEY}"
	value="{$APP.LBL_SEARCH_BUTTON_LABEL}" />
</td>
<td align='right'></td>
</tr>
</table>
</form>
</td>
</tr>
</table>

{$jsLang}
{$LIST_HEADER}
<section class="list-view-smarty list-view-popup list view">
<table cellpadding='0' cellspacing='0' width='100%' border='0'>
{if empty($isMobileDevice)}
	<tr class='pagination'>
		<td colspan='{$colCount+1}'>
			<table border='0' cellpadding='0' cellspacing='0' width='100%'>
			<tr>
				<td width="400"></td>
				<td>
					{include file='include/Popups/tpls/PopupPageButtons.tpl'}
				</td>
			</tr>
		</table>
		</td>
	</tr>
{/if}
	<tr height='20'>
		{if $prerow}
			<th scope='col' nowrap="nowrap" width='1%'>
				<input type='checkbox' class='checkbox' name='massall' value='' onclick='sListView.check_all(document.MassUpdate, "mass[]", this.checked);' />
			</th>
		{/if}
		{counter start=0 name="colCounter" print=false assign="colCounter"}
		{foreach from=$displayColumns key=colHeader item=params}
			<th scope='col' width='{$params.width}%' nowrap="nowrap">
				<div style='white-space: nowrap;width:100%;' align='{$params.align|default:'left'}'>
				{if $params.sortable|default:true}              
					<a href='#' onclick='location.href="{$pageData.urls.orderBy}{$params.orderBy|default:$colHeader|lower}"; return sListView.save_checks(0, "{$moduleString}");' class='listViewThLinkS1'>{incomCRM_translate label=$params.label module=$pageData.bean.moduleDir}&nbsp;&nbsp;
					{if $params.orderBy|default:$colHeader|lower == $pageData.ordering.orderBy}
						{if $pageData.ordering.sortOrder == 'ASC'}
							{capture assign="imageName"}arrow_down.{$arrowExt}{/capture}
							<img border='0' src='{incomCRM_getimagepath file=$imageName}' width='{$arrowWidth}' height='{$arrowHeight}' align='absmiddle' alt='{$arrowAlt}'>
						{else}
							{capture assign="imageName"}arrow_up.{$arrowExt}{/capture}
							<img border='0' src='{incomCRM_getimagepath file=$imageName}' width='{$arrowWidth}' height='{$arrowHeight}' align='absmiddle' alt='{$arrowAlt}'>
						{/if}
					{else}
						{capture assign="imageName"}arrow.{$arrowExt}{/capture}
						<img border='0' src='{incomCRM_getimagepath file=$imageName}' width='{$arrowWidth}' height='{$arrowHeight}' align='absmiddle' alt='{$arrowAlt}'>
					{/if}
					</a>
				{else}
					{incomCRM_translate label=$params.label module=$pageData.bean.moduleDir}
				{/if}
				</div>
			</th>
			{counter name="colCounter"}
		{/foreach}
		<th scope='col' nowrap="nowrap" width='1%'>&nbsp;</th>
	</tr>
		
	{foreach name=rowIteration from=$data key=id item=rowData}
		{if $smarty.foreach.rowIteration.iteration is odd}
			{assign var='_rowColor' value=$rowColor[0]}
		{else}
			{assign var='_rowColor' value=$rowColor[1]}
		{/if}
		<tr height='20' class='{$_rowColor}S1'>
			{if $prerow}
			<td width='1%' nowrap='nowrap'>
				<input onclick='sListView.check_item(this, document.MassUpdate)' type='checkbox' class='checkbox' name='mass[]' value='{$rowData[$params.id]|default:$rowData.ID}'>
			</td>
			{/if}
			{counter start=0 name="colCounter" print=false assign="colCounter"}
			{foreach from=$displayColumns key=col item=params}
				<td scope='row' align='{$params.align|default:'left'}' valign="top" {if ($params.type == 'teamset')}class="nowrap"{/if}>
				{if $col == 'NAME' || $params.bold}<b>{/if}
					{if $params.link && !$params.customCode}
						<{$pageData.tag.$id[$params.ACLTag]|default:$pageData.tag.$id.MAIN} href='#' onclick="send_back('{if $params.dynamic_module}{$rowData[$params.dynamic_module]}{else}{$params.module|default:$pageData.bean.moduleDir}{/if}','{$rowData[$params.id]|default:$rowData.ID}');">{$rowData.$col}</{$pageData.tag.$id[$params.ACLTag]|default:$pageData.tag.$id.MAIN}>
					{elseif $params.customCode} 
						{incomCRM_evalcolumn_old var=$params.customCode rowData=$rowData}
					{elseif $params.currency_format} 
						{incomCRM_currency_format 
                            var=$rowData.$col 
                            round=$params.currency_format.round 
                            decimals=$params.currency_format.decimals 
                            symbol=$params.currency_format.symbol
                            convert=$params.currency_format.convert
                            currency_symbol=$params.currency_format.currency_symbol
						}
					{elseif $params.type == 'bool'}
						<input type='checkbox' disabled=disabled class='checkbox' {if !empty($rowData[$col])}checked='checked'{/if} />
					{elseif $params.type == 'multienum'}
						{if !empty($rowData[$col])}
							{counter name="oCount" assign="oCount" start=0}
							{multienum_to_array string=$rowData.$col assign="vals"}
							{foreach from=$vals item=item}
								{counter name="oCount"}
								{if is_array($params.options) }{$params.options[$item]}{elseif !empty($params.options) }{incomCRM_translate label=$params.options select=$item}{else}{$item}{/if}{if $oCount != count($vals)},{/if}
							{/foreach}
						{/if}
					{else}
						{$rowData.$col}
					{/if}
					{if empty($rowData.$col) && $rowData.$col != '0' }&nbsp;{/if}
				{if $col == 'NAME' || $params.bold}</b>{/if}
				</td>
				{counter name="colCounter"}
			{/foreach}
			<td align='right'>{$pageData.additionalDetails.$id}</td>
	 	</tr>
	{/foreach}
{if empty($isMobileDevice)}
	<tr class='pagination'>
		<td colspan='{$colCount+1}' align='right'>
			<table border='0' cellpadding='0' cellspacing='0' width='100%'>
			<tr>
				<td width="400"></td>
				<td>
					{include file='include/Popups/tpls/PopupPageButtons.tpl'}
				</td>
			</tr>
			</table>
		</td>
	</tr>
{/if}
</table>
</section>
{if !empty($isMobileDevice)}
<div class="pagination d-flex jc-center py-5">
	<div style="position:relative">
		{include file='include/Popups/tpls/PopupPageButtons.tpl'}
	</div>
</div>
{/if}
{if $prerow}
<a href='javascript:sListView.check_all(document.MassUpdate, "mass[]", false);'>{$clearAll}</a>
<script>
{literal}function lvg_dtails(id){return incomCRM.util.getAdditionalDetails( '{/literal}{$module}{literal}',id, 'adspan_'+id);}{/literal}
</script>
{/if}

{literal}
<script type="text/javascript">
/* initialize the popup request from the parent */
if(window.document.forms['popup_query_form'].request_data.value == "") {
	window.document.forms['popup_query_form'].request_data.value = JSON.stringify(window.opener.get_popup_request_data());
}
enableQS(false);
</script>
{/literal}
