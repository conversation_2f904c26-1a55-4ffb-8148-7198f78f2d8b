<?php
// created: 2025-02-26 15:24:03
$GL<PERSON><PERSON>LS["dictionary"]["Revenue_Promotion"] = array (
  'table' => 'Revenue_Promotions',
  'audited' => true,
  'unified_search' => false,
  'duplicate_merge' => false,
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'name',
      'dbType' => 'varchar',
      'len' => 250,
      'acl' => true,
      'audited' => true,
      'unified_search' => true,
      'merge_filter' => 'selected',
      'importable' => 'required',
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
      'comment' => 'Full text of the note',
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'revenue_promotions_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'revenue_promotions_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'branch_id' => 
    array (
      'name' => 'branch_id',
      'vname' => 'LBL_BRANCH_ID',
      'type' => 'enum',
      'options' => 'users_branch_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'BranchID of assigned user',
    ),
    'department_id' => 
    array (
      'name' => 'department_id',
      'vname' => 'LBL_DEPARTMENT_ID',
      'type' => 'enum',
      'options' => 'users_department_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'DepartmentID of assigned user',
    ),
    'assigned_user_id' => 
    array (
      'name' => 'assigned_user_id',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'vname' => 'LBL_ASSIGNED_TO_ID',
      'group' => 'assigned_user_name',
      'type' => 'relate',
      'table' => 'users',
      'module' => 'Users',
      'isnull' => 'false',
      'dbType' => 'id',
      'audited' => true,
      'reportable' => true,
      'duplicate_merge' => 'disabled',
      'comment' => 'User ID assigned to record',
    ),
    'assigned_user_name' => 
    array (
      'name' => 'assigned_user_name',
      'link' => 'assigned_user_link',
      'vname' => 'LBL_ASSIGNED_TO_NAME',
      'rname' => 'user_name',
      'type' => 'relate',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'assigned_user_id',
      'module' => 'Users',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'assigned_user_link' => 
    array (
      'name' => 'assigned_user_link',
      'type' => 'link',
      'relationship' => 'revenue_promotions_assigned_user',
      'vname' => 'LBL_ASSIGNED_TO_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
      'duplicate_merge' => 'enabled',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'table' => 'users',
    ),
    'status' => 
    array (
      'name' => 'status',
      'vname' => 'LBL_STATUS',
      'type' => 'enum',
      'options' => 'products_status_dom',
      'len' => 20,
      'audited' => true,
      'massupdate' => false,
    ),
    'date_valid_from' => 
    array (
      'name' => 'date_valid_from',
      'vname' => 'LBL_DATE_VALID_FROM',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'date_valid_to' => 
    array (
      'name' => 'date_valid_to',
      'vname' => 'LBL_DATE_VALID_TO',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'month_discount' => 
    array (
      'name' => 'month_discount',
      'vname' => 'LBL_MONTH_DISCOUNT',
      'type' => 'double',
      'audited' => true,
    ),
    'month_amount' => 
    array (
      'name' => 'month_amount',
      'vname' => 'LBL_MONTH_AMOUNT',
      'type' => 'double',
      'audited' => true,
    ),
    'quarter_discount' => 
    array (
      'name' => 'quarter_discount',
      'vname' => 'LBL_QUARTER_DISCOUNT',
      'type' => 'double',
      'audited' => true,
    ),
    'quarter_amount' => 
    array (
      'name' => 'quarter_amount',
      'vname' => 'LBL_QUARTER_AMOUNT',
      'type' => 'double',
      'audited' => true,
    ),
    'year_discount' => 
    array (
      'name' => 'year_discount',
      'vname' => 'LBL_YEAR_DISCOUNT',
      'type' => 'double',
      'audited' => true,
    ),
    'year_amount' => 
    array (
      'name' => 'year_amount',
      'vname' => 'LBL_YEAR_AMOUNT',
      'type' => 'double',
      'audited' => true,
    ),
    'qt_target' => 
    array (
      'name' => 'qt_target',
      'vname' => 'LBL_QT_TARGET',
      'type' => 'double',
      'audited' => true,
    ),
    'qt_amount_1' => 
    array (
      'name' => 'qt_amount_1',
      'vname' => 'LBL_QT_AMOUNT_1',
      'type' => 'double',
      'audited' => true,
    ),
    'qt_amount_2' => 
    array (
      'name' => 'qt_amount_2',
      'vname' => 'LBL_QT_AMOUNT_2',
      'type' => 'double',
      'audited' => true,
    ),
    'qt_amount_3' => 
    array (
      'name' => 'qt_amount_3',
      'vname' => 'LBL_QT_AMOUNT_3',
      'type' => 'double',
      'audited' => true,
    ),
    'qt_amount_4' => 
    array (
      'name' => 'qt_amount_4',
      'vname' => 'LBL_QT_AMOUNT_4',
      'type' => 'double',
      'audited' => true,
    ),
    'branch_all' => 
    array (
      'name' => 'branch_all',
      'vname' => 'LBL_BRANCH_ALL',
      'type' => 'enum',
      'options' => 'branch_all_dom',
      'dbType' => 'text',
      'isMultiSelect' => true,
      'isCheckBoxList' => true,
      'audited' => true,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
    ),
    'account_promotion_all' => 
    array (
      'name' => 'account_promotion_all',
      'vname' => 'LBL_ACCOUNT_PROMOTION_ALL',
      'type' => 'enum',
      'options' => 'membership_card_dom',
      'dbType' => 'varchar',
      'len' => 250,
      'isMultiSelect' => true,
      'isCheckBoxList' => true,
      'audited' => true,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
    ),
    'tqt_productseries_id' => 
    array (
      'name' => 'tqt_productseries_id',
      'vname' => 'LBL_TQT_PRODUCTSERIES_ID',
      'type' => 'id',
      'required' => false,
      'reportable' => false,
    ),
    'tqt_productseries_name' => 
    array (
      'name' => 'tqt_productseries_name',
      'rname' => 'name',
      'id_name' => 'tqt_productseries_id',
      'vname' => 'LBL_TQT_PRODUCTSERIES_NAME',
      'table' => 'tqt_productseries',
      'type' => 'relate',
      'link' => 'tqt_productseries',
      'join_name' => 'tqt_productseries',
      'isnull' => 'true',
      'module' => 'TQT_ProductSeries',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'tqt_productseries' => 
    array (
      'name' => 'tqt_productseries',
      'type' => 'link',
      'relationship' => 'tqt_productseries_revenue_promotion',
      'vname' => 'LBL_LIST_PRODUCTSERIES_NAME',
      'source' => 'non-db',
    ),
    'prod_series_teams' => 
    array (
      'name' => 'prod_series_teams',
      'vname' => 'LBL_PROD_SERIES_TEAMS',
      'type' => 'enum',
      'dbType' => 'text',
      'module' => 'TQT_ProductSeries',
      'function' => 'getProductSeriesTeamOptions',
      'isMultiSelect' => true,
      'isCheckBoxList' => true,
      'audited' => true,
      'massupdate' => false,
    ),
    'unit_type' => 
    array (
      'name' => 'unit_type',
      'vname' => 'LBL_UNIT_TYPE',
      'type' => 'enum',
      'options' => 'unit_management_dom',
      'len' => 50,
      'massupdate' => false,
      'source' => 'non-db',
    ),
    'period_type' => 
    array (
      'name' => 'period_type',
      'vname' => 'LBL_PERIOD_TYPE',
      'type' => 'enum',
      'options' => 'revenue_promotion_period_type_dom',
      'len' => 20,
      'massupdate' => false,
      'source' => 'non-db',
    ),
    'period_date' => 
    array (
      'name' => 'period_date',
      'vname' => 'LBL_PERIOD_DATE',
      'type' => 'varchar',
      'len' => 50,
      'massupdate' => false,
      'source' => 'non-db',
    ),
    'amount_sales' => 
    array (
      'name' => 'amount_sales',
      'vname' => 'LBL_AMOUNT_SALES',
      'type' => 'double',
      'source' => 'non-db',
    ),
    'amount_total' => 
    array (
      'name' => 'amount_total',
      'vname' => 'LBL_AMOUNT_TOTAL',
      'type' => 'double',
      'source' => 'non-db',
    ),
    'amount_used' => 
    array (
      'name' => 'amount_used',
      'vname' => 'LBL_AMOUNT_USED',
      'type' => 'double',
      'source' => 'non-db',
    ),
    'amount_left' => 
    array (
      'name' => 'amount_left',
      'vname' => 'LBL_AMOUNT_LEFT',
      'type' => 'double',
      'source' => 'non-db',
    ),
  ),
  'indices' => 
  array (
    'id' => 
    array (
      'name' => 'revenue_promotionspk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    'idx_revenue_promotions_branch_id' => 
    array (
      'name' => 'idx_revenue_promotions_branch_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
      ),
    ),
    'idx_revenue_promotions_department_id' => 
    array (
      'name' => 'idx_revenue_promotions_department_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'department_id',
      ),
    ),
    'idx_revenue_promotions_branch_dept' => 
    array (
      'name' => 'idx_revenue_promotions_branch_dept',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
        1 => 'department_id',
      ),
    ),
    'idx_revenue_promotions_assigned' => 
    array (
      'name' => 'idx_revenue_promotions_assigned',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'assigned_user_id',
      ),
    ),
    0 => 
    array (
      'name' => 'idx_revenue_promotions_id_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'id',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_revenue_promotions_name_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'name',
      ),
    ),
    2 => 
    array (
      'name' => 'idx_revenue_promotions_status_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'status',
      ),
    ),
    3 => 
    array (
      'name' => 'idx_revenue_promotions_date_valid',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'date_valid_from',
        1 => 'date_valid_to',
      ),
    ),
    4 => 
    array (
      'name' => 'idx_revenue_promotions_sid',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'tqt_productseries_id',
      ),
    ),
  ),
  'relationships' => 
  array (
    'revenue_promotions_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Revenue_Promotions',
      'rhs_table' => 'revenue_promotions',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'revenue_promotions_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Revenue_Promotions',
      'rhs_table' => 'revenue_promotions',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
    'revenue_promotions_assigned_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Revenue_Promotions',
      'rhs_table' => 'revenue_promotions',
      'rhs_key' => 'assigned_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'tqt_productseries_revenue_promotion' => 
    array (
      'lhs_module' => 'TQT_ProductSeries',
      'lhs_table' => 'tqt_productseries',
      'lhs_key' => 'id',
      'rhs_module' => 'Revenue_Promotions',
      'rhs_table' => 'revenue_promotions',
      'rhs_key' => 'tqt_productseries_id',
      'relationship_type' => 'one-to-one',
    ),
  ),
  'optimistic_lock' => true,
  'templates' => 
  array (
    'assignable' => 'assignable',
    'groups' => 'groups',
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
