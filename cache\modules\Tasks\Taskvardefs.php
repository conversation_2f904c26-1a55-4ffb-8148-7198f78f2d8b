<?php
// created: 2025-03-01 09:58:13
$<PERSON><PERSON><PERSON><PERSON><PERSON>["dictionary"]["Task"] = array (
  'table' => 'tasks',
  'audited' => true,
  'unified_search' => true,
  'duplicate_merge' => false,
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'dbType' => 'varchar',
      'type' => 'name',
      'len' => '250',
      'importable' => 'required',
      'audited' => true,
      'required' => false,
      'unified_search' => true,
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
      'comment' => 'Full text of the note',
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'tasks_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'tasks_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'branch_id' => 
    array (
      'name' => 'branch_id',
      'vname' => 'LBL_BRANCH_ID',
      'type' => 'enum',
      'options' => 'users_branch_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'BranchID of assigned user',
    ),
    'department_id' => 
    array (
      'name' => 'department_id',
      'vname' => 'LBL_DEPARTMENT_ID',
      'type' => 'enum',
      'options' => 'users_department_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'DepartmentID of assigned user',
    ),
    'assigned_user_id' => 
    array (
      'name' => 'assigned_user_id',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'vname' => 'LBL_ASSIGNED_TO_ID',
      'group' => 'assigned_user_name',
      'type' => 'relate',
      'table' => 'users',
      'module' => 'Users',
      'isnull' => 'false',
      'dbType' => 'id',
      'audited' => true,
      'reportable' => true,
      'duplicate_merge' => 'disabled',
      'comment' => 'User ID assigned to record',
    ),
    'assigned_user_name' => 
    array (
      'name' => 'assigned_user_name',
      'link' => 'assigned_user_link',
      'vname' => 'LBL_ASSIGNED_TO_NAME',
      'rname' => 'user_name',
      'type' => 'relate',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'assigned_user_id',
      'module' => 'Users',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'assigned_user_link' => 
    array (
      'name' => 'assigned_user_link',
      'type' => 'link',
      'relationship' => 'tasks_assigned_user',
      'vname' => 'LBL_ASSIGNED_TO_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
      'duplicate_merge' => 'enabled',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'table' => 'users',
    ),
    'parent_type' => 
    array (
      'name' => 'parent_type',
      'vname' => 'LBL_PARENT_NAME',
      'type' => 'parent_type',
      'dbType' => 'varchar',
      'group' => 'parent_name',
      'len' => '50',
      'audited' => true,
      'required' => false,
      'massupdate' => false,
      'display_default' => 'Accounts',
      'comment' => 'The incomCRM object to which the call is related',
    ),
    'parent_name' => 
    array (
      'name' => 'parent_name',
      'parent_type' => 'record_type_display',
      'type_name' => 'parent_type',
      'id_name' => 'parent_id',
      'vname' => 'LBL_LIST_RELATED_TO',
      'type' => 'parent',
      'group' => 'parent_name',
      'source' => 'non-db',
      'options' => 'parent_type_display',
      'massupdate' => false,
    ),
    'parent_id' => 
    array (
      'name' => 'parent_id',
      'type' => 'id',
      'group' => 'parent_name',
      'vname' => 'LBL_PARENT_ID',
      'reportable' => false,
    ),
    'status' => 
    array (
      'name' => 'status',
      'vname' => 'LBL_STATUS',
      'type' => 'enum',
      'options' => 'task_status_dom',
      'len' => 25,
      'audited' => true,
      'display_default' => 'Planned',
    ),
    'priority' => 
    array (
      'name' => 'priority',
      'vname' => 'LBL_PRIORITY',
      'type' => 'enum',
      'options' => 'task_priority_dom',
      'len' => 25,
      'audited' => true,
      'massupdate' => false,
    ),
    'date_due_flag' => 
    array (
      'name' => 'date_due_flag',
      'vname' => 'LBL_DATE_DUE_FLAG',
      'type' => 'bool',
      'default' => 1,
      'group' => 'date_due',
      'massupdate' => false,
    ),
    'date_due' => 
    array (
      'name' => 'date_due',
      'vname' => 'LBL_DUE_DATE',
      'type' => 'datetime',
      'group' => 'date_due',
      'audited' => true,
      'massupdate' => false,
      'display_default' => 'now',
    ),
    'time_due' => 
    array (
      'name' => 'time_due',
      'vname' => 'LBL_DUE_TIME',
      'type' => 'datetime',
      'source' => 'non-db',
      'importable' => 'false',
      'massupdate' => false,
    ),
    'date_due_field' => 
    array (
      'name' => 'date_due_field',
      'group' => 'date_due',
      'vname' => 'LBL_DUE_DATE_AND_TIME',
      'type' => 'datetimecombo',
      'date' => 'date_due',
      'time' => 'time_due',
      'date_readonly' => 'date_due_readonly',
      'time_readonly' => 'time_due_readonly',
      'noneCheckbox' => true,
      'noneCheckboxJavascript' => 'onClick="set_date_due_values(this.form);"',
      'checkboxId' => 'date_due_flag',
      'checked' => 'date_due_checked',
      'meridian' => 'date_due_meridian',
      'showFormats' => true,
      'source' => 'non-db',
      'comment' => 'Used for meta-data framework',
      'importable' => 'false',
    ),
    'date_start_flag' => 
    array (
      'name' => 'date_start_flag',
      'vname' => 'LBL_DATE_START_FLAG',
      'type' => 'bool',
      'group' => 'date_start',
      'default' => 1,
      'massupdate' => false,
    ),
    'date_start' => 
    array (
      'name' => 'date_start',
      'vname' => 'LBL_START_DATE',
      'type' => 'datetime',
      'group' => 'date_start',
      'audited' => true,
      'massupdate' => false,
      'display_default' => 'now',
    ),
    'date_start_field' => 
    array (
      'group' => 'date_start',
      'name' => 'date_start_field',
      'vname' => 'LBL_DUE_DATE_AND_TIME',
      'type' => 'datetimecombo',
      'date' => 'date_start',
      'time' => 'time_start',
      'date_readonly' => 'date_start_readonly',
      'time_readonly' => 'time_start_readonly',
      'noneCheckbox' => true,
      'noneCheckboxJavascript' => 'onClick="set_date_start_values(this.form);"',
      'checkboxId' => 'date_start_flag',
      'checked' => 'date_start_checked',
      'meridian' => 'date_start_meridian',
      'showFormats' => true,
      'source' => 'non-db',
      'comment' => 'Used for meta-data framework',
    ),
    'contact_id' => 
    array (
      'name' => 'contact_id',
      'type' => 'id',
      'group' => 'contact_name',
      'reportable' => false,
      'vname' => 'LBL_CONTACT_ID',
      'audited' => true,
    ),
    'contact_name' => 
    array (
      'name' => 'contact_name',
      'rname' => 'last_name',
      'db_concat_fields' => 
      array (
        0 => 'first_name',
        1 => 'last_name',
      ),
      'source' => 'non-db',
      'len' => '510',
      'group' => 'contact_name',
      'vname' => 'LBL_CONTACT_NAME',
      'reportable' => false,
      'id_name' => 'contact_id',
      'join_name' => 'contacts',
      'type' => 'relate',
      'module' => 'Contacts',
      'link' => 'contacts_direct',
      'table' => 'contacts',
      'massupdate' => false,
    ),
    'contact_phone' => 
    array (
      'name' => 'contact_phone',
      'type' => 'phone',
      'source' => 'non-db',
      'vname' => 'LBL_CONTACT_PHONE',
    ),
    'contact_email' => 
    array (
      'name' => 'contact_email',
      'type' => 'varchar',
      'vname' => 'LBL_EMAIL_ADDRESS',
      'source' => 'non-db',
    ),
    'contacts_direct' => 
    array (
      'name' => 'contacts_direct',
      'type' => 'link',
      'relationship' => 'contacts_tasks_direct',
      'source' => 'non-db',
      'side' => 'right',
      'vname' => 'LBL_CONTACT',
    ),
    'contacts' => 
    array (
      'name' => 'contacts',
      'type' => 'link',
      'relationship' => 'contact_tasks',
      'source' => 'non-db',
      'side' => 'right',
      'vname' => 'LBL_CONTACT',
    ),
    'accounts' => 
    array (
      'name' => 'accounts',
      'type' => 'link',
      'relationship' => 'account_tasks',
      'source' => 'non-db',
      'vname' => 'LBL_ACCOUNT',
    ),
    'opportunities' => 
    array (
      'name' => 'opportunities',
      'type' => 'link',
      'relationship' => 'opportunity_tasks',
      'source' => 'non-db',
      'vname' => 'LBL_OPPORTUNITY',
    ),
    'cases' => 
    array (
      'name' => 'cases',
      'type' => 'link',
      'relationship' => 'case_tasks',
      'source' => 'non-db',
      'vname' => 'LBL_CASE',
    ),
    'bugs' => 
    array (
      'name' => 'bugs',
      'type' => 'link',
      'relationship' => 'bug_tasks',
      'source' => 'non-db',
      'vname' => 'LBL_BUGS',
    ),
    'leads' => 
    array (
      'name' => 'leads',
      'type' => 'link',
      'relationship' => 'lead_tasks',
      'source' => 'non-db',
      'vname' => 'LBL_LEADS',
    ),
    'projects' => 
    array (
      'name' => 'projects',
      'type' => 'link',
      'relationship' => 'projects_tasks',
      'source' => 'non-db',
      'vname' => 'LBL_PROJECTS',
    ),
    'project_tasks' => 
    array (
      'name' => 'project_tasks',
      'type' => 'link',
      'relationship' => 'project_tasks_tasks',
      'source' => 'non-db',
      'vname' => 'LBL_PROJECT_TASKS',
    ),
    'notes' => 
    array (
      'name' => 'notes',
      'type' => 'link',
      'relationship' => 'tasks_notes',
      'module' => 'Notes',
      'bean_name' => 'Note',
      'source' => 'non-db',
      'vname' => 'LBL_NOTES',
    ),
    'user_err_id' => 
    array (
      'name' => 'user_err_id',
      'type' => 'id',
      'group' => 'user_err_name',
      'reportable' => false,
      'vname' => 'LBL_USER_ERR_ID',
      'audited' => true,
    ),
    'user_err_name' => 
    array (
      'name' => 'user_err_name',
      'rname' => 'last_name',
      'db_concat_fields' => 
      array (
        0 => 'first_name',
        1 => 'last_name',
      ),
      'source' => 'non-db',
      'len' => '510',
      'group' => 'user_err_name',
      'vname' => 'LBL_USER_ERR_NAME',
      'reportable' => false,
      'id_name' => 'user_err_id',
      'join_name' => 'user_errs',
      'type' => 'relate',
      'module' => 'Users',
      'link' => 'user_err_tasks',
      'table' => 'users',
      'massupdate' => false,
    ),
    'user_errs' => 
    array (
      'name' => 'user_errs',
      'type' => 'link',
      'relationship' => 'user_errs_tasks',
      'source' => 'non-db',
      'side' => 'right',
      'vname' => 'LBL_USER_ERRS',
    ),
    'description_error' => 
    array (
      'name' => 'description_error',
      'vname' => 'LBL_DESCRIPTION_ERROR',
      'type' => 'text',
      'audited' => true,
    ),
    'confirm' => 
    array (
      'name' => 'confirm',
      'vname' => 'LBL_CONFIRM',
      'type' => 'bool',
      'default' => 0,
      'audited' => true,
      'massupdate' => false,
    ),
    'subject' => 
    array (
      'name' => 'subject',
      'vname' => 'LBL_SUBJECT',
      'dbType' => 'varchar',
      'type' => 'name',
      'len' => '250',
    ),
    'task_relate_id' => 
    array (
      'name' => 'task_relate_id',
      'vname' => 'LBL_TASK_RELATE_ID',
      'type' => 'id',
      'required' => false,
      'reportable' => false,
      'audited' => true,
      'comment' => 'Task ID of the parent of this task',
    ),
    'task_relate_name' => 
    array (
      'name' => 'task_relate_name',
      'rname' => 'subject',
      'id_name' => 'task_relate_id',
      'vname' => 'LBL_TASK_RELATE_NAME',
      'type' => 'relate',
      'isnull' => 'true',
      'module' => 'Tasks',
      'massupdate' => false,
      'source' => 'non-db',
      'len' => 36,
      'link' => 'member_of',
      'unified_search' => true,
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
    ),
    'main_id' => 
    array (
      'name' => 'main_id',
      'vname' => 'LBL_MAIN_TASK_ID',
      'type' => 'id',
      'required' => false,
      'reportable' => false,
      'audited' => true,
      'comment' => 'Task ID of the parent of this task',
    ),
    'main_name' => 
    array (
      'name' => 'main_name',
      'rname' => 'subject',
      'id_name' => 'main_id',
      'vname' => 'LBL_MAIN_TASK_NAME',
      'type' => 'relate',
      'isnull' => 'true',
      'module' => 'Tasks',
      'massupdate' => false,
      'source' => 'non-db',
      'len' => 36,
      'link' => 'member_of',
      'unified_search' => true,
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
    ),
    'members' => 
    array (
      'name' => 'members',
      'type' => 'link',
      'relationship' => 'member_tasks',
      'module' => 'Tasks',
      'bean_name' => 'Task',
      'source' => 'non-db',
      'vname' => 'LBL_MEMBERS',
      'duplicate_merge' => 'disabled',
    ),
    'member_of' => 
    array (
      'name' => 'member_of',
      'type' => 'link',
      'relationship' => 'member_tasks',
      'module' => 'Tasks',
      'bean_name' => 'Task',
      'link_type' => 'one',
      'source' => 'non-db',
      'vname' => 'LBL_MEMBER_OF',
      'side' => 'left',
      'duplicate_merge' => 'disabled',
    ),
    'trackreview' => 
    array (
      'name' => 'trackreview',
      'type' => 'link',
      'relationship' => 'tasks_trackreview',
      'source' => 'non-db',
      'vname' => 'LBL_TRACK_REVIEWS',
    ),
    'tqt_comments' => 
    array (
      'name' => 'tqt_comments',
      'type' => 'link',
      'relationship' => 'tasks_tqt_comments',
      'source' => 'non-db',
      'vname' => 'LBL_TQT_COMMENTS',
    ),
    'SecurityGroups' => 
    array (
      'name' => 'SecurityGroups',
      'type' => 'link',
      'relationship' => 'securitygroups_tasks',
      'module' => 'SecurityGroups',
      'bean_name' => 'SecurityGroup',
      'source' => 'non-db',
      'vname' => 'LBL_SECURITYGROUPS',
    ),
    'panel_name' => 
    array (
      'name' => 'panel_name',
      'type' => 'varchar',
      'len' => '100',
      'source' => 'non-db',
      'vname' => 'LBL_PANEL_NAME',
    ),
    'date_converter' => 
    array (
      'name' => 'date_converter',
      'vname' => 'LBL_DATE_CONVERTER',
      'type' => 'date',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'contact_info' => 
    array (
      'name' => 'contact_info',
      'vname' => 'LBL_CONTACT_INFO',
      'type' => 'text',
    ),
    'comment' => 
    array (
      'name' => 'comment',
      'vname' => 'LBL_COMMENT',
      'type' => 'text',
      'audited' => true,
      'massupdate' => false,
    ),
    'cmt_type' => 
    array (
      'name' => 'cmt_type',
      'vname' => 'LBL_CMT_TYPE',
      'type' => 'enum',
      'options' => 'comment_types_dom',
      'len' => '100',
      'audited' => true,
      'massupdate' => false,
    ),
    'cmt_user' => 
    array (
      'name' => 'cmt_user',
      'vname' => 'LBL_CMT_USER',
      'type' => 'varchar',
      'len' => '100',
      'audited' => true,
    ),
    'share_users' => 
    array (
      'name' => 'share_users',
      'vname' => 'LBL_SHARE_USERS',
      'type' => 'enum',
      'source' => 'non-db',
      'module' => 'Users',
      'function' => 'getShareUserOptions',
      'isMultiSelect' => true,
      'isCheckBoxList' => true,
      'displayGroupBy' => 'users.department',
      'massupdate' => false,
    ),
    'care_result' => 
    array (
      'name' => 'care_result',
      'vname' => 'LBL_CARE_RESULT',
      'type' => 'enum',
      'options' => 'task_care_result_dom',
      'len' => 50,
      'audited' => true,
      'massupdate' => false,
    ),
    'opportunity_id' => 
    array (
      'name' => 'opportunity_id',
      'vname' => 'LBL_OPPORTUNITY_ID',
      'type' => 'id',
      'audited' => true,
      'required' => false,
      'reportable' => false,
      'massupdate' => false,
    ),
    'opportunity_name' => 
    array (
      'name' => 'opportunity_name',
      'rname' => 'name',
      'id_name' => 'opportunity_id',
      'vname' => 'LBL_OPPORTUNITY_NAME',
      'table' => 'opportunities',
      'type' => 'relate',
      'link' => 'opportunities_direct',
      'join_name' => 'opportunities',
      'isnull' => 'true',
      'module' => 'Opportunities',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'opportunities_direct' => 
    array (
      'name' => 'opportunities_direct',
      'type' => 'link',
      'relationship' => 'opportunities_tasks_direct',
      'vname' => 'LBL_OPPORTUNITIES',
      'source' => 'non-db',
    ),
    'group_id' => 
    array (
      'name' => 'group_id',
      'vname' => 'LBL_GROUP_ID',
      'type' => 'id',
      'audited' => true,
      'massupdate' => false,
    ),
    'group_name' => 
    array (
      'name' => 'group_name',
      'rname' => 'name',
      'id_name' => 'group_id',
      'vname' => 'LBL_GROUP_NAME',
      'table' => 'tasks_groups',
      'type' => 'relate',
      'link' => 'tasks_groups',
      'join_name' => 'tasks_groups',
      'isnull' => 'true',
      'module' => 'Tasks_Groups',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'tasks_groups' => 
    array (
      'name' => 'tasks_groups',
      'type' => 'link',
      'relationship' => 'tasks_groups_tasks',
      'vname' => 'LBL_TASKS_GROUPS',
      'source' => 'non-db',
    ),
    'group_users' => 
    array (
      'name' => 'group_users',
      'type' => 'link',
      'relationship' => 'tasks_group_users',
      'vname' => 'LBL_GROUP_USERS',
      'source' => 'non-db',
    ),
    'group_steps' => 
    array (
      'name' => 'group_steps',
      'vname' => 'LBL_GROUP_STEPS',
      'type' => 'text',
      'source' => 'non-db',
    ),
    'job_id' => 
    array (
      'name' => 'job_id',
      'vname' => 'LBL_JOB_ID',
      'type' => 'id',
      'audited' => true,
      'massupdate' => false,
    ),
    'job_type' => 
    array (
      'name' => 'job_type',
      'vname' => 'LBL_JOB_TYPE',
      'type' => 'enum',
      'options' => 'todo_list_job_type_dom',
      'dbType' => 'char',
      'len' => 1,
      'audited' => true,
      'massupdate' => false,
      'display_default' => '1',
    ),
    'job_mark' => 
    array (
      'name' => 'job_mark',
      'vname' => 'LBL_JOB_MARK',
      'type' => 'tinyint',
      'len' => 4,
      'audited' => true,
      'massupdate' => false,
    ),
    'processing_time' => 
    array (
      'name' => 'processing_time',
      'vname' => 'LBL_PROCESSING_TIME',
      'type' => 'int',
      'len' => 4,
      'audited' => true,
      'massupdate' => false,
    ),
    'working_time_by' => 
    array (
      'name' => 'working_time_by',
      'vname' => 'LBL_WORKING_TIME_BY',
      'type' => 'tinyint',
      'len' => 2,
      'source' => 'non-db',
      'audited' => false,
      'massupdate' => false,
      'reportable' => false,
    ),
    'ref_id' => 
    array (
      'name' => 'ref_id',
      'vname' => 'LBL_REF_ID',
      'type' => 'id',
      'audited' => false,
      'massupdate' => false,
      'reportable' => false,
    ),
    'table_params' => 
    array (
      'name' => 'table_params',
      'vname' => 'LBL_TABLE_PARAMS',
      'type' => 'text',
      'audited' => false,
      'massupdate' => false,
      'reportable' => false,
    ),
    'recurrence_data' => 
    array (
      'name' => 'recurrence_data',
      'vname' => 'LBL_RECURRENCE_DATA',
      'type' => 'text',
      'source' => 'non-db',
      'reportable' => false,
    ),
    'recurrence_text' => 
    array (
      'name' => 'recurrence_text',
      'vname' => 'LBL_RECURRENCE_TEXT',
      'type' => 'text',
      'source' => 'non-db',
      'reportable' => false,
    ),
    'is_locked' => 
    array (
      'name' => 'is_locked',
      'vname' => 'LBL_IS_LOCKED',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => false,
      'default' => 0,
    ),
    'asap' => 
    array (
      'name' => 'asap',
      'vname' => 'LBL_ASAP',
      'type' => 'enum',
      'dbType' => 'tinyint',
      'options' => 'task_asap_dom',
      'len' => 1,
      'audited' => true,
      'massupdate' => false,
      'default' => 0,
    ),
    'transactions' => 
    array (
      'name' => 'transactions',
      'vname' => 'LBL_TRANSACTIONS',
      'type' => 'enum',
      'options' => 'task_transactions_dom',
      'len' => 100,
      'audited' => true,
      'massupdate' => true,
    ),
    'communication' => 
    array (
      'name' => 'communication',
      'vname' => 'LBL_COMMUNICATION',
      'type' => 'enum',
      'options' => 'task_communication_dom',
      'len' => 100,
      'audited' => true,
      'massupdate' => true,
    ),
    'date_completion' => 
    array (
      'name' => 'date_completion',
      'vname' => 'LBL_COMPLETION_DATE',
      'type' => 'datetime',
      'group' => 'date_completion',
      'audited' => true,
      'massupdate' => false,
    ),
    'time_completion' => 
    array (
      'name' => 'time_completion',
      'vname' => 'LBL_COMPLETION_TIME',
      'type' => 'datetime',
      'source' => 'non-db',
      'importable' => 'false',
      'massupdate' => false,
    ),
    'date_completion_flag' => 
    array (
      'name' => 'date_completion_flag',
      'vname' => 'LBL_DATE_COMPLETION_FLAG',
      'type' => 'bool',
      'group' => 'date_completion',
      'default' => 1,
      'massupdate' => false,
    ),
    'date_completion_field' => 
    array (
      'group' => 'date_completion',
      'name' => 'date_completion_field',
      'vname' => 'LBL_COMPLETION_DATE_AND_TIME',
      'type' => 'datetimecombo',
      'date' => 'date_completion',
      'time' => 'time_completion',
      'date_readonly' => 'date_completion_readonly',
      'time_readonly' => 'time_completion_readonly',
      'noneCheckbox' => true,
      'noneCheckboxJavascript' => 'onClick="set_date_completion_values(this.form);"',
      'checkboxId' => 'date_completion_flag',
      'checked' => 'date_completion_checked',
      'meridian' => 'date_completion_meridian',
      'showFormats' => true,
      'source' => 'non-db',
      'comment' => 'Used for meta-data framework',
    ),
    'date_meeting' => 
    array (
      'name' => 'date_meeting',
      'vname' => 'LBL_MEETING_DATE',
      'type' => 'datetime',
      'group' => 'date_meeting',
      'audited' => true,
      'massupdate' => false,
    ),
    'time_meeting' => 
    array (
      'name' => 'time_meeting',
      'vname' => 'LBL_MEETING_TIME',
      'type' => 'datetime',
      'source' => 'non-db',
      'importable' => 'false',
      'massupdate' => false,
    ),
    'date_meeting_flag' => 
    array (
      'name' => 'date_meeting_flag',
      'vname' => 'LBL_DATE_MEETING_FLAG',
      'type' => 'bool',
      'group' => 'date_meeting',
      'default' => 1,
      'massupdate' => false,
    ),
    'date_meeting_field' => 
    array (
      'group' => 'date_meeting',
      'name' => 'date_meeting_field',
      'vname' => 'LBL_MEETING_DATE_AND_TIME',
      'type' => 'datetimecombo',
      'date' => 'date_meeting',
      'time' => 'time_meeting',
      'date_readonly' => 'date_meeting_readonly',
      'time_readonly' => 'time_meeting_readonly',
      'noneCheckbox' => true,
      'noneCheckboxJavascript' => 'onClick="set_date_meeting_values(this.form);"',
      'checkboxId' => 'date_meeting_flag',
      'checked' => 'date_meeting_checked',
      'meridian' => 'date_meeting_meridian',
      'showFormats' => true,
      'source' => 'non-db',
      'comment' => 'Used for meta-data framework',
    ),
    'support_by' => 
    array (
      'name' => 'support_by',
      'vname' => 'LBL_SUPPORT_BY',
      'type' => 'bool',
      'massupdate' => false,
    ),
    'date_meeting_from' => 
    array (
      'name' => 'date_meeting_from',
      'vname' => 'LBL_MEETING_DATE_FORM',
      'type' => 'date',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'date_meeting_to' => 
    array (
      'name' => 'date_meeting_to',
      'vname' => 'LBL_MEETING_DATE_TO',
      'type' => 'date',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'reminder_checked' => 
    array (
      'name' => 'reminder_checked',
      'vname' => 'LBL_REMINDER',
      'type' => 'bool',
      'source' => 'non-db',
      'comment' => 'checkbox indicating whether or not the reminder value is set (Meta-data only)',
      'massupdate' => false,
    ),
    'reminder_time' => 
    array (
      'name' => 'reminder_time',
      'vname' => 'LBL_REMINDER_TIME',
      'type' => 'int',
      'function' => 
      array (
        'name' => 'getReminderTime',
        'returns' => 'html',
        'include' => 'modules/Calls/CallHelper.php',
      ),
      'required' => false,
      'reportable' => false,
      'default' => -1,
      'len' => '6',
      'comment' => 'Specifies when a reminder alert should be issued; -1 means no alert; otherwise the number of seconds prior to the start',
    ),
    'note' => 
    array (
      'name' => 'note',
      'vname' => 'LBL_NOTE',
      'type' => 'text',
      'audited' => true,
    ),
    'content' => 
    array (
      'name' => 'content',
      'vname' => 'LBL_CONTENT',
      'type' => 'text',
      'audited' => true,
    ),
    'next_step' => 
    array (
      'name' => 'next_step',
      'vname' => 'LBL_NEXT_STEP',
      'type' => 'text',
      'audited' => true,
    ),
    'location_nearby' => 
    array (
      'name' => 'location_nearby',
      'vname' => 'LBL_LOCATION_NEARBY',
      'type' => 'varchar',
      'len' => 100,
      'id_address' => 'location_address',
    ),
    'location_address' => 
    array (
      'name' => 'location_address',
      'vname' => 'LBL_LOCATION_ADDRESS',
      'type' => 'varchar',
      'len' => 250,
      'source' => 'non-db',
    ),
    'parent_phone' => 
    array (
      'name' => 'parent_phone',
      'vname' => 'LBL_PARENT_PHONE',
      'type' => 'varchar',
      'len' => '50',
      'source' => 'non-db',
    ),
    'parent_addr' => 
    array (
      'name' => 'parent_addr',
      'vname' => 'LBL_PARENT_ADDR',
      'type' => 'text',
      'source' => 'non-db',
    ),
    'notifications' => 
    array (
      'name' => 'notifications',
      'vname' => 'LBL_NOTIFICATIONS',
      'type' => 'link',
      'relationship' => 'tasks_notifications',
      'source' => 'non-db',
    ),
    'pin_id' => 
    array (
      'name' => 'pin_id',
      'type' => 'id',
      'vname' => 'LBL_PIN_ID',
      'reportable' => false,
    ),
    'users_all' => 
    array (
      'name' => 'users_all',
      'vname' => 'LBL_USERS_ALL',
      'type' => 'enum',
      'dbType' => 'text',
      'module' => 'Users',
      'function' => 'getShareUserOptions',
      'isMultiSelect' => true,
      'isCheckBoxList' => true,
      'displayGroupBy' => 'users.department',
      'massupdate' => false,
      'reportable' => false,
    ),
    'forward_user_id' => 
    array (
      'name' => 'forward_user_id',
      'vname' => 'LBL_FORWARD_USER_ID',
      'type' => 'id',
      'audited' => true,
      'massupdate' => false,
    ),
    'forward_user_name' => 
    array (
      'name' => 'forward_user_name',
      'rname' => 'user_name',
      'id_name' => 'forward_user_id',
      'vname' => 'LBL_FORWARD_USER_NAME',
      'source' => 'non-db',
      'type' => 'relate',
      'link' => 'forward_users',
      'join_name' => 'forwards',
      'table' => 'users',
      'module' => 'Users',
      'massupdate' => false,
    ),
    'forward_users' => 
    array (
      'name' => 'forward_users',
      'vname' => 'LBL_FORWARD_USERS',
      'type' => 'link',
      'source' => 'non-db',
      'relationship' => 'forward_users_tasks',
    ),
    'is_opp_sales' => 
    array (
      'name' => 'is_opp_sales',
      'vname' => 'LBL_IS_OPP_SALES',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => false,
    ),
    'refusal_reasons' => 
    array (
      'name' => 'refusal_reasons',
      'vname' => 'LBL_REFUSAL_REASONS',
      'type' => 'text',
      'audited' => true,
    ),
    'vehicle_work' => 
    array (
      'name' => 'vehicle_work',
      'vname' => 'LBL_VEHICLE_WORK',
      'type' => 'enum',
      'options' => 'task_vehicle_work_dom',
      'dbType' => 'char',
      'len' => 1,
      'default' => '0',
      'audited' => true,
      'massupdate' => false,
    ),
    'vehicle_reasons' => 
    array (
      'name' => 'vehicle_reasons',
      'vname' => 'LBL_VEHICLE_REASONS',
      'type' => 'text',
      'audited' => true,
    ),
    'approval_level_id' => 
    array (
      'name' => 'approval_level_id',
      'vname' => 'LBL_APPROVAL_LEVEL_ID',
      'type' => 'id',
      'audited' => true,
      'massupdate' => false,
    ),
    'approval_level_name' => 
    array (
      'name' => 'approval_level_name',
      'rname' => 'name',
      'id_name' => 'approval_level_id',
      'vname' => 'LBL_APPROVAL_LEVEL_NAME',
      'table' => 'approval_levels',
      'type' => 'relate',
      'link' => 'approval_levels',
      'join_name' => 'approval_levels',
      'isnull' => 'true',
      'module' => 'Approval_Levels',
      'source' => 'non-db',
      'audited' => true,
      'massupdate' => false,
    ),
    'approval_levels' => 
    array (
      'name' => 'approval_levels',
      'type' => 'link',
      'relationship' => 'approval_levels_tasks',
      'vname' => 'LBL_TASKS_APPROVAL_LEVELS',
      'source' => 'non-db',
    ),
  ),
  'indices' => 
  array (
    'id' => 
    array (
      'name' => 'taskspk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    'idx_tasks_branch_id' => 
    array (
      'name' => 'idx_tasks_branch_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
      ),
    ),
    'idx_tasks_department_id' => 
    array (
      'name' => 'idx_tasks_department_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'department_id',
      ),
    ),
    'idx_tasks_branch_dept' => 
    array (
      'name' => 'idx_tasks_branch_dept',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
        1 => 'department_id',
      ),
    ),
    'idx_tasks_assigned' => 
    array (
      'name' => 'idx_tasks_assigned',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'assigned_user_id',
      ),
    ),
    0 => 
    array (
      'name' => 'idx_tsk_name',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'name',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_task_con_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'contact_id',
        1 => 'deleted',
      ),
    ),
    2 => 
    array (
      'name' => 'idx_task_par_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'parent_id',
        1 => 'parent_type',
        2 => 'deleted',
      ),
    ),
    3 => 
    array (
      'name' => 'idx_task_status',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'status',
      ),
    ),
    4 => 
    array (
      'name' => 'idx_task_priority',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'priority',
      ),
    ),
    5 => 
    array (
      'name' => 'idx_task_asap',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'asap',
      ),
    ),
    6 => 
    array (
      'name' => 'idx_task_transactions',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'transactions',
      ),
    ),
    7 => 
    array (
      'name' => 'idx_task_communication',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'communication',
      ),
    ),
    8 => 
    array (
      'name' => 'idx_task_status_trans',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'status',
        1 => 'transactions',
      ),
    ),
    9 => 
    array (
      'name' => 'idx_task_assign_status',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'assigned_user_id',
        1 => 'status',
      ),
    ),
    10 => 
    array (
      'name' => 'idx_task_assign_trans',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'assigned_user_id',
        1 => 'transactions',
      ),
    ),
    11 => 
    array (
      'name' => 'idx_task_assign_stt_trans',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'assigned_user_id',
        1 => 'status',
        2 => 'transactions',
      ),
    ),
    12 => 
    array (
      'name' => 'idx_task_parent',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'parent_id',
        1 => 'deleted',
      ),
    ),
    13 => 
    array (
      'name' => 'idx_task_parent_status',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'parent_id',
        1 => 'status',
      ),
    ),
    14 => 
    array (
      'name' => 'idx_task_del_forward',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'forward_user_id',
      ),
    ),
    'idx_task_care_result' => 
    array (
      'name' => 'idx_task_care_result',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'care_result',
      ),
    ),
    'idx_task_opp_id' => 
    array (
      'name' => 'idx_task_opp_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'opportunity_id',
      ),
    ),
    'idx_task_grp_id_del' => 
    array (
      'name' => 'idx_task_grp_id_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'group_id',
      ),
    ),
    'idx_task_job_id' => 
    array (
      'name' => 'idx_task_job_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'job_id',
      ),
    ),
    'idx_task_job_type' => 
    array (
      'name' => 'idx_task_job_type',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'job_type',
      ),
    ),
    'idx_task_ref_id' => 
    array (
      'name' => 'idx_task_ref_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'ref_id',
      ),
    ),
    'idx_task_locked' => 
    array (
      'name' => 'idx_task_locked',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'is_locked',
      ),
    ),
    'idx_task_del_is_opp' => 
    array (
      'name' => 'idx_task_del_is_opp',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'is_opp_sales',
      ),
    ),
    'idx_task_aprovelv_id_del' => 
    array (
      'name' => 'idx_task_aprovelv_id_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'approval_level_id',
      ),
    ),
  ),
  'relationships' => 
  array (
    'tasks_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Tasks',
      'rhs_table' => 'tasks',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'tasks_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Tasks',
      'rhs_table' => 'tasks',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
    'tasks_assigned_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Tasks',
      'rhs_table' => 'tasks',
      'rhs_key' => 'assigned_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'user_errs_tasks' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Tasks',
      'rhs_table' => 'tasks',
      'rhs_key' => 'user_err_id',
      'relationship_type' => 'one-to-many',
    ),
    'tasks_notes' => 
    array (
      'lhs_module' => 'Tasks',
      'lhs_table' => 'tasks',
      'lhs_key' => 'id',
      'rhs_module' => 'Notes',
      'rhs_table' => 'notes',
      'rhs_key' => 'parent_id',
      'relationship_type' => 'one-to-many',
    ),
    'contacts_tasks_direct' => 
    array (
      'lhs_module' => 'Contacts',
      'lhs_table' => 'contacts',
      'lhs_key' => 'id',
      'rhs_module' => 'Tasks',
      'rhs_table' => 'tasks',
      'rhs_key' => 'contact_id',
      'relationship_type' => 'one-to-many',
    ),
    'member_tasks' => 
    array (
      'lhs_module' => 'Tasks',
      'lhs_table' => 'tasks',
      'lhs_key' => 'id',
      'rhs_module' => 'Tasks',
      'rhs_table' => 'tasks',
      'rhs_key' => 'main_id',
      'relationship_type' => 'one-to-many',
    ),
    'tasks_trackreview' => 
    array (
      'lhs_module' => 'Tasks',
      'lhs_table' => 'tasks',
      'lhs_key' => 'id',
      'rhs_module' => 'TrackReview',
      'rhs_table' => 'trackreview',
      'rhs_key' => 'parent_id',
      'relationship_type' => 'one-to-many',
      'relationship_role_column' => 'parent_type',
      'relationship_role_column_value' => 'Tasks',
    ),
    'opportunities_tasks_direct' => 
    array (
      'lhs_module' => 'Opportunities',
      'lhs_table' => 'opportunities',
      'lhs_key' => 'id',
      'rhs_module' => 'Tasks',
      'rhs_table' => 'tasks',
      'rhs_key' => 'opportunity_id',
      'relationship_type' => 'one-to-many',
    ),
    'tasks_groups_tasks' => 
    array (
      'lhs_module' => 'Tasks_Groups',
      'lhs_table' => 'tasks_groups',
      'lhs_key' => 'id',
      'rhs_module' => 'Tasks',
      'rhs_table' => 'tasks',
      'rhs_key' => 'group_id',
      'relationship_type' => 'one-to-many',
    ),
    'forward_users_tasks' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Tasks',
      'rhs_table' => 'tasks',
      'rhs_key' => 'forward_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'approval_levels_tasks' => 
    array (
      'lhs_module' => 'Approval_Levels',
      'lhs_table' => 'approval_levels',
      'lhs_key' => 'id',
      'rhs_module' => 'Tasks',
      'rhs_table' => 'tasks',
      'rhs_key' => 'approval_level_id',
      'relationship_type' => 'one-to-many',
    ),
  ),
  'optimistic_locking' => true,
  'templates' => 
  array (
    'assignable' => 'assignable',
    'groups' => 'groups',
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
