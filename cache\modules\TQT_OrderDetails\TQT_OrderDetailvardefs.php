<?php
// created: 2025-02-26 15:24:03
$<PERSON><PERSON><PERSON><PERSON><PERSON>["dictionary"]["TQT_OrderDetail"] = array (
  'table' => 'tqt_orderdetails',
  'audited' => true,
  'duplicate_merge' => true,
  'unified_search' => false,
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'int',
      'len' => '4',
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
      'comment' => 'Full text of the note',
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'tqt_orderdetails_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'tqt_orderdetails_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'branch_id' => 
    array (
      'name' => 'branch_id',
      'vname' => 'LBL_BRANCH_ID',
      'type' => 'enum',
      'options' => 'users_branch_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'BranchID of assigned user',
    ),
    'department_id' => 
    array (
      'name' => 'department_id',
      'vname' => 'LBL_DEPARTMENT_ID',
      'type' => 'enum',
      'options' => 'users_department_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'DepartmentID of assigned user',
    ),
    'assigned_user_id' => 
    array (
      'name' => 'assigned_user_id',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'vname' => 'LBL_ASSIGNED_TO_ID',
      'group' => 'assigned_user_name',
      'type' => 'relate',
      'table' => 'users',
      'module' => 'Users',
      'isnull' => 'false',
      'dbType' => 'id',
      'audited' => true,
      'reportable' => true,
      'duplicate_merge' => 'disabled',
      'comment' => 'User ID assigned to record',
    ),
    'assigned_user_name' => 
    array (
      'name' => 'assigned_user_name',
      'link' => 'assigned_user_link',
      'vname' => 'LBL_ASSIGNED_TO_NAME',
      'rname' => 'user_name',
      'type' => 'relate',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'assigned_user_id',
      'module' => 'Users',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'assigned_user_link' => 
    array (
      'name' => 'assigned_user_link',
      'type' => 'link',
      'relationship' => 'tqt_orderdetails_assigned_user',
      'vname' => 'LBL_ASSIGNED_TO_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
      'duplicate_merge' => 'enabled',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'table' => 'users',
    ),
    'add_ext' => 
    array (
      'name' => 'add_ext',
      'vname' => 'LBL_ADD_EXT',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => false,
      'default' => 0,
    ),
    'add_int' => 
    array (
      'name' => 'add_int',
      'vname' => 'LBL_ADD_INT',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => false,
      'default' => 0,
    ),
    'date_arise' => 
    array (
      'name' => 'date_arise',
      'vname' => 'LBL_DATE_ARISE',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
      'display_default' => 'now',
    ),
    'amount' => 
    array (
      'name' => 'amount',
      'vname' => 'LBL_AMOUNT',
      'type' => 'double',
      'audited' => true,
    ),
    'quantity' => 
    array (
      'name' => 'quantity',
      'vname' => 'LBL_QUANTITY',
      'type' => 'float',
      'audited' => true,
    ),
    'fee_type' => 
    array (
      'name' => 'fee_type',
      'vname' => 'LBL_FEE_TYPE',
      'type' => 'enum',
      'options' => 'incurred_fee_type_dom',
      'dbType' => 'char',
      'len' => 1,
      'audited' => true,
      'massupdate' => false,
    ),
    'opportunity_id' => 
    array (
      'name' => 'opportunity_id',
      'vname' => 'LBL_OPPORTUNITY_ID',
      'type' => 'id',
      'required' => false,
      'reportable' => false,
      'audited' => true,
      'massupdate' => false,
    ),
    'opportunity_name' => 
    array (
      'name' => 'opportunity_name',
      'rname' => 'name',
      'id_name' => 'opportunity_id',
      'vname' => 'LBL_OPPORTUNITY_NAME',
      'table' => 'opportunities',
      'type' => 'relate',
      'link' => 'opportunities',
      'join_name' => 'opportunities',
      'isnull' => 'true',
      'module' => 'Opportunities',
      'source' => 'non-db',
      'massupdate' => false,
      'additionalFields' => 
      array (
        'date_closed' => 'date_closed',
        'contract_revenue' => 'contract_revenue',
        'amount' => 'opp_amount',
        'estimated_revenue' => 'opp_expected',
        'actual_output' => 'opp_act_output',
        'expected_output' => 'opp_exp_output',
      ),
    ),
    'opportunities' => 
    array (
      'name' => 'opportunities',
      'type' => 'link',
      'relationship' => 'opportunities_tqt_orderdetails',
      'vname' => 'LBL_OPPORTUNITIES',
      'source' => 'non-db',
    ),
    'date_closed' => 
    array (
      'name' => 'date_closed',
      'vname' => 'LBL_DATE_CLOSED',
      'type' => 'date',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'contract_revenue' => 
    array (
      'name' => 'contract_revenue',
      'vname' => 'LBL_CONTRACT_REVENUE',
      'type' => 'double',
      'source' => 'non-db',
    ),
    'opp_amount' => 
    array (
      'name' => 'opp_amount',
      'vname' => 'LBL_OPP_AMOUNT',
      'type' => 'double',
      'source' => 'non-db',
    ),
    'opp_expected' => 
    array (
      'name' => 'opp_expected',
      'vname' => 'LBL_OPP_EXPECTED',
      'type' => 'double',
      'source' => 'non-db',
    ),
    'opp_act_output' => 
    array (
      'name' => 'opp_act_output',
      'vname' => 'LBL_OPP_ACT_OUTPUT',
      'type' => 'float',
      'source' => 'non-db',
    ),
    'opp_exp_output' => 
    array (
      'name' => 'opp_exp_output',
      'vname' => 'LBL_OPP_EXP_OUTPUT',
      'type' => 'float',
      'source' => 'non-db',
    ),
    'opp_acc_name' => 
    array (
      'name' => 'opp_acc_name',
      'vname' => 'LBL_OPP_ACC_NAME',
      'type' => 'varchar',
      'len' => '250',
      'source' => 'non-db',
    ),
    'account_id' => 
    array (
      'name' => 'account_id',
      'vname' => 'LBL_ACCOUNT_ID',
      'type' => 'id',
      'audited' => true,
      'required' => false,
      'reportable' => false,
      'massupdate' => false,
    ),
    'account_name' => 
    array (
      'name' => 'account_name',
      'rname' => 'name',
      'id_name' => 'account_id',
      'vname' => 'LBL_ACCOUNT_NAME',
      'table' => 'accounts',
      'type' => 'relate',
      'link' => 'accounts',
      'join_name' => 'accounts',
      'isnull' => 'true',
      'module' => 'Accounts',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'accounts' => 
    array (
      'name' => 'accounts',
      'type' => 'link',
      'relationship' => 'accounts_tqt_orderdetails',
      'vname' => 'LBL_ACCOUNTS',
      'source' => 'non-db',
    ),
    'SecurityGroups' => 
    array (
      'name' => 'SecurityGroups',
      'type' => 'link',
      'relationship' => 'securitygroups_tqt_orderdetails',
      'module' => 'SecurityGroups',
      'bean_name' => 'SecurityGroup',
      'source' => 'non-db',
      'vname' => 'LBL_SECURITYGROUPS',
    ),
    'is_locked' => 
    array (
      'name' => 'is_locked',
      'vname' => 'LBL_IS_LOCKED',
      'type' => 'bool',
      'audited' => true,
      'massupdate' => false,
      'default' => 0,
    ),
    'list_incurred_cost_id' => 
    array (
      'name' => 'list_incurred_cost_id',
      'vname' => 'LBL_LIST_INCURRED_COST_ID',
      'type' => 'id',
      'required' => false,
      'reportable' => false,
      'audited' => true,
      'massupdate' => false,
    ),
    'list_incurred_cost_name' => 
    array (
      'name' => 'list_incurred_cost_name',
      'rname' => 'name',
      'id_name' => 'list_incurred_cost_id',
      'vname' => 'LBL_LIST_INCURRED_COST_NAME',
      'type' => 'relate',
      'link' => 'list_incurred_costs',
      'table' => 'list_incurred_costs',
      'join_name' => 'list_incurred_costs',
      'isnull' => 'true',
      'module' => 'List_Incurred_Costs',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'list_incurred_costs' => 
    array (
      'name' => 'list_incurred_costs',
      'type' => 'link',
      'relationship' => 'list_incurred_costs_tqt_orderdetails',
      'vname' => 'LBL_LIST_INCURRED_COSTS',
      'source' => 'non-db',
    ),
  ),
  'indices' => 
  array (
    'id' => 
    array (
      'name' => 'tqt_orderdetailspk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    'idx_tqt_orderdetails_branch_id' => 
    array (
      'name' => 'idx_tqt_orderdetails_branch_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
      ),
    ),
    'idx_tqt_orderdetails_department_id' => 
    array (
      'name' => 'idx_tqt_orderdetails_department_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'department_id',
      ),
    ),
    'idx_tqt_orderdetails_branch_dept' => 
    array (
      'name' => 'idx_tqt_orderdetails_branch_dept',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
        1 => 'department_id',
      ),
    ),
    'idx_tqt_orderdetails_assigned' => 
    array (
      'name' => 'idx_tqt_orderdetails_assigned',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'assigned_user_id',
      ),
    ),
    0 => 
    array (
      'name' => 'idx_order_dtl_id_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'id',
        1 => 'deleted',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_order_dtl_del_opp',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'opportunity_id',
      ),
    ),
    2 => 
    array (
      'name' => 'idx_order_dtl_del_fee',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'fee_type',
      ),
    ),
    3 => 
    array (
      'name' => 'idx_order_dtl_del_acc',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'account_id',
      ),
    ),
    'idx_order_dtl_locked' => 
    array (
      'name' => 'idx_order_dtl_locked',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'is_locked',
      ),
    ),
    'idx_order_dtl_incurred' => 
    array (
      'name' => 'idx_order_dtl_incurred',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'list_incurred_cost_id',
      ),
    ),
  ),
  'relationships' => 
  array (
    'tqt_orderdetails_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_OrderDetails',
      'rhs_table' => 'tqt_orderdetails',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'tqt_orderdetails_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_OrderDetails',
      'rhs_table' => 'tqt_orderdetails',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
    'tqt_orderdetails_assigned_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_OrderDetails',
      'rhs_table' => 'tqt_orderdetails',
      'rhs_key' => 'assigned_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'opportunities_tqt_orderdetails' => 
    array (
      'lhs_module' => 'Opportunities',
      'lhs_table' => 'opportunities',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_OrderDetails',
      'rhs_table' => 'tqt_orderdetails',
      'rhs_key' => 'opportunity_id',
      'relationship_type' => 'one-to-many',
    ),
    'accounts_tqt_orderdetails' => 
    array (
      'lhs_module' => 'Accounts',
      'lhs_table' => 'accounts',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_OrderDetails',
      'rhs_table' => 'tqt_orderdetails',
      'rhs_key' => 'account_id',
      'relationship_type' => 'one-to-many',
    ),
  ),
  'optimistic_lock' => true,
  'templates' => 
  array (
    'assignable' => 'assignable',
    'groups' => 'groups',
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
