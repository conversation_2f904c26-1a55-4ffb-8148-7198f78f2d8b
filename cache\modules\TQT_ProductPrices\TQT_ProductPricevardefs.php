<?php
// created: 2025-02-26 15:24:03
$G<PERSON><PERSON><PERSON><PERSON>["dictionary"]["TQT_ProductPrice"] = array (
  'table' => 'tqt_productprices',
  'audited' => true,
  'unified_search' => false,
  'duplicate_merge' => false,
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'name',
      'dbType' => 'varchar',
      'len' => 50,
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
      'comment' => 'Full text of the note',
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'tqt_productprices_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'tqt_productprices_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'branch_id' => 
    array (
      'name' => 'branch_id',
      'vname' => 'LBL_BRANCH_ID',
      'type' => 'enum',
      'options' => 'users_branch_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'BranchID of assigned user',
    ),
    'department_id' => 
    array (
      'name' => 'department_id',
      'vname' => 'LBL_DEPARTMENT_ID',
      'type' => 'enum',
      'options' => 'users_department_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'DepartmentID of assigned user',
    ),
    'assigned_user_id' => 
    array (
      'name' => 'assigned_user_id',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'vname' => 'LBL_ASSIGNED_TO_ID',
      'group' => 'assigned_user_name',
      'type' => 'relate',
      'table' => 'users',
      'module' => 'Users',
      'isnull' => 'false',
      'dbType' => 'id',
      'audited' => true,
      'reportable' => true,
      'duplicate_merge' => 'disabled',
      'comment' => 'User ID assigned to record',
    ),
    'assigned_user_name' => 
    array (
      'name' => 'assigned_user_name',
      'link' => 'assigned_user_link',
      'vname' => 'LBL_ASSIGNED_TO_NAME',
      'rname' => 'user_name',
      'type' => 'relate',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'assigned_user_id',
      'module' => 'Users',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'assigned_user_link' => 
    array (
      'name' => 'assigned_user_link',
      'type' => 'link',
      'relationship' => 'tqt_productprices_assigned_user',
      'vname' => 'LBL_ASSIGNED_TO_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
      'duplicate_merge' => 'enabled',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'table' => 'users',
    ),
    'price' => 
    array (
      'name' => 'price',
      'vname' => 'LBL_PRICE',
      'type' => 'float',
      'dbType' => 'double',
      'audited' => true,
    ),
    'tqt_product_id' => 
    array (
      'name' => 'tqt_product_id',
      'vname' => 'LBL_TQT_PRODUCT_ID',
      'type' => 'id',
      'audited' => true,
      'massupdate' => false,
      'required' => false,
      'reportable' => false,
    ),
    'tqt_product_name' => 
    array (
      'name' => 'tqt_product_name',
      'rname' => 'name',
      'id_name' => 'tqt_product_id',
      'vname' => 'LBL_TQT_PRODUCT_NAME',
      'table' => 'tqt_products',
      'type' => 'relate',
      'link' => 'tqt_products',
      'join_name' => 'tqt_products',
      'isnull' => 'true',
      'module' => 'TQT_Products',
      'source' => 'non-db',
      'massupdate' => false,
      'additionalFields' => 
      array (
        'price' => 'product_price',
      ),
    ),
    'tqt_product_code' => 
    array (
      'name' => 'tqt_product_code',
      'rname' => 'code',
      'id_name' => 'tqt_product_id',
      'vname' => 'LBL_TQT_PRODUCT_CODE',
      'table' => 'tqt_products',
      'type' => 'relate',
      'link' => 'tqt_products',
      'join_name' => 'tqt_products',
      'isnull' => 'true',
      'module' => 'TQT_Products',
      'source' => 'non-db',
      'massupdate' => false,
      'quicksearch' => true,
      'additionalFields' => 
      array (
        'price' => 'product_price',
      ),
    ),
    'tqt_products' => 
    array (
      'name' => 'tqt_products',
      'type' => 'link',
      'relationship' => 'tqt_products_tqt_product_prices',
      'vname' => 'LBL_LIST_PRODUCTS',
      'source' => 'non-db',
    ),
    'product_price' => 
    array (
      'name' => 'product_price',
      'vname' => 'LBL_PRODUCT_PRICE',
      'type' => 'double',
      'source' => 'non-db',
    ),
    'account_id' => 
    array (
      'name' => 'account_id',
      'vname' => 'LBL_ACCOUNT_ID',
      'type' => 'id',
      'audited' => true,
      'required' => false,
      'reportable' => false,
      'massupdate' => false,
    ),
    'account_name' => 
    array (
      'name' => 'account_name',
      'rname' => 'name',
      'id_name' => 'account_id',
      'vname' => 'LBL_ACCOUNT_NAME',
      'table' => 'accounts',
      'type' => 'relate',
      'link' => 'accounts',
      'join_name' => 'accounts',
      'isnull' => 'true',
      'module' => 'Accounts',
      'source' => 'non-db',
      'massupdate' => false,
      'additionalFields' => 
      array (
        'billing_address_street' => 'account_address',
      ),
    ),
    'reference_code' => 
    array (
      'name' => 'reference_code',
      'rname' => 'reference_code',
      'id_name' => 'account_id',
      'vname' => 'LBL_REFERENCE_CODE',
      'table' => 'accounts',
      'type' => 'relate',
      'link' => 'accounts',
      'join_name' => 'accounts',
      'isnull' => 'true',
      'module' => 'Accounts',
      'source' => 'non-db',
      'massupdate' => false,
      'additionalFields' => 
      array (
        'billing_address_street' => 'account_address',
      ),
    ),
    'accounts' => 
    array (
      'name' => 'accounts',
      'type' => 'link',
      'relationship' => 'accounts_tqt_product_prices',
      'vname' => 'LBL_ACCOUNTS',
      'source' => 'non-db',
    ),
    'account_address' => 
    array (
      'name' => 'account_address',
      'vname' => 'LBL_ACCOUNT_ADDRESS',
      'type' => 'varchar',
      'len' => '250',
      'source' => 'non-db',
    ),
  ),
  'indices' => 
  array (
    'id' => 
    array (
      'name' => 'tqt_productpricespk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    'idx_tqt_productprices_branch_id' => 
    array (
      'name' => 'idx_tqt_productprices_branch_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
      ),
    ),
    'idx_tqt_productprices_department_id' => 
    array (
      'name' => 'idx_tqt_productprices_department_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'department_id',
      ),
    ),
    'idx_tqt_productprices_branch_dept' => 
    array (
      'name' => 'idx_tqt_productprices_branch_dept',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
        1 => 'department_id',
      ),
    ),
    'idx_tqt_productprices_assigned' => 
    array (
      'name' => 'idx_tqt_productprices_assigned',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'assigned_user_id',
      ),
    ),
    0 => 
    array (
      'name' => 'idx_prod_price_id_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'id',
        1 => 'deleted',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_prod_price_assigned_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'assigned_user_id',
      ),
    ),
    2 => 
    array (
      'name' => 'idx_prod_price_del_acc',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'account_id',
      ),
    ),
    3 => 
    array (
      'name' => 'idx_prod_price_del_prod',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'tqt_product_id',
      ),
    ),
  ),
  'relationships' => 
  array (
    'tqt_productprices_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_ProductPrices',
      'rhs_table' => 'tqt_productprices',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'tqt_productprices_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_ProductPrices',
      'rhs_table' => 'tqt_productprices',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
    'tqt_productprices_assigned_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_ProductPrices',
      'rhs_table' => 'tqt_productprices',
      'rhs_key' => 'assigned_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'tqt_products_tqt_product_prices' => 
    array (
      'lhs_module' => 'TQT_Products',
      'lhs_table' => 'tqt_products',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_ProductPrices',
      'rhs_table' => 'tqt_productprices',
      'rhs_key' => 'tqt_product_id',
      'relationship_type' => 'one-to-many',
    ),
    'accounts_tqt_product_prices' => 
    array (
      'lhs_module' => 'Accounts',
      'lhs_table' => 'accounts',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_ProductPrices',
      'rhs_table' => 'tqt_productprices',
      'rhs_key' => 'account_id',
      'relationship_type' => 'one-to-many',
    ),
  ),
  'optimistic_lock' => true,
  'templates' => 
  array (
    'assignable' => 'assignable',
    'groups' => 'groups',
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
