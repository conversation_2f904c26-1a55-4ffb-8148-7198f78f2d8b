<?php
// created: 2024-10-04 02:03:23
$pruneDatabase = array (
  0 => 'INSERT INTO accounts_opportunities (id, opportunity_id, account_id, date_modified, deleted) VALUES ("3e73f190-e481-e259-7e59-66f3bfba879a", "27e21ee2-96c2-4c8e-d386-66f3bf2ad68c", "293d3b55-df85-46f9-6347-5d5d364920e9", "2024-09-25 07:43:33", "1");',
  1 => 'INSERT INTO accounts_opportunities (id, opportunity_id, account_id, date_modified, deleted) VALUES ("163e76c9-558e-e06f-a68b-66ed3c25f8cf", "f9c60d23-fdd7-e11d-c0c3-66ed3cb57bd0", "b2dd214d-cdf2-e874-5fe0-5d5d363f0771", "2024-09-20 09:11:48", "1");',
  2 => 'INSERT INTO cashes_collect_details (id, deleted, date_modified, modified_by, cash_id, account_id, opportunity_id, amount, date_record, notes) VALUES ("13642c7c-9374-4914-4677-66d7c472c465", "1", "2024-09-04 02:22:30", "1", "1225f908-8788-2eae-dbfe-66d7c4c64f2d", "293d3b55-df85-46f9-6347-5d5d364920e9", "acd9f5bc-6a1d-73e1-26ec-66d7c4f7a17a", "30000", "2024-09-04", "[SYS] Hủy từ đơn hàng");',
  3 => 'INSERT INTO opportunities (id, date_entered, date_modified, modified_user_id, created_by, deleted, name, description, assigned_user_id, branch_id, department_id, opportunity_type, campaign_id, lead_source, amount, amount_usdollar, currency_id, date_closed, sales_stage, probability, priority, estimated_revenue, opportunity_scope, date_start_process, object, expected_output, actual_output, contract_revenue, actual_receipts, order_number, prod_lock, delivered_all, contact_name, vat_tax, vat_amount, vat_more_amt, vat_other_amt, discount_amount, cost_amount, comm_more_amount, incurred_amount, incurred_other, incurred_domestic, incurred_foreign, commission_amount, arise_cost_amount, profit_amount, rate_usd, is_locked, processing_stage, processing_content, amount_collected, amount_owed, handle_user_id, parent_id, payment_status, money_deposit, money_refund, is_target, account_targets, causes_failure, failure_notes, debt_order, payment_type, account_address, account_tax_code, shipping_fee, no_currency, account_phone, account_email, date_payment, order_type, quote_number, contract_number, contract_date, order_discount, payment_period, project_id, project_name, bank_account_id, disable_auto_price, quotation_date, quotation_version, location_nearby, rate_id, rate_foreign, service_total, service_used, service_left, vat_stock, ordered_merge, warranty_period, table_params, ignore_payment, re_calc_profit, is_auto_cpps, total_amount_in, date_return, prod_returned, vat_spec_amt, vat_imp_amt, vat_total_amount, ext_incurred_amount, int_incurred_amount, is_import, ttt_date_shipment, ttt_date_contract, ttt_date_declaration, ttt_date_storage, ttt_date_arrival, ttt_date_clearance, ttt_fee_amount, ttt_insurance_number, ttt_insurance_date, ttt_insurance_amount, ttt_insurance_extension, ttt_insurance_coop, ttt_date_lc_payment, ttt_lc_number, ttt_date_lc_expired, ttt_lc_amount, ttt_lc_reiss_amt, ttt_lc_repair_amt, ttt_lc_payment_amt, ttt_lc_bank_name, ttt_invoice_number, ttt_invoice_date, ttt_bol, ttt_bol_date, ttt_packinglist, ttt_co, ttt_milltest, ttt_dost, ttt_condi_trading, ttt_declaration, ttt_declaration_number, ttt_declaration_amount, ttt_vat_amount, ttt_verification, ttt_verification_amount, ttt_vat_import, ttt_customs_fee, ttt_clearance, ttt_arrival_notice, ttt_discharging_port, ttt_vessel, ttt_vessel_vn, ttt_owed_contract, ttt_storage_fee, ttt_vessel_fee, ttt_repair_cont_fee, ttt_deposit_cont_fee, ttt_expected_output, ttt_cnt_amount, ttt_foreign_amount, ttt_actual_output, ttt_commodity, ttt_tolerance, ttt_shipment_method, ttt_partial_shipment, ttt_transhipment, ttt_packing, ttt_quality, ttt_order_condi, total_foreign_amount, order_purchase, order_date, quotes_note, shipping_time, payment_method, shipping_note, failure_cause, date_record, is_export_vat, accounting_category_type) VALUES ("f9c60d23-fdd7-e11d-c0c3-66ed3cb57bd0", "2024-09-20 09:10:52", "2024-09-20 09:11:48", "1", "1", "1", "SO-2409-0007", "", "3f16d59e-7898-d881-0266-5778bc22a9c1", "2", "3", "1", "", "", "0", "0", "-99", "2024-09-20", "Cancel", "0", "0", "0", "", "2024-09-20", "Proposal", "0", "0", "0", "0", "", "1", "0", "", "-1", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1", "0", "", "", "0", "0", "", "", "", "0", "0", "0", "0", "", "1", "0", "TM", "40 Đại Lộ Tự Do, KCN VN- Singapore, TX Thuận An, Bình Dương", "", "0", "0", "159", "", "", "Packages", "BG-24-0062", "", "", "", "", "", "", "ee901ddc-9ddb-884e-a360-65322ec9a19f", "0", "", "", "12.9728,108.2408", "-99", "1", "0", "", "0", "0", "0", "", "YTozOntzOjE0OiJvcmRlcl9kaXNjb3VudCI7ZDowO3M6MTY6InByb2R1Y3RfZGlzY291bnQiO2k6MDtzOjEwOiJwb2ludF91c2VkIjtpOjA7fQ==", "0", "0", "0", "0", "", "0", "", "", "", "", "", "0", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "Giá trên đã bao gồm VAT.\\r\\nBao gồm chi phí vận chuyển, lắp đặt tại TPHCM.\\r\\nBảng báo giá có giá trị trong vòng 30 ngày kề từ ngày báo.", "Chia thành nhiều đợt theo từng đơn đặt hàng của bên A Hàng có sẵn (Tối đa trong vòng 3-5 ngày theo từng đơn hàng của bên A)", "Bên A thanh toán cho Bên B bằng phương thức chuyển khoản hoặc tiền mặt 100% tổng giá trị hợp đồng tối đa sau 30 ngày kể từ khi Bên B giao đủ hàng, nghiệm thu, thanh lý và hóa đơn tài chính theo quy định cho Bên A.", "", "", "", "0", "1");',
  4 => 'INSERT INTO opportunities (id, date_entered, date_modified, modified_user_id, created_by, deleted, name, description, assigned_user_id, branch_id, department_id, opportunity_type, campaign_id, lead_source, amount, amount_usdollar, currency_id, date_closed, sales_stage, probability, priority, estimated_revenue, opportunity_scope, date_start_process, object, expected_output, actual_output, contract_revenue, actual_receipts, order_number, prod_lock, delivered_all, contact_name, vat_tax, vat_amount, vat_more_amt, vat_other_amt, discount_amount, cost_amount, comm_more_amount, incurred_amount, incurred_other, incurred_domestic, incurred_foreign, commission_amount, arise_cost_amount, profit_amount, rate_usd, is_locked, processing_stage, processing_content, amount_collected, amount_owed, handle_user_id, parent_id, payment_status, money_deposit, money_refund, is_target, account_targets, causes_failure, failure_notes, debt_order, payment_type, account_address, account_tax_code, shipping_fee, no_currency, account_phone, account_email, date_payment, order_type, quote_number, contract_number, contract_date, order_discount, payment_period, project_id, project_name, bank_account_id, disable_auto_price, quotation_date, quotation_version, location_nearby, rate_id, rate_foreign, service_total, service_used, service_left, vat_stock, ordered_merge, warranty_period, table_params, ignore_payment, re_calc_profit, is_auto_cpps, total_amount_in, date_return, prod_returned, vat_spec_amt, vat_imp_amt, vat_total_amount, ext_incurred_amount, int_incurred_amount, is_import, ttt_date_shipment, ttt_date_contract, ttt_date_declaration, ttt_date_storage, ttt_date_arrival, ttt_date_clearance, ttt_fee_amount, ttt_insurance_number, ttt_insurance_date, ttt_insurance_amount, ttt_insurance_extension, ttt_insurance_coop, ttt_date_lc_payment, ttt_lc_number, ttt_date_lc_expired, ttt_lc_amount, ttt_lc_reiss_amt, ttt_lc_repair_amt, ttt_lc_payment_amt, ttt_lc_bank_name, ttt_invoice_number, ttt_invoice_date, ttt_bol, ttt_bol_date, ttt_packinglist, ttt_co, ttt_milltest, ttt_dost, ttt_condi_trading, ttt_declaration, ttt_declaration_number, ttt_declaration_amount, ttt_vat_amount, ttt_verification, ttt_verification_amount, ttt_vat_import, ttt_customs_fee, ttt_clearance, ttt_arrival_notice, ttt_discharging_port, ttt_vessel, ttt_vessel_vn, ttt_owed_contract, ttt_storage_fee, ttt_vessel_fee, ttt_repair_cont_fee, ttt_deposit_cont_fee, ttt_expected_output, ttt_cnt_amount, ttt_foreign_amount, ttt_actual_output, ttt_commodity, ttt_tolerance, ttt_shipment_method, ttt_partial_shipment, ttt_transhipment, ttt_packing, ttt_quality, ttt_order_condi, total_foreign_amount, order_purchase, order_date, quotes_note, shipping_time, payment_method, shipping_note, failure_cause, date_record, is_export_vat, accounting_category_type) VALUES ("27e21ee2-96c2-4c8e-d386-66f3bf2ad68c", "2024-09-25 07:43:33", "2024-09-25 07:43:33", "627d3b02-1dea-cd40-ca0a-5f96342ef270", "627d3b02-1dea-cd40-ca0a-5f96342ef270", "1", "PREVIEW-SO-2409-0009", "", "627d3b02-1dea-cd40-ca0a-5f96342ef270", "2", "3", "1", "", "", "2614000", "0", "-99", "2024-09-25", "Cancel", "0", "0", "2704000", "", "2024-09-25", "Proposal", "2", "4", "2875000", "2875400", "", "0", "0", "", "10", "261400", "0", "0", "90000", "1202190.58", "0", "0", "0", "0", "0", "0", "1292190.58", "1411809.42", "1", "0", "", "", "", "2875000", "", "", "1", "0", "0", "0", "0", "", "", "0", "TM", "P2-096/097, PHNOM PENH SPECIAL SANGKAT BOEUNG THOMM KHAN", "", "0", "0", "11", "", "", "Packages", "", "", "", "", "", "", "", "ee901ddc-9ddb-884e-a360-65322ec9a19f", "0", "", "", "10.8456022,106.6365102", "-99", "1", "0", "", "0", "0", "0", "", "YTozOntzOjE0OiJvcmRlcl9kaXNjb3VudCI7ZDowO3M6MTY6InByb2R1Y3RfZGlzY291bnQiO2Q6OTAwMDA7czoxMDoicG9pbnRfdXNlZCI7aTowO30=", "0", "0", "0", "0", "", "0", "", "", "", "", "", "0", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "Giá trên đã bao gồm VAT.\\r\\nBao gồm chi phí vận chuyển, lắp đặt tại TPHCM.\\r\\nBảng báo giá có giá trị trong vòng 30 ngày kề từ ngày báo.", "Chia thành nhiều đợt theo từng đơn đặt hàng của bên A Hàng có sẵn (Tối đa trong vòng 3-5 ngày theo từng đơn hàng của bên A)", "Bên A thanh toán cho Bên B bằng phương thức chuyển khoản hoặc tiền mặt 100% tổng giá trị hợp đồng tối đa sau 30 ngày kể từ khi Bên B giao đủ hàng, nghiệm thu, thanh lý và hóa đơn tài chính theo quy định cho Bên A.", "", "", "", "0", "1");',
  5 => 'INSERT INTO opportunities_tasks (id, date_entered, date_modified, modified_user_id, created_by, deleted, name, description, status, current_step, next_step, date_start, date_end, times_total, opportunity_id) VALUES ("f7bb0d59-c494-d7b5-f31c-66ed3c1a2376", "2024-09-20 09:10:53", "2024-09-20 09:11:48", "1", "1", "1", "1", "", "Processing", "1", "", "2024-09-20 09:10:00", "", "", "");',
  6 => 'INSERT INTO opportunities_tqt_products (id, deleted, date_modified, modified_user_id, opportunity_id, product_id, group_price_id, product_gift_id, ref_id, parent_id, parent_note, product_combo, warehouse_id, ordering, product_code, product_name, prod_note, prod_quantity, prod_price, prod_orig_price, prod_sale_price, prod_discount, prod_discount_amt, prod_promotion, prod_promotion_amt, prod_more_percent, prod_vat_tax, prod_vat_amount, prod_amount, prod_total_amount, prod_orig_amount, prod_profit_amount, product_unit, prod_unit_opt, prod_unit_qty, prod_unit_length, prod_unit_width, prod_unit_price, product_type, product_gift, prod_convert_kg, prod_total_kg, prod_convert_mt, prod_total_mt, product_lots, prod_lot_id, delivered_qty, missing_qty, prod_custom, prod_num_uses, prod_date_start, prod_repeat_cycle, handling_note, prod_foreign_price, prod_foreign_amount, prod_actual_price, prod_stock_price, prod_more_amount, prod_comm_amount, prod_comm_more_amt, prod_more_price, prod_comm_price, prod_more_ret_tax, prod_more_ret_amt, prod_more_receive_amt, prod_more_note, prod_01_comm_per, prod_01_comm_amt, prod_01_comm_ret_tax, prod_01_comm_ret_amt, prod_01_comm_receive_amt, prod_01_comm_note, prod_02_comm_per, prod_02_comm_amt, prod_02_comm_ret_tax, prod_02_comm_ret_amt, prod_02_comm_receive_amt, prod_02_comm_note, prod_tax_exempt, prod_per_cpps, prod_amount_cpps, prod_total_stock_price, prod_total_amount_in, have_warehoused, prod_ext_incur_amount, prod_value_vat_import, prod_vat_tax_import, prod_vat_amount_import, prod_value_vat_spec, prod_vat_tax_spec, prod_vat_amount_spec, prod_value_vat_amount, prod_int_incur_amount, product_primary, product_set, prod_orig2_price, prod_return_orig_price, prod_custom_unit_qty, prod_custom_unit_price) VALUES ("499af4a4-432e-1369-857d-66f3bf503397", "1", "2024-09-25 07:43:33", "627d3b02-1dea-cd40-ca0a-5f96342ef270", "27e21ee2-96c2-4c8e-d386-66f3bf2ad68c", "20663b79-e25e-20d1-b1d8-5e9e9f137e55", "", "", "", "", "", "", "2e5ac341-c6b6-0c24-34f2-55530a9c12bc", "1", "ALRP-N/18-8 test", "ANTI ALKALI R.PRIMER NHẠT", "", "2", "900000", "451095.29", "855000", "5", "90000", "0", "0", "0", "10", "171000", "1710000", "1881000", "902190.58", "807809.42", "Thùng", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "", "", "0", "2", "", "0", "", "", "", "0", "0", "900000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "", "451095.2903", "0", "0", "0");',
  7 => 'INSERT INTO opportunities_tqt_products (id, deleted, date_modified, modified_user_id, opportunity_id, product_id, group_price_id, product_gift_id, ref_id, parent_id, parent_note, product_combo, warehouse_id, ordering, product_code, product_name, prod_note, prod_quantity, prod_price, prod_orig_price, prod_sale_price, prod_discount, prod_discount_amt, prod_promotion, prod_promotion_amt, prod_more_percent, prod_vat_tax, prod_vat_amount, prod_amount, prod_total_amount, prod_orig_amount, prod_profit_amount, product_unit, prod_unit_opt, prod_unit_qty, prod_unit_length, prod_unit_width, prod_unit_price, product_type, product_gift, prod_convert_kg, prod_total_kg, prod_convert_mt, prod_total_mt, product_lots, prod_lot_id, delivered_qty, missing_qty, prod_custom, prod_num_uses, prod_date_start, prod_repeat_cycle, handling_note, prod_foreign_price, prod_foreign_amount, prod_actual_price, prod_stock_price, prod_more_amount, prod_comm_amount, prod_comm_more_amt, prod_more_price, prod_comm_price, prod_more_ret_tax, prod_more_ret_amt, prod_more_receive_amt, prod_more_note, prod_01_comm_per, prod_01_comm_amt, prod_01_comm_ret_tax, prod_01_comm_ret_amt, prod_01_comm_receive_amt, prod_01_comm_note, prod_02_comm_per, prod_02_comm_amt, prod_02_comm_ret_tax, prod_02_comm_ret_amt, prod_02_comm_receive_amt, prod_02_comm_note, prod_tax_exempt, prod_per_cpps, prod_amount_cpps, prod_total_stock_price, prod_total_amount_in, have_warehoused, prod_ext_incur_amount, prod_value_vat_import, prod_vat_tax_import, prod_vat_amount_import, prod_value_vat_spec, prod_vat_tax_spec, prod_vat_amount_spec, prod_value_vat_amount, prod_int_incur_amount, product_primary, product_set, prod_orig2_price, prod_return_orig_price, prod_custom_unit_qty, prod_custom_unit_price) VALUES ("4a3982f0-1ed6-833f-1de5-66f3bf9832d0", "1", "2024-09-25 07:43:33", "627d3b02-1dea-cd40-ca0a-5f96342ef270", "27e21ee2-96c2-4c8e-d386-66f3bf2ad68c", "bbc2205e-df60-9a45-b579-5d5d23263708", "", "", "", "", "", "", "2e5ac341-c6b6-0c24-34f2-55530a9c12bc", "2", "VT10190", "Hương Haagen Dazs Vanilla -CK2052 Haagen - Dazs Vanilla Liquid Flavor (food grade)", "", "2", "452000", "150000", "452000", "0", "0", "0", "0", "0", "10", "90400", "904000", "994400", "300000", "604000", "Kg", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "", "", "0", "2", "", "0", "", "", "", "0", "0", "452000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "", "150000", "0", "0", "0");',
  8 => 'INSERT INTO securitygroups_records (id, securitygroup_id, record_id, module, date_modified, deleted) VALUES ("5fa8c025-51bf-28cc-c43d-66f3bfbc33d4", "9a4fcc0e-c0d7-c39b-789a-5d5cda661ed2", "27e21ee2-96c2-4c8e-d386-66f3bf2ad68c", "Opportunities", "2024-09-25 07:43:33", "1");',
  9 => 'INSERT INTO securitygroups_records (id, securitygroup_id, record_id, module, date_modified, deleted) VALUES ("5dcee77d-f95a-86b3-a16c-66f3bf0c363f", "1f28b2e6-69cb-1e48-631d-57dc08f22aed", "27e21ee2-96c2-4c8e-d386-66f3bf2ad68c", "Opportunities", "2024-09-25 07:43:33", "1");',
  10 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("7b39aa40-0d23-b32e-348a-66e4f45f00a9", "1", "2024-09-14 02:25:56", "1", "admin", "z21232f297a57a5a743894a0e4a801fc3z9b045c31ea6aff22f11de768a37fa0e5z");',
  11 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("76870fe4-8f7b-d1b8-7156-66e3a122f4c3", "1", "2024-09-13 02:22:35", "1", "admin", "z21232f297a57a5a743894a0e4a801fc3z9b045c31ea6aff22f11de768a37fa0e5z");',
  12 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("9352f1c2-fa2e-f270-ee77-66e29cf32886", "1", "2024-09-12 07:49:26", "1", "admin", "z21232f297a57a5a743894a0e4a801fc3z9b045c31ea6aff22f11de768a37fa0e5z");',
  13 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("981a014c-5e0e-8037-d016-66debb2691f4", "1", "2024-09-09 09:10:22", "1", "admin", "z21232f297a57a5a743894a0e4a801fc3z9b045c31ea6aff22f11de768a37fa0e5z");',
  14 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("f1a50e45-2395-58fd-2bea-66de9b9a5d30", "1", "2024-09-09 06:54:13", "1", "admin", "z21232f297a57a5a743894a0e4a801fc3z9b045c31ea6aff22f11de768a37fa0e5z");',
  15 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("8fc98eb1-6c69-cff1-3af3-66e50a0ffa3c", "1", "2024-09-14 03:59:23", "1", "admin", "z21232f297a57a5a743894a0e4a801fc3z9b045c31ea6aff22f11de768a37fa0e5z");',
  16 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("276147e0-e4db-86ac-b12d-66e7870970f4", "1", "2024-09-16 01:20:34", "1", "admin", "z21232f297a57a5a743894a0e4a801fc3z9b045c31ea6aff22f11de768a37fa0e5z");',
  17 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("596ffd47-a8ed-e79f-fa7c-66e796a11148", "1", "2024-09-16 02:23:16", "dfd64c2f-adf4-9bf4-24a3-5d5c745cb796", "chinh.le", "z71e446e53d5d2871da48ef7410a82b07zce7003ced967a93922e122079841d287z");',
  18 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("bfe52c85-da89-2f37-059b-66e7ef2a68b2", "1", "2024-09-16 10:51:13", "1", "admin", "z21232f297a57a5a743894a0e4a801fc3z9b045c31ea6aff22f11de768a37fa0e5z");',
  19 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("5dcd9475-95ad-bc41-ce55-66f3d437a624", "1", "2024-09-25 09:22:12", "1", "admin", "z21232f297a57a5a743894a0e4a801fc3z9b045c31ea6aff22f11de768a37fa0e5z");',
  20 => 'INSERT INTO warehouse_out_in_related (id, deleted, date_modified, modified_user_id, date_perform, warehouse_in_id, warehouse_out_id, opportunity_id, product_lot_id, warehouse_id, product_id, product_stock_id, out_product_id, quantity, price, product_code, product_name, notes) VALUES ("9a110ff7-7e10-7a16-6f54-66f7742c4079", "1", "2024-09-28 03:19:52", "627d3b02-1dea-cd40-ca0a-5f96342ef270", "2024-09-27", "5b2df4c0-ada0-4d03-db4c-66f617c3aa27", "87dde1bd-865f-d4ea-2547-66f61793fdcb", "5a3e77e7-5ce8-10f8-d116-66f6179433bd", "", "2e5ac341-c6b6-0c24-34f2-55530a9c12bc", "c3787549-8cd8-3b58-1188-65db3c74a76a", "a89f6adc-7c77-11ef-9873-00163e3b7e12", "c2d3dd01-7d47-11ef-ad4d-00163e3b7e12", "47", "90000", "", "", "Lấy lại hiệu chỉnh bởi: Nguyễn Đăng Hoàng");',
  21 => 'INSERT INTO warehouse_out_in_related (id, deleted, date_modified, modified_user_id, date_perform, warehouse_in_id, warehouse_out_id, opportunity_id, product_lot_id, warehouse_id, product_id, product_stock_id, out_product_id, quantity, price, product_code, product_name, notes) VALUES ("923c71e2-6462-3305-3188-66f774e03a9c", "1", "2024-09-28 03:19:52", "627d3b02-1dea-cd40-ca0a-5f96342ef270", "2024-09-27", "a222eca6-ea7b-8eb9-d612-65e2e3671941", "87dde1bd-865f-d4ea-2547-66f61793fdcb", "5a3e77e7-5ce8-10f8-d116-66f6179433bd", "", "2e5ac341-c6b6-0c24-34f2-55530a9c12bc", "c3787549-8cd8-3b58-1188-65db3c74a76a", "53f60613-d86f-11ee-af53-00163e3b7e12", "c2d3dd01-7d47-11ef-ad4d-00163e3b7e12", "3", "90000", "", "", "Lấy lại hiệu chỉnh bởi: Nguyễn Đăng Hoàng");',
);
?>
