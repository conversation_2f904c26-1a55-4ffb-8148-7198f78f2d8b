<?php
// created: 2025-02-26 15:12:15
$GL<PERSON><PERSON>LS["dictionary"]["MonthlyTarget"] = array (
  'table' => 'monthlytargets',
  'audited' => true,
  'unified_search' => false,
  'duplicate_merge' => false,
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'varchar',
      'len' => '200',
      'source' => 'non-db',
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'monthlytargets_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'monthlytargets_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'branch_id' => 
    array (
      'name' => 'branch_id',
      'vname' => 'LBL_BRANCH_ID',
      'type' => 'enum',
      'options' => 'users_branch_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'BranchID of assigned user',
    ),
    'department_id' => 
    array (
      'name' => 'department_id',
      'vname' => 'LBL_DEPARTMENT_ID',
      'type' => 'enum',
      'options' => 'users_department_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'DepartmentID of assigned user',
    ),
    'assigned_user_id' => 
    array (
      'name' => 'assigned_user_id',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'vname' => 'LBL_ASSIGNED_TO_ID',
      'group' => 'assigned_user_name',
      'type' => 'relate',
      'table' => 'users',
      'module' => 'Users',
      'isnull' => 'false',
      'dbType' => 'id',
      'audited' => true,
      'reportable' => true,
      'duplicate_merge' => 'disabled',
      'comment' => 'User ID assigned to record',
    ),
    'assigned_user_name' => 
    array (
      'name' => 'assigned_user_name',
      'link' => 'assigned_user_link',
      'vname' => 'LBL_ASSIGNED_TO_NAME',
      'rname' => 'user_name',
      'type' => 'relate',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'assigned_user_id',
      'module' => 'Users',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'assigned_user_link' => 
    array (
      'name' => 'assigned_user_link',
      'type' => 'link',
      'relationship' => 'monthlytargets_assigned_user',
      'vname' => 'LBL_ASSIGNED_TO_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
      'duplicate_merge' => 'enabled',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'table' => 'users',
    ),
    'yy' => 
    array (
      'name' => 'yy',
      'vname' => 'LBL_YY',
      'type' => 'enum',
      'options' => 'years_dom',
      'len' => '4',
      'audited' => true,
      'massupdate' => false,
    ),
    'mm' => 
    array (
      'name' => 'mm',
      'vname' => 'LBL_MM',
      'type' => 'enum',
      'options' => 'months_dom',
      'len' => '2',
      'audited' => true,
      'massupdate' => false,
    ),
    'revenue' => 
    array (
      'name' => 'revenue',
      'vname' => 'LBL_REVENUE',
      'type' => 'double',
      'audited' => true,
    ),
    'output' => 
    array (
      'name' => 'output',
      'vname' => 'LBL_OUTPUT',
      'type' => 'double',
      'audited' => true,
    ),
    'collection' => 
    array (
      'name' => 'collection',
      'vname' => 'LBL_COLLECTION',
      'type' => 'double',
      'audited' => true,
    ),
    'new_shop' => 
    array (
      'name' => 'new_shop',
      'vname' => 'LBL_NEW_SHOP',
      'type' => 'int',
      'len' => '4',
      'audited' => true,
    ),
    'tqt_products' => 
    array (
      'name' => 'tqt_products',
      'type' => 'link',
      'relationship' => 'monthlytarget_products',
      'source' => 'non-db',
      'vname' => 'LBL_TQT_PRODUCTS',
    ),
    'ts_revenue' => 
    array (
      'name' => 'ts_revenue',
      'vname' => 'LBL_TS_REVENUE',
      'len' => '5',
      'type' => 'double',
      'audited' => true,
    ),
    'ts_collection' => 
    array (
      'name' => 'ts_collection',
      'vname' => 'LBL_TS_COLLECTION',
      'len' => '5',
      'type' => 'double',
      'audited' => true,
    ),
    'ts_new_shop' => 
    array (
      'name' => 'ts_new_shop',
      'vname' => 'LBL_TS_NEW_SHOP',
      'len' => '5',
      'type' => 'double',
      'audited' => true,
    ),
    'ts_kpi' => 
    array (
      'name' => 'ts_kpi',
      'vname' => 'LBL_TS_KPI',
      'len' => '5',
      'type' => 'double',
      'audited' => true,
    ),
    'total_per' => 
    array (
      'name' => 'total_per',
      'vname' => 'LBL_TOTAL_PER',
      'len' => '5',
      'type' => 'int',
    ),
    'error_kpi' => 
    array (
      'name' => 'error_kpi',
      'vname' => 'LBL_ERROR_KPI',
      'len' => '5',
      'type' => 'double',
      'audited' => true,
    ),
    'SecurityGroups' => 
    array (
      'name' => 'SecurityGroups',
      'type' => 'link',
      'relationship' => 'securitygroups_monthlytargets',
      'module' => 'SecurityGroups',
      'bean_name' => 'SecurityGroup',
      'source' => 'non-db',
      'vname' => 'LBL_SECURITYGROUPS',
    ),
  ),
  'indices' => 
  array (
    'id' => 
    array (
      'name' => 'monthlytargetspk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    'idx_monthlytargets_branch_id' => 
    array (
      'name' => 'idx_monthlytargets_branch_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
      ),
    ),
    'idx_monthlytargets_department_id' => 
    array (
      'name' => 'idx_monthlytargets_department_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'department_id',
      ),
    ),
    'idx_monthlytargets_branch_dept' => 
    array (
      'name' => 'idx_monthlytargets_branch_dept',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
        1 => 'department_id',
      ),
    ),
    'idx_monthlytargets_assigned' => 
    array (
      'name' => 'idx_monthlytargets_assigned',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'assigned_user_id',
      ),
    ),
    0 => 
    array (
      'name' => 'idx_mon_tg_id_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'id',
        1 => 'deleted',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_mon_tg_assigned_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'assigned_user_id',
      ),
    ),
    2 => 
    array (
      'name' => 'idx_mon_tg_date',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'yy',
        1 => 'mm',
      ),
    ),
  ),
  'relationships' => 
  array (
    'monthlytargets_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'MonthlyTargets',
      'rhs_table' => 'monthlytargets',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'monthlytargets_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'MonthlyTargets',
      'rhs_table' => 'monthlytargets',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
    'monthlytargets_assigned_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'MonthlyTargets',
      'rhs_table' => 'monthlytargets',
      'rhs_key' => 'assigned_user_id',
      'relationship_type' => 'one-to-many',
    ),
  ),
  'optimistic_lock' => true,
  'templates' => 
  array (
    'assignable' => 'assignable',
    'groups' => 'groups',
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
