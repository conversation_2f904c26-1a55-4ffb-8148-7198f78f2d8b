

{math equation="floor(left / right)"
	left=12
		right=$templateMeta.maxColumns
			assign=colClass
	}

<section class="container2 search-form ml-0 search_form_adv">
	<div class="row">
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="code_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_CODE' module='Team_Groups'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.code_advanced.value) <= 0}
	{assign var="value" value=$fields.code_advanced.default_value }
{else}
	{assign var="value" value=$fields.code_advanced.value }
{/if}
{if isTypeNumber($fields.code_advanced.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.code_advanced.name}' id='{$fields.code_advanced.name}' size='30' maxlength='20' value='{$value}' title='' tabindex=''  /> 

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="name_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_NAME' module='Team_Groups'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.name_advanced.value) <= 0}
	{assign var="value" value=$fields.name_advanced.default_value }
{else}
	{assign var="value" value=$fields.name_advanced.value }
{/if}
{if isTypeNumber($fields.name_advanced.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.name_advanced.name}' id='{$fields.name_advanced.name}' size='30' maxlength='200' value='{$value}' title='' tabindex=''  /> 

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="branch_id_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_BRANCH_ID' module='Team_Groups'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="branch_id_advanced[]" id="{$fields.branch_id_advanced.name}" size="4" multiple="1"  onchange="changeBranchDepartments(this, this.form.department_id_advanced)" >
{html_options options=$fields.branch_id_advanced.options selected=$fields.branch_id_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="department_id_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_DEPARTMENT_ID' module='Team_Groups'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="department_id_advanced[]" id="{$fields.department_id_advanced.name}" size="4" multiple="1"  >
{html_options options=$fields.department_id_advanced.options selected=$fields.department_id_advanced.value}
</select>

									</div>
			</div>
				</div>
	</div>
</section>

{if $HAS_ADVANCED_SEARCH}
<table cellspacing="0" cellpadding="0" border="0" class="mt-10">
<tr>
	<td scope="row" nowrap="nowrap" class="pt-10">
		<a style="font-size:10pt; text-decoration:none;" onclick="incomCRM.searchForm.searchFormSelect('{$module}|basic_search','{$module}|advanced_search')" href="#">[ {$APP.LNK_BASIC_SEARCH} ]</a>
	</td>
</tr>
<tr>
	<td>&nbsp;</td>
</tr>
</table>
{/if}

{if $DISPLAY_SAVED_SEARCH}
<div class="saved-search-adv table-responsive mt-10 pt-10">
<table cellspacing="0" cellpadding="0" border="0">
<tr>
	<td rowspan="2" width="40%">
		<a class='tabFormAdvLink' onhover href='javascript:toggleInlineSearch()'>
		<img src='{incomCRM_getimagepath file="advanced_search.gif"}' id='up_down_img' border="0" />&nbsp;{$APP.LNK_SAVED_VIEWS}
		</a><br/>
		<input type='hidden' id='showSSDIV' name='showSSDIV' value='{$SHOWSSDIV}' />
	</td>
	<td scope='row' width="20%" nowrap="nowrap">
		{incomCRM_translate label='LBL_SAVE_SEARCH_AS' module='SavedSearch'}:
	</td>
	<td nowrap>
		<input type='text' name='saved_search_name' />
		<input type='hidden' name='search_module' value='' />
		<input type='hidden' name='saved_search_action' value='' />
		<input title='{$APP.LBL_SAVE_BUTTON_LABEL}' value='{$APP.LBL_SAVE_BUTTON_LABEL}' class='button' type='button' name='saved_search_submit' onclick='incomCRM.savedViews.setChooser(); return incomCRM.savedViews.saved_search_action("save");' />
	</td>
</tr>
<tr>
	<td scope='row' nowrap="nowrap">
		{incomCRM_translate label='LBL_MODIFY_CURRENT_SEARCH' module='SavedSearch'}:
	</td>
	<td nowrap>
		<input class='button' onclick='incomCRM.savedViews.setChooser(); return incomCRM.savedViews.saved_search_action("update")' value='{$APP.LBL_UPDATE}' title='{$APP.LBL_UPDATE}' name='ss_update' id='ss_update' type='button' />
		<input class='button' onclick='return incomCRM.savedViews.saved_search_action("delete", "{incomCRM_translate label='LBL_DELETE_CONFIRM' module='SavedSearch'}")' value='{$APP.LBL_DELETE}' title='{$APP.LBL_DELETE}' name='ss_delete' id='ss_delete' type='button' />
		<br/><span id='curr_search_name'></span>
	</td>
</tr>
<tr>
	<td colspan="3">
		<div style="{$DISPLAYSS}" id="inlineSavedSearch">{$SAVED_SEARCH}</div>
	</td>
</tr>
</table>
</div>
{/if}

<script type="text/javascript">
{literal}
if( typeof(loadSSL_Scripts) == 'function' ) {
	loadSSL_Scripts();
}
YAHOO.util.Event.onDOMReady(function(){
	var form = null;
	if( document.search_form ) form = document.search_form;
	else if( document.popup_query_form ) form = document.popup_query_form;
	else return;
	if( form ) {
		if( form.branch_id_advanced ) {
			if( form.department_id_advanced )
				changeBranchDepartments(form.branch_id_advanced, form.department_id_advanced);
			else if( form.department_advanced )
				changeBranchDepartments(form.branch_id_advanced, form.department_advanced);
		}
		if( form.department_id_advanced && form.assigned_user_id_advanced )
			changeDepartmentUsers(form.department_id_advanced, form.assigned_user_id_advanced);
		if( form.location_district_advanced && form.location_city_advanced )
			changeParentSelectedOption(form.location_city_advanced, form.location_district_advanced, 'location_district_dom');
	}
});
{/literal}
</script>

{literal}<script type="text/javascript">if(typeof sqs_objects == 'undefined'){var sqs_objects = new Array;}
sqs_objects['popup_query_form_modified_by_name_advanced'] = {"form":"popup_query_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["modified_by_name_advanced","modified_user_id_advanced"],"required_list":["modified_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['popup_query_form_created_by_name_advanced'] = {"form":"popup_query_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["created_by_name_advanced","created_by_advanced"],"required_list":["created_by"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
</script>{/literal}