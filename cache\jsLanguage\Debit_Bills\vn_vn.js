incomCRM.language.setLanguage('Debit_Bills', {"LBL_MODULE_NAME":"N\u1ee3 ph\u1ea3i thu","LBL_MODULE_TITLE":"N\u1ee3 ph\u1ea3i thu: Trang ch\u1ee7","LNK_LIST":"C\u00f4ng n\u1ee3 ph\u1ea3i thu","LBL_SEARCH_FORM_TITLE":"T\u00ecm ki\u1ebfm","LBL_LIST_FORM_TITLE":"Danh s\u00e1ch n\u1ee3 ph\u1ea3i thu","LBL_OVERDUE_DEBT":"N\u1ee3 ph\u1ea3i Thu - Theo tu\u1ed5i n\u1ee3","LBL_SUBPAGE_TITLE":"Chi ti\u1ebft theo t\u1eebng Kh\u00e1ch h\u00e0ng","LBL_AMT_THU":"Ph\u00e1t sinh Thu","LBL_AMT_CHI":"Ph\u00e1t Sinh Tr\u1ea3","LBL_PAID_THU":"Ti\u1ec1n \u0111\u00e3 Thu","LBL_PAID_CHI":"Ti\u1ec1n \u0111\u00e3 Tr\u1ea3","LBL_OWED_THU":"C\u00f2n l\u1ea1i ph\u1ea3i Thu","LBL_OWED_CHI":"C\u00f2n l\u1ea1i ph\u1ea3i Tr\u1ea3","LBL_ACCOUNT_NAME":"Kh\u00e1ch h\u00e0ng","LBL_RPT_SALE_STATISTICS":"B\u00e1o c\u00e1o C\u00f4ng n\u1ee3 Kh\u00e1ch h\u00e0ng - NCC"});