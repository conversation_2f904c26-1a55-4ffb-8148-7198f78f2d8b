
<section id="EditViewViewBody" class="view-module-{$module|strtolower}">

<form action="index.php" method="POST" name="{$form_name}" id="{$form_id}" class="_edit-{$instanceName}" {$enctype}>
<input type="hidden" name="module" value="{$module}" />
<input type="hidden" name="action" id="action" />
{if isset($smarty.request.isDuplicate) && $smarty.request.isDuplicate eq "true"}
<input type="hidden" name="record" id="record" value="" />
<input type="hidden" name="duplicateSave" value="true" />
{else}
<input type="hidden" name="record" id="record" value="{$fields.id.value}" />
{/if}
<input type="hidden" name="return_module" value="{$smarty.request.return_module}" />
<input type="hidden" name="return_action" value="{if $smarty.request.return_action eq 'LoadTabSubpanels'}DetailView{else}{$smarty.request.return_action}{/if}" />
<input type="hidden" name="return_id" value="{$smarty.request.return_id}" />
<input type="hidden" name="isDuplicate" value="false" />
<input type="hidden" name="module_tab" /> 
<input type="hidden" name="contact_role" />
{if !empty($smarty.request.return_module)}
<input type="hidden" name="relate_to" value="{if $smarty.request.return_relationship}{$smarty.request.return_relationship}{else}{$smarty.request.return_module}{/if}" />
<input type="hidden" name="relate_id" value="{$smarty.request.return_id}" />
{/if}
<input type="hidden" name="offset" value="{$offset}" />
{if empty($fields.id.value)}
<input type="hidden" name="_notify_random" value="{php}echo create_guid();{/php}" />
{/if}
<input type="hidden" name="isFromEditView" value="{$module}" />
<table width="100%" cellpadding="0" cellspacing="0" border="0">
<tr>
<td class="buttons">
{if $bean->aclAccess("save")}<input title="{$APP.LBL_SAVE_BUTTON_TITLE}" accessKey="{$APP.LBL_SAVE_BUTTON_KEY}" class="button save" onclick="{if $isDuplicate}this.form.return_id.value=''; {/if}this.form.action.value='Save'; return check_form('EditView');" type="submit" name="button" value="{$APP.LBL_SAVE_BUTTON_LABEL}" />{/if} 
{if !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($fields.id.value))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {elseif !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($smarty.request.return_id))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {else}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='index'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {/if}
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=Employees", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</td>
</tr>
</table>
{incomCRM_include include=$includes}
<div id="DEFAULT" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="DEFAULT_GROUP" style="display:block">
<table width="100%" border="0" cellspacing="1" cellpadding="0">
<tr>
<td valign="top" id='user_code_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_USER_CODE' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_user_code_field' >
{counter name="panelFieldCount"}
{if $DISPLAY_EDIT}<input tabindex="100"  type="text" name="{$fields.user_code.name}" id="{$fields.user_code.name}" size="25" maxlength="50" value="{$fields.user_code.value}" title="" tabindex="1" />{else}{$fields.user_code.value}<input tabindex="100"  type="hidden" name="{$fields.user_code.name}" id="{$fields.user_code.name}" value="{$fields.user_code.value}" />{/if}
</td>
<td valign="top" id='employee_status_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_EMPLOYEE_STATUS' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_employee_status_field' >
{counter name="panelFieldCount"}

{$fields.employee_status.value}
</td>
</tr>
<tr>
<td valign="top" id='last_name_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_LAST_NAME' module='Employees'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='33.33%' id='_last_name_field' >
{counter name="panelFieldCount"}
{html_options name="salutation" options=$fields.salutation.options selected=$fields.salutation.value style="width:50px;"}&nbsp;<input tabindex="102"  name="last_name" size="20" maxlength="100" type="text" value="{$fields.last_name.value}" />
</td>
<td valign="top" id='id_citizen_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ID_CITIZEN' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_id_citizen_field' >
{counter name="panelFieldCount"}

{if strlen($fields.id_citizen.value) <= 0}
{assign var="value" value=$fields.id_citizen.default_value }
{else}
{assign var="value" value=$fields.id_citizen.value }
{/if}
{if isTypeNumber($fields.id_citizen.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.id_citizen.name}' id='{$fields.id_citizen.name}' size='30' maxlength='20' value='{$value}' title='' tabindex='103'  /> 

</td>
</tr>
<tr>
<td valign="top" id='gender_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_GENDER' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_gender_field' >
{counter name="panelFieldCount"}

<select name="{$fields.gender.name}" id="{$fields.gender.name}" title='' tabindex="104"  >
{if isset($fields.gender.value) && $fields.gender.value != ''}
{html_options options=$fields.gender.options selected=$fields.gender.value}
{else}
{html_options options=$fields.gender.options selected=$fields.gender.default}
{/if}
</select>
</td>
<td valign="top" id='issued_date_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ISSUED_DATE' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_issued_date_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.issued_date.value }
<input autocomplete="off" type="text" name="{$fields.issued_date.name}" id="{$fields.issued_date.name}" value="{$date_value}" title=''  tabindex='105' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.issued_date.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.issued_date.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.issued_date.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
</tr>
<tr>
<td valign="top" id='birthdate_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_BIRTHDATE' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_birthdate_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.birthdate.value }
<input autocomplete="off" type="text" name="{$fields.birthdate.name}" id="{$fields.birthdate.name}" value="{$date_value}" title=''  tabindex='106' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.birthdate.name}_trigger" align="absmiddle" />
&nbsp;(<span class="dateFormat">{$USER_DATEFORMAT}</span>)
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.birthdate.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.birthdate.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
<td valign="top" id='issued_by_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ISSUED_BY' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_issued_by_field' >
{counter name="panelFieldCount"}

{if strlen($fields.issued_by.value) <= 0}
{assign var="value" value=$fields.issued_by.default_value }
{else}
{assign var="value" value=$fields.issued_by.value }
{/if}
{if isTypeNumber($fields.issued_by.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.issued_by.name}' id='{$fields.issued_by.name}' size='30'  value='{$value}' title='' tabindex='107'  /> 

</td>
</tr>
<tr>
<td valign="top" id='academic_level_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACADEMIC_LEVEL' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_academic_level_field' >
{counter name="panelFieldCount"}

{if strlen($fields.academic_level.value) <= 0}
{assign var="value" value=$fields.academic_level.default_value }
{else}
{assign var="value" value=$fields.academic_level.value }
{/if}
{if isTypeNumber($fields.academic_level.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.academic_level.name}' id='{$fields.academic_level.name}' size='30' maxlength='250' value='{$value}' title='' tabindex='108'  /> 

</td>
<td valign="top" id='address_street_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_PRIMARY_ADDRESS2' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_address_street_field' >
{counter name="panelFieldCount"}

{if empty($fields.address_street.value)}
{assign var="value" value=$fields.address_street.default_value }
{else}
{assign var="value" value=$fields.address_street.value }
{/if}
<textarea id="{$fields.address_street.name}" name="{$fields.address_street.name}" rows="2" cols="40" title='' tabindex="109"  >{$value}</textarea>
</td>
</tr>
<tr>
<td valign="top" id='specialized_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_SPECIALIZED' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_specialized_field' >
{counter name="panelFieldCount"}

{if strlen($fields.specialized.value) <= 0}
{assign var="value" value=$fields.specialized.default_value }
{else}
{assign var="value" value=$fields.specialized.value }
{/if}
{if isTypeNumber($fields.specialized.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.specialized.name}' id='{$fields.specialized.name}' size='30' maxlength='250' value='{$value}' title='' tabindex='110'  /> 

</td>
<td valign="top" id='address_permanent_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ADDRESS_PERMANENT' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_address_permanent_field' >
{counter name="panelFieldCount"}

{if empty($fields.address_permanent.value)}
{assign var="value" value=$fields.address_permanent.default_value }
{else}
{assign var="value" value=$fields.address_permanent.value }
{/if}
<textarea id="{$fields.address_permanent.name}" name="{$fields.address_permanent.name}" rows="2" cols="40" title='' tabindex="111"  >{$value}</textarea>
</td>
</tr>
<tr>
<td valign="top" id='title_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_TITLE' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_title_field' >
{counter name="panelFieldCount"}

{if strlen($fields.title.value) <= 0}
{assign var="value" value=$fields.title.default_value }
{else}
{assign var="value" value=$fields.title.value }
{/if}
{if isTypeNumber($fields.title.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.title.name}' id='{$fields.title.name}' size='30' maxlength='50' value='{$value}' title='' tabindex='112'  /> 

</td>
<td valign="top" id='address_temporary_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ADDRESS_TEMPORARY' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_address_temporary_field' >
{counter name="panelFieldCount"}

{if empty($fields.address_temporary.value)}
{assign var="value" value=$fields.address_temporary.default_value }
{else}
{assign var="value" value=$fields.address_temporary.value }
{/if}
<textarea id="{$fields.address_temporary.name}" name="{$fields.address_temporary.name}" rows="2" cols="40" title='' tabindex="113"  >{$value}</textarea>
</td>
</tr>
<tr>
<td valign="top" id='branch_id_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_BRANCH_ID' module='Employees'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='33.33%' id='_branch_id_field' >
{counter name="panelFieldCount"}
{if $DISPLAY_EDIT}{html_options name="branch_id" options=$fields.branch_id.options selected=$fields.branch_id.value onchange="changeBranchDepartments(this, this.form.department)"}{else}{$fields.branch_id.options[$fields.branch_id.value]}<input tabindex="114"  type="hidden" name="{$fields.branch_id.name}" id="{$fields.branch_id.name}" value="{$fields.branch_id.value}" />{/if}
</td>
<td valign="top" id='phone_mobile_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_EMP_PHONE' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_phone_mobile_field' >
{counter name="panelFieldCount"}

{if strlen($fields.phone_mobile.value) <= 0}
{assign var="value" value=$fields.phone_mobile.default_value }
{else}
{assign var="value" value=$fields.phone_mobile.value }
{/if}
{if isTypeNumber($fields.phone_mobile.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.phone_mobile.name}' id='{$fields.phone_mobile.name}' size='30' maxlength='50' value='{$value}' title='' tabindex='115'  /> 

</td>
</tr>
<tr>
<td valign="top" id='department_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DEPARTMENT' module='Employees'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='33.33%' id='_department_field' >
{counter name="panelFieldCount"}
{if $DISPLAY_EDIT}{html_options name="department" options=$fields.department.options selected=$fields.department.value onchange="changeDepartmentTeams(this, this.form.teams)"}{else}{$fields.department.options[$fields.department.value]}<input tabindex="116"  type="hidden" name="{$fields.department.name}" id="{$fields.department.name}" value="{$fields.department.value}" />{/if}
</td>
<td valign="top" id='insurance_number_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_INSURANCE_NUMBER' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_insurance_number_field' >
{counter name="panelFieldCount"}

{if strlen($fields.insurance_number.value) <= 0}
{assign var="value" value=$fields.insurance_number.default_value }
{else}
{assign var="value" value=$fields.insurance_number.value }
{/if}
{if isTypeNumber($fields.insurance_number.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.insurance_number.name}' id='{$fields.insurance_number.name}' size='30' maxlength='50' value='{$value}' title='' tabindex='117'  /> 

</td>
</tr>
<tr>
<td valign="top" id='teams_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_TEAMS' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_teams_field' >
{counter name="panelFieldCount"}
{if $DISPLAY_EDIT}{html_options name="teams" options=$fields.teams.options selected=$fields.teams.value}{else}{$fields.teams.options[$fields.teams.value]}<input tabindex="118"  type="hidden" name="{$fields.teams.name}" id="{$fields.teams.name}" value="{$fields.teams.value}" />{/if}
</td>
<td valign="top" id='insurance_month_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_INSURANCE_MONTH' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_insurance_month_field' >
{counter name="panelFieldCount"}

{if strlen($fields.insurance_month.value) <= 0}
{assign var="value" value=$fields.insurance_month.default_value }
{else}
{assign var="value" value=$fields.insurance_month.value }
{/if}
{if isTypeNumber($fields.insurance_month.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.insurance_month.name}' id='{$fields.insurance_month.name}' size='30' maxlength='50' value='{$value}' title='' tabindex='119'  /> 

</td>
</tr>
<tr>
<td valign="top" id='work_start_date_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DATE_START' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" colspan='3' id='_work_start_date_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.work_start_date.value }
<input autocomplete="off" type="text" name="{$fields.work_start_date.name}" id="{$fields.work_start_date.name}" value="{$date_value}" title=''  tabindex='120' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.work_start_date.name}_trigger" align="absmiddle" />
&nbsp;(<span class="dateFormat">{$USER_DATEFORMAT}</span>)
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.work_start_date.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.work_start_date.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
</tr>
<tr>
<td valign="top" id='work_end_date_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DATE_END' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_work_end_date_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.work_end_date.value }
<input autocomplete="off" type="text" name="{$fields.work_end_date.name}" id="{$fields.work_end_date.name}" value="{$date_value}" title=''  tabindex='121' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.work_end_date.name}_trigger" align="absmiddle" />
&nbsp;(<span class="dateFormat">{$USER_DATEFORMAT}</span>)
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.work_end_date.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.work_end_date.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
<td valign="top" id='maternity_leave_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_MATERNITY_LEAVE' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_maternity_leave_field' >
{counter name="panelFieldCount"}

{if strlen($fields.maternity_leave.value) <= 0}
{assign var="value" value=$fields.maternity_leave.default_value }
{else}
{assign var="value" value=$fields.maternity_leave.value }
{/if}
{if isTypeNumber($fields.maternity_leave.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.maternity_leave.name}' id='{$fields.maternity_leave.name}' size='30' maxlength='50' value='{$value}' title='' tabindex='122'  /> 

</td>
</tr>
<tr>
<td valign="top" id='seniority_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_SENIORITY' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_seniority_field' >
{counter name="panelFieldCount"}

{if strlen($fields.seniority.value) <= 0}
{assign var="value" value=$fields.seniority.default_value }
{else}
{assign var="value" value=$fields.seniority.value }
{/if}
{if isTypeNumber($fields.seniority.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.seniority.name}' id='{$fields.seniority.name}' size='30' maxlength='250' value='{$value}' title='' tabindex='123'  /> 

</td>
<td valign="top" id='avartar_image_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_AVARTAR_IMAGE' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_avartar_image_field' >
{counter name="panelFieldCount"}
<input tabindex="124"  type="file" name="image_url" size="30" />{if !empty($fields.avartar_image.value)}<input tabindex="124"  type="hidden" name="image_url_curr" value="{$fields.avartar_image.value}" /><br/><a class="avatar-pic" href="javascript:;" title="Xem hình" onclick="popupDialogImage('{$fields.avartar_image.value|validDownloadFile|htmlspecialchars}', {ldelim}title:'{$fields.name.value|htmlspecialchars}'{rdelim})"><img border="0" src="{$fields.avartar_image.value|validDownloadFile}" alt="{$fields.avartar_image.value|basename}" height="50" /></a>{/if}
</td>
</tr>
<tr>
<td valign="top" id='email1_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_EMAIL' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_email1_field' >
{counter name="panelFieldCount"}

{$fields.email1.value}
</td>
<td valign="top" id='notes_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ATTACHMENT_FILES' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_notes_field' >
{counter name="panelFieldCount"}
<div id="attachment_files">{$ATTACHMENTS}</div>
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("DEFAULT").style.display='none';</script>
{/if}
<div id="LBL_PROBATION_CONTRACT" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_PROBATION_CONTRACT');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_PROBATION_CONTRACT_IMG" border="0" />
{incomCRM_translate label='LBL_PROBATION_CONTRACT' module='Employees'}
</a>
</h4>
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_PROBATION_CONTRACT_GROUP" style="display:none">
<table width="100%" border="0" cellspacing="1" cellpadding="0">
<tr>
<td valign="top" id='contract_number_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTRACT_NUMBER' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_contract_number_field' >
{counter name="panelFieldCount"}

{if strlen($fields.contract_number.value) <= 0}
{assign var="value" value=$fields.contract_number.default_value }
{else}
{assign var="value" value=$fields.contract_number.value }
{/if}
{if isTypeNumber($fields.contract_number.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.contract_number.name}' id='{$fields.contract_number.name}' size='30' maxlength='50' value='{$value}' title='' tabindex='127'  /> 

</td>
<td valign="top" id='salary_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_SALARY' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_salary_field' >
{counter name="panelFieldCount"}

{if strlen($fields.salary.value) <= 0}
{assign var="value" value=$fields.salary.default_value }
{else}
{assign var="value" value=$fields.salary.value }
{/if}
{if isTypeNumber($fields.salary.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.salary.name}' id='{$fields.salary.name}' size='30' maxlength='50' value='{$value}' title='' tabindex='128'  /> 

</td>
</tr>
<tr>
<td valign="top" id='contract_date_start_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTRACT_DATE_START' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_contract_date_start_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.contract_date_start.value }
<input autocomplete="off" type="text" name="{$fields.contract_date_start.name}" id="{$fields.contract_date_start.name}" value="{$date_value}" title=''  tabindex='129' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.contract_date_start.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.contract_date_start.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.contract_date_start.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
<td valign="top" id='contract_date_end_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTRACT_DATE_END' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_contract_date_end_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.contract_date_end.value }
<input autocomplete="off" type="text" name="{$fields.contract_date_end.name}" id="{$fields.contract_date_end.name}" value="{$date_value}" title=''  tabindex='130' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.contract_date_end.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.contract_date_end.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.contract_date_end.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_PROBATION_CONTRACT").style.display='none';</script>
{/if}
<div id="LBL_12M_CONTRACT" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_12M_CONTRACT');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_12M_CONTRACT_IMG" border="0" />
{incomCRM_translate label='LBL_12M_CONTRACT' module='Employees'}
</a>
</h4>
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_12M_CONTRACT_GROUP" style="display:none">
<table width="100%" border="0" cellspacing="1" cellpadding="0">
<tr>
<td valign="top" id='contract12_number_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTRACT_NUMBER' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_contract12_number_field' >
{counter name="panelFieldCount"}

{if strlen($fields.contract12_number.value) <= 0}
{assign var="value" value=$fields.contract12_number.default_value }
{else}
{assign var="value" value=$fields.contract12_number.value }
{/if}
{if isTypeNumber($fields.contract12_number.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.contract12_number.name}' id='{$fields.contract12_number.name}' size='30' maxlength='50' value='{$value}' title='' tabindex='131'  /> 

</td>
<td valign="top" id='contract12_salary_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_SALARY' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_contract12_salary_field' >
{counter name="panelFieldCount"}

{if strlen($fields.contract12_salary.value) <= 0}
{assign var="value" value=$fields.contract12_salary.default_value }
{else}
{assign var="value" value=$fields.contract12_salary.value }
{/if}
{if isTypeNumber($fields.contract12_salary.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.contract12_salary.name}' id='{$fields.contract12_salary.name}' size='30' maxlength='50' value='{$value}' title='' tabindex='132'  /> 

</td>
</tr>
<tr>
<td valign="top" id='contract12_date_start_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTRACT_DATE_START' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_contract12_date_start_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.contract12_date_start.value }
<input autocomplete="off" type="text" name="{$fields.contract12_date_start.name}" id="{$fields.contract12_date_start.name}" value="{$date_value}" title=''  tabindex='133' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.contract12_date_start.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.contract12_date_start.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.contract12_date_start.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
<td valign="top" id='contract12_date_end_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTRACT_DATE_END' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_contract12_date_end_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.contract12_date_end.value }
<input autocomplete="off" type="text" name="{$fields.contract12_date_end.name}" id="{$fields.contract12_date_end.name}" value="{$date_value}" title=''  tabindex='134' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.contract12_date_end.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.contract12_date_end.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.contract12_date_end.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
</tr>
<tr>
<td valign="top" id='appendix_date_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_APPENDIX_DATE' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_appendix_date_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.appendix_date.value }
<input autocomplete="off" type="text" name="{$fields.appendix_date.name}" id="{$fields.appendix_date.name}" value="{$date_value}" title=''  tabindex='135' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.appendix_date.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.appendix_date.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.appendix_date.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
<td valign="top" id='description_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_APPENDIX_NOTE' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_description_field' >
{counter name="panelFieldCount"}

{if empty($fields.description.value)}
{assign var="value" value=$fields.description.default_value }
{else}
{assign var="value" value=$fields.description.value }
{/if}
<textarea id="{$fields.description.name}" name="{$fields.description.name}" rows="2" cols="40" title='' tabindex="136"  >{$value}</textarea>
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_12M_CONTRACT").style.display='none';</script>
{/if}
<div id="LBL_UNSPECIFIED_CONTRACT" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_UNSPECIFIED_CONTRACT');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_UNSPECIFIED_CONTRACT_IMG" border="0" />
{incomCRM_translate label='LBL_UNSPECIFIED_CONTRACT' module='Employees'}
</a>
</h4>
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_UNSPECIFIED_CONTRACT_GROUP" style="display:none">
<table width="100%" border="0" cellspacing="1" cellpadding="0">
<tr>
<td valign="top" id='contract_unspecified_number_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTRACT_NUMBER' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_contract_unspecified_number_field' >
{counter name="panelFieldCount"}

{if strlen($fields.contract_unspecified_number.value) <= 0}
{assign var="value" value=$fields.contract_unspecified_number.default_value }
{else}
{assign var="value" value=$fields.contract_unspecified_number.value }
{/if}
{if isTypeNumber($fields.contract_unspecified_number.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.contract_unspecified_number.name}' id='{$fields.contract_unspecified_number.name}' size='30' maxlength='50' value='{$value}' title='' tabindex='137'  /> 

</td>
<td valign="top" id='contract_unspecified_salary_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_SALARY' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_contract_unspecified_salary_field' >
{counter name="panelFieldCount"}

{if strlen($fields.contract_unspecified_salary.value) <= 0}
{assign var="value" value=$fields.contract_unspecified_salary.default_value }
{else}
{assign var="value" value=$fields.contract_unspecified_salary.value }
{/if}
{if isTypeNumber($fields.contract_unspecified_salary.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.contract_unspecified_salary.name}' id='{$fields.contract_unspecified_salary.name}' size='30' maxlength='50' value='{$value}' title='' tabindex='138'  /> 

</td>
</tr>
<tr>
<td valign="top" id='contract_unspecified_date_start_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTRACT_DATE_START' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_contract_unspecified_date_start_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.contract_unspecified_date_start.value }
<input autocomplete="off" type="text" name="{$fields.contract_unspecified_date_start.name}" id="{$fields.contract_unspecified_date_start.name}" value="{$date_value}" title=''  tabindex='139' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.contract_unspecified_date_start.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.contract_unspecified_date_start.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.contract_unspecified_date_start.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
<td valign="top" id='_label' width='16.67%' scope="row">
</td>
<td valign="top" width='33.33%' id='__field' >
</td>
</tr>
<tr>
<td valign="top" id='contract_unspecified_appendix_date_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_APPENDIX_DATE' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_contract_unspecified_appendix_date_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.contract_unspecified_appendix_date.value }
<input autocomplete="off" type="text" name="{$fields.contract_unspecified_appendix_date.name}" id="{$fields.contract_unspecified_appendix_date.name}" value="{$date_value}" title=''  tabindex='141' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.contract_unspecified_appendix_date.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.contract_unspecified_appendix_date.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.contract_unspecified_appendix_date.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
<td valign="top" id='appendix_note_label' width='16.67%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_APPENDIX_NOTE' module='Employees'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='33.33%' id='_appendix_note_field' >
{counter name="panelFieldCount"}

{if empty($fields.appendix_note.value)}
{assign var="value" value=$fields.appendix_note.default_value }
{else}
{assign var="value" value=$fields.appendix_note.value }
{/if}
<textarea id="{$fields.appendix_note.name}" name="{$fields.appendix_note.name}" rows="2" cols="40" title='' tabindex="142"  >{$value}</textarea>
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_UNSPECIFIED_CONTRACT").style.display='none';</script>
{/if}

<div class="buttons" style="margin-top:15px;">
{if $bean->aclAccess("save")}<input title="{$APP.LBL_SAVE_BUTTON_TITLE}" accessKey="{$APP.LBL_SAVE_BUTTON_KEY}" class="button save" onclick="{if $isDuplicate}this.form.return_id.value=''; {/if}this.form.action.value='Save'; return check_form('EditView');" type="submit" name="button" value="{$APP.LBL_SAVE_BUTTON_LABEL}" />{/if} 
{if !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($fields.id.value))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {elseif !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($smarty.request.return_id))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {else}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='index'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {/if}
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=Employees", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</div>
</form>
{$set_focus_block}
<!-- Begin Meta-Data Javascript -->
<script type="text/javascript">
		{literal}
function changeContract(form){
	var isOk = false, el, val = form.contract_type.value, arr = ["contract_nunber_label", "_contract_nunber_field", "salary_label", "_salary_field", "contract_date_start_label", "_contract_date_start_field", "contract_date_end_label", "_contract_date_end_field","appendix_date_label", "_appendix_date_field", "description_label", "_description_field"];
	

	for( ii = 0; ii < arr.length; ii++ )
	{
		
		el = getObjectById(arr[ii]);
		if( val == "1") {
			if(ii > 7 )
				el.style.display = "none";
			else el.style.display = "";
		}
		else if(val == 2)
		{
			 el.style.display = "";
		}
		else if(val == 3)
		{
			if(ii == 6 || ii == 7) el.style.display = "none";
			else el.style.display = "";
		}
		else{
			el.style.display = "none";
		}
	}
}
function calcWorkDay(form){

 	var $elem = form.remaining_day;
 	var today = new Date();
 	var date = today.getDate()+"/"+(today.getMonth()+1)+"/"+today.getFullYear();
	calcDate2NumberByDate2( date, form.contract_date_end.value, $elem);
}

{/literal}
YAHOO.util.Event.onDOMReady(function(){ldelim}
{if $DISPLAY_EDIT}
{if !empty($fields.branch_id.value)}
	if( document.{$form_name}.branch_id && document.{$form_name}.department )
		changeBranchDepartments(document.{$form_name}.branch_id, document.{$form_name}.department);
{/if}
{if !empty($fields.department.value)}
	if( document.{$form_name}.department && document.{$form_name}.teams )
		changeDepartmentTeams(document.{$form_name}.department, document.{$form_name}.teams);
{/if}
{/if}
{if !$EDIT_REPORTS_TO}
	if( document.{$form_name}.reports_to_name ) document.{$form_name}.reports_to_name.disabled = true;
	if( document.{$form_name}.btn_reports_to_name ) document.{$form_name}.btn_reports_to_name.disabled = true;
	if( document.{$form_name}.btn_clr_reports_to_name ) document.{$form_name}.btn_clr_reports_to_name.disabled = true;
{/if}
changeContract( document.{$form_name} );

{rdelim});
</script>
<!-- End Meta-Data Javascript -->
</section>{literal}
<script type="text/javascript">
addToValidate('EditView', 'id', 'id', false, '{/literal}{incomCRM_translate label='LBL_ID' module='Employees'}{literal}' );
addToValidate('EditView', 'user_name', 'user_name', false, '{/literal}{incomCRM_translate label='LBL_USER_NAME' module='Employees'}{literal}' );
addToValidate('EditView', 'user_hash', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_USER_HASH' module='Employees'}{literal}' );
addToValidate('EditView', 'system_generated_password', 'bool', true, '{/literal}{incomCRM_translate label='LBL_SYSTEM_GENERATED_PASSWORD' module='Employees'}{literal}' );
addToValidate('EditView', 'pwd_last_changed_date', 'date', false, 'LBL_PSW_MODIFIED' );
addToValidate('EditView', 'authenticate_id', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_AUTHENTICATE_ID' module='Employees'}{literal}' );
addToValidate('EditView', 'incomcrm_login', 'bool', false, '{/literal}{incomCRM_translate label='LBL_INCOMCRM_LOGIN' module='Employees'}{literal}' );
addToValidate('EditView', 'first_name', 'name', false, '{/literal}{incomCRM_translate label='LBL_FIRST_NAME' module='Employees'}{literal}' );
addToValidate('EditView', 'last_name', 'name', true, '{/literal}{incomCRM_translate label='LBL_LAST_NAME' module='Employees'}{literal}' );
addToValidate('EditView', 'full_name', 'name', false, '{/literal}{incomCRM_translate label='LBL_NAME' module='Employees'}{literal}' );
addToValidate('EditView', 'name', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_NAME' module='Employees'}{literal}' );
addToValidate('EditView', 'reports_to_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_REPORTS_TO_ID' module='Employees'}{literal}' );
addToValidate('EditView', 'reports_to_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_REPORTS_TO_NAME' module='Employees'}{literal}' );
addToValidate('EditView', 'is_admin', 'bool', false, '{/literal}{incomCRM_translate label='LBL_IS_ADMIN' module='Employees'}{literal}' );
addToValidate('EditView', 'external_auth_only', 'bool', false, '{/literal}{incomCRM_translate label='LBL_EXT_AUTHENTICATE' module='Employees'}{literal}' );
addToValidate('EditView', 'receive_notifications', 'bool', false, '{/literal}{incomCRM_translate label='LBL_RECEIVE_NOTIFICATIONS' module='Employees'}{literal}' );
addToValidate('EditView', 'description', 'text', false, '{/literal}{incomCRM_translate label='LBL_APPENDIX_NOTE' module='Employees'}{literal}' );
addToValidate('EditView', 'date_entered_date', 'date', false, 'Ngày tạo' );
addToValidate('EditView', 'date_modified_date', 'date', false, 'Thay đổi cuối' );
addToValidate('EditView', 'modified_user_id', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_BY_ID' module='Employees'}{literal}' );
addToValidate('EditView', 'modified_by_name', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_BY' module='Employees'}{literal}' );
addToValidate('EditView', 'created_by', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO' module='Employees'}{literal}' );
addToValidate('EditView', 'title', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TITLE' module='Employees'}{literal}' );
addToValidate('EditView', 'department', 'enum', true, '{/literal}{incomCRM_translate label='LBL_DEPARTMENT' module='Employees'}{literal}' );
addToValidate('EditView', 'phone_home', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_HOME_PHONE' module='Employees'}{literal}' );
addToValidate('EditView', 'phone_mobile', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_EMP_PHONE' module='Employees'}{literal}' );
addToValidate('EditView', 'phone_work', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_WORK_PHONE' module='Employees'}{literal}' );
addToValidate('EditView', 'phone_other', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_OTHER_PHONE' module='Employees'}{literal}' );
addToValidate('EditView', 'phone_fax', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_FAX_PHONE' module='Employees'}{literal}' );
addToValidate('EditView', 'status', 'enum', false, '{/literal}{incomCRM_translate label='LBL_STATUS' module='Employees'}{literal}' );
addToValidate('EditView', 'address_street', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_PRIMARY_ADDRESS2' module='Employees'}{literal}' );
addToValidate('EditView', 'address_permanent', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ADDRESS_PERMANENT' module='Employees'}{literal}' );
addToValidate('EditView', 'address_temporary', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ADDRESS_TEMPORARY' module='Employees'}{literal}' );
addToValidate('EditView', 'address_city', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ADDRESS_CITY' module='Employees'}{literal}' );
addToValidate('EditView', 'address_state', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ADDRESS_STATE' module='Employees'}{literal}' );
addToValidate('EditView', 'address_country', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ADDRESS_COUNTRY' module='Employees'}{literal}' );
addToValidate('EditView', 'address_postalcode', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ADDRESS_POSTALCODE' module='Employees'}{literal}' );
addToValidate('EditView', 'user_preferences', 'text', false, '{/literal}{incomCRM_translate label='LBL_USER_PREFERENCES' module='Employees'}{literal}' );
addToValidate('EditView', 'deleted', 'bool', true, '{/literal}{incomCRM_translate label='LBL_DELETED' module='Employees'}{literal}' );
addToValidate('EditView', 'portal_only', 'bool', false, '{/literal}{incomCRM_translate label='LBL_PORTAL_ONLY_USER' module='Employees'}{literal}' );
addToValidate('EditView', 'employee_status', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_EMPLOYEE_STATUS' module='Employees'}{literal}' );
addToValidate('EditView', 'messenger_id', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_MESSENGER_ID' module='Employees'}{literal}' );
addToValidate('EditView', 'messenger_type', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_MESSENGER_TYPE' module='Employees'}{literal}' );
addToValidate('EditView', 'email1', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_EMAIL' module='Employees'}{literal}' );
addToValidate('EditView', 'is_group', 'bool', false, '{/literal}{incomCRM_translate label='LBL_GROUP_USER' module='Employees'}{literal}' );
addToValidate('EditView', 'c_accept_status_fields', 'relate', false, '{/literal}{incomCRM_translate label='LBL_LIST_ACCEPT_STATUS' module='Employees'}{literal}' );
addToValidate('EditView', 'm_accept_status_fields', 'relate', false, '{/literal}{incomCRM_translate label='LBL_LIST_ACCEPT_STATUS' module='Employees'}{literal}' );
addToValidate('EditView', 'accept_status_id', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_LIST_ACCEPT_STATUS' module='Employees'}{literal}' );
addToValidate('EditView', 'accept_status_name', 'enum', false, '{/literal}{incomCRM_translate label='LBL_LIST_ACCEPT_STATUS' module='Employees'}{literal}' );
addToValidate('EditView', 'work_start_date', 'date', false, '{/literal}{incomCRM_translate label='LBL_DATE_START' module='Employees'}{literal}' );
addToValidate('EditView', 'work_end_date', 'date', false, '{/literal}{incomCRM_translate label='LBL_DATE_END' module='Employees'}{literal}' );
addToValidate('EditView', 'gender', 'enum', false, '{/literal}{incomCRM_translate label='LBL_GENDER' module='Employees'}{literal}' );
addToValidate('EditView', 'academic_level', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ACADEMIC_LEVEL' module='Employees'}{literal}' );
addToValidate('EditView', 'specialized', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_SPECIALIZED' module='Employees'}{literal}' );
addToValidate('EditView', 'id_citizen', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ID_CITIZEN' module='Employees'}{literal}' );
addToValidate('EditView', 'issued_date', 'date', false, '{/literal}{incomCRM_translate label='LBL_ISSUED_DATE' module='Employees'}{literal}' );
addToValidate('EditView', 'issued_by', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ISSUED_BY' module='Employees'}{literal}' );
addToValidate('EditView', 'insurance_number', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_INSURANCE_NUMBER' module='Employees'}{literal}' );
addToValidate('EditView', 'insurance_month', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_INSURANCE_MONTH' module='Employees'}{literal}' );
addToValidate('EditView', 'maternity_leave', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_MATERNITY_LEAVE' module='Employees'}{literal}' );
addToValidate('EditView', 'seniority', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_SENIORITY' module='Employees'}{literal}' );
addToValidate('EditView', 'contract_type', 'enum', false, '{/literal}{incomCRM_translate label='LBL_CONTRACT_TYPE' module='Employees'}{literal}' );
addToValidate('EditView', 'contract_number', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_CONTRACT_NUMBER' module='Employees'}{literal}' );
addToValidate('EditView', 'salary', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_SALARY' module='Employees'}{literal}' );
addToValidate('EditView', 'contract_date_start', 'date', false, '{/literal}{incomCRM_translate label='LBL_CONTRACT_DATE_START' module='Employees'}{literal}' );
addToValidate('EditView', 'contract_date_end', 'date', false, '{/literal}{incomCRM_translate label='LBL_CONTRACT_DATE_END' module='Employees'}{literal}' );
addToValidate('EditView', 'remaining_day', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_REMAINING_DAY' module='Employees'}{literal}' );
addToValidate('EditView', 'contract12_number', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_CONTRACT_NUMBER' module='Employees'}{literal}' );
addToValidate('EditView', 'contract12_salary', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_SALARY' module='Employees'}{literal}' );
addToValidate('EditView', 'contract12_date_start', 'date', false, '{/literal}{incomCRM_translate label='LBL_CONTRACT_DATE_START' module='Employees'}{literal}' );
addToValidate('EditView', 'contract12_date_end', 'date', false, '{/literal}{incomCRM_translate label='LBL_CONTRACT_DATE_END' module='Employees'}{literal}' );
addToValidate('EditView', 'appendix_date', 'date', false, '{/literal}{incomCRM_translate label='LBL_APPENDIX_DATE' module='Employees'}{literal}' );
addToValidate('EditView', 'contract_unspecified_number', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_CONTRACT_NUMBER' module='Employees'}{literal}' );
addToValidate('EditView', 'contract_unspecified_salary', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_SALARY' module='Employees'}{literal}' );
addToValidate('EditView', 'contract_unspecified_date_start', 'date', false, '{/literal}{incomCRM_translate label='LBL_CONTRACT_DATE_START' module='Employees'}{literal}' );
addToValidate('EditView', 'contract_unspecified_date_end', 'date', false, '{/literal}{incomCRM_translate label='LBL_CONTRACT_DATE_END' module='Employees'}{literal}' );
addToValidate('EditView', 'contract_unspecified_appendix_date', 'date', false, '{/literal}{incomCRM_translate label='LBL_APPENDIX_DATE' module='Employees'}{literal}' );
addToValidate('EditView', 'appendix_note', 'text', false, '{/literal}{incomCRM_translate label='LBL_APPENDIX_NOTE' module='Employees'}{literal}' );
addToValidate('EditView', 'rowindex', 'int', false, '{/literal}{incomCRM_translate label='LBL_ROWINDEX' module='Employees'}{literal}' );
addToValidate('EditView', 'note', 'text', false, '{/literal}{incomCRM_translate label='LBL_NOTE' module='Employees'}{literal}' );
addToValidate('EditView', 'table_params', 'text', false, '{/literal}{incomCRM_translate label='LBL_TABLE_PARAMS' module='Employees'}{literal}' );
addToValidate('EditView', 'securitygroup_noninher_fields', 'relate', false, '{/literal}{incomCRM_translate label='LBL_USER_NAME' module='Employees'}{literal}' );
addToValidate('EditView', 'securitygroup_noninherit_id', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_securitygroup_noninherit_id' module='Employees'}{literal}' );
addToValidate('EditView', 'securitygroup_noninheritable', 'bool', false, '{/literal}{incomCRM_translate label='LBL_SECURITYGROUP_NONINHERITABLE' module='Employees'}{literal}' );
addToValidate('EditView', 'branch_id', 'enum', true, '{/literal}{incomCRM_translate label='LBL_BRANCH_ID' module='Employees'}{literal}' );
addToValidate('EditView', 'user_code', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_USER_CODE' module='Employees'}{literal}' );
addToValidate('EditView', 'salutation', 'enum', false, '{/literal}{incomCRM_translate label='LBL_SALUTATION' module='Employees'}{literal}' );
addToValidate('EditView', 'teams', 'enum', false, '{/literal}{incomCRM_translate label='LBL_TEAMS' module='Employees'}{literal}' );
addToValidate('EditView', 'hierarchical', 'enum', false, '{/literal}{incomCRM_translate label='LBL_HIERARCHICAL' module='Employees'}{literal}' );
addToValidate('EditView', 'birthdate', 'date', false, '{/literal}{incomCRM_translate label='LBL_BIRTHDATE' module='Employees'}{literal}' );
addToValidate('EditView', 'assign_work', 'text', false, '{/literal}{incomCRM_translate label='LBL_ASSIGN_WORK' module='Employees'}{literal}' );
addToValidate('EditView', 'location_area', 'enum', false, '{/literal}{incomCRM_translate label='LBL_LOCATION_AREA' module='Employees'}{literal}' );
addToValidate('EditView', 'login_on', 'enum', false, '{/literal}{incomCRM_translate label='LBL_LOGIN_ON' module='Employees'}{literal}' );
addToValidate('EditView', 'avartar_image', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_AVARTAR_IMAGE' module='Employees'}{literal}' );
addToValidate('EditView', 'branch_admin', 'bool', false, '{/literal}{incomCRM_translate label='LBL_BRANCH_ADMIN' module='Employees'}{literal}' );
</script>
{/literal}
