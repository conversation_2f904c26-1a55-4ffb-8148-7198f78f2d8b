

{math equation="floor(left / right)"
	left=12
		right=$templateMeta.maxColumns
			assign=colClass
	}

<section class="container2 search-form ml-0 search_form_adv">
	<div class="row">
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="name_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_NAME' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.name_advanced.value) <= 0}
	{assign var="value" value=$fields.name_advanced.default_value }
{else}
	{assign var="value" value=$fields.name_advanced.value }
{/if}
{if isTypeNumber($fields.name_advanced.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.name_advanced.name}' id='{$fields.name_advanced.name}' size='30' maxlength='200' value='{$value}' title='' tabindex=''  /> 

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="address_street_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_ANY_ADDRESS' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.address_street_advanced.value) <= 0}
	{assign var="value" value=$fields.address_street_advanced.default_value }
{else}
	{assign var="value" value=$fields.address_street_advanced.value }
{/if}
{if isTypeNumber($fields.address_street_advanced.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.address_street_advanced.name}' id='{$fields.address_street_advanced.name}' size='30'  value='{$value}' title='' tabindex=''  /> 

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="current_user_only_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_CURRENT_USER_FILTER' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strval($fields.current_user_only_advanced.value) == "1" || strval($fields.current_user_only_advanced.value) == "yes" || strval($fields.current_user_only_advanced.value) == "on"} 
{assign var="checked" value="CHECKED"}
{else}
{assign var="checked" value=""}
{/if}
<input type="hidden" name="{$fields.current_user_only_advanced.name}" value="0" /> 
<input type="checkbox" id="{$fields.current_user_only_advanced.name}" name="{$fields.current_user_only_advanced.name}" value="1" title='' tabindex="" {$checked}  />

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="account_code_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_ACCOUNT_CODE' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.account_code_advanced.value) <= 0}
	{assign var="value" value=$fields.account_code_advanced.default_value }
{else}
	{assign var="value" value=$fields.account_code_advanced.value }
{/if}
{if isTypeNumber($fields.account_code_advanced.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.account_code_advanced.name}' id='{$fields.account_code_advanced.name}' size='30' maxlength='30' value='{$value}' title='' tabindex=''  /> 

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="phone_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_ANY_PHONE' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.phone_advanced.value) <= 0}
	{assign var="value" value=$fields.phone_advanced.default_value }
{else}
	{assign var="value" value=$fields.phone_advanced.value }
{/if}
{if isTypeNumber($fields.phone_advanced.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.phone_advanced.name}' id='{$fields.phone_advanced.name}' size='30'  value='{$value}' title='' tabindex=''  /> 

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="marking_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_MARKING' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{assign var="yes" value=""}
{assign var="no" value=""}
{assign var="default" value=""}

{if strval($fields.marking_advanced.value) == "1"}
	{assign var="yes" value="SELECTED"}
{elseif strval($fields.marking_advanced.value) == "0"}
	{assign var="no" value="SELECTED"}
{else}
	{assign var="default" value="SELECTED"}
{/if}

<select id="{$fields.marking_advanced.name}" name="{$fields.marking_advanced.name}" tabindex="" style="width:80px !important;" >
 <option value="" {$default}></option>
 <option value = "0" {$no}> {$APP.LBL_SEARCH_DROPDOWN_NO}</option>
 <option value = "1" {$yes}> {$APP.LBL_SEARCH_DROPDOWN_YES}</option>
</select>


									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="tax_code_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_TAX_CODE' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.tax_code_advanced.value) <= 0}
	{assign var="value" value=$fields.tax_code_advanced.default_value }
{else}
	{assign var="value" value=$fields.tax_code_advanced.value }
{/if}
{if isTypeNumber($fields.tax_code_advanced.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.tax_code_advanced.name}' id='{$fields.tax_code_advanced.name}' size='30' maxlength='30' value='{$value}' title='' tabindex=''  /> 

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="email_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_ANY_EMAIL' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.email_advanced.value) <= 0}
	{assign var="value" value=$fields.email_advanced.default_value }
{else}
	{assign var="value" value=$fields.email_advanced.value }
{/if}
{if isTypeNumber($fields.email_advanced.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.email_advanced.name}' id='{$fields.email_advanced.name}' size='30'  value='{$value}' title='' tabindex=''  /> 

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="on_incorporation_from_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_ON_INCORPORATION_FROM' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{assign var=date_value value=$fields.on_incorporation_from_advanced.value }
<input autocomplete="off" type="text" name="{$fields.on_incorporation_from_advanced.name}" id="{$fields.on_incorporation_from_advanced.name}" value="{$date_value}" title=''  tabindex='' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.on_incorporation_from_advanced.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.on_incorporation_from_advanced.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.on_incorporation_from_advanced.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="keywords_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_KEYWORDS' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.keywords_advanced.value) <= 0}
	{assign var="value" value=$fields.keywords_advanced.default_value }
{else}
	{assign var="value" value=$fields.keywords_advanced.value }
{/if}
{if isTypeNumber($fields.keywords_advanced.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.keywords_advanced.name}' id='{$fields.keywords_advanced.name}' size='30'  value='{$value}' title='' tabindex=''  /> 

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="website_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_WEBSITE' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.website_advanced.value) <= 0}
	{assign var="value" value=$fields.website_advanced.default_value }
{else}
	{assign var="value" value=$fields.website_advanced.value }
{/if}
{if isTypeNumber($fields.website_advanced.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.website_advanced.name}' id='{$fields.website_advanced.name}' size='30' maxlength='255' value='{$value}' title='' tabindex=''  /> 

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="on_incorporation_to_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_ON_INCORPORATION_TO' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{assign var=date_value value=$fields.on_incorporation_to_advanced.value }
<input autocomplete="off" type="text" name="{$fields.on_incorporation_to_advanced.name}" id="{$fields.on_incorporation_to_advanced.name}" value="{$date_value}" title=''  tabindex='' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.on_incorporation_to_advanced.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.on_incorporation_to_advanced.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.on_incorporation_to_advanced.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="account_type_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_TYPE' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="account_type_advanced[]" id="{$fields.account_type_advanced.name}" size="4" multiple="1"  >
{html_options options=$fields.account_type_advanced.options selected=$fields.account_type_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="department_manager_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_DEPARTMENT_MANAGER' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="department_manager_advanced[]" id="{$fields.department_manager_advanced.name}" size="4" multiple="1"  >
{html_options options=$fields.department_manager_advanced.options selected=$fields.department_manager_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="account_status_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_ACCOUNT_STATUS' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="account_status_advanced[]" id="{$fields.account_status_advanced.name}" size="4" multiple="1"  >
{html_options options=$fields.account_status_advanced.options selected=$fields.account_status_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="industry_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_INDUSTRY' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="industry_advanced[]" id="{$fields.industry_advanced.name}" size="4" multiple="1"  >
{html_options options=$fields.industry_advanced.options selected=$fields.industry_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="account_scope_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_ACCOUNT_SCOPE' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="account_scope_advanced[]" id="{$fields.account_scope_advanced.name}" size="4" multiple="1"  >
{html_options options=$fields.account_scope_advanced.options selected=$fields.account_scope_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="location_area_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_LOCATION_AREA' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="location_area_advanced[]" id="{$fields.location_area_advanced.name}" size="4" multiple="1"  >
{html_options options=$fields.location_area_advanced.options selected=$fields.location_area_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="transaction_level_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_TRANSACTION_LEVEL' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="transaction_level_advanced[]" id="{$fields.transaction_level_advanced.name}" size="4" multiple="1"  >
{html_options options=$fields.transaction_level_advanced.options selected=$fields.transaction_level_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="periodic_purchase_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_PERIODIC_PURCHASE' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="periodic_purchase_advanced[]" id="{$fields.periodic_purchase_advanced.name}" size="4" multiple="1"  >
{html_options options=$fields.periodic_purchase_advanced.options selected=$fields.periodic_purchase_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="location_city_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_LOCATION_CITY' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="location_city_advanced[]" id="{$fields.location_city_advanced.name}" size="4" multiple="1"  onchange="changeParentSelectedOption(this, this.form.location_district_advanced, 'location_district_dom')" >
{html_options options=$fields.location_city_advanced.options selected=$fields.location_city_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="account_warn_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_ACCOUNT_WARN' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="account_warn_advanced[]" id="{$fields.account_warn_advanced.name}" size="4" multiple="1"  >
{html_options options=$fields.account_warn_advanced.options selected=$fields.account_warn_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="sector_vertical_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_SECTOR_VERTICAL' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="sector_vertical_advanced[]" id="{$fields.sector_vertical_advanced.name}" size="4" multiple="1"  >
{html_options options=$fields.sector_vertical_advanced.options selected=$fields.sector_vertical_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="location_district_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_LOCATION_DISTRICT' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="location_district_advanced[]" id="{$fields.location_district_advanced.name}" size="4" multiple="1"  >
{html_options options=$fields.location_district_advanced.options selected=$fields.location_district_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="lead_source_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_LEAD_SOURCE' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="lead_source_advanced[]" id="{$fields.lead_source_advanced.name}" size="5" multiple="1"  >
{html_options options=$fields.lead_source_advanced.options selected=$fields.lead_source_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="branch_id_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_BRANCH_ID' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="branch_id_advanced[]" id="{$fields.branch_id_advanced.name}" size="4" multiple="1"  onchange="changeBranchDepartments(this, this.form.department_id_advanced)" >
{html_options options=$fields.branch_id_advanced.options selected=$fields.branch_id_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="department_id_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_DEPARTMENT_ID' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="department_id_advanced[]" id="{$fields.department_id_advanced.name}" size="5" multiple="1"  onchange="changeDepartmentUsers(this, this.form.assigned_user_id_advanced)" >
{html_options options=$fields.department_id_advanced.options selected=$fields.department_id_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="care_level_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_CARE_LEVEL' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="care_level_advanced[]" id="{$fields.care_level_advanced.name}" size="4" multiple="1"  >
{html_options options=$fields.care_level_advanced.options selected=$fields.care_level_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="assigned_user_id_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_ASSIGNED_TO' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="assigned_user_id_advanced[]" id="{$fields.assigned_user_id_advanced.name}" size="5" multiple="1" Array >
{html_options options=$fields.assigned_user_id_advanced.options selected=$fields.assigned_user_id_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="user_shared_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='Nv được chia sẻ' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="user_shared_advanced[]" id="{$fields.user_shared_advanced.name}" size="5" multiple="1" Array >
{html_options options=$fields.user_shared_advanced.options selected=$fields.user_shared_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="date_entered_from_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_DATE_ENTERED_FROM' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{assign var=date_value value=$fields.date_entered_from_advanced.value }
<input autocomplete="off" type="text" name="{$fields.date_entered_from_advanced.name}" id="{$fields.date_entered_from_advanced.name}" value="{$date_value}" title=''  tabindex='' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.date_entered_from_advanced.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.date_entered_from_advanced.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.date_entered_from_advanced.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="date_modified_from_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_DATE_MODIFIED_FROM' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{assign var=date_value value=$fields.date_modified_from_advanced.value }
<input autocomplete="off" type="text" name="{$fields.date_modified_from_advanced.name}" id="{$fields.date_modified_from_advanced.name}" value="{$date_value}" title=''  tabindex='' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.date_modified_from_advanced.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.date_modified_from_advanced.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.date_modified_from_advanced.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 hide-md">
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="date_entered_to_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_RPT_DATE_TO' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{assign var=date_value value=$fields.date_entered_to_advanced.value }
<input autocomplete="off" type="text" name="{$fields.date_entered_to_advanced.name}" id="{$fields.date_entered_to_advanced.name}" value="{$date_value}" title=''  tabindex='' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.date_entered_to_advanced.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.date_entered_to_advanced.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.date_entered_to_advanced.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="date_modified_to_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_RPT_DATE_TO' module='Suppliers'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{assign var=date_value value=$fields.date_modified_to_advanced.value }
<input autocomplete="off" type="text" name="{$fields.date_modified_to_advanced.name}" id="{$fields.date_modified_to_advanced.name}" value="{$date_value}" title=''  tabindex='' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.date_modified_to_advanced.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.date_modified_to_advanced.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.date_modified_to_advanced.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 hide-md">
				</div>
	</div>
</section>

{if $HAS_ADVANCED_SEARCH}
<table cellspacing="0" cellpadding="0" border="0" class="mt-10">
<tr>
	<td scope="row" nowrap="nowrap" class="pt-10">
		<a style="font-size:10pt; text-decoration:none;" onclick="incomCRM.searchForm.searchFormSelect('{$module}|basic_search','{$module}|advanced_search')" href="#">[ {$APP.LNK_BASIC_SEARCH} ]</a>
	</td>
</tr>
<tr>
	<td>&nbsp;</td>
</tr>
</table>
{/if}

{if $DISPLAY_SAVED_SEARCH}
<div class="saved-search-adv table-responsive mt-10 pt-10">
<table cellspacing="0" cellpadding="0" border="0">
<tr>
	<td rowspan="2" width="40%">
		<a class='tabFormAdvLink' onhover href='javascript:toggleInlineSearch()'>
		<img src='{incomCRM_getimagepath file="advanced_search.gif"}' id='up_down_img' border="0" />&nbsp;{$APP.LNK_SAVED_VIEWS}
		</a><br/>
		<input type='hidden' id='showSSDIV' name='showSSDIV' value='{$SHOWSSDIV}' />
	</td>
	<td scope='row' width="20%" nowrap="nowrap">
		{incomCRM_translate label='LBL_SAVE_SEARCH_AS' module='SavedSearch'}:
	</td>
	<td nowrap>
		<input type='text' name='saved_search_name' />
		<input type='hidden' name='search_module' value='' />
		<input type='hidden' name='saved_search_action' value='' />
		<input title='{$APP.LBL_SAVE_BUTTON_LABEL}' value='{$APP.LBL_SAVE_BUTTON_LABEL}' class='button' type='button' name='saved_search_submit' onclick='incomCRM.savedViews.setChooser(); return incomCRM.savedViews.saved_search_action("save");' />
	</td>
</tr>
<tr>
	<td scope='row' nowrap="nowrap">
		{incomCRM_translate label='LBL_MODIFY_CURRENT_SEARCH' module='SavedSearch'}:
	</td>
	<td nowrap>
		<input class='button' onclick='incomCRM.savedViews.setChooser(); return incomCRM.savedViews.saved_search_action("update")' value='{$APP.LBL_UPDATE}' title='{$APP.LBL_UPDATE}' name='ss_update' id='ss_update' type='button' />
		<input class='button' onclick='return incomCRM.savedViews.saved_search_action("delete", "{incomCRM_translate label='LBL_DELETE_CONFIRM' module='SavedSearch'}")' value='{$APP.LBL_DELETE}' title='{$APP.LBL_DELETE}' name='ss_delete' id='ss_delete' type='button' />
		<br/><span id='curr_search_name'></span>
	</td>
</tr>
<tr>
	<td colspan="3">
		<div style="{$DISPLAYSS}" id="inlineSavedSearch">{$SAVED_SEARCH}</div>
	</td>
</tr>
</table>
</div>
{/if}

<script type="text/javascript">
{literal}
if( typeof(loadSSL_Scripts) == 'function' ) {
	loadSSL_Scripts();
}
YAHOO.util.Event.onDOMReady(function(){
	var form = null;
	if( document.search_form ) form = document.search_form;
	else if( document.popup_query_form ) form = document.popup_query_form;
	else return;
	if( form ) {
		if( form.branch_id_advanced ) {
			if( form.department_id_advanced )
				changeBranchDepartments(form.branch_id_advanced, form.department_id_advanced);
			else if( form.department_advanced )
				changeBranchDepartments(form.branch_id_advanced, form.department_advanced);
		}
		if( form.department_id_advanced && form.assigned_user_id_advanced )
			changeDepartmentUsers(form.department_id_advanced, form.assigned_user_id_advanced);
		if( form.location_district_advanced && form.location_city_advanced )
			changeParentSelectedOption(form.location_city_advanced, form.location_district_advanced, 'location_district_dom');
	}
});
{/literal}
</script>

{literal}<script type="text/javascript">if(typeof sqs_objects == 'undefined'){var sqs_objects = new Array;}
sqs_objects['search_form_modified_by_name_advanced'] = {"form":"search_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["modified_by_name_advanced","modified_user_id_advanced"],"required_list":["modified_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['search_form_created_by_name_advanced'] = {"form":"search_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["created_by_name_advanced","created_by_advanced"],"required_list":["created_by"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['search_form_assigned_user_name_advanced'] = {"form":"search_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["assigned_user_name_advanced","assigned_user_id_advanced"],"required_list":["assigned_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['search_form_parent_name_advanced'] = {"form":"search_form","method":"query","modules":["Accounts"],"group":"or","field_list":["name","id"],"populate_list":["search_form_parent_name_advanced","parent_id_advanced"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"required_list":["parent_id"],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['search_form_campaign_name_advanced'] = {"form":"search_form","method":"query","modules":["Campaigns"],"group":"or","field_list":["name","id"],"populate_list":["campaign_name_advanced","campaign_id_advanced"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"required_list":["campaign_id"],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['search_form_account_group_name_advanced'] = {"form":"search_form","method":"query","modules":["Account_Groups"],"group":"or","field_list":["name","id"],"populate_list":["account_group_name_advanced","account_group_id_advanced"],"required_list":["parent_id"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['search_form_sales_line_name_advanced'] = {"form":"search_form","method":"query","modules":["Sales_Lines"],"group":"or","field_list":["name","id"],"populate_list":["sales_line_name_advanced","sales_line_id_advanced"],"required_list":["parent_id"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['search_form_primary_contact_name_advanced'] = {"form":"search_form","method":"get_contact_array","modules":["Contacts"],"field_list":["salutation","first_name","last_name","id"],"populate_list":["primary_contact_name_advanced","primary_contact_id_advanced","primary_contact_id_advanced","primary_contact_id_advanced"],"required_list":["primary_contact_id"],"group":"or","conditions":[{"name":"first_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"last_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['search_form_secondary_contact_name_advanced'] = {"form":"search_form","method":"get_contact_array","modules":["Contacts"],"field_list":["salutation","first_name","last_name","id"],"populate_list":["secondary_contact_name_advanced","secondary_contact_id_advanced","secondary_contact_id_advanced","secondary_contact_id_advanced"],"required_list":["secondary_contact_id"],"group":"or","conditions":[{"name":"first_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"last_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['search_form_debt_user_name_advanced'] = {"form":"search_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["debt_user_name_advanced","debt_user_id_advanced"],"required_list":["debt_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['search_form_support_user_name_advanced'] = {"form":"search_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["support_user_name_advanced","support_user_id_advanced"],"required_list":["support_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
</script>{/literal}