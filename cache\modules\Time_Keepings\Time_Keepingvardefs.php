<?php
// created: 2025-02-26 15:20:02
$<PERSON><PERSON><PERSON><PERSON><PERSON>["dictionary"]["Time_Keeping"] = array (
  'table' => 'time_keepings',
  'audited' => true,
  'unified_search' => false,
  'duplicate_merge' => false,
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'varchar',
      'len' => 50,
      'acl' => true,
      'audited' => false,
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'time_keepings_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'time_keepings_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'branch_id' => 
    array (
      'name' => 'branch_id',
      'vname' => 'LBL_BRANCH_ID',
      'type' => 'enum',
      'options' => 'users_branch_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'BranchID of assigned user',
    ),
    'department_id' => 
    array (
      'name' => 'department_id',
      'vname' => 'LBL_DEPARTMENT_ID',
      'type' => 'enum',
      'options' => 'users_department_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'DepartmentID of assigned user',
    ),
    'assigned_user_id' => 
    array (
      'name' => 'assigned_user_id',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'vname' => 'LBL_ASSIGNED_TO_ID',
      'group' => 'assigned_user_name',
      'type' => 'relate',
      'table' => 'users',
      'module' => 'Users',
      'isnull' => 'false',
      'dbType' => 'id',
      'audited' => true,
      'reportable' => true,
      'duplicate_merge' => 'disabled',
      'comment' => 'User ID assigned to record',
      'massupdate' => false,
    ),
    'assigned_user_name' => 
    array (
      'name' => 'assigned_user_name',
      'link' => 'assigned_user_link',
      'vname' => 'LBL_ASSIGNED_TO_NAME',
      'rname' => 'user_name',
      'type' => 'relate',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'assigned_user_id',
      'module' => 'Users',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
      'massupdate' => false,
    ),
    'assigned_user_link' => 
    array (
      'name' => 'assigned_user_link',
      'type' => 'link',
      'relationship' => 'time_keepings_assigned_user',
      'vname' => 'LBL_ASSIGNED_TO_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
      'duplicate_merge' => 'enabled',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'table' => 'users',
    ),
    'date_perform' => 
    array (
      'name' => 'date_perform',
      'vname' => 'LBL_DATE_PERFORM',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
      'display_default' => 'now',
    ),
    'morning' => 
    array (
      'name' => 'morning',
      'vname' => 'LBL_MORNING',
      'type' => 'enum',
      'options' => 'time_keepings_status_dom',
      'len' => 2,
      'audited' => true,
      'massupdate' => false,
    ),
    'afternoon' => 
    array (
      'name' => 'afternoon',
      'vname' => 'LBL_AFTERNOON',
      'type' => 'enum',
      'options' => 'time_keepings_status_dom',
      'len' => 2,
      'audited' => true,
      'massupdate' => false,
    ),
    'work_time' => 
    array (
      'name' => 'work_time',
      'vname' => 'LBL_WORK_TIME',
      'type' => 'float',
      'audited' => true,
      'massupdate' => false,
    ),
    'handle_user_id' => 
    array (
      'name' => 'handle_user_id',
      'vname' => 'LBL_HANDLE_USER_ID',
      'type' => 'id',
      'audited' => true,
      'massupdate' => false,
    ),
    'handle_user_name' => 
    array (
      'name' => 'handle_user_name',
      'rname' => 'user_name',
      'id_name' => 'handle_user_id',
      'vname' => 'LBL_HANDLE_USER_NAME',
      'table' => 'users',
      'join_name' => 'handle_users',
      'type' => 'relate',
      'link' => 'handle_users',
      'module' => 'Users',
      'source' => 'non-db',
      'massupdate' => false,
    ),
    'handle_users' => 
    array (
      'name' => 'handle_users',
      'vname' => 'LBL_HANDLE_USERS',
      'type' => 'link',
      'source' => 'non-db',
      'relationship' => 'handle_users_time_keepings',
    ),
    'check_in' => 
    array (
      'name' => 'check_in',
      'vname' => 'LBL_CHECK_IN',
      'type' => 'char',
      'len' => 5,
      'source' => 'non-db',
      'massupdate' => false,
      'importable' => 'false',
    ),
    'check_out' => 
    array (
      'name' => 'check_out',
      'vname' => 'LBL_CHECK_OUT',
      'type' => 'char',
      'len' => 5,
      'source' => 'non-db',
      'massupdate' => false,
      'importable' => 'false',
    ),
    'SecurityGroups' => 
    array (
      'name' => 'SecurityGroups',
      'type' => 'link',
      'relationship' => 'securitygroups_time_keepings',
      'module' => 'SecurityGroups',
      'bean_name' => 'SecurityGroup',
      'source' => 'non-db',
      'vname' => 'LBL_SECURITYGROUPS',
    ),
  ),
  'indices' => 
  array (
    'id' => 
    array (
      'name' => 'time_keepingspk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    'idx_time_keepings_branch_id' => 
    array (
      'name' => 'idx_time_keepings_branch_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
      ),
    ),
    'idx_time_keepings_department_id' => 
    array (
      'name' => 'idx_time_keepings_department_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'department_id',
      ),
    ),
    'idx_time_keepings_branch_dept' => 
    array (
      'name' => 'idx_time_keepings_branch_dept',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
        1 => 'department_id',
      ),
    ),
    'idx_time_keepings_assigned' => 
    array (
      'name' => 'idx_time_keepings_assigned',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'assigned_user_id',
      ),
    ),
    0 => 
    array (
      'name' => 'idx_time_keep_id_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'id',
        1 => 'deleted',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_time_keep_date',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'date_perform',
      ),
    ),
    2 => 
    array (
      'name' => 'idx_time_keep_morning',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'morning',
      ),
    ),
    3 => 
    array (
      'name' => 'idx_time_keep_afternoon',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'afternoon',
      ),
    ),
    4 => 
    array (
      'name' => 'idx_time_keep_usr_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'handle_user_id',
      ),
    ),
  ),
  'relationships' => 
  array (
    'time_keepings_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Time_Keepings',
      'rhs_table' => 'time_keepings',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'time_keepings_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Time_Keepings',
      'rhs_table' => 'time_keepings',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
    'time_keepings_assigned_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Time_Keepings',
      'rhs_table' => 'time_keepings',
      'rhs_key' => 'assigned_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'handle_users_time_keepings' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Time_Keepings',
      'rhs_table' => 'time_keepings',
      'rhs_key' => 'handle_user_id',
      'relationship_type' => 'one-to-many',
    ),
  ),
  'optimistic_lock' => true,
  'templates' => 
  array (
    'assignable' => 'assignable',
    'groups' => 'groups',
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
