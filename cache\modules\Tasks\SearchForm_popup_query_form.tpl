

{math equation="floor(left / right)"
	left=12
		right=$templateMeta.maxColumns
			assign=colClass
	}

<section class="container2 search-form ml-0 search_form_adv">
	<div class="row">
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="subject_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_SUBJECT' module='Tasks'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.subject_advanced.value) <= 0}
	{assign var="value" value=$fields.subject_advanced.default_value }
{else}
	{assign var="value" value=$fields.subject_advanced.value }
{/if}
{if isTypeNumber($fields.subject_advanced.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.subject_advanced.name}' id='{$fields.subject_advanced.name}' size='30' maxlength='250' value='{$value}' title='' tabindex=''  /> 

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="asap_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_ASAP' module='Tasks'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="asap_advanced[]" id="{$fields.asap_advanced.name}" size="1"   >
{html_options options=$fields.asap_advanced.options selected=$fields.asap_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="date_start_from_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_DATE_START_FORM' module='Tasks'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{assign var=date_value value=$fields.date_start_from_advanced.value }
<input autocomplete="off" type="text" name="{$fields.date_start_from_advanced.name}" id="{$fields.date_start_from_advanced.name}" value="{$date_value}" title=''  tabindex='' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.date_start_from_advanced.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.date_start_from_advanced.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.date_start_from_advanced.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="account_name_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_ACCOUNTS_NAME' module='Tasks'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.account_name_advanced.value) <= 0}
	{assign var="value" value=$fields.account_name_advanced.default_value }
{else}
	{assign var="value" value=$fields.account_name_advanced.value }
{/if}
{if isTypeNumber($fields.account_name_advanced.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.account_name_advanced.name}' id='{$fields.account_name_advanced.name}' size='30'  value='{$value}' title='' tabindex=''  /> 

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="content_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_CONTENT' module='Tasks'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.content_advanced.value) <= 0}
	{assign var="value" value=$fields.content_advanced.default_value }
{else}
	{assign var="value" value=$fields.content_advanced.value }
{/if}
{if isTypeNumber($fields.content_advanced.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.content_advanced.name}' id='{$fields.content_advanced.name}' size='30'  value='{$value}' title='' tabindex=''  /> 

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="date_start_to_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_RPT_DATE_TO' module='Tasks'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{assign var=date_value value=$fields.date_start_to_advanced.value }
<input autocomplete="off" type="text" name="{$fields.date_start_to_advanced.name}" id="{$fields.date_start_to_advanced.name}" value="{$date_value}" title=''  tabindex='' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.date_start_to_advanced.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.date_start_to_advanced.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.date_start_to_advanced.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="status_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_STATUS' module='Tasks'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="status_advanced[]" id="{$fields.status_advanced.name}" size="4" multiple="1"  >
{html_options options=$fields.status_advanced.options selected=$fields.status_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="transactions_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_TRANSACTIONS' module='Tasks'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="transactions_advanced[]" id="{$fields.transactions_advanced.name}" size="4" multiple="1"  >
{html_options options=$fields.transactions_advanced.options selected=$fields.transactions_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="communication_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_COMMUNICATION' module='Tasks'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="communication_advanced[]" id="{$fields.communication_advanced.name}" size="4" multiple="1"  >
{html_options options=$fields.communication_advanced.options selected=$fields.communication_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="branch_id_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_BRANCH_ID' module='Tasks'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="branch_id_advanced[]" id="{$fields.branch_id_advanced.name}" size="4" multiple="1"  onchange="changeBranchDepartments(this, this.form.department_id_advanced)" >
{html_options options=$fields.branch_id_advanced.options selected=$fields.branch_id_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="department_id_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_DEPARTMENT_ID' module='Tasks'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="department_id_advanced[]" id="{$fields.department_id_advanced.name}" size="4" multiple="1"  onchange="changeDepartmentUsers(this, this.form.assigned_user_id_advanced)" >
{html_options options=$fields.department_id_advanced.options selected=$fields.department_id_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="assigned_user_id_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='Tasks'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="assigned_user_id_advanced[]" id="{$fields.assigned_user_id_advanced.name}" size="4" multiple="1"  >
{html_options options=$fields.assigned_user_id_advanced.options selected=$fields.assigned_user_id_advanced.value}
</select>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="date_entered_from_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_DATE_ENTERED_FROM' module='Tasks'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{assign var=date_value value=$fields.date_entered_from_advanced.value }
<input autocomplete="off" type="text" name="{$fields.date_entered_from_advanced.name}" id="{$fields.date_entered_from_advanced.name}" value="{$date_value}" title=''  tabindex='' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.date_entered_from_advanced.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.date_entered_from_advanced.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.date_entered_from_advanced.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>

									</div>
			</div>
				</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12 ">
					<div class="row">
				<label class="col-5 pr-0" for="date_entered_to_advanced">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_RPT_DATE_TO' module='Tasks'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{assign var=date_value value=$fields.date_entered_to_advanced.value }
<input autocomplete="off" type="text" name="{$fields.date_entered_to_advanced.name}" id="{$fields.date_entered_to_advanced.name}" value="{$date_value}" title=''  tabindex='' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.date_entered_to_advanced.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.date_entered_to_advanced.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.date_entered_to_advanced.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>

									</div>
			</div>
				</div>
	</div>
</section>

{if $HAS_ADVANCED_SEARCH}
<table cellspacing="0" cellpadding="0" border="0" class="mt-10">
<tr>
	<td scope="row" nowrap="nowrap" class="pt-10">
		<a style="font-size:10pt; text-decoration:none;" onclick="incomCRM.searchForm.searchFormSelect('{$module}|basic_search','{$module}|advanced_search')" href="#">[ {$APP.LNK_BASIC_SEARCH} ]</a>
	</td>
</tr>
<tr>
	<td>&nbsp;</td>
</tr>
</table>
{/if}

{if $DISPLAY_SAVED_SEARCH}
<div class="saved-search-adv table-responsive mt-10 pt-10">
<table cellspacing="0" cellpadding="0" border="0">
<tr>
	<td rowspan="2" width="40%">
		<a class='tabFormAdvLink' onhover href='javascript:toggleInlineSearch()'>
		<img src='{incomCRM_getimagepath file="advanced_search.gif"}' id='up_down_img' border="0" />&nbsp;{$APP.LNK_SAVED_VIEWS}
		</a><br/>
		<input type='hidden' id='showSSDIV' name='showSSDIV' value='{$SHOWSSDIV}' />
	</td>
	<td scope='row' width="20%" nowrap="nowrap">
		{incomCRM_translate label='LBL_SAVE_SEARCH_AS' module='SavedSearch'}:
	</td>
	<td nowrap>
		<input type='text' name='saved_search_name' />
		<input type='hidden' name='search_module' value='' />
		<input type='hidden' name='saved_search_action' value='' />
		<input title='{$APP.LBL_SAVE_BUTTON_LABEL}' value='{$APP.LBL_SAVE_BUTTON_LABEL}' class='button' type='button' name='saved_search_submit' onclick='incomCRM.savedViews.setChooser(); return incomCRM.savedViews.saved_search_action("save");' />
	</td>
</tr>
<tr>
	<td scope='row' nowrap="nowrap">
		{incomCRM_translate label='LBL_MODIFY_CURRENT_SEARCH' module='SavedSearch'}:
	</td>
	<td nowrap>
		<input class='button' onclick='incomCRM.savedViews.setChooser(); return incomCRM.savedViews.saved_search_action("update")' value='{$APP.LBL_UPDATE}' title='{$APP.LBL_UPDATE}' name='ss_update' id='ss_update' type='button' />
		<input class='button' onclick='return incomCRM.savedViews.saved_search_action("delete", "{incomCRM_translate label='LBL_DELETE_CONFIRM' module='SavedSearch'}")' value='{$APP.LBL_DELETE}' title='{$APP.LBL_DELETE}' name='ss_delete' id='ss_delete' type='button' />
		<br/><span id='curr_search_name'></span>
	</td>
</tr>
<tr>
	<td colspan="3">
		<div style="{$DISPLAYSS}" id="inlineSavedSearch">{$SAVED_SEARCH}</div>
	</td>
</tr>
</table>
</div>
{/if}

<script type="text/javascript">
{literal}
if( typeof(loadSSL_Scripts) == 'function' ) {
	loadSSL_Scripts();
}
YAHOO.util.Event.onDOMReady(function(){
	var form = null;
	if( document.search_form ) form = document.search_form;
	else if( document.popup_query_form ) form = document.popup_query_form;
	else return;
	if( form ) {
		if( form.branch_id_advanced ) {
			if( form.department_id_advanced )
				changeBranchDepartments(form.branch_id_advanced, form.department_id_advanced);
			else if( form.department_advanced )
				changeBranchDepartments(form.branch_id_advanced, form.department_advanced);
		}
		if( form.department_id_advanced && form.assigned_user_id_advanced )
			changeDepartmentUsers(form.department_id_advanced, form.assigned_user_id_advanced);
		if( form.location_district_advanced && form.location_city_advanced )
			changeParentSelectedOption(form.location_city_advanced, form.location_district_advanced, 'location_district_dom');
	}
});
{/literal}
</script>

{literal}<script type="text/javascript">if(typeof sqs_objects == 'undefined'){var sqs_objects = new Array;}
sqs_objects['popup_query_form_modified_by_name_advanced'] = {"form":"popup_query_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["modified_by_name_advanced","modified_user_id_advanced"],"required_list":["modified_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['popup_query_form_created_by_name_advanced'] = {"form":"popup_query_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["created_by_name_advanced","created_by_advanced"],"required_list":["created_by"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['popup_query_form_assigned_user_name_advanced'] = {"form":"popup_query_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["assigned_user_name_advanced","assigned_user_id_advanced"],"required_list":["assigned_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['popup_query_form_parent_name_advanced'] = {"form":"popup_query_form","method":"query","modules":["Accounts"],"group":"or","field_list":["name","id"],"populate_list":["parent_name_advanced","parent_id_advanced"],"required_list":["parent_id"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['popup_query_form_contact_name_advanced'] = {"form":"popup_query_form","method":"get_contact_array","modules":["Contacts"],"field_list":["salutation","first_name","last_name","id"],"populate_list":["contact_name_advanced","contact_id_advanced","contact_id_advanced","contact_id_advanced"],"required_list":["contact_id"],"group":"or","conditions":[{"name":"first_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"last_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['popup_query_form_user_err_name_advanced'] = {"form":"popup_query_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["user_err_name_advanced","user_err_id_advanced"],"required_list":["user_err_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['popup_query_form_task_relate_name_advanced'] = {"form":"popup_query_form","method":"query","modules":["Tasks"],"group":"or","field_list":["name","id"],"populate_list":["task_relate_name_advanced","task_relate_id_advanced"],"required_list":["parent_id"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['popup_query_form_main_name_advanced'] = {"form":"popup_query_form","method":"query","modules":["Tasks"],"group":"or","field_list":["name","id"],"populate_list":["main_name_advanced","main_id_advanced"],"required_list":["parent_id"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['popup_query_form_opportunity_name_advanced'] = {"form":"popup_query_form","method":"query","modules":["Opportunities"],"group":"or","field_list":["name","id"],"populate_list":["opportunity_name_advanced","opportunity_id_advanced"],"required_list":["parent_id"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['popup_query_form_group_name_advanced'] = {"form":"popup_query_form","method":"query","modules":["Tasks_Groups"],"group":"or","field_list":["name","id"],"populate_list":["group_name_advanced","group_id_advanced"],"required_list":["parent_id"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['popup_query_form_forward_user_name_advanced'] = {"form":"popup_query_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["forward_user_name_advanced","forward_user_id_advanced"],"required_list":["forward_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['popup_query_form_approval_level_name_advanced'] = {"form":"popup_query_form","method":"query","modules":["Approval_Levels"],"group":"or","field_list":["name","id"],"populate_list":["approval_level_name_advanced","approval_level_id_advanced"],"required_list":["parent_id"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
</script>{/literal}