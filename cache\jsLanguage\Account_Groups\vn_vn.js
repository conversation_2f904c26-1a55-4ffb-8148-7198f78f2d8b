incomCRM.language.setLanguage('Account_Groups', {"LBL_ID":"ID","LBL_DATE_ENTERED":"Ng\u00e0y t\u1ea1o","LBL_DATE_MODIFIED":"Ng\u00e0y c\u1eadp nh\u1eadt","LBL_MODIFIED":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_MODIFIED_ID":"ID ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_MODIFIED_NAME":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_CREATED":"Ng\u01b0\u1eddi t\u1ea1o","LBL_CREATED_ID":"ID ng\u01b0\u1eddi t\u1ea1o","LBL_DESCRIPTION":"<PERSON>hi ch\u00fa","LBL_DELETED":"\u0110\u00e3 x\u00f3a?","LBL_NAME":"T\u00ean nh\u00f3m","LBL_CREATED_USER":"Ng\u01b0\u1eddi t\u1ea1o","LBL_MODIFIED_USER":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_ASSIGNED_TO":"Nv.Ql\u00fd","LBL_ASSIGNED_TO_ID":"ID Nv.Ql\u00fd","LBL_ASSIGNED_TO_NAME":"Nv.Ql\u00fd","LBL_ASSIGNED_TO_USER":"Nv.Ql\u00fd","LBL_ASSIGNED_USER":"Nv.Ql\u00fd","LBL_MODULE_NAME":"Nh\u00f3m kh\u00e1ch h\u00e0ng","LBL_MODULE_TITLE":"Nh\u00f3m kh\u00e1ch h\u00e0ng: Trang ch\u1ee7","LNK_LIST":"Danh s\u00e1ch","LNK_NEW_RECORD":"Th\u00eam m\u1edbi","LBL_LIST_FORM_TITLE":"Danh s\u00e1ch nh\u00f3m kh\u00e1ch h\u00e0ng","LBL_SEARCH_FORM_TITLE":"T\u00ecm ki\u1ebfm","LBL_CODE":"M\u00e3 nh\u00f3m","LBL_STATUS":"T\u00ecnh tr\u1ea1ng","LBL_ENTRY_COUNT":"T\u1ed5ng s\u1ed1 KH","LBL_ACCOUNTS":"Danh s\u00e1ch kh\u00e1ch h\u00e0ng","LBL_ERR_DUPLICATE":"%s [%s] b\u1ea1n nh\u1eadp \u0111\u00e3 t\u1ed3n t\u1ea1i, vui l\u00f2ng nh\u1eadp l\u1ea1i."});