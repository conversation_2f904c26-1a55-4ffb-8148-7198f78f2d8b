<?php
// created: 2025-02-26 15:11:49
$<PERSON><PERSON><PERSON><PERSON><PERSON>["dictionary"]["TQT_Announcement"] = array (
  'table' => 'tqt_announcement',
  'audited' => true,
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'name',
      'dbType' => 'varchar',
      'len' => 255,
      'audited' => true,
      'required' => true,
      'unified_search' => true,
      'merge_filter' => 'selected',
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'html',
      'audited' => true,
      'comment' => 'Full text of the note',
      'dbType' => 'text',
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'tqt_announcement_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'tqt_announcement_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'branch_id' => 
    array (
      'name' => 'branch_id',
      'vname' => 'LBL_BRANCH_ID',
      'type' => 'enum',
      'options' => 'users_branch_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'BranchID of assigned user',
    ),
    'department_id' => 
    array (
      'name' => 'department_id',
      'vname' => 'LBL_DEPARTMENT_ID',
      'type' => 'enum',
      'options' => 'users_department_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'DepartmentID of assigned user',
    ),
    'assigned_user_id' => 
    array (
      'name' => 'assigned_user_id',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'vname' => 'LBL_ASSIGNED_TO_ID',
      'group' => 'assigned_user_name',
      'type' => 'relate',
      'table' => 'users',
      'module' => 'Users',
      'isnull' => 'false',
      'dbType' => 'id',
      'audited' => true,
      'reportable' => true,
      'duplicate_merge' => 'disabled',
      'comment' => 'User ID assigned to record',
    ),
    'assigned_user_name' => 
    array (
      'name' => 'assigned_user_name',
      'link' => 'assigned_user_link',
      'vname' => 'LBL_ASSIGNED_TO_NAME',
      'rname' => 'user_name',
      'type' => 'relate',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'assigned_user_id',
      'module' => 'Users',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'assigned_user_link' => 
    array (
      'name' => 'assigned_user_link',
      'type' => 'link',
      'relationship' => 'tqt_announcement_assigned_user',
      'vname' => 'LBL_ASSIGNED_TO_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
      'duplicate_merge' => 'enabled',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'table' => 'users',
    ),
    'status' => 
    array (
      'name' => 'status',
      'vname' => 'LBL_STATUS',
      'type' => 'enum',
      'options' => 'announcement_status_list',
      'len' => 30,
      'audited' => true,
      'massupdate' => true,
      'display_default' => 'Applicable',
    ),
    'promulgation_date' => 
    array (
      'required' => false,
      'name' => 'promulgation_date',
      'vname' => 'LBL_PROMULGATION_DATE',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
      'display_default' => 'now',
    ),
    'effective_date' => 
    array (
      'name' => 'effective_date',
      'vname' => 'LBL_EFFECTIVE_DATE',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
      'display_default' => 'now',
    ),
    'expired_date' => 
    array (
      'name' => 'expired_date',
      'vname' => 'LBL_EXPIRED_DATE',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'note' => 
    array (
      'name' => 'note',
      'vname' => 'LBL_NOTE',
      'type' => 'text',
    ),
    'department_all' => 
    array (
      'name' => 'department_all',
      'vname' => 'LBL_DEPARTMENT_ALL',
      'type' => 'enum',
      'options' => 'department_all_dom',
      'dbType' => 'text',
      'isMultiSelect' => true,
      'isCheckBoxList' => true,
      'audited' => true,
      'massupdate' => true,
      'unified_search' => false,
      'reportable' => true,
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
    ),
    'teams_all' => 
    array (
      'name' => 'teams_all',
      'vname' => 'LBL_TEAMS_ALL',
      'type' => 'enum',
      'dbType' => 'text',
      'options' => 'users_teams_dom',
      'audited' => true,
      'massupdate' => false,
      'isMultiSelect' => true,
      'isCheckBoxList' => true,
    ),
    'users_all' => 
    array (
      'name' => 'users_all',
      'vname' => 'LBL_USERS_ALL',
      'type' => 'enum',
      'dbType' => 'text',
      'function' => 'getAllUserOptions',
      'audited' => true,
      'massupdate' => false,
      'isMultiSelect' => true,
      'isCheckBoxList' => true,
    ),
    'filename' => 
    array (
      'name' => 'filename',
      'vname' => 'LBL_FILENAME',
      'type' => 'varchar',
      'len' => '255',
      'reportable' => true,
      'comment' => 'File name associated with the note (attachment)',
    ),
    'file_mime_type' => 
    array (
      'name' => 'file_mime_type',
      'vname' => 'LBL_FILE_MIME_TYPE',
      'type' => 'varchar',
      'len' => '100',
      'comment' => 'Attachment MIME type',
    ),
    'file_url' => 
    array (
      'name' => 'file_url',
      'vname' => 'LBL_FILE_URL',
      'type' => 'function',
      'function_require' => 'include/upload_file.php',
      'function_class' => 'UploadFile',
      'function_name' => 'get_url',
      'function_params' => 
      array (
        0 => 'filename',
        1 => 'id',
      ),
      'source' => 'function',
      'reportable' => false,
      'comment' => 'Path to file (can be URL)',
    ),
    'available' => 
    array (
      'name' => 'available',
      'vname' => 'LBL_AVAILABLE',
      'type' => 'bool',
      'source' => 'non-db',
    ),
    'tqt_comments' => 
    array (
      'name' => 'tqt_comments',
      'type' => 'link',
      'relationship' => 'tqt_announcement_tqt_comments',
      'source' => 'non-db',
      'vname' => 'LBL_TQT_COMMENTS',
    ),
    'SecurityGroups' => 
    array (
      'name' => 'SecurityGroups',
      'type' => 'link',
      'relationship' => 'securitygroups_tqt_announcement',
      'module' => 'SecurityGroups',
      'bean_name' => 'SecurityGroup',
      'source' => 'non-db',
      'vname' => 'LBL_SECURITYGROUPS',
    ),
  ),
  'indices' => 
  array (
    'id' => 
    array (
      'name' => 'tqt_announcementpk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    'idx_tqt_announcement_branch_id' => 
    array (
      'name' => 'idx_tqt_announcement_branch_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
      ),
    ),
    'idx_tqt_announcement_department_id' => 
    array (
      'name' => 'idx_tqt_announcement_department_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'department_id',
      ),
    ),
    'idx_tqt_announcement_branch_dept' => 
    array (
      'name' => 'idx_tqt_announcement_branch_dept',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
        1 => 'department_id',
      ),
    ),
    'idx_tqt_announcement_assigned' => 
    array (
      'name' => 'idx_tqt_announcement_assigned',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'assigned_user_id',
      ),
    ),
    0 => 
    array (
      'name' => 'idx_announcement_status',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'status',
      ),
    ),
  ),
  'relationships' => 
  array (
    'tqt_announcement_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_Announcement',
      'rhs_table' => 'tqt_announcement',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'tqt_announcement_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_Announcement',
      'rhs_table' => 'tqt_announcement',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
    'tqt_announcement_assigned_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_Announcement',
      'rhs_table' => 'tqt_announcement',
      'rhs_key' => 'assigned_user_id',
      'relationship_type' => 'one-to-many',
    ),
  ),
  'optimistic_lock' => true,
  'templates' => 
  array (
    'assignable' => 'assignable',
    'groups' => 'groups',
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
