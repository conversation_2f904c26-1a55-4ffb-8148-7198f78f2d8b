
<section id="DetailViewBody" class="view-module-{$module|strtolower}">

<form action="index.php" method="post" name="{$form_name|default:'DetailView'}" id="form" class="_view-{$instanceName}">
<table cellpadding="1" cellspacing="0" border="0" width="100%">
<tr>
<td class="buttons" align="left" valign="top" nowrap>
<input type="hidden" name="module" value="{$module}" />
<input type="hidden" name="action" value="EditView" />
<input type="hidden" name="record" value="{$fields.id.value}" />
<input type="hidden" name="return_action" />
<input type="hidden" name="return_module" />
<input type="hidden" name="return_id" />
<input type="hidden" name="module_tab" /> 
<input type="hidden" name="isDuplicate" value="false" />
<input type="hidden" name="offset" value="{$offset}" />
{if $bean->aclAccess("edit")}<input title="{$APP.LBL_EDIT_BUTTON_TITLE}" accessKey="{$APP.LBL_EDIT_BUTTON_KEY}" class="button bedit" onclick="this.form.return_module.value='Accounts_Leads'; this.form.return_action.value='DetailView'; this.form.return_id.value='{$id}'; this.form.action.value='EditView';" type="submit" name="Edit" id="edit_button" value="{$APP.LBL_EDIT_BUTTON_LABEL}" />{/if} 
{if $bean->aclAccess("edit")}<input title="{$APP.LBL_DUPLICATE_BUTTON_TITLE}" accessKey="{$APP.LBL_DUPLICATE_BUTTON_KEY}" class="button duplicate" onclick="this.form.return_module.value='Accounts_Leads'; this.form.return_action.value='DetailView'; this.form.isDuplicate.value=true; this.form.action.value='EditView'; this.form.return_id.value='{$id}';" type="submit" name="Duplicate" value="{$APP.LBL_DUPLICATE_BUTTON_LABEL}" id="duplicate_button" />{/if} 
{if $bean->aclAccess("delete")}<input title="{$APP.LBL_DELETE_BUTTON_TITLE}" accessKey="{$APP.LBL_DELETE_BUTTON_KEY}" class="button delete" onclick="this.form.return_module.value='Accounts_Leads'; this.form.return_action.value='ListView'; this.form.action.value='Delete'; return confirm('{$APP.NTC_DELETE_CONFIRMATION}');" type="submit" name="Delete" value="{$APP.LBL_DELETE_BUTTON_LABEL}" />{/if} 
{if $bean->aclAccess("edit") and $bean->aclAccess("delete")}<input title="{$APP.LBL_DUP_MERGE}" accessKey="M" class="button find_duplicate" onclick="this.form.return_module.value='Accounts_Leads'; this.form.return_action.value='DetailView'; this.form.return_id.value='{$id}'; this.form.action.value='Step1'; this.form.module.value='MergeRecords';" type="submit" name="Merge" value="{$APP.LBL_DUP_MERGE}" />{/if} 
{if !empty($accessCoords) and $bean->ACLAccess("latlng") }<input type="submit" class="button highlight" name="btn" value=" {$MOD.LBL_MAP_COORDINATES} " onclick="this.form.action.value='Coords';" /> {/if}
{if $convertToAccount and $bean->aclAccess("edit") and $fields.unit_management.value eq "Accounts_Leads" }<input type="submit" class="button highlight" name="btn" value=" Chuyển qua khách hàng " onclick="this.form.action.value='EditView'; this.form.module.value='Accounts';" /> {/if}
</td>
<td class="buttons" align="left" valign="top" nowrap>
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=Accounts_Leads", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</td>
<td align="right" width="90%">{$ADMIN_EDIT}</td>
</tr>
</table>
</form>
{incomCRM_include include=$includes}
<div id="LBL_ACCOUNT_INFORMATION" class="detailview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_ACCOUNT_INFORMATION');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_ACCOUNT_INFORMATION_IMG" border="0" />
{incomCRM_translate label='LBL_ACCOUNT_INFORMATION' module='Accounts_Leads'}
</a>
</h4>
<div class="detail view {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_ACCOUNT_INFORMATION_GROUP" style="display:block">
<table id='detailpanel_1' cellspacing='{$gridline}'>
{$PAGINATION}
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ACCOUNT_CODE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_account_code_field' >
{counter name="panelFieldCount"}

<span id='{$fields.account_code.name}' >
{$fields.account_code.value}
</span>
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ACCOUNT_STATUS' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_account_status_field' >
{counter name="panelFieldCount"}

{ $fields.account_status.options[$fields.account_status.value]}
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_NAME' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_name_field' >
{counter name="panelFieldCount"}

<span id='{$fields.name.name}' >
{$fields.name.value}
</span>
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DEPARTMENT_MANAGER' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_department_manager_field' >
{counter name="panelFieldCount"}

{ $fields.department_manager.options[$fields.department_manager.value]}
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_PHONE_OFFICE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_phone_office_field' >
{counter name="panelFieldCount"}

{if !empty($fields.phone_office.value)}
{assign var="phone_value" value=$fields.phone_office.value }
{incomCRM_phone value=$phone_value }
{/if}
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ACCOUNT_WARN' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_account_warn_field' >
{counter name="panelFieldCount"}

{ $fields.account_warn.options[$fields.account_warn.value]}
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_BILLING_ADDRESS_STREET' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_billing_address_street_field' >
{counter name="panelFieldCount"}

{$fields.billing_address_street.value|url2html|nl2br}
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_USAGE_STATUS' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_usage_status_field' >
{counter name="panelFieldCount"}

{ $fields.usage_status.options[$fields.usage_status.value]}
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_SHIPPING_ADDRESS_STREET' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_shipping_address_street_field' >
{counter name="panelFieldCount"}

{$fields.shipping_address_street.value|url2html|nl2br}
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_RATING' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_rating_field' >
{counter name="panelFieldCount"}

{ $fields.rating.options[$fields.rating.value]}
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_TAX_CODE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_tax_code_field' >
{counter name="panelFieldCount"}

<span id='{$fields.tax_code.name}' >
{$fields.tax_code.value}
</span>
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_RATE_NOTE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_rate_note_field' >
{counter name="panelFieldCount"}

{$fields.rate_note.value|url2html|nl2br}
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_TYPE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_account_type_field' >
{counter name="panelFieldCount"}

{ $fields.account_type.options[$fields.account_type.value]}
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_REFERENCE_CODE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_reference_code_field' >
{counter name="panelFieldCount"}

<span id='{$fields.reference_code.name}' >
{$fields.reference_code.value}
</span>
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_INDUSTRY' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_industry_field' >
{counter name="panelFieldCount"}

{ $fields.industry.options[$fields.industry.value]}
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_FAX' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_phone_fax_field' >
{counter name="panelFieldCount"}

{if !empty($fields.phone_fax.value)}
{assign var="phone_value" value=$fields.phone_fax.value }
{incomCRM_phone value=$phone_value }
{/if}
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_TRANSACTION_LEVEL' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_transaction_level_field' >
{counter name="panelFieldCount"}

{ $fields.transaction_level.options[$fields.transaction_level.value]}
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_WEBSITE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_website_field' >
{counter name="panelFieldCount"}

<span id='{$fields.website.name}' >
{$fields.website.value}
</span>
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_SECTOR_VERTICAL' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_sector_vertical_field' >
{counter name="panelFieldCount"}

{ $fields.sector_vertical.options[$fields.sector_vertical.value]}
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_LOCATION_AREA' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_location_area_field' >
{counter name="panelFieldCount"}

{ $fields.location_area.options[$fields.location_area.value]}
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ACCOUNT_SCOPE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_account_scope_field' >
{counter name="panelFieldCount"}

{ $fields.account_scope.options[$fields.account_scope.value]}
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_LOCATION_CITY' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_location_city_field' >
{counter name="panelFieldCount"}

{ $fields.location_city.options[$fields.location_city.value]}
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_LEAD_SOURCE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_lead_source_field' >
{counter name="panelFieldCount"}

{ $fields.lead_source.options[$fields.lead_source.value]}
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_LOCATION_DISTRICT' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_location_district_field' >
{counter name="panelFieldCount"}

{ $fields.location_district.options[$fields.location_district.value]}
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ACCOUNT_GROUP_NAME' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_account_group_name_field' >
{counter name="panelFieldCount"}

{if !empty($fields.account_group_id.value)}<a href="index.php?module=Account_Groups&action=DetailView&record={$fields.account_group_id.value}">{/if}
{$fields.account_group_name.value}
{if !empty($fields.account_group_id.value)}</a>{/if}
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_MEMBER_OF' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_parent_name_field' >
{counter name="panelFieldCount"}

{if !empty($fields.parent_id.value)}<a href="index.php?module=Accounts&action=DetailView&record={$fields.parent_id.value}">{/if}
{$fields.parent_name.value}
{if !empty($fields.parent_id.value)}</a>{/if}
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_assigned_user_name_field' >
{counter name="panelFieldCount"}

{$fields.assigned_user_name.value}
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DATE_ENTERED' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_date_entered_field' >
{counter name="panelFieldCount"}
{$fields.date_entered.value} {$APP.LBL_BY} {$fields.created_by_name.value}	
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DEBT_USER_NAME' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_debt_user_name_field' >
{counter name="panelFieldCount"}

{$fields.debt_user_name.value}
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DATE_MODIFIED' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_date_modified_field' >
{counter name="panelFieldCount"}
{$fields.date_modified.value} {$APP.LBL_BY} {$fields.modified_by_name.value}	
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_EMAIL' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_email1_field' >
{counter name="panelFieldCount"}

<span id='{$fields.email1.name}' >
{$fields.email1.value}
</span>
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_FILENAME' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_filename_field' >
{counter name="panelFieldCount"}
{if !empty($fields.filename.value)}<a href="index.php?entryPoint=download&id={$fields.id.value}&type=Accounts" target="_blank"><img align="absmiddle" alt="{$fields.filename.value}" height="60" src="{$fields.file_url.value}?d={$fields.date_modified.value|urlencode}" /></a>{else}&nbsp;{/if}	
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_MEMBERSHIP_CARD' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td colspan='3' id='_membership_card_field' >
{counter name="panelFieldCount"}

{ $fields.membership_card.options[$fields.membership_card.value]}
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_POINT_TOTAL' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_point_total_field' >
{counter name="panelFieldCount"}

<span id='{$fields.point_total.name}' >
{$fields.point_total.value}
</span>
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_POINT_USED' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_point_used_field' >
{counter name="panelFieldCount"}

<span id='{$fields.point_used.name}' >
{$fields.point_used.value}
</span>
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_POINT_CURRENT' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td colspan='3' id='_point_current_field' >
{counter name="panelFieldCount"}

<span id='{$fields.point_current.name}' >
{$fields.point_current.value}
</span>
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_ACCOUNT_INFORMATION").style.display='none';</script>
{/if}
<div id="LBL_DEBT_NORMS" class="detailview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_DEBT_NORMS');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_DEBT_NORMS_IMG" border="0" />
{incomCRM_translate label='LBL_DEBT_NORMS' module='Accounts_Leads'}
</a>
</h4>
<div class="detail view {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_DEBT_NORMS_GROUP" style="display:none">
<table id='detailpanel_2' cellspacing='{$gridline}'>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_RECEIVABLE_DEBTS' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_receivable_debts_field' >
{counter name="panelFieldCount"}

<span id='{$fields.receivable_debts.name}' >
{$fields.receivable_debts.value}
</span>
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DEBT_NORM_MIN' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_debt_norm_min_field' >
{counter name="panelFieldCount"}

<span id='{$fields.debt_norm_min.name}' >
{$fields.debt_norm_min.value}
</span>
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DEBT_DELAY_DAY' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_debt_delay_day_field' >
{counter name="panelFieldCount"}

<span id='{$fields.debt_delay_day.name}' >
{$fields.debt_delay_day.value}
</span>
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DEBT_NORM_MAX' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_debt_norm_max_field' >
{counter name="panelFieldCount"}

<span id='{$fields.debt_norm_max.name}' >
{$fields.debt_norm_max.value}
</span>
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_DEBT_NORMS").style.display='none';</script>
{/if}
<div id="LBL_BUYING_CYCLE_TITLE" class="detailview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_BUYING_CYCLE_TITLE');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_BUYING_CYCLE_TITLE_IMG" border="0" />
{incomCRM_translate label='LBL_BUYING_CYCLE_TITLE' module='Accounts_Leads'}
</a>
</h4>
<div class="detail view {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_BUYING_CYCLE_TITLE_GROUP" style="display:none">
<table id='detailpanel_3' cellspacing='{$gridline}'>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_PERIODIC_PURCHASE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_periodic_purchase_field' >
{counter name="panelFieldCount"}

{ $fields.periodic_purchase.options[$fields.periodic_purchase.value]}
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CYCLE_METHOD' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_cycle_method_field' >
{counter name="panelFieldCount"}

{ $fields.cycle_method.options[$fields.cycle_method.value]}
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_BUYING_CYCLE_TITLE").style.display='none';</script>
{/if}
<div id="LBL_CONTACT_INFORMATION" class="detailview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_CONTACT_INFORMATION');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_CONTACT_INFORMATION_IMG" border="0" />
{incomCRM_translate label='LBL_CONTACT_INFORMATION' module='Accounts_Leads'}
</a>
</h4>
<div class="detail view {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_CONTACT_INFORMATION_GROUP" style="display:none">
<table id='detailpanel_4' cellspacing='{$gridline}'>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_PRIMARY_CONTACT_NAME' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_primary_contact_name_field' >
{counter name="panelFieldCount"}

{if !empty($fields.primary_contact_id.value)}<a href="index.php?module=Contacts&action=DetailView&record={$fields.primary_contact_id.value}">{/if}
{$fields.primary_contact_name.value}
{if !empty($fields.primary_contact_id.value)}</a>{/if}
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_SECONDARY_CONTACT_NAME' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_secondary_contact_name_field' >
{counter name="panelFieldCount"}

{if !empty($fields.secondary_contact_id.value)}<a href="index.php?module=Contacts&action=DetailView&record={$fields.secondary_contact_id.value}">{/if}
{$fields.secondary_contact_name.value}
{if !empty($fields.secondary_contact_id.value)}</a>{/if}
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CONTACT_POSITION' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_primary_contact_position_field' >
{counter name="panelFieldCount"}

<span id='{$fields.primary_contact_position.name}' >
{$fields.primary_contact_position.value}
</span>
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CONTACT_POSITION' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_secondary_contact_position_field' >
{counter name="panelFieldCount"}

<span id='{$fields.secondary_contact_position.name}' >
{$fields.secondary_contact_position.value}
</span>
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CONTACT_DEPARTMENT' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_primary_contact_department_field' >
{counter name="panelFieldCount"}

<span id='{$fields.primary_contact_department.name}' >
{$fields.primary_contact_department.value}
</span>
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CONTACT_DEPARTMENT' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_secondary_contact_department_field' >
{counter name="panelFieldCount"}

<span id='{$fields.secondary_contact_department.name}' >
{$fields.secondary_contact_department.value}
</span>
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CONTACT_BIRTHDATE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_primary_contact_birthdate_field' >
{counter name="panelFieldCount"}

<span id='{$fields.primary_contact_birthdate.name}' >
{$fields.primary_contact_birthdate.value}
</span>
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CONTACT_BIRTHDATE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_secondary_contact_birthdate_field' >
{counter name="panelFieldCount"}

<span id='{$fields.secondary_contact_birthdate.name}' >
{$fields.secondary_contact_birthdate.value}
</span>
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CONTACT_PHONE_WORK' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_primary_contact_phone_work_field' >
{counter name="panelFieldCount"}

<span id='{$fields.primary_contact_phone_work.name}' >
{$fields.primary_contact_phone_work.value}
</span>
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CONTACT_PHONE_WORK' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_secondary_contact_phone_work_field' >
{counter name="panelFieldCount"}

<span id='{$fields.secondary_contact_phone_work.name}' >
{$fields.secondary_contact_phone_work.value}
</span>
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CONTACT_PHONE_MOBILE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_primary_contact_phone_mobile_field' >
{counter name="panelFieldCount"}

<span id='{$fields.primary_contact_phone_mobile.name}' >
{$fields.primary_contact_phone_mobile.value}
</span>
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CONTACT_PHONE_MOBILE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_secondary_contact_phone_mobile_field' >
{counter name="panelFieldCount"}

<span id='{$fields.secondary_contact_phone_mobile.name}' >
{$fields.secondary_contact_phone_mobile.value}
</span>
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CONTACT_PHONE_FAX' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_primary_contact_phone_fax_field' >
{counter name="panelFieldCount"}

<span id='{$fields.primary_contact_phone_fax.name}' >
{$fields.primary_contact_phone_fax.value}
</span>
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CONTACT_PHONE_FAX' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_secondary_contact_phone_fax_field' >
{counter name="panelFieldCount"}

<span id='{$fields.secondary_contact_phone_fax.name}' >
{$fields.secondary_contact_phone_fax.value}
</span>
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CONTACT_EMAIL' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_primary_contact_email_field' >
{counter name="panelFieldCount"}

<span id='{$fields.primary_contact_email.name}' >
{$fields.primary_contact_email.value}
</span>
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CONTACT_EMAIL' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_secondary_contact_email_field' >
{counter name="panelFieldCount"}

<span id='{$fields.secondary_contact_email.name}' >
{$fields.secondary_contact_email.value}
</span>
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CONTACT_ADDRESS' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_primary_contact_address_field' >
{counter name="panelFieldCount"}

{$fields.primary_contact_address.value|url2html|nl2br}
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CONTACT_ADDRESS' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_secondary_contact_address_field' >
{counter name="panelFieldCount"}

{$fields.secondary_contact_address.value|url2html|nl2br}
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CONTACT_NOTE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_primary_contact_note_field' >
{counter name="panelFieldCount"}

{$fields.primary_contact_note.value|url2html|nl2br}
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CONTACT_NOTE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_secondary_contact_note_field' >
{counter name="panelFieldCount"}

{$fields.secondary_contact_note.value|url2html|nl2br}
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_CONTACT_INFORMATION").style.display='none';</script>
{/if}
<div id="LBL_MORE_INFO" class="detailview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_MORE_INFO');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_MORE_INFO_IMG" border="0" />
{incomCRM_translate label='LBL_MORE_INFO' module='Accounts_Leads'}
</a>
</h4>
<div class="detail view {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_MORE_INFO_GROUP" style="display:none">
<table id='detailpanel_5' cellspacing='{$gridline}'>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ENGLISH_NAME' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_english_name_field' >
{counter name="panelFieldCount"}

<span id='{$fields.english_name.name}' >
{$fields.english_name.value}
</span>
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_EMPLOYEES' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_employees_field' >
{counter name="panelFieldCount"}

<span id='{$fields.employees.name}' >
{$fields.employees.value}
</span>
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ABBREVIATION' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_abbreviation_field' >
{counter name="panelFieldCount"}

<span id='{$fields.abbreviation.name}' >
{$fields.abbreviation.value}
</span>
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_MARKING' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_marking_field' >
{counter name="panelFieldCount"}

{if strval($fields.marking.value) == "1" || strval($fields.marking.value) == "yes" || strval($fields.marking.value) == "on"} 
{assign var="checked" value="CHECKED"}
{else}
{assign var="checked" value=""}
{/if}
<input type="checkbox" class="checkbox" name="{$fields.marking.name}" disabled="true" {$checked} />
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_PHONE_ALT' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_phone_alternate_field' >
{counter name="panelFieldCount"}

{if !empty($fields.phone_alternate.value)}
{assign var="phone_value" value=$fields.phone_alternate.value }
{incomCRM_phone value=$phone_value }
{/if}
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_MARK_NOTE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_mark_note_field' >
{counter name="panelFieldCount"}

{$fields.mark_note.value|url2html|nl2br}
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DESCRIPTION' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_description_field' >
{counter name="panelFieldCount"}

{$fields.description.value|url2html|nl2br}
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_RESPONSIBLE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_responsible_field' >
{counter name="panelFieldCount"}

{$fields.responsible.value|url2html|nl2br}
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_MORE_INFO").style.display='none';</script>
{/if}
<div id="LBL_BANK_INFORMATION" class="detailview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_BANK_INFORMATION');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_BANK_INFORMATION_IMG" border="0" />
{incomCRM_translate label='LBL_BANK_INFORMATION' module='Accounts_Leads'}
</a>
</h4>
<div class="detail view {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_BANK_INFORMATION_GROUP" style="display:none">
<table id='detailpanel_6' cellspacing='{$gridline}'>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_REGISTRATION_NUMBER' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_registration_number_field' >
{counter name="panelFieldCount"}

<span id='{$fields.registration_number.name}' >
{$fields.registration_number.value}
</span>
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_STOCK_CODE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_stock_code_field' >
{counter name="panelFieldCount"}

<span id='{$fields.stock_code.name}' >
{$fields.stock_code.value}
</span>
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_DATE_ISSUE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_date_issue_field' >
{counter name="panelFieldCount"}

<span id='{$fields.date_issue.name}' >
{$fields.date_issue.value}
</span>
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_CHARTER_CAPITAL' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_charter_capital_field' >
{counter name="panelFieldCount"}

{ $fields.charter_capital.options[$fields.charter_capital.value]}
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_REGISTRATION_PLACE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_registration_place_field' >
{counter name="panelFieldCount"}

<span id='{$fields.registration_place.name}' >
{$fields.registration_place.value}
</span>
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_ON_INCORPORATION' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_on_incorporation_field' >
{counter name="panelFieldCount"}

<span id='{$fields.on_incorporation.name}' >
{$fields.on_incorporation.value}
</span>
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_BANK_ACCOUNT_NUMBER' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td colspan='3' id='_bank_account_number_field' >
{counter name="panelFieldCount"}

<span id='{$fields.bank_account_number.name}' >
{$fields.bank_account_number.value}
</span>
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_BANK_NAME' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_bank_name_field' >
{counter name="panelFieldCount"}

<span id='{$fields.bank_name.name}' >
{$fields.bank_name.value}
</span>
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_BANK_ACCOUNT_OWNER' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_bank_account_owner_field' >
{counter name="panelFieldCount"}

<span id='{$fields.bank_account_owner.name}' >
{$fields.bank_account_owner.value}
</span>
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_BANK_INFORMATION").style.display='none';</script>
{/if}
<div id="LBL_BILLING_CONTRACT_INFO" class="detailview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_BILLING_CONTRACT_INFO');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_BILLING_CONTRACT_INFO_IMG" border="0" />
{incomCRM_translate label='LBL_BILLING_CONTRACT_INFO' module='Accounts_Leads'}
</a>
</h4>
<div class="detail view {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_BILLING_CONTRACT_INFO_GROUP" style="display:none">
<table id='detailpanel_7' cellspacing='{$gridline}'>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_BILLING_REPRESENTATION' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_billing_representation_field' >
{counter name="panelFieldCount"}

<span id='{$fields.billing_representation.name}' >
{$fields.billing_representation.value}
</span>
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_BILLING_POSITION' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_billing_position_field' >
{counter name="panelFieldCount"}

<span id='{$fields.billing_position.name}' >
{$fields.billing_position.value}
</span>
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_BILLING_CONTRACT_INFO").style.display='none';</script>
{/if}
<div id="LBL_TELESALES_WORKING_INFO" class="detailview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<h4>
<a class="tabFormAdvLink" href="javascript:showHideGroup('LBL_TELESALES_WORKING_INFO');">
<img src='{incomCRM_getimagepath file="basic_search.gif"}' id="LBL_TELESALES_WORKING_INFO_IMG" border="0" />
{incomCRM_translate label='LBL_TELESALES_WORKING_INFO' module='Accounts_Leads'}
</a>
</h4>
<div class="detail view {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="LBL_TELESALES_WORKING_INFO_GROUP" style="display:none">
<table id='detailpanel_8' cellspacing='{$gridline}'>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_PURCHASE_DATE' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_purchase_date_field' >
{counter name="panelFieldCount"}

<span id='{$fields.purchase_date.name}' >
{$fields.purchase_date.value}
</span>
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_SUPPORT_USER_NAME' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_support_user_name_field' >
{counter name="panelFieldCount"}

{$fields.support_user_name.value}
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_TS_CUSTOMER_NEED' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_ts_customer_need_field' >
{counter name="panelFieldCount"}

{if !empty($fields.ts_customer_need.value)}
{multienum_to_array string=$fields.ts_customer_need.value assign="vals"}
{foreach from=$vals item=item}
<li>{ $fields.ts_customer_need.options.$item }</li>
{/foreach}
{/if}
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_TELESALES_STATUS' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_telesales_status_field' >
{counter name="panelFieldCount"}

{ $fields.telesales_status.options[$fields.telesales_status.value]}
</td>
</tr>
<tr>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_TS_PRODUCTS_NEED' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_ts_products_need_field' >
{counter name="panelFieldCount"}

{if !empty($fields.ts_products_need.value)}
{multienum_to_array string=$fields.ts_products_need.value assign="vals"}
{foreach from=$vals item=item}
<li>{ $fields.ts_products_need.options.$item }</li>
{/foreach}
{/if}
</td>
<td width='15%' scope="row">
{capture name="label" assign="label"}
{incomCRM_translate label='LBL_TS_NOTES' module='Accounts_Leads'}
{/capture}
{$label|strip_semicolon}:
</td>
<td width='35%' id='_ts_notes_field' >
{counter name="panelFieldCount"}

{$fields.ts_notes.value|url2html|nl2br}
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_TELESALES_WORKING_INFO").style.display='none';</script>
{/if}

</section>