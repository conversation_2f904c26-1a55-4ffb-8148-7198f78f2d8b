<?php
// created: 2025-02-26 15:24:03
$<PERSON><PERSON><PERSON><PERSON><PERSON>["dictionary"]["TQT_GrowthAbility"] = array (
  'table' => 'tqt_growthabilities',
  'audited' => false,
  'duplicate_merge' => false,
  'unified_search' => false,
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'varchar',
      'len' => 200,
      'source' => 'non-db',
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
      'comment' => 'Full text of the note',
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'tqt_growthabilities_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'tqt_growthabilities_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'branch_id' => 
    array (
      'name' => 'branch_id',
      'vname' => 'LBL_BRANCH_ID',
      'type' => 'enum',
      'options' => 'users_branch_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'BranchID of assigned user',
    ),
    'department_id' => 
    array (
      'name' => 'department_id',
      'vname' => 'LBL_DEPARTMENT_ID',
      'type' => 'enum',
      'options' => 'users_department_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'DepartmentID of assigned user',
    ),
    'assigned_user_id' => 
    array (
      'name' => 'assigned_user_id',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'vname' => 'LBL_ASSIGNED_TO_ID',
      'group' => 'assigned_user_name',
      'type' => 'relate',
      'table' => 'users',
      'module' => 'Users',
      'isnull' => 'false',
      'dbType' => 'id',
      'audited' => true,
      'reportable' => true,
      'duplicate_merge' => 'disabled',
      'comment' => 'User ID assigned to record',
    ),
    'assigned_user_name' => 
    array (
      'name' => 'assigned_user_name',
      'link' => 'assigned_user_link',
      'vname' => 'LBL_ASSIGNED_TO_NAME',
      'rname' => 'user_name',
      'type' => 'relate',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'assigned_user_id',
      'module' => 'Users',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'assigned_user_link' => 
    array (
      'name' => 'assigned_user_link',
      'type' => 'link',
      'relationship' => 'tqt_growthabilities_assigned_user',
      'vname' => 'LBL_ASSIGNED_TO_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
      'duplicate_merge' => 'enabled',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'table' => 'users',
    ),
    'growth_ability' => 
    array (
      'name' => 'growth_ability',
      'vname' => 'LBL_GROWTH_ABILITY',
      'type' => 'enum',
      'options' => 'undefined_dom',
      'len' => 1,
      'massupdate' => false,
      'required' => false,
    ),
    'purchased_products' => 
    array (
      'name' => 'purchased_products',
      'vname' => 'LBL_PURCHASED_PRODUCTS',
      'type' => 'text',
    ),
    'budget_marketing' => 
    array (
      'name' => 'budget_marketing',
      'vname' => 'LBL_BUDGET_MARKETING',
      'type' => 'text',
    ),
    'competitor_service' => 
    array (
      'name' => 'competitor_service',
      'vname' => 'LBL_COMPETITOR_SERVICE',
      'type' => 'text',
    ),
    'service_feedback' => 
    array (
      'name' => 'service_feedback',
      'vname' => 'LBL_SERVICE_FEEDBACK',
      'type' => 'text',
    ),
    'tqt_productgroup_id' => 
    array (
      'name' => 'tqt_productgroup_id',
      'vname' => 'LBL_TQT_PRODUCTGROUP_ID',
      'type' => 'id',
      'required' => false,
      'reportable' => false,
    ),
    'tqt_productgroup_name' => 
    array (
      'name' => 'tqt_productgroup_name',
      'rname' => 'name',
      'id_name' => 'tqt_productgroup_id',
      'vname' => 'LBL_TQT_PRODUCTGROUP_NAME',
      'table' => 'tqt_productgroup',
      'type' => 'relate',
      'link' => 'tqt_productgroup',
      'join_name' => 'tqt_productgroup',
      'isnull' => 'true',
      'module' => 'TQT_ProductGroup',
      'source' => 'non-db',
    ),
    'tqt_productgroup' => 
    array (
      'name' => 'tqt_productgroup',
      'type' => 'link',
      'relationship' => 'tqt_productgroup_tqt_growthabilities',
      'vname' => 'LBL_LIST_PRODUCTGROUP_NAME',
      'source' => 'non-db',
    ),
    'tqt_productseries_id' => 
    array (
      'name' => 'tqt_productseries_id',
      'vname' => 'LBL_TQT_PRODUCTSERIES_ID',
      'type' => 'id',
      'required' => false,
      'reportable' => false,
    ),
    'tqt_productseries_name' => 
    array (
      'name' => 'tqt_productseries_name',
      'rname' => 'name',
      'id_name' => 'tqt_productseries_id',
      'vname' => 'LBL_TQT_PRODUCTSERIES_NAME',
      'table' => 'tqt_productseries',
      'type' => 'relate',
      'link' => 'tqt_productseries',
      'join_name' => 'tqt_productseries',
      'isnull' => 'true',
      'module' => 'TQT_ProductSeries',
      'source' => 'non-db',
    ),
    'tqt_productseries' => 
    array (
      'name' => 'tqt_productseries',
      'type' => 'link',
      'relationship' => 'tqt_productseries_tqt_growthabilities',
      'vname' => 'LBL_LIST_PRODUCTSERIES_NAME',
      'source' => 'non-db',
    ),
    'account_id' => 
    array (
      'name' => 'account_id',
      'vname' => 'LBL_ACCOUNT_ID',
      'type' => 'id',
      'required' => false,
      'reportable' => false,
      'comment' => 'Account ID note is associated with',
    ),
    'account_name' => 
    array (
      'name' => 'account_name',
      'rname' => 'name',
      'id_name' => 'account_id',
      'vname' => 'LBL_ACCOUNT_NAME',
      'table' => 'accounts',
      'type' => 'relate',
      'link' => 'accounts',
      'join_name' => 'accounts',
      'isnull' => 'true',
      'module' => 'Accounts',
      'source' => 'non-db',
    ),
    'accounts' => 
    array (
      'name' => 'accounts',
      'type' => 'link',
      'relationship' => 'accounts_tqt_growthabilities',
      'vname' => 'LBL_LIST_ACCOUNT_NAME',
      'source' => 'non-db',
    ),
    'SecurityGroups' => 
    array (
      'name' => 'SecurityGroups',
      'type' => 'link',
      'relationship' => 'securitygroups_tqt_growthabilities',
      'module' => 'SecurityGroups',
      'bean_name' => 'SecurityGroup',
      'source' => 'non-db',
      'vname' => 'LBL_SECURITYGROUPS',
    ),
  ),
  'indices' => 
  array (
    'id' => 
    array (
      'name' => 'tqt_growthabilitiespk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    'idx_tqt_growthabilities_branch_id' => 
    array (
      'name' => 'idx_tqt_growthabilities_branch_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
      ),
    ),
    'idx_tqt_growthabilities_department_id' => 
    array (
      'name' => 'idx_tqt_growthabilities_department_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'department_id',
      ),
    ),
    'idx_tqt_growthabilities_branch_dept' => 
    array (
      'name' => 'idx_tqt_growthabilities_branch_dept',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
        1 => 'department_id',
      ),
    ),
    'idx_tqt_growthabilities_assigned' => 
    array (
      'name' => 'idx_tqt_growthabilities_assigned',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'assigned_user_id',
      ),
    ),
    0 => 
    array (
      'name' => 'idx_growthabilities_assigned',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'assigned_user_id',
      ),
    ),
  ),
  'relationships' => 
  array (
    'tqt_growthabilities_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_GrowthAbilities',
      'rhs_table' => 'tqt_growthabilities',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'tqt_growthabilities_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_GrowthAbilities',
      'rhs_table' => 'tqt_growthabilities',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
    'tqt_growthabilities_assigned_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_GrowthAbilities',
      'rhs_table' => 'tqt_growthabilities',
      'rhs_key' => 'assigned_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'accounts_tqt_growthabilities' => 
    array (
      'lhs_module' => 'Accounts',
      'lhs_table' => 'accounts',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_GrowthAbilities',
      'rhs_table' => 'tqt_growthabilities',
      'rhs_key' => 'account_id',
      'relationship_type' => 'one-to-many',
    ),
    'tqt_productgroup_tqt_growthabilities' => 
    array (
      'lhs_module' => 'TQT_ProductGroup',
      'lhs_table' => 'tqt_productgroup',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_GrowthAbilities',
      'rhs_table' => 'tqt_growthabilities',
      'rhs_key' => 'tqt_productgroup_id',
      'relationship_type' => 'one-to-many',
    ),
    'tqt_productseries_tqt_growthabilities' => 
    array (
      'lhs_module' => 'TQT_ProductSeries',
      'lhs_table' => 'tqt_productseries',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_GrowthAbilities',
      'rhs_table' => 'tqt_growthabilities',
      'rhs_key' => 'tqt_productseries_id',
      'relationship_type' => 'one-to-many',
    ),
  ),
  'optimistic_lock' => true,
  'templates' => 
  array (
    'assignable' => 'assignable',
    'groups' => 'groups',
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
