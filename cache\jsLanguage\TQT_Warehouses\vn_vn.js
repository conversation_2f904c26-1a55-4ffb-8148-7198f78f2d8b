incomCRM.language.setLanguage('TQT_Warehouses', {"LBL_ID":"ID","LBL_DATE_ENTERED":"Ng\u00e0y t\u1ea1o","LBL_DATE_MODIFIED":"Ng\u00e0y c\u1eadp nh\u1eadt","LBL_MODIFIED":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_MODIFIED_ID":"ID ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_MODIFIED_NAME":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_CREATED":"Ng\u01b0\u1eddi t\u1ea1o","LBL_CREATED_ID":"ID ng\u01b0\u1eddi t\u1ea1o","LBL_DESCRIPTION":"M\u00f4 t\u1ea3 kho","LBL_DELETED":"\u0110\u00e3 x\u00f3a?","LBL_NAME":"T\u00ean Kho h\u00e0ng","LBL_CREATED_USER":"Ng\u01b0\u1eddi t\u1ea1o","LBL_MODIFIED_USER":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_ASSIGNED_TO":"Nv.Kho","LBL_ASSIGNED_TO_ID":"M\u00e3 Nv.Kho","LBL_ASSIGNED_TO_NAME":"Nv.Kho","LBL_ASSIGNED_TO_USER":"Nv.Ql\u00fd","LBL_ASSIGNED_USER":"Nv.Kho","LBL_MODULE_NAME":"Danh s\u00e1ch Kho h\u00e0ng","LBL_MODULE_TITLE":"Danh s\u00e1ch Kho h\u00e0ng","LNK_NEW_RECORD":"Th\u00eam m\u1edbi","LNK_LIST":"Danh s\u00e1ch Kho h\u00e0ng","LBL_SEARCH_FORM_TITLE":"T\u00ecm ki\u1ebfm","LBL_LIST_FORM_TITLE":"Danh s\u00e1ch Kho h\u00e0ng","LBL_NEW_FORM_TITLE":"Th\u00eam m\u1edbi","LBL_CODE":"M\u00e3 kho","LBL_STATUS":"Tr\u1ea1ng th\u00e1i","LBL_USERS":"Nh\u00e2n vi\u00ean s\u1eed d\u1ee5ng kho","LBL_WARNING_DELETED":"Kho h\u00e0ng n\u00e0y \u0111\u00e3 c\u00f3 s\u1ea3n ph\u1ea9m nh\u1eadp kho, b\u1ea1n kh\u00f4ng th\u1ec3 x\u00f3a.","LBL_PRODUCT_COSTS":"Gi\u00e1 v\u1ed1n s\u1ea3n ph\u1ea9m","LBL_PRODUCT_BGN_COSTS":"Gi\u00e1 v\u1ed1n trung b\u00ecnh cu\u1ed1i k\u1ef3 theo th\u00e1ng","LBL_PRICE_ORIG":"Gi\u00e1 v\u1ed1n","LBL_FIX_COST_BEGIN":"Gi\u00e1 v\u1ed1n trung b\u00ecnh","LBL_DATE_FIX_COST":"Ng\u00e0y x\u00e9t gi\u00e1","LBL_CALC_AVG_COST":"T\u00ednh l\u1ea1i gi\u00e1 v\u1ed1n","LBL_SECURITYGROUPS":"Security Groups","LBL_SECURITYGROUPS_SUBPANEL_TITLE":"Security Groups","LBL_TQT_PRODUCT_STOCKS":"S\u1ea3n ph\u1ea9m trong kho"});