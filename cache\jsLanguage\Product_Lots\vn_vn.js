incomCRM.language.setLanguage('Product_Lots', {"LBL_ID":"ID","LBL_DATE_ENTERED":"Ng\u00e0y t\u1ea1o","LBL_DATE_MODIFIED":"Ng\u00e0y c\u1eadp nh\u1eadt","LBL_MODIFIED":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_MODIFIED_ID":"ID ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_MODIFIED_NAME":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_CREATED":"Ng\u01b0\u1eddi t\u1ea1o","LBL_CREATED_ID":"ID ng\u01b0\u1eddi t\u1ea1o","LBL_DESCRIPTION":"Di\u1ec5n gi\u1ea3i th\u00eam","LBL_DELETED":"\u0110\u00e3 x\u00f3a?","LBL_NAME":"M\u00e3 L\u00f4","LBL_CREATED_USER":"Ng\u01b0\u1eddi t\u1ea1o","LBL_MODIFIED_USER":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_ASSIGNED_TO":"Nv.Ql\u00fd","LBL_ASSIGNED_TO_ID":"ID Nv.Ql\u00fd","LBL_ASSIGNED_TO_NAME":"Nv.Ql\u00fd","LBL_ASSIGNED_TO_USER":"Nv.Ql\u00fd","LBL_ASSIGNED_USER":"Nv.Ql\u00fd","LBL_MODULE_NAME":"Qu\u1ea3n l\u00fd theo l\u00f4","LBL_MODULE_TITLE":"Qu\u1ea3n  l\u00fd theo l\u00f4: Trang ch\u1ee7","LNK_NEW_RECORD":"Th\u00eam m\u1edbi","LNK_LIST":"Danh s\u00e1ch","LBL_NEW_FORM_TITLE":"Th\u00eam m\u1edbi","LBL_SEARCH_FORM_TITLE":"T\u00ecm ki\u1ebfm","LBL_LIST_FORM_TITLE":"Danh s\u00e1ch L\u00f4","LNK_NEW_LOT":"T\u1ea1o L\u00f4","LBL_CODE":"S\u1ed1 L\u00f4","LBL_DATE_PERFORM":"Ng\u00e0y L\u00f4","LBL_DATE_EXPIRED":"Ng\u00e0y h\u1ebft h\u1ea1n","LBL_NUM_DAYS":"S\u1ed1 ng\u00e0y","LBL_TQT_PRODUCTGROUP_ID":"ID Lo\u1ea1i SP","LBL_TQT_PRODUCTGROUP_NAME":"Lo\u1ea1i SP","LBL_TQT_PRODUCTGROUP":"Lo\u1ea1i s\u1ea3n ph\u1ea9m","LBL_DATE_PERFORM_FROM":"Ng\u00e0y L\u00f4 T\u1eeb ng\u00e0y","LBL_DATE_EXPIRED_FROM":"Ng\u00e0y h\u1ebft h\u1ea1n T\u1eeb ng\u00e0y","LBL_ERR_DUPLICATE":"%s [%s] b\u1ea1n nh\u1eadp \u0111\u00e3 t\u1ed3n t\u1ea1i, vui l\u00f2ng nh\u1eadp l\u1ea1i.","LBL_WARNING_DELETED":"LOT n\u00e0y \u0111\u00e3 c\u00f3 s\u1ea3n ph\u1ea9m trong kho, b\u1ea1n kh\u00f4ng th\u1ec3 x\u00f3a.","LBL_IMPORT_EXCEL":"Nh\u1eadp Excel","LBL_SECURITYGROUPS":"Security Groups","LBL_SECURITYGROUPS_SUBPANEL_TITLE":"Security Groups","LBL_TQT_PRODUCT_STOCKS":"Chi ti\u1ebft trong kho"});