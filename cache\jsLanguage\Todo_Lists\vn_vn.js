incomCRM.language.setLanguage('Todo_Lists', {"LBL_ID":"ID","LBL_DATE_ENTERED":"Ng\u00e0y t\u1ea1o","LBL_DATE_MODIFIED":"Ng\u00e0y c\u1eadp nh\u1eadt","LBL_MODIFIED":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_MODIFIED_ID":"ID ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_MODIFIED_NAME":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_CREATED":"Ng\u01b0\u1eddi t\u1ea1o","LBL_CREATED_ID":"ID ng\u01b0\u1eddi t\u1ea1o","LBL_DESCRIPTION":"<PERSON>hi ch\u00fa","LBL_DELETED":"\u0110\u00e3 x\u00f3a?","LBL_NAME":"T\u00ean \u0111\u1ea7u vi\u1ec7c","LBL_CREATED_USER":"Ng\u01b0\u1eddi t\u1ea1o","LBL_MODIFIED_USER":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_ASSIGNED_TO":"Nv.Ql\u00fd","LBL_ASSIGNED_TO_ID":"ID Nv.Ql\u00fd","LBL_ASSIGNED_TO_NAME":"Nv.Ql\u00fd","LBL_ASSIGNED_TO_USER":"Nv.Ql\u00fd","LBL_ASSIGNED_USER":"Nv.Ql\u00fd","LBL_MODULE_NAME":"Danh s\u00e1ch \u0111\u1ea7u vi\u1ec7c","LBL_MODULE_TITLE":"Danh s\u00e1ch \u0111\u1ea7u vi\u1ec7c: Trang ch\u1ee7","LNK_NEW_RECORD":"Th\u00eam \u0111\u1ea7u vi\u1ec7c","LNK_LIST":"Danh s\u00e1ch \u0111\u1ea7u vi\u1ec7c","LBL_NEW_FORM_TITLE":"Th\u00eam m\u1edbi","LBL_LIST_FORM_TITLE":"Danh s\u00e1ch \u0111\u1ea7u vi\u1ec7c","LBL_SEARCH_FORM_TITLE":"T\u00ecm ki\u1ebfm","LBL_CODE":"M\u00e3 \u0111\u1ea7u vi\u1ec7c","LBL_PROCESSING_TIME":"Th\u1eddi l\u01b0\u1ee3ng x\u1eed l\u00fd","LBL_CYCLE_TYPE":"Chu k\u1ef3 l\u1eb7p l\u1ea1i","LBL_JOB_TYPE":"Lo\u1ea1i c\u00f4ng vi\u1ec7c","LBL_DEPARTMENT_ALL":"Ph\u00f2ng ban","LBL_TEAMS_ALL":"B\u1ed9 ph\u1eadn","LBL_USERS_ALL":"Nh\u00e2n vi\u00ean","LBL_WORKFLOW_CODE":"M\u00e3 quy tr\u00ecnh","LBL_LIST_PROCESSING_TIME":"Th\u1eddi gian","LBL_LIST_CYCLE_TYPE":"Chu k\u1ef3","LBL_LIST_JOB_TYPE":"Lo\u1ea1i CV","LBL_TIME_UNIT":"ph\u00fat","LBL_GROUP_ID":"ID Nh\u00f3m ng\u01b0\u1eddi th\u1ef1c hi\u1ec7n","LBL_GROUP_NAME":"Nh\u00f3m ng\u01b0\u1eddi th\u1ef1c hi\u1ec7n","LBL_TASKS_GROUPS":"Nh\u00f3m ng\u01b0\u1eddi th\u1ef1c hi\u1ec7n","LBL_GROUP_STEPS":"M\u00f4 t\u1ea3 vai tr\u00f2 c\u1ee7a t\u1eebng nh\u00e2n s\u1ef1"});