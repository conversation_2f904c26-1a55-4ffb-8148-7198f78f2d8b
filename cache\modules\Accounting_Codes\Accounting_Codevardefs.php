<?php
// created: 2025-03-03 22:47:49
$<PERSON><PERSON><PERSON><PERSON><PERSON>["dictionary"]["Accounting_Code"] = array (
  'table' => 'accounting_codes',
  'audited' => true,
  'unified_search' => false,
  'duplicate_merge' => false,
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'varchar',
      'len' => '200',
      'acl' => true,
      'audited' => true,
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'accounting_codes_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'accounting_codes_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'code' => 
    array (
      'name' => 'code',
      'vname' => 'LBL_CODE',
      'type' => 'int',
      'len' => 6,
      'audited' => true,
      'disable_num_format' => true,
    ),
    'code_sort' => 
    array (
      'name' => 'code_sort',
      'vname' => 'LBL_CODE',
      'type' => 'varchar',
      'len' => 20,
    ),
    'parent_id' => 
    array (
      'name' => 'parent_id',
      'vname' => 'LBL_PARENT_ID',
      'type' => 'id',
      'required' => false,
      'reportable' => false,
      'audited' => true,
    ),
    'parent_name' => 
    array (
      'name' => 'parent_name',
      'rname' => 'name',
      'id_name' => 'parent_id',
      'vname' => 'LBL_PARENT_NAME',
      'type' => 'relate',
      'table' => 'accounting_codes',
      'isnull' => 'true',
      'module' => 'Accounting_Codes',
      'massupdate' => false,
      'source' => 'non-db',
      'link' => 'member_of',
      'unified_search' => true,
      'importable' => 'false',
    ),
    'member_of' => 
    array (
      'name' => 'member_of',
      'type' => 'link',
      'relationship' => 'member_accounting_codes',
      'module' => 'Accounting_Codes',
      'bean_name' => 'Accounting_Code',
      'link_type' => 'one',
      'source' => 'non-db',
      'vname' => 'LBL_MEMBER_OF',
      'side' => 'left',
    ),
    'members' => 
    array (
      'name' => 'members',
      'type' => 'link',
      'relationship' => 'member_accounting_codes',
      'module' => 'Accounting_Codes',
      'bean_name' => 'Accounting_Code',
      'source' => 'non-db',
      'vname' => 'LBL_MEMBERS',
    ),
    'accounting_code' => 
    array (
      'name' => 'accounting_code',
      'vname' => 'LBL_ACCOUNTING_CODE',
      'fields' => 
      array (
        0 => 'code',
        1 => 'name',
      ),
      'db_concat_fields' => 
      array (
        0 => 'code',
        1 => 'name',
      ),
      'sort_on' => 'code',
      'group' => 'code',
      'type' => 'varchar',
      'len' => '250',
      'source' => 'non-db',
    ),
    'cashes' => 
    array (
      'name' => 'cashes',
      'type' => 'link',
      'relationship' => 'accounting_codes_cashes',
      'vname' => 'LBL_CASHES',
      'source' => 'non-db',
    ),
  ),
  'indices' => 
  array (
    'id' => 
    array (
      'name' => 'accounting_codespk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    0 => 
    array (
      'name' => 'idx_accounting_code_id_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'id',
        1 => 'deleted',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_accounting_code_name',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'name',
      ),
    ),
    2 => 
    array (
      'name' => 'idx_accounting_code_code',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'code',
      ),
    ),
    3 => 
    array (
      'name' => 'idx_accounting_code_parent',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'parent_id',
      ),
    ),
  ),
  'relationships' => 
  array (
    'accounting_codes_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Accounting_Codes',
      'rhs_table' => 'accounting_codes',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'accounting_codes_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Accounting_Codes',
      'rhs_table' => 'accounting_codes',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
    'member_accounting_codes' => 
    array (
      'lhs_module' => 'Accounting_Codes',
      'lhs_table' => 'accounting_codes',
      'lhs_key' => 'id',
      'rhs_module' => 'Accounting_Codes',
      'rhs_table' => 'accounting_codes',
      'rhs_key' => 'parent_id',
      'relationship_type' => 'one-to-many',
    ),
    'accounting_codes_cashes' => 
    array (
      'lhs_module' => 'Accounting_Codes',
      'lhs_table' => 'accounting_codes',
      'lhs_key' => 'id',
      'rhs_module' => 'Cashes',
      'rhs_table' => 'cashes',
      'rhs_key' => 'accounting_code_id',
      'relationship_type' => 'one-to-many',
    ),
  ),
  'optimistic_lock' => true,
  'templates' => 
  array (
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
