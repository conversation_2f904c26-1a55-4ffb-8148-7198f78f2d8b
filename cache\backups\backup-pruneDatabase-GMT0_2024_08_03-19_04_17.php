<?php
// created: 2024-08-04 02:04:25
$pruneDatabase = array (
  0 => 'INSERT INTO accounts_opportunities (id, opportunity_id, account_id, date_modified, deleted) VALUES ("4e263dba-03c6-9c10-6018-66ab3fa61732", "e74ac1ea-1481-2eee-fb1b-669f645c4e6c", "293d3b55-df85-46f9-6347-5d5d364920e9", "2024-08-01 07:55:31", "1");',
  1 => 'INSERT INTO accounts_opportunities (id, opportunity_id, account_id, date_modified, deleted) VALUES ("8fff7a1e-714b-5187-9685-66a0a6a5a12a", "7ff8279c-5bd7-41a8-f83a-66a0a62af390", "d15e9abc-39f9-9890-bc8a-612f0279e921", "2024-08-01 20:54:11", "1");',
  2 => 'INSERT INTO cash_reasons (id, name, date_entered, date_modified, modified_user_id, created_by, description, deleted, reason_type, accounting_type, list_expense, ignore_summary, reason_opp_type, ignore_banks, cash_group, debt_type, is_net_cost, cash_project_group) VALUES ("e1cc8f25-028e-4fe4-8113-6073adb02e26", "Thu hồi tiền Đặt cọc mua hàng (NCC trả lại)", "2021-04-12 02:16:42", "2024-07-11 08:18:09", "36b84839-162f-0c5c-d652-59229c92215c", "1", "Nhà cung cấp trả lại tiền đặt cọc mua hàng", "1", "1", "331", "0", "0", "5", "0", "", "331", "0", "");',
  3 => 'INSERT INTO cash_reasons (id, name, date_entered, date_modified, modified_user_id, created_by, description, deleted, reason_type, accounting_type, list_expense, ignore_summary, reason_opp_type, ignore_banks, cash_group, debt_type, is_net_cost, cash_project_group) VALUES ("e65c8a7d-cbfd-4196-0e9e-62724da694af", "Thu hồi tiền tạm ứng mua hàng", "2022-05-04 09:54:28", "2024-07-11 08:01:52", "36b84839-162f-0c5c-d652-59229c92215c", "1", "", "1", "1", "331", "0", "0", "", "0", "", "331", "0", "");',
  4 => 'INSERT INTO opportunities (id, date_entered, date_modified, modified_user_id, created_by, deleted, name, description, assigned_user_id, branch_id, department_id, opportunity_type, campaign_id, lead_source, amount, amount_usdollar, currency_id, date_closed, sales_stage, probability, priority, estimated_revenue, opportunity_scope, date_start_process, object, expected_output, actual_output, contract_revenue, actual_receipts, order_number, prod_lock, delivered_all, contact_name, vat_tax, vat_amount, vat_more_amt, vat_other_amt, discount_amount, cost_amount, comm_more_amount, incurred_amount, incurred_other, incurred_domestic, incurred_foreign, commission_amount, arise_cost_amount, profit_amount, rate_usd, is_locked, processing_stage, processing_content, amount_collected, amount_owed, handle_user_id, parent_id, payment_status, money_deposit, money_refund, is_target, account_targets, causes_failure, failure_notes, debt_order, payment_type, account_address, account_tax_code, shipping_fee, no_currency, account_phone, account_email, date_payment, order_type, quote_number, contract_number, contract_date, order_discount, payment_period, project_id, project_name, bank_account_id, disable_auto_price, quotation_date, quotation_version, location_nearby, rate_id, rate_foreign, service_total, service_used, service_left, vat_stock, ordered_merge, warranty_period, table_params, ignore_payment, re_calc_profit, is_auto_cpps, total_amount_in, date_return, prod_returned, vat_spec_amt, vat_imp_amt, vat_total_amount, ext_incurred_amount, int_incurred_amount, is_import, ttt_date_shipment, ttt_date_contract, ttt_date_declaration, ttt_date_storage, ttt_date_arrival, ttt_date_clearance, ttt_fee_amount, ttt_insurance_number, ttt_insurance_date, ttt_insurance_amount, ttt_insurance_extension, ttt_insurance_coop, ttt_date_lc_payment, ttt_lc_number, ttt_date_lc_expired, ttt_lc_amount, ttt_lc_reiss_amt, ttt_lc_repair_amt, ttt_lc_payment_amt, ttt_lc_bank_name, ttt_invoice_number, ttt_invoice_date, ttt_bol, ttt_bol_date, ttt_packinglist, ttt_co, ttt_milltest, ttt_dost, ttt_condi_trading, ttt_declaration, ttt_declaration_number, ttt_declaration_amount, ttt_vat_amount, ttt_verification, ttt_verification_amount, ttt_vat_import, ttt_customs_fee, ttt_clearance, ttt_arrival_notice, ttt_discharging_port, ttt_vessel, ttt_vessel_vn, ttt_owed_contract, ttt_storage_fee, ttt_vessel_fee, ttt_repair_cont_fee, ttt_deposit_cont_fee, ttt_expected_output, ttt_cnt_amount, ttt_foreign_amount, ttt_actual_output, ttt_commodity, ttt_tolerance, ttt_shipment_method, ttt_partial_shipment, ttt_transhipment, ttt_packing, ttt_quality, ttt_order_condi, total_foreign_amount, order_purchase, order_date, quotes_note, shipping_time, payment_method, shipping_note, failure_cause, date_record, is_export_vat, accounting_category_type) VALUES ("e74ac1ea-1481-2eee-fb1b-669f645c4e6c", "2024-07-23 08:08:14", "2024-08-01 07:55:31", "627d3b02-1dea-cd40-ca0a-5f96342ef270", "627d3b02-1dea-cd40-ca0a-5f96342ef270", "1", "PREVIEW-SO-2408-0002", "", "627d3b02-1dea-cd40-ca0a-5f96342ef270", "2", "3", "1", "", "", "0", "0", "-99", "2024-08-01", "Cancel", "0", "0", "0", "", "2024-08-01", "Proposal", "1", "5", "0", "0", "", "0", "0", "", "-1", "0", "0", "0", "0", "1000000", "0", "0", "0", "0", "0", "0", "1000000", "-1000000", "1", "0", "", "", "", "0", "", "", "3", "0", "0", "0", "0", "", "", "0", "TM", "P2-096/097, PHNOM PENH SPECIAL SANGKAT BOEUNG THOMM KHAN", "", "0", "0", "11", "", "", "Packages", "", "", "", "", "", "", "", "ee901ddc-9ddb-884e-a360-65322ec9a19f", "0", "", "", "10.8197469,106.6381017", "-99", "1", "0", "", "0", "0", "0", "", "YTozOntzOjE0OiJvcmRlcl9kaXNjb3VudCI7ZDowO3M6MTY6InByb2R1Y3RfZGlzY291bnQiO2k6MDtzOjEwOiJwb2ludF91c2VkIjtpOjA7fQ==", "0", "0", "0", "0", "", "0", "", "", "", "", "", "0", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "Giá trên đã bao gồm VAT.\\r\\nBao gồm chi phí vận chuyển, lắp đặt tại TPHCM.\\r\\nBảng báo giá có giá trị trong vòng 30 ngày kề từ ngày báo.", "Chia thành nhiều đợt theo từng đơn đặt hàng của bên A Hàng có sẵn (Tối đa trong vòng 3-5 ngày theo từng đơn hàng của bên A)", "Bên A thanh toán cho Bên B bằng phương thức chuyển khoản hoặc tiền mặt 100% tổng giá trị hợp đồng tối đa sau 30 ngày kể từ khi Bên B giao đủ hàng, nghiệm thu, thanh lý và hóa đơn tài chính theo quy định cho Bên A.", "", "", "2024-08-01", "0", "0");',
  5 => 'INSERT INTO opportunities (id, date_entered, date_modified, modified_user_id, created_by, deleted, name, description, assigned_user_id, branch_id, department_id, opportunity_type, campaign_id, lead_source, amount, amount_usdollar, currency_id, date_closed, sales_stage, probability, priority, estimated_revenue, opportunity_scope, date_start_process, object, expected_output, actual_output, contract_revenue, actual_receipts, order_number, prod_lock, delivered_all, contact_name, vat_tax, vat_amount, vat_more_amt, vat_other_amt, discount_amount, cost_amount, comm_more_amount, incurred_amount, incurred_other, incurred_domestic, incurred_foreign, commission_amount, arise_cost_amount, profit_amount, rate_usd, is_locked, processing_stage, processing_content, amount_collected, amount_owed, handle_user_id, parent_id, payment_status, money_deposit, money_refund, is_target, account_targets, causes_failure, failure_notes, debt_order, payment_type, account_address, account_tax_code, shipping_fee, no_currency, account_phone, account_email, date_payment, order_type, quote_number, contract_number, contract_date, order_discount, payment_period, project_id, project_name, bank_account_id, disable_auto_price, quotation_date, quotation_version, location_nearby, rate_id, rate_foreign, service_total, service_used, service_left, vat_stock, ordered_merge, warranty_period, table_params, ignore_payment, re_calc_profit, is_auto_cpps, total_amount_in, date_return, prod_returned, vat_spec_amt, vat_imp_amt, vat_total_amount, ext_incurred_amount, int_incurred_amount, is_import, ttt_date_shipment, ttt_date_contract, ttt_date_declaration, ttt_date_storage, ttt_date_arrival, ttt_date_clearance, ttt_fee_amount, ttt_insurance_number, ttt_insurance_date, ttt_insurance_amount, ttt_insurance_extension, ttt_insurance_coop, ttt_date_lc_payment, ttt_lc_number, ttt_date_lc_expired, ttt_lc_amount, ttt_lc_reiss_amt, ttt_lc_repair_amt, ttt_lc_payment_amt, ttt_lc_bank_name, ttt_invoice_number, ttt_invoice_date, ttt_bol, ttt_bol_date, ttt_packinglist, ttt_co, ttt_milltest, ttt_dost, ttt_condi_trading, ttt_declaration, ttt_declaration_number, ttt_declaration_amount, ttt_vat_amount, ttt_verification, ttt_verification_amount, ttt_vat_import, ttt_customs_fee, ttt_clearance, ttt_arrival_notice, ttt_discharging_port, ttt_vessel, ttt_vessel_vn, ttt_owed_contract, ttt_storage_fee, ttt_vessel_fee, ttt_repair_cont_fee, ttt_deposit_cont_fee, ttt_expected_output, ttt_cnt_amount, ttt_foreign_amount, ttt_actual_output, ttt_commodity, ttt_tolerance, ttt_shipment_method, ttt_partial_shipment, ttt_transhipment, ttt_packing, ttt_quality, ttt_order_condi, total_foreign_amount, order_purchase, order_date, quotes_note, shipping_time, payment_method, shipping_note, failure_cause, date_record, is_export_vat, accounting_category_type) VALUES ("7ff8279c-5bd7-41a8-f83a-66a0a62af390", "2024-07-24 06:59:25", "2024-08-01 20:54:11", "1e1668f2-b0d4-5001-0a4d-4c8b539c7437", "1", "1", "SO-2407-0006", "", "1", "2", "3", "1", "", "", "477000", "0", "-99", "2024-07-24", "Quotes", "50", "0", "530000", "", "2024-07-24", "Proposal", "1", "10", "477000", "477000", "", "0", "0", "", "-1", "0", "0", "0", "53000", "1000000", "0", "0", "0", "0", "0", "0", "1053000", "-523000", "1", "0", "", "", "", "477000", "", "", "1", "0", "0", "0", "0", "", "", "0", "TM", "215 Norodom Boulevard, Sangkat tole Khan Chamkar Morn, 12301 Phnom Penh, Cambodia", "", "0", "0", "**********", "", "", "Packages", "BG-24-0049", "", "", "", "", "", "", "ee901ddc-9ddb-884e-a360-65322ec9a19f", "1", "", "", "10.8467678,106.6180898", "-99", "1", "0", "", "0", "0", "0", "", "YTozOntzOjE0OiJvcmRlcl9kaXNjb3VudCI7ZDowO3M6MTY6InByb2R1Y3RfZGlzY291bnQiO2Q6NTMwMDA7czoxMDoicG9pbnRfdXNlZCI7aTowO30=", "0", "0", "0", "0", "", "0", "", "", "", "", "", "0", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "Giá trên đã bao gồm VAT.\\r\\nBao gồm chi phí vận chuyển, lắp đặt tại TPHCM.\\r\\nBảng báo giá có giá trị trong vòng 30 ngày kề từ ngày báo.", "Chia thành nhiều đợt theo từng đơn đặt hàng của bên A Hàng có sẵn (Tối đa trong vòng 3-5 ngày theo từng đơn hàng của bên A)", "Bên A thanh toán cho Bên B bằng phương thức chuyển khoản hoặc tiền mặt 100% tổng giá trị hợp đồng tối đa sau 30 ngày kể từ khi Bên B giao đủ hàng, nghiệm thu, thanh lý và hóa đơn tài chính theo quy định cho Bên A.", "", "", "", "0", "0");',
  6 => 'INSERT INTO opportunities_tqt_products (id, deleted, date_modified, modified_user_id, opportunity_id, product_id, group_price_id, product_gift_id, ref_id, parent_id, parent_note, product_combo, warehouse_id, ordering, product_code, product_name, prod_note, prod_quantity, prod_price, prod_orig_price, prod_sale_price, prod_discount, prod_discount_amt, prod_promotion, prod_promotion_amt, prod_more_percent, prod_vat_tax, prod_vat_amount, prod_amount, prod_total_amount, prod_orig_amount, prod_profit_amount, product_unit, prod_unit_opt, prod_unit_qty, prod_unit_length, prod_unit_width, prod_unit_price, product_type, product_gift, prod_convert_kg, prod_total_kg, prod_convert_mt, prod_total_mt, product_lots, prod_lot_id, delivered_qty, missing_qty, prod_custom, prod_num_uses, prod_date_start, prod_repeat_cycle, handling_note, prod_foreign_price, prod_foreign_amount, prod_actual_price, prod_stock_price, prod_more_amount, prod_comm_amount, prod_comm_more_amt, prod_more_price, prod_comm_price, prod_more_ret_tax, prod_more_ret_amt, prod_more_receive_amt, prod_more_note, prod_01_comm_per, prod_01_comm_amt, prod_01_comm_ret_tax, prod_01_comm_ret_amt, prod_01_comm_receive_amt, prod_01_comm_note, prod_02_comm_per, prod_02_comm_amt, prod_02_comm_ret_tax, prod_02_comm_ret_amt, prod_02_comm_receive_amt, prod_02_comm_note, prod_tax_exempt, prod_per_cpps, prod_amount_cpps, prod_total_stock_price, prod_total_amount_in, have_warehoused, prod_ext_incur_amount, prod_value_vat_import, prod_vat_tax_import, prod_vat_amount_import, prod_value_vat_spec, prod_vat_tax_spec, prod_vat_amount_spec, prod_value_vat_amount, prod_int_incur_amount, product_primary, product_set, prod_orig2_price, prod_return_orig_price, prod_custom_unit_qty, prod_custom_unit_price) VALUES ("716e9c6b-9f78-8c09-c4a3-66a0a65a7c2b", "1", "2024-08-01 20:54:11", "1", "7ff8279c-5bd7-41a8-f83a-66a0a62af390", "b43bcf9b-deda-a0b5-96ff-65db3c1e12f1", "761b03bd-ee3d-85e5-3916-65f2ba1711a7", "", "", "", "", "", "2e5ac341-c6b6-0c24-34f2-55530a9c12bc", "1", "AOTBGAI", "Áo thun bé gái", "", "10", "53000", "100000", "47700", "10", "53000", "0", "0", "0", "0", "0", "477000", "477000", "1000000", "-523000", "Cái", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "", "", "0", "10", "", "0", "", "", "", "0", "0", "53000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "", "100000", "0", "0", "0");',
  7 => 'INSERT INTO opportunities_tqt_products (id, deleted, date_modified, modified_user_id, opportunity_id, product_id, group_price_id, product_gift_id, ref_id, parent_id, parent_note, product_combo, warehouse_id, ordering, product_code, product_name, prod_note, prod_quantity, prod_price, prod_orig_price, prod_sale_price, prod_discount, prod_discount_amt, prod_promotion, prod_promotion_amt, prod_more_percent, prod_vat_tax, prod_vat_amount, prod_amount, prod_total_amount, prod_orig_amount, prod_profit_amount, product_unit, prod_unit_opt, prod_unit_qty, prod_unit_length, prod_unit_width, prod_unit_price, product_type, product_gift, prod_convert_kg, prod_total_kg, prod_convert_mt, prod_total_mt, product_lots, prod_lot_id, delivered_qty, missing_qty, prod_custom, prod_num_uses, prod_date_start, prod_repeat_cycle, handling_note, prod_foreign_price, prod_foreign_amount, prod_actual_price, prod_stock_price, prod_more_amount, prod_comm_amount, prod_comm_more_amt, prod_more_price, prod_comm_price, prod_more_ret_tax, prod_more_ret_amt, prod_more_receive_amt, prod_more_note, prod_01_comm_per, prod_01_comm_amt, prod_01_comm_ret_tax, prod_01_comm_ret_amt, prod_01_comm_receive_amt, prod_01_comm_note, prod_02_comm_per, prod_02_comm_amt, prod_02_comm_ret_tax, prod_02_comm_ret_amt, prod_02_comm_receive_amt, prod_02_comm_note, prod_tax_exempt, prod_per_cpps, prod_amount_cpps, prod_total_stock_price, prod_total_amount_in, have_warehoused, prod_ext_incur_amount, prod_value_vat_import, prod_vat_tax_import, prod_vat_amount_import, prod_value_vat_spec, prod_vat_tax_spec, prod_vat_amount_spec, prod_value_vat_amount, prod_int_incur_amount, product_primary, product_set, prod_orig2_price, prod_return_orig_price, prod_custom_unit_qty, prod_custom_unit_price) VALUES ("5bbde3a6-8279-9acd-20d7-66ab3f636fdc", "1", "2024-08-01 07:55:31", "627d3b02-1dea-cd40-ca0a-5f96342ef270", "e74ac1ea-1481-2eee-fb1b-669f645c4e6c", "d28ee6ff-254b-e3c4-e5b9-65db3cd555cd", "723a071b-0f6f-3a25-6972-5ed0b62a11b4", "b1d2ee3d-cade-8a31-5c24-658cfc59d420", "", "", "", "", "2e5ac341-c6b6-0c24-34f2-55530a9c12bc", "1", "AOJEANNAM", "Áo jean nam", "", "5", "0", "200000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1000000", "-1000000", "Cái", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "", "", "0", "5", "", "0", "", "", "", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "", "200000", "0", "0", "0");',
  8 => 'INSERT INTO securitygroups_records (id, securitygroup_id, record_id, module, date_modified, deleted) VALUES ("72631772-b379-20ed-a5f1-66ab3fb85418", "1f28b2e6-69cb-1e48-631d-57dc08f22aed", "e74ac1ea-1481-2eee-fb1b-669f645c4e6c", "Opportunities", "2024-08-01 07:55:31", "1");',
  9 => 'INSERT INTO securitygroups_records (id, securitygroup_id, record_id, module, date_modified, deleted) VALUES ("263e205d-ba94-c2f1-3edc-669f640d39ce", "9a4fcc0e-c0d7-c39b-789a-5d5cda661ed2", "e74ac1ea-1481-2eee-fb1b-669f645c4e6c", "Opportunities", "2024-08-01 07:55:31", "1");',
  10 => 'INSERT INTO securitygroups_records (id, securitygroup_id, record_id, module, date_modified, deleted) VALUES ("a419c235-e60a-67ab-203e-669f6474ba16", "9a4fcc0e-c0d7-c39b-789a-5d5cda661ed2", "e74ac1ea-1481-2eee-fb1b-669f645c4e6c", "Opportunities", "2024-08-01 07:55:31", "1");',
  11 => 'INSERT INTO securitygroups_records (id, securitygroup_id, record_id, module, date_modified, deleted) VALUES ("7573b893-b014-5221-5827-66ab3f5b502e", "9a4fcc0e-c0d7-c39b-789a-5d5cda661ed2", "e74ac1ea-1481-2eee-fb1b-669f645c4e6c", "Opportunities", "2024-08-01 07:55:31", "1");',
  12 => 'INSERT INTO securitygroups_records (id, securitygroup_id, record_id, module, date_modified, deleted) VALUES ("dcb1de29-d4e2-11ec-b793-00163effd27c", "4c4e6142-1b04-e6f2-6f22-6281f1f498cd", "24ecb1bb-6876-7f84-a44a-5e8c8bb05acd", "Tasks", "2024-08-03 16:58:12", "1");',
  13 => 'INSERT INTO users_access_others (id, deleted, date_modified, user_id, record_id, access_type) VALUES ("23f1a443-0fde-5a18-333c-668ded86e5e5", "1", "2024-07-25 09:35:25", "36b84839-162f-0c5c-d652-59229c92215c", "dfd64c2f-adf4-9bf4-24a3-5d5c745cb796", "2");',
  14 => 'INSERT INTO users_departments (id, deleted, date_modified, department_id, user_id) VALUES ("ee32163c-6d5c-0aa8-772d-66a217ead038", "1", "2024-07-25 09:17:35", "432a58f6-4a05-542e-a9b3-577dd1762854", "dfd64c2f-adf4-9bf4-24a3-5d5c745cb796");',
  15 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("b64bb730-36b1-8b12-9be1-668b4575f317", "1", "2024-07-08 01:49:25", "36b84839-162f-0c5c-d652-59229c92215c", "GD2", "z5b48fc0500aba44f641002230b9928d7zaaf9cd6e194f7a5ecbd8fc31843b717cz");',
  16 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("36e6dcf5-bd64-81e8-5fa7-66963320890b", "1", "2024-07-16 08:48:36", "1", "admin", "z21232f297a57a5a743894a0e4a801fc3zffd45ede90b1dcae308670700fe393b1z");',
  17 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("c6993eb6-4bb9-8a91-7989-66a45f594838", "1", "2024-07-27 02:48:33", "1", "admin", "z21232f297a57a5a743894a0e4a801fc3zffd45ede90b1dcae308670700fe393b1z");',
  18 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("bd1f3304-15ff-3f4b-48cb-66ac96dd4969", "1", "2024-08-02 09:27:33", "1e1668f2-b0d4-5001-0a4d-4c8b539c7437", "thuyqt", "z2fa02e7499217f3478aca5fa3f389d3cz7eef66199cec2b1231fa4be4aa47d578z");',
  19 => 'INSERT INTO users_login_tokens (id, deleted, date_modified, user_id, user_name, auth_key) VALUES ("8aed03b7-f2d8-d621-8589-66aca87d9e70", "1", "2024-08-02 09:34:43", "1", "admin", "z21232f297a57a5a743894a0e4a801fc3zffd45ede90b1dcae308670700fe393b1z");',
  20 => 'INSERT INTO warehouse_out_in_related (id, deleted, date_modified, modified_user_id, date_perform, warehouse_in_id, warehouse_out_id, opportunity_id, product_lot_id, warehouse_id, product_id, product_stock_id, out_product_id, quantity, price, product_code, product_name, notes) VALUES ("87dfed49-1b85-b716-f1f2-66826fcc812b", "1", "2024-07-17 02:06:31", "1", "2024-07-01", "1ad94755-816b-b27e-fb00-663d944512c0", "7fa0b3f6-cbcc-01a9-115e-66826f99fd15", "5f150de9-b4dc-9e0d-ee96-66826fe30060", "3b27c962-5212-5f0b-e635-634f9caf591b", "2e5ac341-c6b6-0c24-34f2-55530a9c12bc", "d28ee6ff-254b-e3c4-e5b9-65db3cd555cd", "1dc52c71-2274-fe9e-f90d-663d9435ee7f", "dcbca88e-3787-11ef-923b-00163e3b7e12", "2", "200000", "", "", "Lấy lại hiệu chỉnh bởi: Administrator");',
  21 => 'INSERT INTO warehouse_out_in_related (id, deleted, date_modified, modified_user_id, date_perform, warehouse_in_id, warehouse_out_id, opportunity_id, product_lot_id, warehouse_id, product_id, product_stock_id, out_product_id, quantity, price, product_code, product_name, notes) VALUES ("ef8506cd-5c38-6fc8-2ba9-66a0c7bc3cb7", "1", "2024-07-24 09:22:57", "1", "2024-07-24", "2f1a57a2-6362-f160-f8e7-660f4c2418ed", "dc1c89fb-46dd-fe65-13dd-66a0c7443c9d", "8f8f4be2-ddfc-c0ce-2c78-66a0c7afe101", "", "2e5ac341-c6b6-0c24-34f2-55530a9c12bc", "af026a1a-eef4-a0b1-f808-65db3c325086", "30a0c3fc-5534-e62c-9e75-660f4cd98cfb", "33798cf8-499e-11ef-a902-00163e3b7e12", "10", "120000", "", "", "Lấy lại hiệu chỉnh bởi: Administrator");',
  22 => 'INSERT INTO warehouse_out_in_related (id, deleted, date_modified, modified_user_id, date_perform, warehouse_in_id, warehouse_out_id, opportunity_id, product_lot_id, warehouse_id, product_id, product_stock_id, out_product_id, quantity, price, product_code, product_name, notes) VALUES ("6bb68d31-f8b3-e081-9368-66a0c7cc8479", "1", "2024-07-26 04:08:45", "dfd64c2f-adf4-9bf4-24a3-5d5c745cb796", "2024-07-24", "2f1a57a2-6362-f160-f8e7-660f4c2418ed", "dc1c89fb-46dd-fe65-13dd-66a0c7443c9d", "8f8f4be2-ddfc-c0ce-2c78-66a0c7afe101", "", "2e5ac341-c6b6-0c24-34f2-55530a9c12bc", "af026a1a-eef4-a0b1-f808-65db3c325086", "30a0c3fc-5534-e62c-9e75-660f4cd98cfb", "5525826d-499e-11ef-a902-00163e3b7e12", "10", "120000", "", "", "Lấy lại hiệu chỉnh bởi: Lê Xuân Chinh");',
);
?>
