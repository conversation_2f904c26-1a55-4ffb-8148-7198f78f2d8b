<?php
// created: 2025-02-26 15:30:40
$GL<PERSON><PERSON>LS["dictionary"]["TQT_Comment"] = array (
  'table' => 'tqt_comments',
  'audited' => false,
  'duplicate_merge' => false,
  'unified_search' => false,
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'varchar',
      'len' => '200',
      'source' => 'non-db',
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
      'comment' => 'Full text of the note',
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'tqt_comments_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'tqt_comments_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'branch_id' => 
    array (
      'name' => 'branch_id',
      'vname' => 'LBL_BRANCH_ID',
      'type' => 'enum',
      'options' => 'users_branch_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'BranchID of assigned user',
    ),
    'department_id' => 
    array (
      'name' => 'department_id',
      'vname' => 'LBL_DEPARTMENT_ID',
      'type' => 'enum',
      'options' => 'users_department_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'DepartmentID of assigned user',
    ),
    'assigned_user_id' => 
    array (
      'name' => 'assigned_user_id',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'vname' => 'LBL_ASSIGNED_TO_ID',
      'group' => 'assigned_user_name',
      'type' => 'relate',
      'table' => 'users',
      'module' => 'Users',
      'isnull' => 'false',
      'dbType' => 'id',
      'audited' => true,
      'reportable' => true,
      'duplicate_merge' => 'disabled',
      'comment' => 'User ID assigned to record',
    ),
    'assigned_user_name' => 
    array (
      'name' => 'assigned_user_name',
      'link' => 'assigned_user_link',
      'vname' => 'LBL_ASSIGNED_TO_NAME',
      'rname' => 'user_name',
      'type' => 'relate',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'assigned_user_id',
      'module' => 'Users',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'assigned_user_link' => 
    array (
      'name' => 'assigned_user_link',
      'type' => 'link',
      'relationship' => 'tqt_comments_assigned_user',
      'vname' => 'LBL_ASSIGNED_TO_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
      'duplicate_merge' => 'enabled',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'table' => 'users',
    ),
    'rowindex' => 
    array (
      'name' => 'rowindex',
      'vname' => 'LBL_ROWINDEX',
      'type' => 'int',
      'len' => '5',
      'acl' => true,
    ),
    'parent_type' => 
    array (
      'name' => 'parent_type',
      'vname' => 'LBL_PARENT_TYPE',
      'type' => 'parent_type',
      'dbType' => 'varchar',
      'group' => 'parent_name',
      'len' => '50',
      'comment' => 'incomCRM module the Comment is associated with',
    ),
    'parent_id' => 
    array (
      'name' => 'parent_id',
      'vname' => 'LBL_PARENT_ID',
      'type' => 'id',
      'required' => false,
      'reportable' => true,
      'comment' => 'The ID of the incomCRM item specified in parent_type',
    ),
    'parent_name' => 
    array (
      'name' => 'parent_name',
      'parent_type' => 'record_type_display',
      'type_name' => 'parent_type',
      'id_name' => 'parent_id',
      'vname' => 'LBL_RELATED_TO',
      'type' => 'parent',
      'source' => 'non-db',
      'options' => 'record_type_display_notes',
      'massupdate' => false,
    ),
    'account_id' => 
    array (
      'name' => 'account_id',
      'vname' => 'LBL_ACCOUNT_ID',
      'type' => 'id',
      'reportable' => false,
      'source' => 'non-db',
    ),
    'accounts' => 
    array (
      'name' => 'accounts',
      'type' => 'link',
      'relationship' => 'accounts_tqt_comments',
      'vname' => 'LBL_ACCOUNTS',
      'source' => 'non-db',
    ),
    'task_id' => 
    array (
      'name' => 'task_id',
      'vname' => 'LBL_TASK_ID',
      'type' => 'id',
      'reportable' => false,
      'source' => 'non-db',
    ),
    'tasks' => 
    array (
      'name' => 'tasks',
      'type' => 'link',
      'relationship' => 'tasks_tqt_comments',
      'vname' => 'LBL_TASKS',
      'source' => 'non-db',
    ),
    'opportunities' => 
    array (
      'name' => 'opportunities',
      'type' => 'link',
      'relationship' => 'opportunities_tqt_comments',
      'vname' => 'LBL_OPPORTUNITIES',
      'source' => 'non-db',
    ),
    'tqt_announcement' => 
    array (
      'name' => 'tqt_announcement',
      'type' => 'link',
      'relationship' => 'tqt_announcement_tqt_comments',
      'vname' => 'LBL_TQT_ANNOUNCEMENT',
      'source' => 'non-db',
    ),
    'notes' => 
    array (
      'name' => 'notes',
      'type' => 'link',
      'relationship' => 'tqt_comments_notes',
      'module' => 'Notes',
      'bean_name' => 'Note',
      'source' => 'non-db',
      'vname' => 'LBL_NOTES',
    ),
    'is_urgent' => 
    array (
      'name' => 'is_urgent',
      'vname' => 'LBL_IS_URGENT',
      'type' => 'bool',
      'default' => 0,
    ),
    'revoked' => 
    array (
      'name' => 'revoked',
      'vname' => 'LBL_REVOKED',
      'type' => 'bool',
      'default' => 0,
    ),
    'removed' => 
    array (
      'name' => 'removed',
      'vname' => 'LBL_REMOVED',
      'type' => 'bool',
      'default' => 0,
    ),
    'reply_id' => 
    array (
      'name' => 'reply_id',
      'vname' => 'LBL_REPLY_ID',
      'type' => 'id',
      'reportable' => false,
    ),
    'users_all' => 
    array (
      'name' => 'users_all',
      'vname' => 'LBL_USERS_ALL',
      'type' => 'enum',
      'dbType' => 'text',
      'module' => 'Users',
      'function' => 'getShareUserOptions',
      'isMultiSelect' => true,
      'isCheckBoxList' => true,
      'displayGroupBy' => 'users.department',
      'massupdate' => false,
      'reportable' => false,
    ),
    'table_params' => 
    array (
      'name' => 'table_params',
      'vname' => 'Params',
      'type' => 'longtext',
    ),
    'SecurityGroups' => 
    array (
      'name' => 'SecurityGroups',
      'type' => 'link',
      'relationship' => 'securitygroups_tqt_comments',
      'module' => 'SecurityGroups',
      'bean_name' => 'SecurityGroup',
      'source' => 'non-db',
      'vname' => 'LBL_SECURITYGROUPS',
    ),
  ),
  'indices' => 
  array (
    'id' => 
    array (
      'name' => 'tqt_commentspk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    'idx_tqt_comments_branch_id' => 
    array (
      'name' => 'idx_tqt_comments_branch_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
      ),
    ),
    'idx_tqt_comments_department_id' => 
    array (
      'name' => 'idx_tqt_comments_department_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'department_id',
      ),
    ),
    'idx_tqt_comments_branch_dept' => 
    array (
      'name' => 'idx_tqt_comments_branch_dept',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
        1 => 'department_id',
      ),
    ),
    'idx_tqt_comments_assigned' => 
    array (
      'name' => 'idx_tqt_comments_assigned',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'assigned_user_id',
      ),
    ),
    0 => 
    array (
      'name' => 'idx_comment_id_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'id',
        1 => 'deleted',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_comment_del_pid',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'parent_id',
      ),
    ),
    2 => 
    array (
      'name' => 'idx_comment_del_ptype',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'parent_type',
      ),
    ),
    3 => 
    array (
      'name' => 'idx_comment_parent',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'parent_type',
        1 => 'parent_id',
      ),
    ),
    4 => 
    array (
      'name' => 'idx_comment_del_revoked',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'revoked',
      ),
    ),
    5 => 
    array (
      'name' => 'idx_comment_del_reply',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'reply_id',
      ),
    ),
    6 => 
    array (
      'name' => 'idx_comment_del_par_rev',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'parent_id',
        2 => 'revoked',
      ),
    ),
    7 => 
    array (
      'name' => 'idx_comment_del_par_reply',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'parent_id',
        2 => 'reply_id',
      ),
    ),
  ),
  'relationships' => 
  array (
    'tqt_comments_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_Comments',
      'rhs_table' => 'tqt_comments',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'tqt_comments_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_Comments',
      'rhs_table' => 'tqt_comments',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
    'tqt_comments_assigned_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_Comments',
      'rhs_table' => 'tqt_comments',
      'rhs_key' => 'assigned_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'accounts_tqt_comments' => 
    array (
      'lhs_module' => 'Accounts',
      'lhs_table' => 'accounts',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_Comments',
      'rhs_table' => 'tqt_comments',
      'rhs_key' => 'parent_id',
      'relationship_role_column' => 'parent_type',
      'relationship_role_column_value' => 'Accounts',
      'relationship_type' => 'one-to-many',
    ),
    'tasks_tqt_comments' => 
    array (
      'lhs_module' => 'Tasks',
      'lhs_table' => 'tasks',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_Comments',
      'rhs_table' => 'tqt_comments',
      'rhs_key' => 'parent_id',
      'relationship_role_column' => 'parent_type',
      'relationship_role_column_value' => 'Tasks',
      'relationship_type' => 'one-to-many',
    ),
    'tqt_comments_notes' => 
    array (
      'lhs_module' => 'TQT_Comments',
      'lhs_table' => 'tqt_comments',
      'lhs_key' => 'id',
      'rhs_module' => 'Notes',
      'rhs_table' => 'notes',
      'rhs_key' => 'parent_id',
      'relationship_type' => 'one-to-many',
      'relationship_role_column' => 'parent_type',
      'relationship_role_column_value' => 'TQT_Comments',
    ),
    'opportunities_tqt_comments' => 
    array (
      'lhs_module' => 'Opportunities',
      'lhs_table' => 'opportunities',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_Comments',
      'rhs_table' => 'tqt_comments',
      'rhs_key' => 'parent_id',
      'relationship_role_column' => 'parent_type',
      'relationship_role_column_value' => 'Opportunities',
      'relationship_type' => 'one-to-many',
    ),
    'tqt_announcement_tqt_comments' => 
    array (
      'lhs_module' => 'TQT_Announcement',
      'lhs_table' => 'tqt_announcement',
      'lhs_key' => 'id',
      'rhs_module' => 'TQT_Comments',
      'rhs_table' => 'tqt_comments',
      'rhs_key' => 'parent_id',
      'relationship_role_column' => 'parent_type',
      'relationship_role_column_value' => 'TQT_Announcement',
      'relationship_type' => 'one-to-many',
    ),
  ),
  'optimistic_lock' => true,
  'templates' => 
  array (
    'assignable' => 'assignable',
    'groups' => 'groups',
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
