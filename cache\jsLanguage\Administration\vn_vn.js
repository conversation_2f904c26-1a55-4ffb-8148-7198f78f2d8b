incomCRM.language.setLanguage('Administration', {"LBL_CAPTCHA_HELP_TEXT":"Captcha is a challenge-response test used to ensure that the response is not generated by a computer. Obtain a Public key and a Private key from reCAPTCHA at http:\/\/recaptcha.net\/.","CAPTCHA":"Captcha Validation","CAPTCHA_PRIVATE_KEY":"Captcha private Key","CAPTCHA_PUBLIC_KEY":"Captcha public Key","ENABLE_CAPTCHA":"Enable reCAPTCHA Validations","ERR_PUBLIC_CAPTCHA_KEY":"Invalid Captcha Key","BTN_REBUILD_CONFIG":"Bi\u00ean d\u1ecbch l\u1ea1i","EXPORT_DELIMITER":"D\u1ea5u ph\u00e2n c\u00e1ch","ADMIN_EXPORT_ONLY":"Ch\u1ec9 ng\u01b0\u1eddi qu\u1ea3n tr\u1ecb \u0111\u01b0\u1ee3c tr\u00edch xu\u1ea5t","EXPORT":"Tr\u00edch xu\u1ea5t c\u1ea5u h\u00ecnh","EXPORT_CHARSET":"Character Set tr\u00edch xu\u1ea5t m\u1eb7c \u0111\u1ecbnh","DISABLE_EXPORT":"Kh\u00f4ng cho ph\u00e9p tr\u00edch xu\u1ea5t","DESC_DROPDOWN_EDITOR":"Th\u00eam, x\u00f3a, ho\u1eb7c thay \u0111\u1ed5i H\u1ed9p th\u1ea3 xu\u1ed1ng trong \u1ee9ng d\u1ee5ng","DESC_EDIT_CUSTOM_FIELDS":"T\u00f9y ch\u1ec9nh c\u00e1c tr\u01b0\u1eddng t\u00f9y bi\u1ebfn \u0111\u01b0\u1ee3c t\u1ea1o trong Giao di\u1ec7n c\u00e1c thu\u1ed9c t\u00ednh","DESC_FILES_INSTALLED":"C\u00e1c n\u00e2ng c\u1ea5p k\u00e8m theo \u0111\u00e3 \u0111\u01b0\u1ee3c c\u00e0i \u0111\u1eb7t","DESC_FILES_QUEUED":"C\u00e1c ph\u00e2n h\u1ec7 k\u00e8m theo \u0111\u00e3 s\u1eb5n s\u00e0ng \u0111\u1ec3 c\u00e0i \u0111\u1eb7t","DESC_IFRAME":"Th\u00eam c\u00e1c th\u1ebb \u0111\u01b0\u1ee3c hi\u1ec3n th\u1ecb","DESC_MODULES_INSTALLED":"C\u00e1c ph\u00e2n h\u1ec7 k\u00e8m theo \u0111\u00e3 \u0111\u01b0\u1ee3c c\u00e0i \u0111\u1eb7t","DOWNLOAD_QUESTION":"B\u1ea1n ch\u1eafc ch\u1eafn mu\u1ed1n t\u1ea3i v\u1ec1 c\u00e1c g\u00f3i \u0111\u01b0\u1ee3c ch\u1ecdn?","ENABLED_OFFLINE_CLIENTS":"Cho ph\u00e9p c\u00e1c M\u00e1y tr\u1ea1m Ngo\u1ea1i tuy\u1ebfn","SEARCHING_UPDATES":"\u0110ang t\u00ecm c\u00e1c phi\u00ean b\u1ea3n c\u1eadp nh\u1eadt...","DOWNLOADING":"\u0110ang t\u1ea3i v\u1ec1...","DL_PACKAGES_DOWNLOADING":"\u0110ang t\u1ea3i v\u1ec1","DL_PACKAGES_OF":"c\u1ee7a","DL_PACKAGES_PACKAGES":"(C\u00e1c) g\u00f3i","LOADING_CATEGORIES":"\u0110ang \u0111\u1ecdc c\u00e1c danh m\u1ee5c...","SEARCHING_PACKAGES":"\u0110ang t\u00ecm c\u00e1c g\u00f3i...","AUTHENTICATING":"\u0110ang ph\u00e2n quy\u1ec1n...","LBL_MINUTES":"Ph\u00fat","LBL_HOURS":"Gi\u1edd","LBL_DAYS":"Ng\u00e0y","LBL_WEEKS":"Tu\u1ea7n","LBL_MONTHS":"Th\u00e1ng","LBL_PUBLIC_KEY":"Public Key","LBL_PRIVATE_KEY":"Private Key","ERR_CANNOT_CREATE_RESTORE_FILE":"L\u1ed7i: Kh\u00f4ng th\u1ec3 t\u1ea1o t\u1eadp tin ph\u1ee5c h\u1ed3i","ERR_CREDENTIALS_MISSING":"Ch\u1ee9ng nh\u1eadn v\u1edbi incomCRM.com c\u1ee7a b\u1ea1n kh\u00f4ng \u0111\u00fang","ERR_DELETE_RECORD":"Ph\u1ea3i ch\u1ec9 r\u00f5 t\u00e0i kho\u1ea3n c\u1ea7n x\u00f3a.","ERR_ENABLE_CURL":"Vui l\u00f2ng ch\u1eafc ch\u1eafn b\u1ea1n \u0111\u01b0\u1ee3c ph\u00e9p thay \u0111\u1ed5i.","ERR_EXPAND_DATABASE_COMPLETED":"L\u1ed7i: T\u1eadp tin restoreExpand.sql ch\u1ee9ng t\u1ecf r\u1eb1ng d\u1eef li\u1ec7u \u0111\u00e3 c\u00f3 s\u1eb5n.","ERR_INCORRECT_REGEX":"The regular expression in the Regex Requirement field contains incorrect syntax.  Please check the expression and provide the correct syntax","ERR_EMPTY_REGEX_DESCRIPTION":"Provide a description of the Regex Requirement.  This description will be displayed for users when they provide new passwords.","ERR_NO_COLUMNS_TO_EXPAND":"L\u1ed7i: D\u1eef li\u1ec7u kh\u00f4ng ch\u1ee9a n\u1ed9i dung c\u1ea7n thi\u1ebft.","ERR_NOT_FOR_MSSQL":"T\u00ednh n\u0103ng n\u00e0y ch\u01b0a \u0111\u01b0\u1ee3c th\u1ef1c hi\u1ec7n ch c\u1ea5u h\u00ecnh n\u00e0y.","ERR_NOT_FOR_MYSQL":"T\u00ednh n\u0103ng n\u00e0y ch\u01b0a \u0111\u01b0\u1ee3c th\u1ef1c hi\u1ec7n ch c\u1ea5u h\u00ecnh n\u00e0y.","ERR_NOT_FOR_ORACLE":"T\u00ednh n\u0103ng n\u00e0y ch\u01b0a \u0111\u01b0\u1ee3c th\u1ef1c hi\u1ec7n ch c\u1ea5u h\u00ecnh n\u00e0y.","ERR_NUM_OFFLINE_CLIENTS_MET":"B\u1ea1n \u0111\u00e3 d\u00f9ng h\u1ebft s\u1ed1 l\u01b0\u1ee3ng cho ph\u00e9p theo b\u1ea3n quy\u1ec1n \u0111\u01b0\u1ee3c c\u1ea5p. B\u1ea1n ph\u1ea3i t\u1eaft b\u1edbt s\u1ed1 m\u00e1y tr\u1ea1m ho\u1eb7c n\u00e2ng c\u1ea5p b\u1ea3n quy\u1ec1n.","ERR_OC_USER_ALREADY_EXISTS":"Ng\u01b0\u1eddi d\u00f9ng b\u1ea1n ch\u1ecdn \u0111\u00e3 c\u00f3 s\u1eb5n trong h\u1ec7 th\u1ed1ng.","ERR_incomCRM_DEPOT_DOWN":"H\u1ec7 th\u1ed1ng kh\u00f4ng th\u1ec3 k\u1ebft n\u1ed1i v\u00e0o CRM Exchange \u0111\u1ec3 t\u00ecm ki\u1ebfm v\u00e0 t\u1ea3i v\u1ec1 c\u00e1c g\u00f3i.","ERR_SMTP_SERVER_NOT_SET":"Warning: An SMTP server for outbound emails is not configured in Email Settings. It must be configured in order to send passwords to users.","ERR_UW_ACCEPT_LICENSE":"Tr\u01b0\u1edbc khi th\u1ef1c hi\u1ec7n b\u1ea1n ph\u1ea3i ch\u1ea5p nh\u1eadn H\u1ee3p \u0111\u1ed3ng b\u1ea3n quy\u1ec1n.","ERR_UW_CONFIG_FAILED":"L\u1ed7i ghi v\u00e0o t\u1eadp tin config.php","ERR_UW_COPY_FAILED":"Kh\u00f4ng th\u1ec3 sao ch\u00e9p t\u1eadp tin.","ERR_UW_INVALID_VIEW":"Kh\u00f4ng th\u1ec3 xem.","ERR_UW_MUST_SELECT_OVERWRITE_OPTION":"Vui l\u00f2ng ch\u1ecdn ghi \u0111\u00e8","ERR_UW_NO_DEPENDENCY":"C\u00e1c th\u00e0nh ph\u1ea7n k\u00e8m theo kh\u00f4ng c\u00f3 trong h\u1ec7 th\u1ed1ng","ERR_UW_NO_FILES":"Ch\u01b0a ch\u1ec9 r\u00f5 t\u1eadp tin c\u1ea7n sao ch\u00e9p.","ERR_UW_NO_INSTALL_FILE":"Ch\u01b0a ch\u1ec9 r\u00f5 t\u1eadp tin c\u00e0i \u0111\u1eb7t.","ERR_UW_NO_LANG_DESC":"Ch\u01b0a ch\u1ec9 r\u00f5 ng\u00f4n ng\u1eef m\u00f4 t\u1ea3.","ERR_UW_NO_LANG":"Ch\u01b0a ch\u1ec9 r\u00f5 t\u00ean ng\u00f4n ng\u1eef.","ERR_UW_NO_MANIFEST":"Thi\u1ebfu t\u1eadp tin manifest.php trong t\u1eadp tin zip. Kh\u00f4ng th\u1ec3 th\u1ef1c hi\u1ec7n.","ERR_UW_NO_MODE":"Ch\u01b0a ch\u1ec9 r\u00f5 ph\u01b0\u01a1ng th\u1ee9c th\u1ef1c hi\u1ec7n.","ERR_UW_NO_TEMP_DIR":"Ch\u01b0a ch\u1ec9 r\u00f5 th\u01b0 m\u1ee5c sao ch\u00e9p t\u1ea1m.","ERR_UW_NO_UPDATE_RECORD":"\u0110\u01b0\u1eddng d\u1eabn c\u00e0i \u0111\u1eb7t kh\u00f4ng \u0111\u00fang","ERR_UW_NO_UPLOAD_FILE":"Vui l\u00f2ng nh\u1ea5n Duy\u1ec7t \u0111\u1ec3 ch\u1ecdn t\u1eadp tin t\u1eeb h\u1ec7 th\u1ed1ng \u0111\u1ec3 t\u1ea3i l\u00ean.","ERR_UW_NO_VIEW":"Vui l\u00f2ng \u0111\u1ebfn trang qu\u1ea3n tr\u1ecb \u0111\u1ec3 duy\u1ec7t \u0111\u1ebfn trang n\u00e0y.","ERR_UW_NOT_ACCEPTIBLE_TYPE":"B\u1ea1n c\u00f3 th\u1ec3 t\u1ea3i l\u00ean c\u00e1c g\u00f3i nh\u01b0 ph\u00e2n h\u1ec7, giao di\u1ec7n, ng\u00f4n ng\u1eef trong trang n\u00e0y.","ERR_UW_NOT_RECOGNIZED":"kh\u00f4ng nh\u1eadn d\u1ea1ng \u0111\u01b0\u1ee3c","ERR_UW_NOT_VALID_UPLOAD":"T\u1ea3i l\u00ean kh\u00f4ng h\u1ee3p l\u1ec7.","ERR_UW_ONLY_PATCHES":"B\u1ea1n ch\u1ec9 c\u00f3 th\u1ec3 t\u1ea3i l\u00ean c\u00e1c b\u1ea3n v\u00e1 trong trang n\u00e0y.","ERR_UW_REMOVE_FAILED":"Kh\u00f4ng th\u1ec3 lo\u1ea1i b\u1ecf t\u1eadp tin.","ERR_UW_REMOVE_PACKAGE":"L\u1ed7i khi \u0111ang lo\u1ea1i b\u1ecf g\u00f3i","ERR_UW_RUN_SQL":"L\u1ed7i khi \u0111ang th\u1ef1c thi t\u1eadp tin sql:","ERR_UW_UPDATE_CONFIG":"L\u1ed7i khi \u0111ang c\u1eadp nh\u1eadt t\u1eadp tin config.php v\u1edbi th\u00f4ng tin phi\u00ean b\u1ea3n m\u1edbi","ERR_UW_UPLOAD_ERROR":"Vi\u1ec7c t\u1ea3i t\u1eadp tin l\u00ean b\u1ecb l\u1ed7i, vui l\u00f2ng th\u1ef1c hi\u1ec7n l\u1ea1i!","ERROR_FLAVOR_INCOMPATIBLE":"T\u1eadp tin \u0111\u01b0\u1ee3c t\u1ea3i l\u00ean kh\u00f4ng t\u01b0\u01a1ng th\u00edch v\u1edbi phi\u00ean b\u1ea3n CRM hi\u1ec7n t\u1ea1i.","ERROR_LICENSE_EXPIRED":"L\u1ed7i: b\u1ea1n \u0111\u00e3 h\u1ebft h\u1ea1n b\u1ea3n quy\u1ec1n.","ERROR_LICENSE_EXPIRED2":"ng\u00e0y c\u00e1ch \u0111\u00e2y. Vui l\u00f2ng v\u00e0o \"Qu\u1ea3n l\u00fd b\u1ea3n quy\u1ec1n\" trong m\u00e0n h\u00ecnh qu\u1ea3n tr\u1ecb \u0111\u1ec3 nh\u1eadp M\u00e3 s\u1ed1 b\u1ea3n quy\u1ec1n m\u1edbi. Ng\u01b0\u1ee3c l\u1ea1i, sau 30 ng\u00e0y b\u1ea1n s\u1ebd kh\u00f4ng th\u1ec3 \u0111ang nh\u1eadp \u1ee9ng d\u1ee5ng.","ERROR_MANIFEST_TYPE":"trong t\u1eadp tin Manifest ph\u1ea3i ch\u1ec9 r\u00f5 lo\u1ea1i g\u00f3i","ERROR_PACKAGE_TYPE":"lo\u1ea1i g\u00f3i trong t\u1eadp tin Manifest kh\u00f4ng c\u00f3","ERROR_VALIDATION_EXPIRED":"L\u1ed7i: \u0110\u00e3 h\u1ebft h\u1ea1n.","ERROR_VALIDATION_EXPIRED2":"ng\u00e0y c\u00e1ch \u0111\u00e2y. Vui l\u00f2ng v\u00e0o \"Qu\u1ea3n l\u00fd b\u1ea3n quy\u1ec1n\" trong m\u00e0n h\u00ecnh qu\u1ea3n tr\u1ecb \u0111\u1ec3 nh\u1eadp M\u00e3 s\u1ed1 b\u1ea3n quy\u1ec1n m\u1edbi. Ng\u01b0\u1ee3c l\u1ea1i, sau 30 ng\u00e0y b\u1ea1n s\u1ebd kh\u00f4ng th\u1ec3 \u0111ang nh\u1eadp \u1ee9ng d\u1ee5ng.","ERROR_VERSION_INCOMPATIBLE":"T\u1eadp tin \u0111\u01b0\u1ee3c t\u1ea3i l\u00ean kh\u00f4ng t\u01b0\u01a1ng th\u00edch v\u1edbi phi\u00ean b\u1ea3n CRM hi\u1ec7n t\u1ea1i.","FATAL_LICENSE_ALTERED":"B\u1ea3n quy\u1ec1n \u0111\u01b0\u1ee3c b\u1ed5 sung. Vui l\u00f2ng xem \"Qu\u1ea3n l\u00fd b\u1ea3n quy\u1ec1n\" trong m\u00e0n h\u00ecnh qu\u1ea3n tr\u1ecb","FATAL_LICENSE_EXPIRED":"L\u1ed7i: B\u1ea3n quy\u1ec1n c\u1ee7a b\u1ea1n \u0111\u00e3 h\u1ebft h\u1ea1n h\u01a1n 30 ng\u00e0y","FATAL_LICENSE_EXPIRED2":"Vui l\u00f2ng v\u00e0o ph\u1ea7n \"Qu\u1ea3n l\u00fd b\u1ea3n quy\u1ec1n\" trong trang qu\u1ea3n tr\u1ecb \u0111\u1ec3 c\u1eadp nh\u1eadt th\u00f4ng tin b\u1ea3n quy\u1ec1n v\u00e0 ph\u1ee5c h\u1ed3i \u0111\u1ea5y \u0111\u1ee7 t\u00ednh n\u0103ng.","FATAL_LICENSE_REQUIRED":"L\u1ed7i: Vui l\u00f2ng v\u00e0o ph\u1ea7n \"Qu\u1ea3n l\u00fd b\u1ea3n quy\u1ec1n\" trong trang qu\u1ea3n tr\u1ecb \u0111\u1ec3 c\u1eadp nh\u1eadt th\u00f4ng tin b\u1ea3n quy\u1ec1n v\u00e0 ph\u1ee5c h\u1ed3i \u0111\u1ea5y \u0111\u1ee7 t\u00ednh n\u0103ng.","FATAL_VALIDATION_EXPIRED":"L\u1ed7i: B\u1ea3n quy\u1ec1n c\u1ee7a b\u1ea1n \u0111\u00e3 h\u1ebft h\u1ea1n h\u01a1n 30 ng\u00e0y","FATAL_VALIDATION_EXPIRED2":"Vui l\u00f2ng v\u00e0o ph\u1ea7n \"Qu\u1ea3n l\u00fd b\u1ea3n quy\u1ec1n\" trong trang qu\u1ea3n tr\u1ecb \u0111\u1ec3 c\u1eadp nh\u1eadt th\u00f4ng tin b\u1ea3n quy\u1ec1n v\u00e0 ph\u1ee5c h\u1ed3i \u0111\u1ea5y \u0111\u1ee7 t\u00ednh n\u0103ng.","FATAL_VALIDATION_REQUIRED":"Vui l\u00f2ng v\u00e0o ph\u1ea7n \"Qu\u1ea3n l\u00fd b\u1ea3n quy\u1ec1n\" trong trang qu\u1ea3n tr\u1ecb \u0111\u1ec3 c\u1eadp nh\u1eadt th\u00f4ng tin b\u1ea3n quy\u1ec1n v\u00e0 ph\u1ee5c h\u1ed3i \u0111\u1ea5y \u0111\u1ee7 t\u00ednh n\u0103ng.","HDR_LOGIN_PANEL":"Vui l\u00f2ng nh\u1eadp x\u00e1c nh\u1eadn CRM c\u1ee7a b\u1ea1n.","HEARTBEAT_MESSAGE":"CRM Updates cho ph\u00e9p m\u00e1y ch\u1ee7 c\u1ee7a b\u1ea1n c\u00f3 th\u1ec3 ki\u1ec3m tra phi\u00ean b\u1ea3n b\u1ea3n c\u1eadp nh\u1eadt ph\u00f9 h\u1ee3p \u0111\u00e3 c\u00f3 hay ch\u01b0a.","LBL_ACCEPT_TERMS":"Ch\u1ea5p nh\u1eadn c\u00e1c \u0111i\u1ec1u kho\u1ea3n","LBL_ACCEPT":"Ch\u1ea5p nh\u1eadn","LBL_ADMIN_incomCRM_SERVER":"T\u00ean Ng\u01b0\u1eddi d\u00f9ng qu\u1ea3n tr\u1ecb:","LBL_ADMINISTRATION_HOME_TITLE":"H\u1ec7 th\u1ed1ng","LBL_ADMINISTRATION_HOME_DESC":"[]","LBL_ALLOW_USER_TABS":"Cho ph\u00e9p ng\u01b0\u1eddi d\u00f9ng c\u1ea5u h\u00ecnh c\u00e1c th\u1ebb","LBL_ALREADY_RUNNING":"M\u00e1y ch\u1ee7 \u0111ang ho\u1ea1t \u0111\u1ed9ng nh\u01b0 m\u1ed9t M\u00e1y kh\u00e1ch Ngo\u1ea1i tuy\u1ebfn","LBL_APPLY_DST_FIX_DESC":"Ch\u1ec9 MYSQL","LBL_APPLY_DST_FIX":"\u00c1p d\u1ee5ng ch\u1ec9nh th\u1eddi gian theo m\u00f9a","LBL_AVAILABLE_MODULES":"Ph\u00e2n h\u1ec7 c\u00f3 th\u1ec3 t\u1ea3i v\u1ec1","LBL_AVAILABLE_UPDATES":"C\u00e1c c\u1eadp nh\u1eadt s\u1eb5n c\u00f3","LBL_BACKUP_BACK_HOME":"Quay l\u1ea1i trang ch\u1ee7 qu\u1ea3n tr\u1ecb","LBL_BACKUP_CONFIRM":"X\u00e1c nh\u1eadn c\u00e1c thi\u1ebft l\u1eadp","LBL_BACKUP_CONFIRMED":"Thi\u1ebft l\u1eadp \u0111\u01b0\u1ee3c x\u00e1c nh\u1eadn. Ch\u1ecdn Sao l\u01b0u \u0111\u1ec3 th\u1ef1c hi\u1ec7n.","LBL_BACKUP_DIRECTORY_ERROR":"B\u1ea1n ph\u1ea3i ch\u1ec9 r\u00f5 th\u01b0 m\u1ee5c sao l\u01b0u.","LBL_BACKUP_DIRECTORY_EXISTS":"B\u1ea1n ph\u1ea3i t\u1ea1o th\u01b0 m\u1ee5c sao l\u01b0u.","LBL_BACKUP_DIRECTORY_NOT_WRITABLE":"Kh\u00f4ng th\u1ec3 l\u01b0u v\u00e0o th\u01b0 m\u1ee5c sao l\u01b0u.","LBL_BACKUP_DIRECTORY_WRITABLE":"Ch\u1ec9 CRM c\u00f3 th\u1ec3 ghi.","LBL_BACKUP_DIRECTORY":"Th\u01b0 m\u1ee5c:","LBL_BACKUP_FILE_AS_SUB":"T\u00ean t\u1eadp tin tr\u00f9ng v\u1edbi t\u00ean th\u01b0 m\u1ee5c con trong th\u01b0 m\u1ee5c sao l\u01b0u","LBL_BACKUP_FILE_EXISTS":"T\u1eadp tin c\u00f3 s\u1eb5n trong th\u01b0 m\u1ee5c","LBL_BACKUP_FILE_STORED":"Vi\u1ec7c sao l\u01b0u ho\u00e0n t\u1ea5t v\u00e0 \u0111\u01b0\u1ee3c l\u01b0u trong","LBL_BACKUP_FILENAME_ERROR":"Ph\u1ea3i ch\u1ec9 r\u00f5 t\u00ean t\u1eadp tin sao l\u01b0u","LBL_BACKUP_FILENAME":"T\u00ean t\u1eadp tin:","LBL_BACKUP_INSTRUCTIONS_1":"C\u00f4ng c\u1ee5 n\u00e0y gi\u00fap t\u1ea1o c\u00e1c sao l\u01b0u cho \u1ee9ng d\u1ee5ng CRM.","LBL_BACKUP_INSTRUCTIONS_2":"\u0110\u1ec3 sao l\u01b0u c\u00e1c t\u1eadp tin \u1ee9ng d\u1ee5ng c\u1ee7a CRM v\u00e0 1 t\u1eadp tin zip, nh\u1eadp th\u00f4ng tin theo","LBL_BACKUP_RUN_BACKUP":"Th\u1ef1c hi\u1ec7n Sao l\u01b0u","LBL_BACKUP_TITLE":"Sao l\u01b0u tr\u1ef1c tuy\u1ebfn","LBL_BACKUP":"L\u1eadp l\u1ecbch sao l\u01b0u cho CRM Online Data Vault. ","LBL_BACKUPS_TITLE":"Sao l\u01b0u","LBL_BACKUPS":"Th\u1ef1c hi\u1ec7n 1 sao l\u01b0u","LBL_BROWSE":"Duy\u1ec7t","LBL_BUG_TITLE":"Theo d\u00f5i l\u1ed7i","LBL_BUG_DESC":"...","LBL_CANCEL_BUTTON_TITLE":"H\u1ee7y","LBL_CAT_VIEW":"Danh m\u1ee5c","LBL_CHANGE_NAME_TABS":"\u0110\u1ed5i t\u00ean c\u00e1c th\u1ebb","LBL_CHECK_FOR_UPDATES":"Ki\u1ec3m tra c\u00e1c phi\u00ean b\u1ea3n n\u00e2ng c\u1ea5p","LBL_CHECK_NOW_LABEL":"Ki\u1ec3m tra ngay","LBL_CHECK_NOW_TITLE":"Ki\u1ec3m tra ngay","LBL_CHECK_DB_VARDEFS":"\u0110ang ki\u1ec3m tra c\u1ea5u tr\u00fac c\u01a1 s\u1edf d\u1eef li\u1ec7u...","LBL_CHOOSE_WHICH":"Ch\u1ecdn c\u00e1c th\u1ebb hi\u1ec3n th\u1ecb trong Thanh n\u1eb1m ngang c\u1ee7a h\u1ec7 th\u1ed1ng","LBL_CHOOSE_WHICH_SUBS":"Choose which subpanels are displayed system-wide","LBL_CLEAR_CHART_DATA_CACHE_DESC":"Lo\u1ea1i c\u00e1c d\u1eef li\u1ec7u l\u01b0u t\u1ea1m cho bi\u1ec3u \u0111\u1ed3...","LBL_CLEAR_CHART_DATA_CACHE_TITLE":"D\u1ecdn d\u1eef li\u1ec7u l\u01b0u t\u1ea1m cho bi\u1ec3u \u0111\u1ed3...","LBL_CLEAR_CHART_DATA_CACHE_FINDING":"\u0110ang t\u00ecm t\u1eadp tin...","LBL_CLEAR_CHART_DATA_CACHE_DELETING1":"\u0110ang x\u00f3a d\u1eef li\u1ec7u l\u01b0u t\u1ea1m cho bi\u1ec3u \u0111\u1ed3...","LBL_CLEAR_CHART_DATA_CACHE_DELETING2":"\u0111ang x\u00f3a","LBL_CLEAR_THEME_CACHE_DESC":"Removes cached data files used by themes","LBL_CLEAR_THEME_CACHE_TITLE":"Clear Theme Cache","LBL_CLEAR_UNIFIED_SEARCH_CACHE_DELETING1":"Deleting unified search cache files...","LBL_CLEAR_UNIFIED_SEARCH_CACHE_DELETING2":"deleting:","LBL_CLEAR_PDFFONTS_DESC":"Removing PDF Font Cache File, will rebuild when needed.","LBL_CLEAR_PDFFONTS_DESC_SUCCESS":"Success : PDF Font Cache File deleted","LBL_CLEAR_PDFFONTS_DESC_FAILURE":"Error : Clearing PDF Font Cache File failed","LBL_CLEAR_PDF_FONT_CACHE_TITLE":"Clear PDF Font File Cache","LBL_CLEAR_PDF_FONT_CACHE_DESC":"Removes cached file used to store PDF fonts data","LBL_CONFIG_CHECK":"C\u1ea5u h\u00ecnh ki\u1ec3m tra","LBL_CONFIG_TABS":"K\u00e9o v\u00e0o th\u1ea3 \u0111\u1ec3 thi\u1ebft l\u1eadp thu\u1ed9c t\u00ednh \u1ea9n ho\u1eb7c hi\u1ec7n. B\u1ecf ch\u1ecdn \"Cho ph\u00e9p ng\u01b0\u1eddi d\u00f9ng c\u1ea5u h\u00ecnh th\u1ebb\" n\u1ebfu b\u1ea1n kh\u00f4ng mu\u1ed1n ng\u01b0\u1eddi d\u00f9ng kh\u00e1c (kh\u00f4ng ph\u1ea3i ng\u01b0\u1eddi qu\u1ea3n tr\u1ecb) c\u1ea5u h\u00ecnh c\u00e1c th\u1ebb.","LBL_CONFIG_SUBPANELS":"Drag and Drop the names of the modules below to set their subpanels as either displayed or hidden.","LBL_CONFIGURATOR_DESC":"Thi\u1ebft l\u1eadp Config.php","LBL_CONFIGURATOR_TITLE":"B\u1ed9 c\u1ea5u h\u00ecnh","LBL_CONFIGURE_GROUP_TABS_DESC":"T\u1ea1o v\u00e0 t\u00f9y ch\u1ec9nh c\u00e1c nh\u00f3m th\u1ebb","LBL_CONFIGURE_GROUP_TABS":"C\u1ea5u h\u00ecnh c\u00e1c Nh\u00f3m th\u1ebb","LBL_CONFIGURE_SETTINGS_TITLE":"Thi\u1ebft l\u1eadp h\u1ec7 th\u1ed1ng","LBL_CONFIGURE_SETTINGS":"C\u1ea5u h\u00ecnh c\u00e1c thi\u1ebft l\u1eadp c\u1ee7a Thanh n\u1eb1m ngang","LBL_CONFIGURE_TABS":"C\u1ea5u h\u00ecnh c\u00e1c Th\u1ebb","LBL_CONFIGURE_SUBPANELS":"Configure Subpanels","LBL_CONFIGURE_UPDATER":"C\u1ea5u h\u00ecnh vi\u1ec7c c\u1eadp nh\u1eadt CRM","LBL_CONTRACT_TITLE":"H\u1ee3p \u0111\u1ed3ng","LBL_CONTRACT_DESC":"\u0110\u1ecbnh ngh\u0129a c\u00e1c lo\u1ea1i h\u1ee3p \u0111\u1ed3ng cho ph\u00e2n h\u1ec7 H\u1ee3p \u0111\u1ed3ng.","LBL_CONTRACT_TYPES":"Qu\u1ea3n l\u00fd c\u00e1c lo\u1ea1i h\u1ee3p \u0111\u1ed3ng","LBL_COULD_NOT_CONNECT":"L\u1ed7i: Kh\u00f4ng th\u1ec3 k\u1ebft n\u1ed1i v\u00e0o m\u00e1y ch\u1ee7 CRM. Vui l\u00f2ng ki\u1ec3m tra trong c\u1ea5u h\u00ecnh Proxy c\u1ee7a b\u1ea1n trong \"Thi\u1ebft l\u1eadp h\u1ec7 th\u1ed1ng\"","LBL_CREATE_RESOTRE_FILE":"t\u1eadp tin restoreExpand.sql \u0111\u00e3 \u0111\u01b0\u1ee3c t\u1ea1o. Vui l\u00f2ng ch\u1ecdn t\u1eadp tin n\u00e0y \u0111\u1ec3 ph\u1ee5c h\u1ed3i c\u00e1c thu\u1ed9c t\u00ednh \u0111\u00e3 thay \u0111\u1ed5i.","LBL_CURRENCY":"Thi\u1ebft l\u1eadp ti\u1ec1n t\u1ec7 v\u00e0 t\u1ec9 gi\u00e1","LBL_CONNECTOR_SETTINGS":"C\u00e1c thi\u1ebft l\u1eadp k\u1ebft n\u1ed1i","LBL_CONNECTOR_SETTINGS_DESC":"Qu\u1ea3n l\u00fd c\u00e1c thi\u1ebft l\u1eadp k\u1ebft n\u1ed1i","LBL_incomCRMPDF_SETTINGS":"PDF Settings","LBL_incomCRMPDF_SETTINGS_DESC":"Manage settings for generated PDF files","LBL_DENY":"Ng\u0103n ch\u1eb7n","LBL_DIAG_CANCEL_BUTTON":"H\u1ee7y","LBL_DIAG_EXECUTE_BUTTON":"Th\u1ef1c hi\u1ec7n vi\u1ec7c \u0111\u00e1nh gi\u00e1","LBL_DIAGNOSTIC_ACCESS":"Ch\u1ec9 c\u00f3 ng\u01b0\u1eddi qu\u1ea3n tr\u1ecb c\u00f3 th\u1ec3 s\u1eed d\u1ee5ng c\u00f4ng c\u1ee5 \u0111\u00e1nh gi\u00e1.","LBL_DIAGNOSTIC_BEANLIST_DESC":"...","LBL_DIAGNOSTIC_BEANLIST_GREEN":"M\u00e0u xanh l\u00e1 ngh\u0129a l\u00e0 t\u1eadp tin \u0111\u00e3 c\u00f3 s\u1eb5n.","LBL_DIAGNOSTIC_BEANLIST_ORANGE":"M\u00e0u cam ngh\u0129a l\u00e0 kh\u00f4ng c\u00f3 ch\u1ec9 m\u1ee5c, v\u00ec th\u1ebf kh\u00f4ng th\u1ec3 t\u00ecm ki\u1ebfm","LBL_DIAGNOSTIC_BEANLIST_RED":"M\u00e0u \u0111\u1ecf ngh\u0129a l\u00e0 t\u1eadp tin ch\u01b0a c\u00f3.","LBL_DIAGNOSTIC_BLBF":"c\u00e1c t\u1eadp tin BeanList\/BeanFiles \u0111\u00e3 c\u00f3 s\u1eb5n","LBL_DIAGNOSTIC_CALCMD5":"    -  Sao ch\u00e9p m\u1ea3ng MD5","LBL_DIAGNOSTIC_CONFIGPHP":"CRM config.php","LBL_DIAGNOSTIC_CUSTOMDIR":"th\u01b0 m\u1ee5c CRM T\u00f9y ch\u1ec9nh","LBL_DIAGNOSTIC_DELETED":"T\u1eadp tin \u0111\u00e3 b\u1ecb x\u00f3a","LBL_DIAGNOSTIC_DELETELINK":"X\u00f3a t\u1eadp tin \u0111\u00e1nh gi\u00e1","LBL_DIAGNOSTIC_FILE":"T\u1eadp tin","LBL_DIAGNOSTIC_ZIP":".zip ch\u01b0a c\u00f3","LBL_DIAGNOSTIC_DELETE_ERROR":"Kh\u00f4ng th\u1ec3 x\u00f3a t\u1eadp tin","LBL_DIAGNOSTIC_DELETE_DIE":"B\u1ea1n \u0111ang mu\u1ed1n x\u00f3a t\u1eadp tin kh\u00f4ng l\u00e0 t\u1eadp tin \u0111\u00e1nh gi\u00e1.","LBL_DIAGNOSTIC_DELETE_RETURN":"Tr\u1edf l\u1ea1i trang Qu\u1ea3n tr\u1ecb","LBL_DIAGNOSTIC_DESC":"Gi\u1eef l\u1ea1i c\u1ea5u h\u00ecnh h\u1ec7 th\u1ed1ng cho vi\u1ec7c ph\u00e2n t\u00edch v\u00e0 \u0111\u00e1nh gi\u00e1","LBL_DIAGNOSTIC_DONE":"Ho\u00e0n t\u1ea5t","LBL_DIAGNOSTIC_DOWNLOADLINK":"T\u1ea3i v\u1ec1 t\u1eadp tin \u0111\u00e1nh gi\u00e1","LBL_DIAGNOSTIC_EXECUTING":"\u0110ang th\u1ef1c hi\u1ec7n vi\u1ec7c \u0111\u00e1nh gi\u00e1","LBL_DIAGNOSTIC_FILESMD5":"    -  Sao chep files.md5","LBL_DIAGNOSTIC_GETBEANFILES":"\u0110ang ki\u1ec3m tra c\u00e1c t\u1eadp tin bean s\u1eb5n c\u00f3...","LBL_DIAGNOSTIC_GETCONFPHP":"\u0110ang \u0111\u1ecdc config.php","LBL_DIAGNOSTIC_GETCUSTDIR":"\u0110ang \u0111\u1ecdc","LBL_DIAGNOSTIC_GETMD5INFO":"\u0110ang \u0111\u1ecdc th\u00f4ng tin md5","LBL_DIAGNOSTIC_GETMYSQLINFO":"l\u1ea5y th\u00f4ng tin mysql","LBL_DIAGNOSTIC_GETMYSQLTD":"l\u1ea5y th\u00f4ng tin mysql","LBL_DIAGNOSTIC_GETMYSQLTS":"l\u1ea5y th\u00f4ng tin mysql","LBL_DIAGNOSTIC_GETPHPINFO":"\u0110ang \u0111\u1ecdc phpinfo()","LBL_DIAGNOSTIC_GETincomCRMLOG":"\u0110ang \u0111\u1ecdc incomCRM.log","LBL_DIAGNOSTIC_GETTING":"\u0110ang \u0111\u1ecdc...","LBL_DIAGNOSTIC_MD5":"th\u00f4ng tin MD5","LBL_DIAGNOSTIC_MYSQLDUMPS":"MySQL","LBL_DIAGNOSTIC_MYSQLINFO":"MySQL","LBL_DIAGNOSTIC_MYSQLSCHEMA":"MySQL","LBL_DIAGNOSTIC_NO_MYSQL":"B\u1ea1n kh\u00f4ng c\u00f3 MySQL","LBL_DIAGNOSTIC_PHPINFO":"phpinfo()","LBL_DIAGNOSTIC_incomCRMLOG":"T\u1eadp tin ghi nh\u1eadn l\u1ecbch s\u1eed s\u1eed d\u1ee5ng CRM","LBL_DIAGNOSTIC_TITLE":"C\u00f4ng c\u1ee5 \u0111\u00e1nh gi\u00e1","LBL_DIAGNOSTIC_VARDEFS":"VARDEFS","LBL_DISABLED":"V\u00f4 hi\u1ec7u h\u00f3a","LBL_DISPLAY_TABS":"Hi\u1ec3n th\u1ecb c\u00e1c th\u1ebb","LBL_DO_OVERWRITE_FILES":"Kh\u00f4ng ghi \u0111\u00e8","LBL_DOCUMENTATION_TITLE":"T\u00e0i li\u1ec7u tr\u1ef1c tuy\u1ebfn","LBL_DOCUMENTATION":"\u0110\u1ecdc t\u00e0i li\u1ec7u cho ng\u01b0\u1eddi qu\u1ea3n tr\u1ecb v\u00e0 ng\u01b0\u1eddi d\u00f9ng cu\u1ed1i","LBL_DONE":"Ho\u00e0n t\u1ea5t","LBL_DROP_HERE":"[Th\u1ea3 xu\u1ed1ng \u0111\u00e2y]","LBL_DROPDOWN_EDITOR":"T\u00f9y ch\u1ec9nh c\u00e1c H\u1ed9p th\u1ea3 xu\u1ed1ng","LBL_DST_APPLY_FIX":"\u00c1p d\u1ee5ng ch\u1ec9nh th\u1eddi gian. Vui l\u00f2ng sao l\u01b0u d\u1eef li\u1ec7u tr\u01b0\u1edbc.","LBL_DST_BEFORE_DESC":"Vui l\u00f2ng sao l\u01b0u m\u1ed9t c\u00e1ch \u0111\u1ea7y \u0111\u1ee7","LBL_DST_BEFORE":"Tr\u01b0\u1edbc khi b\u1eaft \u0111\u1ea7u:","LBL_DST_CURRENT_SERVER_TIME_ZONE_LOCALE":"M\u00fai gi\u1edd \u0111\u1ecba ph\u01b0\u01a1ng:","LBL_DST_CURRENT_SERVER_TIME_ZONE":"M\u00fai gi\u1edd c\u1ee7a m\u00e1y ch\u1ee7:","LBL_DST_CURRENT_SERVER_TIME":"Gi\u1edd theo m\u00e1y ch\u1ee7","LBL_DST_END_DATE_TIME":"Ng\u00e0y gi\u1edd k\u1ebft th\u00fac","LBL_DST_FIX_CONFIRM_DESC":"Vui l\u00f2ng xem qua tr\u01b0\u1edbc khi x\u00e1c nh\u1eadn","LBL_DST_FIX_CONFIRM":"X\u00e1c nh\u1eadn:","LBL_DST_FIX_DONE_DESC":"Vi\u1ec7c thay \u0111\u1ed5i m\u00fai gi\u1edd \u0111\u00e3 th\u1ef1c hi\u1ec7n ho\u00e0n t\u1ea5t","LBL_DST_FIX_TARGET":"\u0110\u00edch:","LBL_DST_FIX_USER_TZ":"Thi\u1ebft l\u1eadp m\u00fai gi\u1edd cho t\u1ea5t c\u1ea3 ng\u01b0\u1eddi d\u00f9ng","LBL_DST_FIX_USER":"M\u00fai gi\u1edd ng\u01b0\u1eddi d\u00f9ng (T\u00d9Y CH\u1eccN):","LBL_DST_SET_USER_TZ":"Thi\u1ebft l\u1eadp m\u00fai gi\u1edd ng\u01b0\u1eddi d\u00f9ng","LBL_DST_START_DATE_TIME":"Ng\u00e0y gi\u1edd b\u1eaft \u0111\u1ea7u","LBL_DST_UPGRADE":"N\u00e2ng c\u1ea5p:","LBL_EDIT_CUSTOM_FIELDS":"T\u00f9y ch\u1ec9nh c\u00e1c thu\u1ed9c t\u00ednh t\u00f9y bi\u1ebfn theo ng\u01b0\u1eddi d\u00f9ng","LBL_EDIT_TABS":"T\u00f9y ch\u1ec9nh Th\u1ebb","LBL_EMAIL_TITLE":"Th\u01b0 \u0111i\u1ec7n t\u1eed","LBL_EMAIL_DESC":"Qu\u1ea3n l\u00fd th\u01b0 \u0111i\u1ec7n t\u1eed.","LBL_EMAIL_ADDRESS_REQUIRED_FOR_FEATURE":"A primary email address is required for each user in order to use this feature.","LBL_ENABLE_MAILMERGE":"Cho ph\u00e9p Tr\u1ed9n th\u01b0?","LBL_ENABLED":"Cho ph\u00e9p","LBL_ERROR_VERSION_INFO":"th\u00f4ng tin phi\u00ean b\u1ea3n kh\u00f4ng \u0111\u00fang, vui l\u00f2ng th\u1eed l\u1ea1i","LBL_EXCEEDING_OC_LICENSES":"L\u1ed7i: S\u1ed1 l\u01b0\u1ee3ng M\u00e1y tr\u1ea1m Ngo\u1ea1i tuy\u1ebfn nhi\u1ec1u h\u01a1n b\u1ea3n quy\u1ec1n cho ph\u00e9p.","LBL_EXECUTE":"Th\u1ef1c thi","LBL_EXPAND_DATABASE_COLUMNS_DESC":"ch\u1ec9 c\u00f3 cho MSSQL","LBL_EXPAND_DATABASE_COLUMNS":"M\u1edf r\u1ed9ng \u0111\u1ed9 r\u1ed9ng c\u1ed9t","LBL_EXPAND_DATABASE_TEXT":"B\u1ea1n c\u00f3 th\u1ec3 ch\u1ecdn m\u1ed9t trong ba: Hi\u1ec3n th\u1ecb l\u00ean m\u00e0n h\u00ecnh    Tr\u00edch xu\u1ea5t ra t\u1eadp tin .sql    Th\u1ef1c thi l\u1ec7nh SQL","LBL_EXPORT_CUSTOM_FIELDS_TITLE":"Tr\u00edch xu\u1ea5t c\u1ea5u tr\u00fac c\u00e1c thu\u1ed9c t\u00ednh t\u00f9y ch\u1ec9nh","LBL_EXPORT_CUSTOM_FIELDS":"Tr\u00edch xu\u1ea5t ra m\u1ed9t t\u1eadp tin .incomCRM","LBL_EXPORT_DOWNLOAD_KEY":"Tr\u00edch xu\u1ea5t m\u00e3 cho ph\u00e9p t\u1ea3i v\u1ec1","LBL_EXTERNAL_DEV_DESC":"Chuy\u1ec3n c\u00e1c thu\u1ed9c t\u00ednh t\u00f9y t\u00f9y bi\u1ebfn v\u00e0o h\u1ec7 th\u1ed1ng kh\u00e1c","LBL_EXTERNAL_DEV_TITLE":"Chuy\u1ec3n c\u00e1c thu\u1ed9c t\u00ednh t\u00f9y t\u00f9y bi\u1ebfn","LBL_FORECAST_TITLE":"D\u1ef1 b\u00e1o","LBL_FORECAST_DESC":"\u0110\u1ecbnh ngh\u0129a c\u00e1c giai \u0111o\u1ea1n cho ph\u00e2n h\u1ec7 D\u1ef1 b\u00e1o","LBL_GLOBAL_TEAM_DESC":"Hi\u1ec3n th\u1ecb to\u00e0n b\u1ed9","LBL_GLOBAL_TEAM_SELECT":"Vui l\u00f2ng ch\u1ecdn m\u1ed9t nh\u00f3m trong danh s\u00e1ch","LBL_GLOBAL_TEAM":"Bi\u00ean d\u1ecbch l\u1ea1i","LBL_GO":"Th\u1ef1c hi\u1ec7n","LBL_HELP_BOOKMARK":"L\u00e0m d\u1ea5u trang n\u00e0y","LBL_HELP_EMAIL":"Th\u01b0 \u0111i\u1ec7n t\u1eed","LBL_HELP_LINK":"N\u1ed1i \u0111\u1ebfn trang n\u00e0y","LBL_HELP_PRINT":"In","LBL_HIDE_TABS":"C\u00e1c th\u1ebb \u1ea9n","LBL_HIDDEN_PANELS":"\u1ea8n Subpanels","LBL_HIDDEN_TABS":"C\u00e1c th\u1ebb \u1ea9n","LBL_HIDE_ADVANCED_OPTIONS":"Hide Advanced Options","LBL_HT_DONE":"--- HO\u00c0N T\u1ea4T ---","LBL_HT_NO_WRITE_2":"T\u1ea1o 1 t\u1eadp tin .htaccess v\u1edbi n\u1ed9i dung sau trong th\u01b0 m\u1ee5c g\u1ed1c \u0111\u1ec3 \u0111\u1ea3m b\u1ea3o an ninh khi truy c\u1eadp t\u1eeb tr\u00ecnh duy\u1ec7t","LBL_HT_NO_WRITE":"Kh\u00f4ng th\u1ec3 ghi t\u1eadp tin:","LBL_ICF_ADDING":"\u0110ang th\u00eam v\u00e0o","LBL_ICF_DROPPING":"\u0110ang h\u1ee7y","LBL_ICF_IMPORT_S":"Nh\u1eadp c\u1ea5u tr\u00fac","LBL_IFRAME":"C\u1ed5ng","LBL_IMPORT_CUSTOM_FIELDS_DESC":"Nh\u1eadp 1 t\u1eadp tin .sug t\u1eeb m\u1ed9t m\u00e1y kh\u00e1c.","LBL_IMPORT_CUSTOM_FIELDS_STRUCT":"incomCRMCustomFieldStruct.sug","LBL_IMPORT_CUSTOM_FIELDS_TITLE":"Nh\u1eadp c\u1ea5u tr\u00fac c\u00e1c thu\u1ed9c t\u00ednh t\u00f9y bi\u1ebfn","LBL_IMPORT_CUSTOM_FIELDS":"Nh\u1eadp \u0111\u1ecbnh ngh\u0129a c\u00e1c thu\u1ed9c t\u00ednh t\u00f9y bi\u1ebfn t\u1eeb m\u1ed9t t\u1eadp tin .incomCRM","LBL_IMPORT_VALIDATION_KEY":"Nh\u1eadp kh\u00f3a x\u00e1c nh\u1eadn","LBL_INBOUND_EMAIL_TITLE":"Th\u01b0 \u0111i\u1ec7n t\u1eed n\u1ed9i b\u1ed9","LBL_LAYOUT":"Th\u00eam, x\u00f3a, s\u1eeda c\u00e1c thu\u1ed9c t\u00ednh v\u00e0 thu\u1ed9c t\u00ednh hi\u1ec3n th\u1ecb","LBL_LICENSE_EXPIRE_DATE":"Ng\u00e0y h\u1ebft h\u1ea1n","LBL_LICENSE_KEY":"Kh\u00f3a t\u1ea3i v\u1ec1","LBL_LICENSE_NUM_LIC_OC":"S\u1ed1 b\u1ea3n quy\u1ec1n","LBL_LICENSE_NUM_PORTAL_USERS":"S\u1ed1 ng\u01b0\u1eddi d\u00f9ng","LBL_LICENSE_USERS":"S\u1ed1 ng\u01b0\u1eddi d\u00f9ng","LBL_LICENSE_VALIDATION_END":"Kh\u00f3a h\u1ee3p l\u1ec7 \u0111\u00e3 h\u1ebft h\u1ea1n","LBL_LICENSE_VALIDATION_KEY":"Kh\u00f3a h\u1ee3p l\u1ec7","LBL_LICENSE_VALIDATION":"B\u1ea3n quy\u1ec1n h\u1ee3p l\u1ec7","LBL_LICENSE":"B\u1ea3n quy\u1ec1n","LBL_LIST_FIRST_CONNECT_DATE":"Ng\u00e0y k\u1ebft n\u1ed1i \u0111\u1ea7u ti\u00ean","LBL_LIST_LAST_CONNECT_DATE":"Ng\u00e0y k\u1ebft n\u1ed1i cu\u1ed1i","LBL_LIST_NUM_SYNCS":"S\u1ed1 l\u1ea7n \u0111\u1ed3ng b\u1ed9","LBL_LIST_SET_STATUS":"Thi\u1ebft l\u1eadp tr\u1ea1ng th\u00e1i","LBL_LIST_SYSTEM_KEY":"Kh\u00f3a c\u1ee7a H\u1ec7 th\u1ed1ng","LBL_LIST_SYSTEM_NAME":"T\u00ean h\u1ec7 th\u1ed1ng","LBL_LIST_VIEW":"Li\u1ec7t k\u00ea","LBL_LDAP_TITLE":"LDAP Support","LBL_LDAP_ENABLE":"Enable LDAP Authentication","LBL_LDAP_HELP_TXT":"When LDAP authentication is enabled, passwords can only be handled through LDAP. None of the CRM Password Management feature settings will apply.","LBL_LDAP_SERVER_HOSTNAME":"Server:","LBL_LDAP_SERVER_PORT":"Port Number:","LBL_LDAP_ADMIN_USER":"User Name:","LBL_LDAP_ADMIN_USER_DESC":"Used to search for the CRM user. [May need to be fully qualified] It will bind anonymously if not provided.","LBL_LDAP_ADMIN_PASSWORD":"Password:","LBL_LDAP_AUTHENTICATION":"Authentication:","LBL_LDAP_AUTHENTICATION_DESC":"Bind to the LDAP server using a specific users credentials","LBL_LDAP_AUTO_CREATE_USERS":"Auto Create Users:","LBL_LDAP_USER_DN":"User DN:","LBL_LDAP_GROUP_DN":"Group DN:","LBL_LDAP_GROUP_DN_DESC":"Example: <em>ou=groups,dc=example,dc=com<\/em>","LBL_LDAP_USER_FILTER":"User Filter:","LBL_LDAP_GROUP_MEMBERSHIP":"Group Membership:","LBL_LDAP_GROUP_MEMBERSHIP_DESC":"Users must be a member of a specific group","LBL_LDAP_GROUP_USER_ATTR":"User Attribute:","LBL_LDAP_GROUP_USER_ATTR_DESC":"The unique identifier of the person that will be used to check if they are a member of the group Example: <em>uid<\/em>","LBL_LDAP_GROUP_ATTR_DESC":"The attribute of the Group that will be used to filter against the User Attribute Example: <em>memberUid<\/em>","LBL_LDAP_GROUP_ATTR":"Group Attribute:","LBL_LDAP_USER_FILTER_DESC":"Any additional filter params to apply when authenticating users e.g.\\nis_incomCRM_user=1 or (is_incomCRM_user=1)(is_sales=1)","LBL_LDAP_LOGIN_ATTRIBUTE":"Login Attribute:","LBL_LDAP_BIND_ATTRIBUTE":"Bind Attribute:","LBL_LDAP_BIND_ATTRIBUTE_DESC":"For Binding the LDAP User<br>Examples:&nbsp;<b>AD:<\/b>&nbsp;userPrincipalName,&nbsp;<b>openLDAP:<\/b>&nbsp;userPrincipalName,&nbsp;<b>Mac&nbsp;OS&nbsp;X:<\/b>&nbsp;uid ","LBL_LDAP_LOGIN_ATTRIBUTE_DESC":"For searching for the LDAP User<br>Examples:<b>AD:<\/b>&nbsp;userPrincipalName,&nbsp;<b>openLDAP:<\/b>&nbsp;dn,&nbsp;<b>Mac&nbsp;OS&nbsp;X:<\/b>&nbsp;dn ","LBL_LDAP_SERVER_HOSTNAME_DESC":"Example: ldap.example.com or ldaps:\/\/ldap.example.com for SSL","LBL_LDAP_SERVER_PORT_DESC":"Example: 389 or 636 for SSL","LBL_LDAP_GROUP_NAME":"Group Name:","LBL_LDAP_GROUP_NAME_DESC":"Example cn=incomCRM","LBL_LDAP_USER_DN_DESC":"Example: ou=people,dc=example,dc=com","LBL_LDAP_AUTO_CREATE_USERS_DESC":"If an authenticated user does not exist one will be created in CRM.","LBL_LDAP_ENC_KEY":"Encryption Key:","LBL_LOADING":"\u0110ang t\u1ea3i, Vui l\u00f2ng ch\u1edd...","LBL_LOCALE_DB_COLLATION_TITLE":"\u0110\u1ed1i chi\u1ebfu C\u01a1 s\u1edf d\u1eef li\u1ec7u","LBL_LOCALE_DB_COLLATION":"\u0110\u1ed1i chi\u1ebfu","LBL_LOCALE_DEFAULT_CURRENCY_ISO4217":"ISO 4217 Currency Code","LBL_LOCALE_DEFAULT_CURRENCY_NAME":"Ti\u1ec1n t\u1ec7","LBL_LOCALE_DEFAULT_CURRENCY_SYMBOL":"K\u00fd t\u1ef1 ti\u1ec1n t\u1ec7","LBL_LOCALE_DEFAULT_CURRENCY":"Lo\u1ea1i ti\u1ec1n t\u1ec7 m\u1eb7c \u0111\u1ecbnh","LBL_LOCALE_DEFAULT_DATE_FORMAT":"\u0110\u1ecbnh d\u1ea1ng ng\u00e0y m\u1eb7c \u0111\u1ecbnh","LBL_LOCALE_DEFAULT_DECIMAL_SEP":"K\u00fd t\u1ef1 th\u1eadp ph\u00e2n","LBL_LOCALE_DEFAULT_LANGUAGE":"Ng\u00f4n ng\u1eef m\u1eb7c \u0111\u1ecbnh","LBL_LOCALE_DEFAULT_NAME_FORMAT":"\u0110\u1ecbnh d\u1ea1ng t\u00ean m\u1eb7c \u0111\u1ecbnh","LBL_LOCALE_DEFAULT_NUMBER_GROUPING_SEP":"1000s","LBL_LOCALE_DEFAULT_SYSTEM_SETTINGS":"Giao di\u1ec7n ng\u01b0\u1eddi d\u00f9ng","LBL_LOCALE_DEFAULT_TIME_FORMAT":"\u0110\u1ecbnh d\u1ea1ng th\u1eddi gian m\u1eb7c \u0111\u1ecbnh","LBL_LOCALE_EXAMPLE_NAME_FORMAT":"V\u00ed d\u1ee5","LBL_LOCALE_NAME_FORMAT_DESC":"\"s\" L\u1eddi ch\u00e0o \"f\" T\u00ean \"l\" H\u1ecd","LBL_LOCALE_TITLE":"Thi\u1ebft l\u1eadp h\u1ec7 th\u1ed1ng theo \u0111\u1ecba ph\u01b0\u01a1ng","LBL_LOCALE":"Thi\u1ebft l\u1eadp c\u1ea5u h\u00ecnh \u0111\u1ecba ph\u01b0\u01a1ng m\u1eb7c \u0111\u1ecbnh","LBL_LOGIN_incomCRM_SERVER_DESC":"- T\u00ean ng\u01b0\u1eddi d\u00f9ng","LBL_LOGIN_incomCRM_SERVER":"T\u00ean ng\u01b0\u1eddi d\u00f9ng:","LBL_MAILBOX_DESC":"Thi\u1ebft l\u1eadp t\u00e0i kho\u1ea3n th\u01b0 \u0111i\u1ec7n t\u1eed n\u1ed9i b\u1ed9","LBL_MANAGE_CONTRACTEMPLATES_TITLE":"Lo\u1ea1i h\u1ee3p \u0111\u1ed3ng","LBL_MANAGE_CURRENCIES":"Ti\u1ec1n t\u1ec7","LBL_MANAGE_GROUPS_TITLE":"Qu\u1ea3n l\u00fd nh\u00f3m","LBL_MANAGE_GROUPS":"Qu\u1ea3n l\u00fd h\u00e0ng \u0111\u1ee3i","LBL_MANAGE_LAYOUT":"Thu\u1ed9c t\u00ednh hi\u1ec3n th\u1ecb","LBL_MANAGE_LICENSE_TITLE":"Qu\u1ea3n l\u00fd b\u1ea3n quy\u1ec1n","LBL_MANAGE_LICENSE":"Qu\u1ea3n l\u00fd thu\u1ed9c t\u00ednh b\u1ea3n quy\u1ec1n","LBL_MANAGE_LOCALE":"Thi\u1ebft l\u1eadp \u0111\u1ecba ph\u01b0\u01a1ng","LBL_MANAGE_MAILBOX":"Th\u01b0 \u0111i\u1ec7n t\u1eed n\u1ed9i b\u1ed9","LBL_MANAGE_OFFLINE_CLIENT":"Xem c\u00e1c M\u00e1y tr\u1ea1m Ngo\u1ea1i tuy\u1ebfn","LBL_MANAGE_OPPORTUNITIES":"C\u01a1 h\u1ed9i","LBL_MANAGE_PASSWORD_TITLE":"Password Management","LBL_MANAGE_PASSWORD":"Manage password requirements and expiration","LBL_MANAGE_RELEASES":"B\u1ea3n ph\u00e1t h\u00e0nh","LBL_MANAGE_ROLES_TITLE":"Vai tr\u00f2 ng\u01b0\u1eddi d\u00f9ng","LBL_MANAGE_ROLES":"Qu\u1ea3n l\u00fd vai tr\u00f2 c\u1ee7a c\u00e1c th\u00e0nh vi\u00ean v\u00e0 thu\u1ed9c t\u00ednh","LBL_MANAGE_TEAMS_TITLE":"Qu\u1ea3n l\u00fd Nh\u00f3m","LBL_MANAGE_TEAMS":"B\u1ed9 ph\u1eadn\/Nh\u00f3m","LBL_MANAGE_TIMEPERIODS_TITLE":"C\u00e1c giai \u0111o\u1ea1n","LBL_MANAGE_TIMEPERIODS":"Qu\u1ea3n l\u00fd c\u00e1c giai \u0111o\u1ea1n","LBL_MANAGE_USERS_TITLE":"Qu\u1ea3n l\u00fd ng\u01b0\u1eddi d\u00f9ng","LBL_MANAGE_USERS":"Qu\u1ea3n l\u00fd t\u00e0i kho\u1ea3n ng\u01b0\u1eddi d\u00f9ng v\u00e0 m\u1eadt kh\u1ea9u","LBL_MANAGE_WORKFLOW":"Qu\u1ea3n l\u00fd Lu\u1ed3ng x\u1eed l\u00fd","LBL_MANUAL_VALIDATION_TXT":"Ki\u1ec3m tra h\u1ee3p l\u1ec7 b\u1eb1ng tay","LBL_MANUAL_VALIDATION":"...","LBL_MANUAL_VALIDATION1":"...","LBL_MANUAL_VALIDATION2":"...","LBL_MANUAL_VALIDATION3":"...","LBL_MANUAL_VALIDATION4":"...","LBL_MANUAL_VALIDATION5":"...","LBL_MANUFACTURERS_TITLE":"Nh\u00e0 s\u1ea3n xu\u1ea5t","LBL_MANUFACTURERS":"Thi\u1ebft l\u1eadp danh s\u00e1ch nh\u00e0 s\u1ea3n xu\u1ea5t","LBL_MASS_EMAIL_CONFIG_DESC":"C\u1ea5u h\u00ecnh c\u00e1c thi\u1ebft l\u1eadp cho th\u01b0 \u0111i\u1ec7n t\u1eed","LBL_MASS_EMAIL_CONFIG_TITLE":"Thi\u1ebft l\u1eadp th\u01b0 \u0111i\u1ec7n t\u1eed","LBL_MASS_EMAIL_MANAGER_DESC":"Qu\u1ea3n tr\u1ecb h\u00e0ng \u0111\u1ee3i c\u00e1c th\u01b0 \u0111i\u1ec7n t\u1eed g\u1eedi \u0111i","LBL_MASS_EMAIL_MANAGER_HEADER":"Qu\u1ea3n l\u00fd th\u01b0 \u0111i\u1ec7n t\u1eed cho chi\u1ebfn d\u1ecbch","LBL_MASS_EMAIL_MANAGER_TITLE":"Qu\u1ea3n l\u00fd h\u00e0ng \u0111\u1ee3i","LBL_MASSAGE_MASS_EMAIL_DESC":"Ch\u1ecdn \"B\u1eaft \u0111\u1ea7u n\u00e2ng c\u1ea5p\" \u0111\u1ec3 ti\u1ebfp t\u1ee5c","LBL_MASSAGE_MASS_EMAIL":"Ch\u1ec9nh ng\u00e0y gi\u1edd GMT cho vi\u1ec7c g\u1eedi th\u01b0 h\u00e0ng lo\u1ea1t","LBL_MISSING_GLOBAL":"...","LBL_MISSING_PRIVATE":"...","LBL_MISSING_TEAMS":"...","LBL_MI_REBUILDING":"\u0110ang bi\u00ean d\u1ecbch l\u1ea1i...","LBL_MI_SECTION":"Phi\u00ean ng\u01b0\u1eddi d\u00f9ng...","LBL_MI_UN_CUSTOMFIELD":"G\u1ee1 b\u1ecf c\u00e1c thu\u1ed9c t\u00ednh t\u00f9y bi\u1ebfn...","LBL_MI_IN_CUSTOMFIELD":"C\u00e0i \u0111\u1eb7t c\u00e1c thu\u1ed9c t\u00ednh t\u00f9y bi\u1ebfn...","LBL_MI_COMPLETE":"Ho\u00e0n t\u1ea5t","LBL_MI_UN_BEAN":"\u0110ang g\u1ee1 b\u1ecf :","LBL_MI_IN_BEAN":"\u0110ang c\u00e0i \u0111\u1eb7t :","LBL_MI_IN_DASHLETS":"\u0110ang c\u00e0i \u0111\u1eb7t B\u1ea3ng th\u00f4ng tin CRM","LBL_MI_UN_DASHLETS":"\u0110ang g\u1ee1 b\u1ecf B\u1ea3ng th\u00f4ng tin CRM","LBL_MI_IN_IMAGES":"\u0110ang c\u00e0i \u0111\u1eb7t c\u00e1c h\u00ecnh \u1ea3nh","LBL_MI_IN_MENUS":"\u0110ang c\u00e0i \u0111\u1eb7t c\u00e1c b\u1ea3ng ch\u1ecdn","LBL_MI_UN_MENUS":"\u0110ang g\u1ee1 b\u1ecf c\u00e1c b\u1ea3ng ch\u1ecdn","LBL_MI_IN_ADMIN":"\u0110ang c\u00e0i \u0111\u1eb7t ph\u1ea7n Qu\u1ea3n tr\u1ecb","LBL_MI_UN_ADMIN":"\u0110ang g\u1ee1 b\u1ecf ph\u1ea7n Qu\u1ea3n tr\u1ecb","LBL_MI_IN_USER":"\u0110ang c\u00e0i \u0111\u1eb7t ph\u1ea7n Trang ng\u01b0\u1eddi d\u00f9ng","LBL_MI_UN_USER":"\u0110ang g\u1ee1 b\u1ecf ph\u1ea7n Trang ng\u01b0\u1eddi d\u00f9ng","LBL_MI_IN_VAR":"\u0110ang c\u00e0i \u0111\u1eb7t c\u00e1c \u0111\u1ecbnh ngh\u0129a tham s\u1ed1","LBL_MI_UN_VAR":"\u0110ang g\u1ee1 b\u1ecf c\u00e1c \u0111\u1ecbnh ngh\u0129a tham s\u1ed1","LBL_MI_IN_SUBPANEL":"\u0110ang c\u00e0i \u0111\u1eb7t c\u00e1c b\u1ea3ng hi\u1ec3n th\u1ecb con","LBL_MI_UN_SUBPANEL":"\u0110ang g\u1ee1 b\u1ecf c\u00e1c b\u1ea3ng hi\u1ec3n th\u1ecb con","LBL_MI_IN_LANG":"\u0110ang c\u00e0i \u0111\u1eb7t g\u00f3i ng\u00f4n ng\u1eef","LBL_MI_UN_LANG":"\u0110ang g\u1ee1 b\u1ecf g\u00f3i ng\u00f4n ng\u1eef","LBL_MI_IN_RELATIONSHIPS":"\u0110ang c\u00e0i \u0111\u1eb7t c\u00e1c Quan h\u1ec7","LBL_MI_UN_RELATIONSHIPS":"\u0110ang g\u1ee1 b\u1ecf c\u00e1c Quan h\u1ec7","LBL_MI_UN_RELATIONSHIPS_DROP":"\u0111ang x\u00f3a b\u1ea3ng d\u1eef li\u1ec7u","LBL_MI_REPAIR_INDICES":"\u0110ang s\u1eeda c\u00e1c ch\u1ec9 m\u1ee5c","LBL_MI_IN_CONNECTORS":"\u0110ang c\u00e0i \u0111\u1eb7t c\u00e1c k\u1ebft n\u1ed1i","LBL_MI_UN_CONNECTORS":"\u0110ang g\u1ee1 b\u1ecf c\u00e1c k\u1ebft n\u1ed1i","LBL_ML_ACTION":"Th\u1ef1c hi\u1ec7n","LBL_ML_CANCEL":"H\u1ee7y","LBL_ML_COMMIT":"Cam k\u1ebft","LBL_ML_DESCRIPTION":"M\u00f4 t\u1ea3","LBL_ML_INSTALLED":"Ng\u00e0y c\u00e0i \u0111\u1eb7t","LBL_ML_NAME":"T\u00ean","LBL_ML_PUBLISHED":"Ng\u00e0y ph\u00e1t h\u00e0nh","LBL_ML_TYPE":"Lo\u1ea1i","LBL_ML_UNINSTALLABLE":"C\u00f3 th\u1ec3 g\u1ee1 b\u1ecf","LBL_ML_VERSION":"Phi\u00ean b\u1ea3n","LBL_ML_INSTALL":"C\u00e0i \u0111\u1eb7t","LBL_ML_ENABLE_OR_DISABLE":"C\u00f3 th\u1ec3\/Kh\u00f4ng th\u1ec3","LBL_ML_DELETE":"X\u00f3a","LBL_MODIFY_CREDENTIALS":"T\u00f9y ch\u1ec9nh c\u00e1c Ch\u1ee9ng nh\u1eadn","LBL_MODULE_LICENSE":"Vui l\u00f2ng \u0111\u1ecdc c\u00e1c x\u00e1c nh\u1eadn b\u1ea3n quy\u1ec1n","LBL_MODULE_LOADER_TITLE":"B\u1ed9 t\u1ea3i c\u00e1c ph\u00e2n h\u1ec7","LBL_MODULE_LOADER":"Th\u00eam ho\u1eb7c x\u00f3a c\u00e1c ph\u00e2n h\u1ec7, giao di\u1ec7n, v\u00e0 g\u00f3i ng\u00f4n ng\u1eef","LBL_MODULE_NAME":"Qu\u1ea3n tr\u1ecb","LBL_MODULE_TITLE":"Qu\u1ea3n tr\u1ecb: Trang ch\u1ee7","LBL_MODULES_TO_DOWNLOAD":"Ph\u00e2n h\u1ec7 \u0111\u1ec3 T\u1ea3i v\u1ec1 (k\u00e9o th\u1ea3 v\u00e0 th\u1ea3 t\u1ea1i \u0111\u00e2y)","LBL_NEVER":"Kh\u00f4ng bao gi\u1edd","LBL_NEW_FORM_TITLE":"T\u1ea1o T\u00e0i kho\u1ea3n","LBL_NOTIFY_SUBJECT":"Ch\u1ee7 \u0111\u1ec1:","LBL_OC_SEARCH_FORM_TITLE":"T\u00ecm M\u00e1y tr\u1ea1m Ngo\u1ea1i tuy\u1ebfn","LBL_OFFLINE_CLIENT":"Xem c\u00e1c M\u00e1y tr\u1ea1m Ngo\u1ea1i tuy\u1ebfn \u0111ang k\u1ebft n\u1ed1i v\u00e0o h\u1ec7 th\u1ed1ng","LBL_OOTB_BOUNCE":"Th\u1ef1c hi\u1ec7n g\u1eebi th\u01b0 (ban \u0111\u00eam) cho chi\u1ebfn d\u1ecbch","LBL_OOTB_CAMPAIGN":"Th\u1ef1c hi\u1ec7n g\u1eebi th\u01b0 (ban \u0111\u00eam) h\u00e0ng lo\u1ea1t","LBL_OOTB_IE":"Ki\u1ec3m tra t\u00e0i kho\u1ea3n th\u01b0 \u0111\u1ebfn","LBL_OOTB_PRUNE":"T\u1ed5 ch\u1ee9c c\u01a1 s\u1edf d\u1eef li\u1ec7u cho th\u00e1ng \u0111\u1ea7u ti\u00ean","LBL_OOTB_REPORTS":"T\u1ea1o b\u00e1o c\u00e1c cho c\u00e1c t\u00e1c v\u1ee5 \u0111\u00e3 \u0111\u01b0\u1ee3c l\u00ean l\u1ecbch","LBL_OOTB_WORKFLOW":"Th\u1ef1c hi\u1ec7n c\u00e1c t\u00e1c v\u1ee5 theo quy tr\u00ecnh","LBL_OOTB_TRACKER":"T\u1ed5 ch\u1ee9c b\u1ea3ng l\u1ecbch s\u1eed ng\u01b0\u1eddi d\u00f9ng cho th\u00e1ng \u0111\u1ea7u ti\u00ean","LBL_UPDATE_TRACKER_SESSIONS":"C\u1eadp nh\u1eadt b\u1ea3ng tracker_sessions","LBL_OOTB_DCE_CLNUP":"\u0110\u00f3ng v\u00f2ng l\u1eb7p \u0111\u1ec3 ho\u00e0n t\u1ea5t DCE","LBL_OOTB_DCE_REPORT":"T\u1ea1o Ho\u1ea1t \u0111\u1ed9ng t\u1ed5ng h\u1ee3p b\u00e1o c\u00e1o h\u00e0ng ng\u00e0y","LBL_OOTB_DCE_SALES_REPORT":"T\u1ea1o th\u01b0 b\u00e1o c\u00e1o b\u00e1n h\u00e0ng h\u00e0ng tu\u1ea7n","LBL_OVERWRITE_FILES":"Ghi \u0111\u00e8 c\u00e1c t\u1eadp tin","LBL_PASSWORD_incomCRM_SERVER_DESC":"M\u1ed9t t\u00ean v\u00e0 m\u1eadt kh\u1ea9u qu\u1ea3n tr\u1ecb tr\u00ean m\u00e1y ch\u1ee7","LBL_PASSWORD_incomCRM_SERVER":"M\u1eadt kh\u1ea9u qu\u1ea3n tr\u1ecb","LBL_PASSWORD":"M\u1eadt kh\u1ea9u","LBL_PASSWORD_RESET_MANAGEMENT":"Reset password option","LBL_PASSWORD_RULES_MANAGEMENT":"Password Requirements","LBL_PASSWORD_TEMPLATE":"Email Templates","LBL_PASSWORD_CREATE_TEMPLATE":"Create","LBL_PASSWORD_EDIT_TEMPLATE":"Edit","LBL_PASSWORD_GENERATE_TEMPLATE_MSG":"Email template containing system-generated password","LBL_PASSWORD_LOST_TEMPLATE_MSG":"Email template containing system-generated link to reset password","LBL_PASSWORD_INVALID_LENGTH":"Minimum Length must be less than Maximum Length","LBL_PASSWORD_INVALID_MINLENGTH":"Minimum Length must be more than 0","LBL_PASSWORD_SYST_GENERATED_PWD_ON":"Enable System-Generated Passwords Feature","LBL_PASSWORD_SYST_GENERATED_PWD_HELP":"When this feature is enabled, users will be emailed a system-generated link to reset their passwords. Requirements for this feature are: 1) a outbound email server must be configured properly in Email Settings, and 2) users must have valid email addresses in their user records","LBL_PASSWORD_EXP_AFTER":"Password Expires upon","LBL_PASSWORD_FORGOT_FEATURE":"Enable Forgot Password feature","LBL_PASSWORD_FORGOT_FEATURE_HELP":"When enabled, users will have the ability to re-set their own passwords at the Login page. Requirements to use this feature: 1) users must have email addresses provided in their user records, and 2) an outbound email server must be configured in the Email Settings page.","LBL_PASSWORD_ONE_UPPER_CASE":"Must contain one upper case letter (A-Z)","LBL_PASSWORD_ONE_LOWER_CASE":"Must contain one lower case letter (a-z)","LBL_PASSWORD_ONE_NUMBER":"Must contain one number (0-9)","LBL_PASSWORD_ONE_SPECIAL_CHAR":"Must contain one of the following special characters (~,!,@,#,$,%,^,&,*,(,),_,+,-,=,{,},|)","LBL_PASSWORD_ONLY_LOWCASE":"Only lowcase caracters","LBL_PASSWORD_LOCKOUT":"Login Lockout","LBL_PASSWORD_LOCKOUT_ATTEMPT1":"Lockout users after ","LBL_PASSWORD_LOCKOUT_ATTEMPT2":" un-successful login attempts","LBL_PASSWORD_LOGIN":"Login","LBL_PASSWORD_LOGINS":"logins","LBL_PASSWORD_LOGIN_DELAY":"Enable login again after","LBL_PASSWORD_NEEDED_CARACTERS":"Needed caracters","LBL_PASSWORD_PROHIBITED_CARACTERS":"Prohibited caracters","LBL_PASSWORD_MINIMUM_LENGTH":"Minimum Length","LBL_PASSWORD_MAXIMUM_LENGTH":"Maximum Length","LBL_PASSWORD_AND_MAXIMUM_LENGTH":"and Maximum Length","LBL_PASSWORD_FIRSTNAME_PROHIBITED":"First name not allowed","LBL_PASSWORD_LASTNAME_PROHIBITED":"Last name not allowed","LBL_PASSWORD_SYST_EXPIRATION":"System-Generated Password Expiration","LBL_PASSWORD_SYST_GENERATED_TITLE":"System-Generated Passwords","LBL_PASSWORD_USER_EXPIRATION":"User-Generated Password Expiration","ERR_PASSWORD_LINK_EXPIRE_TIME":"Specify the time after which the generated link will expire.","ERR_PASSWORD_EXPIRE_TIME":"Specify the time after which the password will expire.","ERR_PASSWORD_EXPIRE_LOGIN":"Specify the number of logins after which the password will expire.","ERR_PASSWORD_LOCKOUT_TIME":"Specify the time after which users may attempt to login again.","ERR_PASSWORD_LOCKOUT_LOGIN":"Specify the number of failed login attempts after which users will be locked out.","LBL_PASSWORD_LINK_EXPIRATION":"Generated Link Expiration","LBL_PASSWORD_LINK_EXPIRATION_HELP":"A link is generated by the system and sent to the user to allow the user to access the Reset Password page.","LBL_PASSWORD_REGEX":"Regex Requirement","LBL_PASSWORD_REGEX_COMMENT":"Regex Description","LBL_PASSWORD_RULES":"Password Rules","LBL_PASSWORD_LINK_EXP_IN":"Link Expires in","LBL_PASSWORD_EXP_IN":"Password Expires in","LBL_PASSWORD_USER_RESET":"User Reset Password","LBL_PERFORM_UPDATE":"Th\u1ef1c hi\u1ec7n c\u1eadp nh\u1ea5t","LBL_PLUGINS_TITLE":"CRM Forge","LBL_PLUGINS":"L\u1ea5y c\u00e1c ch\u01b0\u01a1ng tr\u00ecnh \u0111\u00ednh k\u00e8m v\u00e0 c\u00e1c ph\u1ea7n m\u1edf r\u1ed9ng","LBL_PRICE_LIST_TITLE":"S\u1ea3n ph\u1ea9m v\u00e0 B\u00e1o gi\u00e1","LBL_PRICE_LIST_DESC":"Qu\u1ea3n l\u00fd danh m\u1ee5c s\u1ea3n ph\u1ea9m v\u00e0 c\u00e1c th\u00f4ng tin li\u00ean quan","LBL_PRIVATE_TEAM":"Bi\u00ean d\u1ecbch l\u1ea1i nh\u00f3m ri\u00eang.","LBL_PRODUCT_CATEGORIES_TITLE":"Danh m\u1ee5c s\u1ea3n ph\u1ea9m","LBL_PRODUCT_CATEGORIES":"C\u1eadp nh\u1eadt danh s\u00e1ch c\u00e1c danh m\u1ee5c s\u1ea3n ph\u1ea9m","LBL_PRODUCT_TYPES_TITLE":"Lo\u1ea1i s\u1ea3n ph\u1ea9m","LBL_PRODUCT_TYPES":"C\u1ea5u h\u00ecnh danh s\u00e1ch lo\u1ea1i s\u1ea3n ph\u1ea9m","LBL_PRODUCTS_TITLE":"Danh m\u1ee5c s\u1ea3n ph\u1ea9m","LBL_PRODUCTS":"Nh\u1eadp c\u00e1c m\u1ee5c trong danh m\u1ee5c s\u1ea3n ph\u1ea9m","LBL_PROXY_AUTH":"X\u00e1c nh\u1eadn?","LBL_PROXY_HOST":"Proxy Host","LBL_PROXY_ON_DESC":"d\u00f9ng proxy","LBL_PROXY_ON":"Proxy?","LBL_PROXY_PASSWORD":"M\u1eadt kh\u1ea9u","LBL_PROXY_PORT":"Port","LBL_PROXY_TITLE":"Thi\u1ebft l\u1eadp Proxy","LBL_PROXY_USERNAME":"T\u00ean ng\u01b0\u1eddi d\u00f9ng","LBL_QUOTES_ORDERS_TITLE":"B\u00e1o gi\u00e1 v\u00e0 \u0110\u01a1n h\u00e0ng","LBL_README":"\u0110\u1ecdc tr\u01b0\u1edbc","LBL_REBUILD_AUDIT_DESC":"Bi\u00ean d\u1ecbch l\u1ea1i b\u1ea3ng ki\u1ec3m tra _audit","LBL_REBUILD_AUDIT_TITLE":"Bi\u00ean d\u1ecbch l\u1ea1i b\u1ea3ng ki\u1ec3m tra","LBL_REBUILD_AUDIT_SEARCH":"\u0110ang t\u00ecm","LBL_REBUILD_AUDIT_CREATING":"\u0111ang t\u1ea1o b\u1ea3ng %1$s cho %2$s .<BR>","LBL_REBUILD_AUDIT_SKIP":"B\u1ea3ng ki\u1ec3m tra cho %1$s \u0111\u00e3 c\u00f3. \u0111ang b\u1ecf qua...<BR>","LBL_REBUILD_CONFIG_DESC":"Bi\u00ean d\u1ecbch l\u1ea1i config.php","LBL_REBUILD_CONFIG":"Bi\u00ean d\u1ecbch l\u1ea1i t\u1eadp tin c\u1ea5u h\u00ecnh","LBL_REBUILD_DASHLETS_DESC_SHORT":"Bi\u00ean d\u1ecbch l\u1ea1i t\u1eadp tin l\u01b0u th\u00f4ng tin t\u1ea1m cho c\u00e1c B\u1ea3ng th\u00f4ng tin CRM","LBL_REBUILD_DASHLETS_DESC_SUCCESS":"T\u1eadp tin l\u01b0u th\u00f4ng tin t\u1ea1m cho c\u00e1c B\u1ea3ng th\u00f4ng tin CRM \u0111\u00e3 \u0111\u01b0\u1ee3c bi\u00ean d\u1ecbch l\u1ea1i","LBL_REBUILD_DASHLETS_DESC":"\u0110ang x\u00f3a th\u00f4ng tin t\u1ea1m cho c\u00e1c B\u1ea3ng th\u00f4ng tin CRM v\u00e0 t\u00ecm c\u00e1c B\u1ea3ng th\u00f4ng tin","LBL_REBUILD_DASHLETS_TITLE":"Bi\u00ean d\u1ecbch l\u1ea1i B\u1ea3ng th\u00f4ng tin CRM","LBL_REBUILD_EXTENSIONS_DESC":"Bi\u00ean d\u1ecbch l\u1ea1i c\u00e1c ph\u1ea7n m\u1edf r\u1ed9ng","LBL_REBUILD_EXTENSIONS_TITLE":"Bi\u00ean d\u1ecbch l\u1ea1i c\u00e1c ph\u1ea7n m\u1edf r\u1ed9ng","LBL_REBUILD_HTACCESS_DESC":"Bi\u00ean d\u1ecbch l\u1ea1i .htaccess \u0111\u1ec3 gi\u1edbi h\u1ea1n vi\u1ec7c truy c\u1eadp c\u00e1c t\u1eadp tin m\u1ed9t c\u00e1ch tr\u1ef1c ti\u1ebfp","LBL_REBUILD_HTACCESS":"Bi\u00ean d\u1ecbch l\u1ea1i t\u1eadp tin .htaccess","LBL_REBUILD_WEBCONFIG_DESC":"Rebuilds web.config to limit access to certain files directly","LBL_REBUILD_WEBCONFIG":"Rebuild web.config File","LBL_REBUILD_JAVASCRIPT_LANG_DESC_SHORT":"Bi\u00ean d\u1ecbch l\u1ea1i phi\u00ean b\u1ea3n javascript c\u1ee7a c\u00e1c t\u1eadp tin ng\u00f4n ng\u1eef","LBL_REBUILD_JAVASCRIPT_LANG_DESC":"Lo\u1ea1i c\u00e1c phi\u00ean b\u1ea3n javascript c\u1ee7a c\u00e1c t\u1eadp tin ng\u00f4n ng\u1eef, s\u1ebd bi\u00ean d\u1ecbch l\u1ea1i khi c\u1ea7n.","LBL_REBUILD_JAVASCRIPT_LANG_TITLE":"Bi\u00ean d\u1ecbch l\u1ea1i t\u1eadp tin ng\u00f4n ng\u1eef JS","LBL_REBUILD_JS_FILES_TITLE":"Bi\u00ean d\u1ecbch l\u1ea1i t\u1eadp tin JS","LBL_REBUILD_JS_FILES_DESC_SHORT":"Sao ch\u00e9p c\u00e1c t\u1eadp tin ngu\u1ed3n JS v\u00e0 thay th\u1ebf b\u1eb1ng c\u00e1c t\u1eadp tin JS n\u00e9n.","LBL_REBUILD_CONCAT_JS_FILES_TITLE":"Bi\u00ean d\u1ecbch l\u1ea1i t\u1eadp tin Nh\u00f3m JS","LBL_REBUILD_CONCAT_JS_FILES_DESC_SHORT":"Re-concatenates and overwrites existing group files with latest versions of group files","LBL_REBUILD_JS_MINI_FILES_TITLE":"Rebuild Minified JS Files","LBL_REBUILD_JS_MINI_FILES_DESC_SHORT":"Copies original Full JS Source Files and minifies them, then replaces existing compressed files","LBL_REPAIR_JS_FILES_TITLE":"Ch\u1ec9nh s\u1eeda t\u1eadp tin JS","LBL_REPAIR_JS_FILES_DESC_SHORT":"Compresses Existing JS files - includes any changes made, but does not overwrite original JS Source files","LBL_REPAIR_JS_FILES_PROCESSING":"Processing files. This may take several minutes.  Going away from this page will not cancel the process, so feel free to move on or wait for confirmation...","LBL_REPAIR_JS_FILES_DONE_PROCESSING":"Done Processing files.","LBL_REPAIR_FIELD_CASING_TITLE":"Repair Non-Lowercase Fields","LBL_REPAIR_FIELD_CASING_DESC_SHORT":"Repair mixed-case custom table(s) and metadata file(s) to fix issues where code expects lowercase field names","LBL_REPAIR_FIELD_CASING_PROCESSING":"Scanning custom fields and files...","LBL_REPAIR_FIELD_CASING_SQL_FIELD_META_DATA":"Updating row entry {0} in table fields_meta_data","LBL_REPAIR_FIELD_CASING_SQL_CUSTOM_TABLE":"Updating column {0} in table {1}","LBL_REBUILD_EXPRESSIONS_TITLE":"Rebuild Expression Engine Plugins","LBL_REBUILD_EXPRESSIONS_DESC":"Rebuilds the expression engine javascript and php cache files.","LBL_REBUILD_REL_DESC":"Bi\u00ean d\u1ecbch l\u1ea1i c\u00e1c quan h\u1ec7 d\u1eef li\u1ec7u v\u00e0 x\u00f3a c\u00e1c t\u1eadp tin t\u1ea1m","LBL_REBUILD_REL_TITLE":"Bi\u00ean d\u1ecbch l\u1ea1i c\u00e1c Quan h\u1ec7 d\u1eef li\u1ec7u","LBL_REBUILD_REL_PROC_META":"\u0110ang x\u1eed l\u00fd quan h\u1ec7 d\u1eef li\u1ec7u cho ","LBL_REBUILD_REL_PROC_C_META":"\u0110ang x\u1eed l\u00fd quan h\u1ec7 d\u1eef li\u1ec7u c\u00f3 t\u00ednh t\u00f9y bi\u1ebfn cho ","LBL_REBUILD_REL_DEL_CACHE":"\u0110ang x\u00f3a t\u1eadp tin quan h\u1ec7 d\u1eef li\u1ec7u t\u1ea1m...","LBL_REBUILD_REL_UPD_WARNING":"\u0110ang c\u1eadp nh\u1eadt c\u00e1c n\u1ed9i dung c\u1ea3nh b\u00e1o t\u1eeb ng\u01b0\u1eddi qu\u1ea3n tr\u1ecb...","LBL_REBUILD_SCHEDULERS_DESC_SHORT":"Bi\u00ean d\u1ecbch l\u1ea1i C\u00e1c c\u00f4ng vi\u1ec7c \u0111\u01b0\u1ee3c l\u00ean l\u1ecbch","LBL_REBUILD_SCHEDULERS_DESC_SUCCESS":"Bi\u00ean d\u1ecbch l\u1ea1i ho\u00e0n t\u1ea5t","LBL_REBUILD_SCHEDULERS_DESC":"...","LBL_REBUILD_SCHEDULERS_TITLE":"Bi\u00ean d\u1ecbch l\u1ea1i B\u1ed9 l\u1eadp l\u1ecbch","LBL_REBUILD_WORKFLOW_DESC":"...","LBL_REBUILD_WORKFLOW":"Bi\u00ean d\u1ecbch l\u1ea1i Lu\u1ed3ng c\u00f4ng vi\u1ec7c","LBL_REBUILD_WORKFLOW_CACHE":"\u0110ang bi\u00ean d\u1ecbch ph\u1ea7n l\u01b0u t\u1ea1m Lu\u1ed3ng c\u00f4ng vi\u1ec7c","LBL_REBUILD_WORKFLOW_COMPILING":"\u0110ang g\u1eafn c\u00e1c ph\u1ea7n b\u1ed5 sung...","LBL_REBUILD":"Bi\u00ean d\u1ecbch l\u1ea1i","LBL_REGEX_HELP_TITLE":"Regex Description","LBL_REGEX_HELP_TEXT":"<b>Regular Expressions <\/b> provide a concise and flexible means for identifying strings of the password, such as particular characters or patterns of characters. You can create custom password rules by providing a regex that will be used in a NOT MATCH condition; the password must not contain a match to any expressions in the regex.","LBL_REGEX_DESC_HELP_TEXT":"This description should explain the Regex Requirement and will be displayed in the list of requirements for users when they provide new passwords.","LBL_RELEASE":"Qu\u1ea3n l\u00fd phi\u00ean b\u1ea3n","LBL_RENAME_TABS":"\u0110\u1ed5i t\u00ean Th\u1ebb","LBL_REPAIR_ACTION":"Thao t\u00e1c b\u1ea1n ch\u1ecdn l\u00e0?","LBL_REPAIR_DATABASE_DESC":"...","LBL_REPAIR_DATABASE_PROCESSING":"\u0110ang \u0111\u1ed1i chi\u1ebfu gi\u1eefa c\u01a1 s\u1edf d\u1eef li\u1ec7u v\u00e0 c\u00e1c \u0111\u1ecbnh ngh\u0129a tham s\u1ed1...","LBL_REPAIR_DATABASE_DIFFERENCES":"S\u1ef1 kh\u00e1c nhau gi\u1eefa c\u01a1 s\u1edf d\u1eef li\u1ec7u v\u00e0 c\u00e1c \u0111\u1ecbnh ngh\u0129a tham s\u1ed1","LBL_REPAIR_DATABASE_TEXT":"The following script will sync the database structure with the structure defined in the vardefs. You have the option of exporting this script and then running it against your database using external database management tools, or to allow the administration module to run the script.<br><br><strong>NOTE: any changes you make to the script in the textbox will be reflected in the exported or executed code.<br><br>","LBL_REPAIR_DATABASE_SYNCED":"C\u00e1c b\u1ea3ng trong c\u01a1 s\u1edf d\u1eef li\u1ec7u \u0111\u00e3 \u0111\u01b0\u1ee3c \u0111\u1ed3ng b\u1ed9 v\u1edbi c\u00e1c \u0111\u1ecbnh ngh\u0129a tham s\u1ed1","LBL_REPAIR_DATABASE_EXECUTE":"Th\u1ef1c hi\u1ec7n","LBL_REPAIR_DATABASE_EXPORT":"Tr\u00edch xu\u1ea5t","LBL_REPAIR_DATABASE":"Ch\u1ec9nh s\u1eeda C\u01a1 s\u1edf d\u1eef li\u1ec7u","LBL_REPAIR_DISPLAYSQL":"Hi\u1ec3n th\u1ecb SQL","LBL_REPAIR_ENTRY_POINTS_DESC":"C\u00e1c \u0111i\u1ec3m ki\u1ec3m tra s\u1eeda ch\u1eefa. Ch\u1ea1y k\u1ecbch b\u1ea3n n\u00e0y n\u1ebfu b\u1ea1n nh\u1eadn \u0111\u01b0\u1ee3c 'Not A Valid Entry Point' l\u1ed7i.","LBL_REPAIR_ENTRY_POINTS":"S\u1eeda ch\u1eefa c\u00e1c \u0111i\u1ec3m nh\u1eadp","LBL_REPAIR_EXECUTESQL":"Th\u1ef1c thi SQL","LBL_REPAIR_EXPORTSQL":"Tr\u00edch xu\u1ea5t SQL","LBL_REPAIR_IE_DESC":"Ch\u1ec9nh s\u1eeda c\u00e1c t\u1ea1i kho\u1ea3n th\u01b0 \u0111i\u1ec7n t\u1eed v\u00e0 m\u00e3 h\u00f3a c\u00e1c m\u1eadt kh\u1ea9u","LBL_REPAIR_IE_FAILURE":"Vui l\u00f2ng nh\u1eadp l\u1ea1i t\u00ean v\u00e0 m\u1eadt kh\u1ea9u:","LBL_REPAIR_IE_SUCCESS":"Ch\u1ec9nh s\u1eeda c\u00e1c t\u00e0i kho\u1ea3n th\u01b0 \u0111i\u1ec7n t\u1eed th\u00e0nh c\u00f4ng","LBL_REPAIR_IE":"Ch\u1ec9nh s\u1eeda c\u00e1c t\u00e0i kho\u1ea3n th\u01b0 \u0111i\u1ec7n t\u1eed","LBL_REPAIR_INDEX":"Ch\u1ec9nh s\u1eeda Ch\u1ec9 m\u1ee5c","LBL_REPAIR_INDEX_DROPPING":"\u0110ang x\u00f3a c\u00e1c r\u00e0ng bu\u1ed9c\/ch\u1ec9 m\u1ee5c","LBL_REPAIR_INDEX_EXECUTING":"\u0110ang th\u1ef1c thi","LBL_REPAIR_INDEX_DROP":"X\u00f3a c\u00e1c r\u00e0ng bu\u1ed9c\/ch\u1ec9 m\u1ee5c n\u00e0y","LBL_REPAIR_INDEX_ADDING":"\u0110ang th\u00eam c\u00e1c r\u00e0ng bu\u1ed9c\/ch\u1ec9 m\u1ee5c","LBL_REPAIR_INDEX_ADD":"Th\u00eam c\u00e1c r\u00e0ng bu\u1ed9c\/ch\u1ec9 m\u1ee5c n\u00e0y.","LBL_REPAIR_INDEX_ALTERING":"\u0110ang b\u1ed5 sung c\u00e1c r\u00e0ng bu\u1ed9c\/ch\u1ec9 m\u1ee5c.","LBL_REPAIR_INDEX_ALTER":"B\u1ed5 sung c\u00e1c r\u00e0ng bu\u1ed9c\/ch\u1ec9 m\u1ee5c n\u00e0y.","LBL_REPAIR_INDEX_SYNC":"C\u00e1c ch\u1ec9 m\u1ee5c \u0111\u00e3 \u0111\u1ed3ng b\u1ed9","LBL_REPAIR_ORACLE_COMMIT_DONE":"Th\u1ef1c thi t\u1ea5t c\u1ea3 c\u00e1c truy v\u1ea5n SQL.","LBL_REPAIR_ORACLE_COMMIT":"X\u00e1c nh\u1eadn","LBL_REPAIR_ORACLE_VARCHAR_DESC_LONG_1":"ORACLE","LBL_REPAIR_ORACLE_VARCHAR_DESC_LONG_2":"ORACLE","LBL_REPAIR_ORACLE_VARCHAR_DESC":"ORACLE","LBL_REPAIR_ORACLE_VARCHAR":"ORACLE","LBL_REPAIR_ROLES_DESC":"Vai tr\u00f2 s\u1eeda ch\u1eefa b\u1eb1ng c\u00e1ch th\u00eam c\u00e1c module m\u1edbi c\u00f3 h\u1ed7 tr\u1ee3 truy c\u1eadp \u0111i\u1ec1u khi\u1ec3n, v\u00e0 b\u1eb1ng c\u00e1ch th\u00eam c\u00e1c \u0111i\u1ec1u khi\u1ec3n truy c\u1eadp b\u1ea5t k\u1ef3 m\u1edbi cho c\u00e1c module hi\u1ec7n c\u00f3","LBL_REPAIR_ROLES":"Ch\u1ec9nh s\u1eeda Vai tr\u00f2","LBL_REPAIR_TEAMS_DESC":"Bi\u00ean d\u1ecbch l\u1ea1i c\u00e1c th\u00e0nh vi\u00ean trong nh\u00f3m ri\u00eang v\u00e0 c\u00e2y t\u1ed5 ch\u1ee9c","LBL_REPAIR_TEAMS":"Ch\u1ec9nh s\u1eeda nh\u00f3m","LBL_REPAIR_TEAMS_PROCESS_USER":"\u0110ang x\u1eed l\u00fd ng\u01b0\u1eddi d\u00f9ng=","LBL_REPAIR_TEAMS_REPORT":"B\u00e1o c\u00e1o \u0111\u1ebfn=","LBL_REPAIR_TEAMS_SKIP_USER":"\u0110ang b\u1ecf qua=","LBL_REPAIR_TEAMS_NO_PROC":"Kh\u00f4ng x\u1eed l\u00fd...","LBL_REPAIR_THEME_IMAGES":"T\u1ed1i \u01b0u Giao di\u1ec7n","LBL_REPAIR_THEME_IMAGES_DESC":"T\u1ed1i \u01b0u giao di\u1ec7n b\u1eb1ng c\u00e1ch x\u00f3a c\u00e1c h\u00ecnh \u1ea3nh gi\u1ed1ng nhau.","LBL_REPAIR_XSS":"X\u00f3a XSS","LBL_REPAIR_ACTIVITIES_DESC":"Ch\u1ec9nh s\u1eeda ng\u00e0y k\u1ebft th\u00fac c\u1ee7a c\u00e1c Ho\u1ea1t \u0111\u1ed9ng","LBL_REPAIR_ACTIVITIES_TEXT":"C\u00f4ng c\u1ee5 n\u00e0y cho ph\u00e9p thay \u0111\u1ed5i ng\u00e0y k\u1ebft th\u00fac c\u1ee7a Cu\u1ed9c g\u1ecdi v\u00e0 H\u1ed9i h\u1ecdp","LBL_REPAIR_ACTIVITIES":"Ch\u1ec9nh s\u1eeda Ho\u1ea1t \u0111\u1ed9ng","LBL_CHECK_REPORTS_DESC":"Ki\u1ec3m tra xem c\u00e1c b\u00e1o c\u00e1o \u0111ang c\u00f2n hi\u1ec7u l\u1ef1c sau khi n\u00e2ng c\u1ea5p v\u00e0 danh s\u00e1ch b\u00e1o c\u00e1o kh\u00f4ng h\u1ee3p l\u1ec7 n\u00e0o \u0111\u01b0\u1ee3c t\u00ecm th\u1ea5y trong qu\u00e1 tr\u00ecnh ki\u1ec3m tra","LBL_CHECK_REPORTS_TEXT":"C\u00f4ng c\u1ee5 n\u00e0y gi\u00fap ki\u1ec3m tra t\u00ednh h\u1ee3p l\u1ec7 c\u1ee7a c\u00e1c b\u00e1o c\u00e1o.","LBL_CHECK_REPORTS":"Ki\u1ec3m tra B\u00e1o c\u00e1o","LBL_ALL":"T\u1ea5t c\u1ea3","LBL_REPAIRXSS_COUNT":"\u0110\u1ed1i t\u01b0\u1ee3ng \u0111\u01b0\u1ee3c t\u00ecm th\u1ea5y","LBL_REPAIRXSS_INSTRUCTIONS":"Nh\u1ea5n th\u1ef1c hi \u0111\u1ec3 b\u1eaft \u0111\u1ea7u.","LBL_REPAIRXSS_REPAIRED":"C\u00e1c \u0111\u1ed1i t\u01b0\u1ee3ng \u0111\u00e3 \u0111\u01b0\u1ee3c ch\u1ec9nh s\u1eeda","LBL_REPAIRXSS_TITLE":"X\u00f3a XSS trong c\u01a1 s\u1edf d\u1eef li\u1ec7u","LBL_RESERVE_OC_SPOT":"Chu\u1ea9n b\u1ecb v\u1ecb tr\u00ed cho M\u00e1y tr\u1ea1m Ngo\u1ea1i tuy\u1ebfn","LBL_RESTORE_BUTTON_LABEL":"Restore","LBL_RETURN":"Quay lai","LBL_REVALIDATE":"Ki\u1ec3m tra l\u1ea1i","LBL_SEARCH_MODULE_NAME":"Nh\u1eadp \u0111\u1ea7y \u0111\u1ee7 ho\u1eb7c m\u1ed9t ph\u1ea7n t\u00ean ph\u00e2n h\u1ec7:","LBL_SELECT_USER":"Ch\u1ecdn Ng\u01b0\u1eddi d\u00f9ng","LBL_SEND_STAT":"G\u1eedi th\u00f4ng tin th\u1ed1ng k\u00ea","LBL_SHIPPERS_TITLE":"\u0110ang b\u1ecf qua Providers","LBL_SHIPPERS":"Thi\u1ebft l\u1eadp danh s\u00e1ch c\u00e1c ph\u01b0\u01a1ng th\u1ee9c v\u1eadn t\u1ea3i","LBL_SHOW_ADVANCED_OPTIONS":"Hi\u1ec7n t\u00f9y ch\u1ecdn n\u00e2ng cao","LBL_STATUS":"Tr\u1ea1ng th\u00e1i","LBL_TOOLS_DESC":"T\u1ea1o v\u00e0 ch\u1ec9nh s\u1eeda m\u00f4-\u0111un v\u00e0 b\u1ed1 tr\u00ed m\u00f4-\u0111un, qu\u1ea3n l\u00fd l\u0129nh v\u1ef1c ti\u00eau chu\u1ea9n v\u00e0 t\u00f9y ch\u1ec9nh v\u00e0 c\u1ea5u h\u00ecnh c\u00e1c tab.","LBL_STUDIO_DESC":"T\u00f9y ch\u1ec9nh c\u00e1c l\u0129nh v\u1ef1c m\u00f4-\u0111un, b\u1ed1 c\u1ee5c v\u00e0 c\u00e1c m\u1ed1i quan h\u1ec7","LBL_STUDIO_TITLE":"C\u00e1c c\u00f4ng c\u1ee5 ph\u00e1t tri\u1ec3n","LBL_STUDIO":"Studio","LBL_incomCRM_NETWORK_TITLE":"M\u1ea1ng l\u01b0\u1edbi CRM","LBL_incomCRM_NETWORK_DESC":"K\u1ebft n\u1ed1i v\u1edbi c\u00e1c d\u1ecbch v\u1ee5 CRM kh\u00e1c nhau, n\u01a1i b\u1ea1n c\u00f3 th\u1ec3 truy c\u1eadp c\u00e1c di\u1ec5n \u0111\u00e0n Di\u1ec5n v\u00e0 \u0111\u01b0\u1eddng Wiki, t\u00ecm ki\u1ebfm FAQ (c\u00e2u h\u1ecfi th\u01b0\u1eddng g\u1eb7p), t\u1ea3i v\u1ec1 t\u1eadp tin phi\u00ean b\u1ea3n m\u1edbi nh\u1ea5t c\u1ee7a CRM, v\u00e0 nghi\u00ean c\u1ee9u b\u00e1o c\u00e1o l\u1ed7i v\u00e0 c\u00e1c t\u00ednh n\u0103ng y\u00eau c\u1ea7u m\u1edbi v\u00e0 nhi\u1ec1u h\u01a1n n\u1eefa.","LBL_incomCRM_SCHEDULER_TITLE":"B\u1ed9 l\u1eadp l\u1ecbch","LBL_incomCRM_SCHEDULER":"Thi\u1ebft l\u1eadp c\u00e1c s\u1ef1 ki\u1ec7n \u0111\u00e3 \u0111\u01b0\u1ee3c l\u00ean l\u1ecbch","LBL_incomCRM_SERVER_URL_DESC":"- URL c\u1ee7a m\u00e1y ch\u1ee7 s\u1ebd k\u1ebft n\u1ed1i (vd: http:\/\/localhost\/incomCRMserver).","LBL_incomCRM_SERVER_URL":"URL m\u00e1y ch\u1ee7:","LBL_incomCRM_UPDATE_TITLE":"C\u1eadp nh\u1eadt CRM","LBL_incomCRM_UPDATE":"Ki\u1ec3m tra phi\u00ean b\u1ea3n cu\u1ed1i","LBL_incomCRM_HELP":"H\u01b0\u1edbng d\u1eabn CRM","LBL_SUPPORT_TITLE":"C\u1ed5ng h\u1ed7 tr\u1ee3 CRM","LBL_SUPPORT":"Truy c\u1eadp c\u1ed5ng c\u00e1 nh\u00e2n cho vi\u1ec7c h\u1ed7 tr\u1ee3 k\u1ef9 thu\u1eadt ho\u1eb7c kh\u00e1c","LBL_SYSTEM_NAME":"T\u00ean h\u1ec7 th\u1ed1ng","LBL_TAXRATES_TITLE":"M\u1ee9c thu\u1ebf","LBL_TAXRATES":"C\u1ea5u h\u00ecnh danh s\u00e1ch c\u00e1c m\u1ee9c thu\u1ebf","LBL_TEAM_HIERARCHY":"Bi\u00ean d\u1ecbch l\u1ea1i c\u00e2y t\u1ed5 ch\u1ee9c.","LBL_TEAMS_TITLE":"Teams","LBL_TERMS_AND_CONDITIONS":"C\u00e1c \u0111i\u1ec1u kho\u1ea3n","LBL_THEME_SETTINGS":"C\u00e0i \u0111\u1eb7t c\u00e1c giao di\u1ec7n","LBL_THEME_SETTINGS_DESC":"Ch\u1ecdn giao di\u1ec7n \u0111\u1ec3 ng\u01b0\u1eddi d\u00f9ng c\u00f3 th\u1ec3 ch\u1ecdn","LBL_THEME_SETTINGS_AVAILABLE_THEMES":"Available Themes","LBL_THEME_SETTINGS_UNAVAILABLE_THEMES":"Unavailable Themes","LBL_TIMEZONE":"M\u00fai gi\u1edd","LBL_TO":" \u0111\u1ebfn ","LBL_TRACKER_SETTINGS":"Tracker Settings","LBL_TRACKER_SETTINGS_DESC":"Enable\/Disable tracking","LBL_incomCRMFEED_SETTINGS":"Thi\u1ebft l\u1eadp CRM Feed","LBL_incomCRMFEED_SETTINGS_DESC":"...","LBL_UPDATE_CHECK_AUTO":"T\u1ef1 \u0111\u1ed9ng","LBL_UPDATE_CHECK_MANUAL":"L\u00e0m b\u1eb1ng tay","LBL_UPDATE_CHECK_TYPE":"Ki\u1ec3m tra c\u00e1c phi\u00ean b\u1ea3n n\u00e2ng c\u1ea5p 1 c\u00e1ch t\u1ef1 \u0111\u1ed9ng.","LBL_UPDATE_DESCRIPTIONS":"M\u00f4 t\u1ea3","LBL_UPDATE_TITLE":"N\u00e2ng c\u1ea5p CRM","LBL_UPGRADE_ADDED_TO_GROUP":"\u0110\u00e3 th\u00eam v\u00e0o nh\u00f3m","LBL_UPGRADE_ALREADY_EXISTS_GROUP":"nh\u00f3m \u0111\u00e3 c\u00f3 s\u1eb5n","LBL_UPGRADE_ALREADY_EXISTS_IN_GROUP":"\u0110\u00e3 c\u00f3 s\u1eb5n trong nh\u00f3m:","LBL_UPGRADE_CONVERT_DISC_CLIENT_TITLE":"Chuy\u1ec3n \u0111\u1ed5i th\u00e0nh M\u00e1y Tr\u1ea1m Ngo\u1ea1i tuy\u1ebfn","LBL_UPGRADE_CONVERT_DISC_CLIENT":"Chuy\u1ec3n \u0111\u1ed5i c\u00e0i \u0111\u1eb7t CRM th\u00e0nh M\u00e1y Tr\u1ea1m Ngo\u1ea1i tuy\u1ebfn","LBL_UPGRADE_CONVERT_DISC_DESCRIPTION":"C\u1ea3nh b\u00e1o: Vui l\u00f2ng ch\u1eafc r\u1eb1ng \u0111\u00e2y l\u00e0 b\u1ea3n c\u00e0i \u0111\u1eb7t m\u1edbi v\u00e0 ch\u01b0a c\u00f3 d\u1eef li\u1ec7u.","LBL_UPGRADE_CURRENCY":"N\u00e2ng c\u1ea5p lo\u1ea1i ti\u1ec1n t\u1ec7 trong","LBL_UPGRADE_CUSTOM_LABELS_DESC":"N\u00e2ng c\u1ea5p \u0111\u1ecbnh d\u1ea1ng hi\u1ec3n th\u1ecb trong c\u00e1c t\u1eadp tin ng\u00f4n ng\u1eef.","LBL_UPGRADE_CUSTOM_LABELS_TITLE":"N\u00e2ng c\u1ea5p c\u00e1c Nh\u00e3n t\u00f9y bi\u1ebfn","LBL_UPGRADE_DB_BEGIN":"B\u1eaft \u0111\u1ea7u n\u00e2ng c\u1ea5p","LBL_UPGRADE_DB_COMPLETE":"N\u00e2ng c\u1ea5p ho\u00e0n t\u1ea5t","LBL_UPGRADE_DB_FAIL":"N\u00e2ng c\u1ea5p th\u1ea5t b\u1ea1i","LBL_UPGRADE_DB_TITLE":"N\u00e2ng c\u1ea5p c\u01a1 s\u1edf d\u1eef li\u1ec7u","LBL_UPGRADE_DB":"N\u00e2ng c\u1ea5p s\u01a1 s\u1edf d\u1eef li\u1ec7u t\u1eeb phi\u00ean b\u1ea3n 2.0.x \u0111\u1ebfn 2.5","LBL_UPGRADE_FOR_PRODUCT":"cho S\u1ea3n ph\u1ea9m","LBL_UPGRADE_NEW_GROUP":"\u0110\u00e3 t\u1ea1o Nh\u00f3m m\u1edbi","LBL_UPGRADE_NOT_UPGRADING":"Kh\u00f4ng n\u00e2ng c\u1ea5p","LBL_UPGRADE_STUDIO_DESC":"...","LBL_UPGRADE_STUDIO_TITLE":"N\u00e2ng c\u1ea5p Ph\u00f2ng ph\u00e1t h\u00e0nh","LBL_UPGRADE_SYNC_DISC_CLIENT_TITLE":"\u0110\u1ed3ng b\u1ed9","LBL_UPGRADE_SYNC_DISC_CLIENT":"\u0110\u1ed3ng b\u1ed9 d\u1eef li\u1ec7u t\u1eadp tin m\u00e1y tr\u1ea1m v\u00e0 d\u1eef li\u1ec7u \u0111\u1ebfn m\u00e1y ch\u1ee7","LBL_UPGRADE_TEAM_CREATE":"T\u1ea1o Nh\u00f3m cho - ","LBL_UPGRADE_TEAM_EXISTS":"Nh\u00f3m \u0111\u00e3 c\u00f3 s\u1eb5n","LBL_UPGRADE_TEAM_TITLE":"N\u00e2ng c\u1ea5p nh\u00f3m","LBL_UPGRADE_TEAMS":"T\u1ea1o nh\u00f3m ng\u01b0\u1eddi d\u00f9ng","LBL_UPGRADE_TITLE":"Ch\u1ec9nh s\u1eeda","LBL_UPGRADE_VERSION":"\u0110ang c\u1eadp nh\u1eadt th\u00f4ng tin phi\u00ean b\u1ea3n","LBL_UPGRADE_WIZARD_TITLE":"N\u00e2ng c\u1ea5p Th\u00f4ng minh","LBL_UPGRADE_WIZARD":"T\u1ea3i l\u00ean v\u00e0 c\u00e0i \u0111\u1eb7t c\u00e1c phi\u00ean b\u1ea3n n\u00e2ng c\u1ea5p","LBL_UPGRADE":"Ki\u1ec3m tra v\u00e0 ch\u1ec9nh s\u1eeda CRM","LBL_UPLOAD_UPGRADE":"T\u1ea3i l\u00ean v\u00e0 n\u00e2ng c\u1ea5p","LBL_UPTODATE":"B\u1ea1n \u0111\u00e3 c\u00f3 phi\u00ean b\u1ea3n m\u1edbi nh\u1ea5t","LBL_USER_NAME":"T\u00ean ng\u01b0\u1eddi d\u00f9ng:","LBL_USERNAME":"T\u00ean ng\u01b0\u1eddi d\u00f9ng:","LBL_USERS_TITLE":"Ng\u01b0\u1eddi d\u00f9ng","LBL_USERS_DESC":"T\u1ea1o, ch\u1ec9nh, k\u00edch ho\u1ea1t v\u00e0 d\u1eebng c\u00e1c ng\u01b0\u1eddi dung trong CRM. T\u1ea1o v\u00e0 qu\u1ea3n l\u00fd nh\u00f3m.","LBL_UW_BTN_BACK_TO_MOD_LOADER":"Quay v\u1ec1 T\u1ea3i Ph\u00e2n h\u1ec7","LBL_UW_BTN_BACK_TO_UW":"Quay l\u1ea1i N\u00e2ng c\u1ea5p Th\u00f4ng minh","LBL_UW_BTN_DELETE_PACKAGE":"X\u00f3a G\u00f3i","LBL_UW_BTN_DOWNLOAD":"T\u1ea3i v\u1ec1","LBL_UW_BTN_INSTALL":"C\u00e0i \u0111\u1eb7t","LBL_UW_BTN_UPLOAD":"T\u1ea3i l\u00ean","LBL_UW_CHECK_ALL":"Ki\u1ec3m tra h\u1ebft","LBL_UW_DESC_MODULES_INSTALLED":"C\u00e1c ph\u1ea7n m\u1edf r\u1ed9ng \u0111\u00e3 \u0111\u01b0\u1ee3c c\u00e0i \u0111\u1eb7t trong h\u1ec7 th\u1ed1ng:","LBL_UW_DESC_MODULES_QUEUED":"C\u00e1c ph\u1ea7n m\u1edf r\u1ed9ng s\u1eb5n s\u00e0ng \u0111\u1ec3 c\u00e0i v\u00e0o h\u1ec7 th\u1ed1ng:","LBL_UW_DISABLE":"Kh\u00f3a","LBL_UW_ENABLE":"M\u1edf","LBL_UW_FOLLOWING_FILES":"C\u00e1c t\u1eadp tin l\u00e0","LBL_UW_HIDE_DETAILS":"C\u00e1c chi ti\u1ebft \u1ea9n","LBL_UW_INCLUDING":"\u0110ang g\u1eafn","LBL_UW_MODULE_READY_DISABLE":"C\u00e1c ph\u00e2n h\u1ec7 s\u1eb5n s\u00e0ng \u0111\u1ec3 kh\u00f3a.","LBL_UW_MODULE_READY_ENABLE":"C\u00e1c ph\u00e2n h\u1ec7 s\u1eb5n s\u00e0ng \u0111\u1ec3 m\u1edf.","LBL_UW_MODULE_READY_UNISTALL":"C\u00e1c ph\u00e2n h\u1ec7 s\u1eb5n s\u00e0ng \u0111\u1ec3 g\u1ee1 b\u1ecf.","LBL_UW_MODULE_READY":"C\u00e1c ph\u00e2n h\u1ec7 s\u1eb5n s\u00e0ng \u0111\u1ec3 c\u00e0i \u0111\u1eb7t.","LBL_UW_LANGPACK_READY_DISABLE":"The language package is ready to be disabled.","LBL_UW_LANGPACK_READY_ENABLE":"The language package is ready to be enabled.","LBL_UW_LANGPACK_READY_UNISTALL":"The language package is ready to be uninstalled.","LBL_UW_LANGPACK_READY":"The language package is ready to be installed.","LBL_UW_NO_FILES_SELECTED":"Ch\u01b0a ch\u1ecdn t\u1eadp tin","LBL_UW_NO_INSTALLED_UPGRADES":"Kh\u00f4ng c\u00f3 n\u1ed9i dung \u0111\u01b0\u1ee3c n\u00e2ng c\u1ea5p","LBL_UW_NONE":"Kh\u00f4ng","LBL_UW_NOT_AVAILABLE":"Kh\u00f4ng c\u00f3 s\u1eb5n","LBL_UW_OP_MODE":"Ph\u01b0\u01a1ng th\u1ee9c x\u1eed l\u00fd:","LBL_UW_PACKAGE_REMOVED":"\u0111\u00e3 \u0111\u01b0\u1ee3c lo\u1ea1i b\u1ecf","LBL_UW_PATCH_READY":"<h2>S\u1eb5n s\u00e0ng \u0111\u1ec3 c\u00e0i \u0111\u1eb7t<\/h2>","LBL_UW_UNINSTALL_READY":"<h2>S\u1eb5n s\u00e0ng \u0111\u1ec3 g\u1ee1 b\u1ecf<\/h2>","LBL_UW_DISABLE_READY":"<h2>S\u1eb5n s\u00e0ng \u0111\u1ec3 v\u00f4 hi\u1ec7u ho\u00e1<\/h2>","LBL_UW_ENABLE_READY":"<h2>S\u1eb5n s\u00e0ng \u0111\u1ec3 k\u00edch ho\u1ea1t<\/h2>","LBL_UW_SHOW_DETAILS":"Xem chi ti\u1ebft","LBL_UW_SUCCESSFUL":"Th\u00e0nh c\u00f4ng","LBL_UW_SUCCESSFULLY":"Th\u00e0nh c\u00f4ng","LBL_UW_UNINSTALL":"G\u1ee1 b\u1ecf","LBL_UW_UPGRADE_SUCCESSFUL":"N\u00e2ng c\u1ea5p th\u00e0nh c\u00f4ng!","LBL_UW_UPLOAD_MODULE":"Ph\u00e2n h\u1ec7","LBL_UW_TYPE_FULL":"B\u1ea3n n\u00e2ng c\u1ea5p \u0111\u1ea7y \u0111\u1ee7","LBL_UW_TYPE_LANGPACK":"Ng\u00f4n ng\u1eef","LBL_UW_TYPE_MODULE":"Ph\u00e2n h\u1ec7","LBL_UW_TYPE_PATCH":"B\u1ea3n v\u00e1","LBL_UW_TYPE_THEME":"Theme","LBL_UW_MODE_INSTALL":"C\u00e0i \u0111\u1eb7t","LBL_UW_MODE_UNINSTALL":"G\u1ee1 b\u1ecf c\u00e0i \u0111\u1eb7t","LBL_UW_MODE_DISABLE":"V\u00f4 hi\u1ec7u h\u00f3a","LBL_UW_MODE_ENABLE":"K\u00edch ho\u1ea1t","LBL_UW_UPLOAD_SUCCESS":" \u0111\u00e3 \u0111\u01b0\u1ee3c t\u1ea3i l\u00ean","LBL_VALIDATION_FAIL_DATE":"X\u00e1c nh\u1eadn b\u1ecb l\u1ed7i g\u1ea7n nh\u1ea5t :","LBL_VALIDATION_FILE":"T\u1eadp tin ch\u1ee9a kh\u00f3a x\u00e1c nh\u1eadn","LBL_VALIDATION_SUCCESS_DATE":"X\u00e1c nh\u1eadn th\u00e0nh c\u00f4ng g\u1ea7n nh\u1ea5t :","LBL_VISIBLE_PANELS":"Displayed Subpanels","LBL_VISIBLE_TABS":"C\u00e1c th\u1ebb hi\u1ec3n th\u1ecb","LBL_WORKFLOW_DESC":"Qu\u1ea3n l\u00fd c\u00e1c \u0111i\u1ec1u ki\u1ec7n, c\u1ea3nh b\u00e1o v\u00e0 c\u00e1c ho\u1ea1t \u0111\u1ed9ng","LBL_WORKFLOW_TITLE":"Qu\u1ea3n l\u00fd Qui tr\u00ecnh","LBL_WORKBENCH":"Chu\u1ed7i c\u00f4ng vi\u1ec7c","LBL_WORKBENCH_DESC":"T\u00f9y ch\u1ec9nh \u1ee9ng d\u1ee5ng v\u00e0 t\u1ea1o c\u00e1c ph\u00e2n h\u1ec7 m\u1edbi","LNK_DISABLE":"Ho\u00e3n","LNK_ENABLE":"K\u00edch ho\u1ea1t","LNK_FORGOT_PASS":"Qu\u00ean m\u1eadt kh\u1ea9u?","LNK_NEW_ACCOUNT":"T\u1ea1o m\u1ed9t t\u00e0i kho\u1ea3n","LNK_NEW_PRODUCT":"T\u1ea1o S\u1ea3n ph\u1ea9m","LNK_NEW_TEAM":"T\u1ea1o Nh\u00f3m","LNK_NEW_USER":"T\u1ea1o Ng\u01b0\u1eddi d\u00f9ng m\u1edbi","LNK_REPAIR_CUSTOM_FIELD":"Ch\u1ec9nh s\u1eeda C\u00e1c thu\u1ed9c t\u00ednh T\u00f9y ch\u1ec9nh","LNK_SELECT_CUSTOM_FIELD":"Ch\u1ecdn Thu\u1ed9c t\u00edch T\u00f9y ch\u1ec9nh","LNK_NEW_PORTAL_USER":"Create New Portal User","LNK_NEW_GROUP_USER":"Create New Group User","MI_LBL_DISABLE_DEPEDENCIES":"Ph\u00e2n h\u1ec7 n\u00e0y kh\u00f4ng th\u1ec3 kh\u00f3a do c\u00f3 ph\u00e2n h\u1ec7 kh\u00e1c ph\u1ee5 thu\u1ed9c v\u00e0o.","MI_REDIRECT_TO_UPGRADE_WIZARD":"Ph\u00e2n h\u1ec7 N\u00e2ng c\u1ea5p Th\u00f4ng minh s\u1ebd t\u1ef1 k\u00edch ho\u1ea1t \u0111\u1ec3 ki\u1ec3m tra h\u1ec7 th\u1ed1ng. B\u1ea1n mu\u1ed1n ti\u1ebfp t\u1ee5c?","ML_DESC_DOCUMENTATION":"Nh\u1ea5n v\u00e0o m\u1ed9t n\u00fat con \u0111\u1ec3 xem chi ti\u1ebft v\u00e0 t\u00e0i li\u1ec7u li\u00ean quan.","ML_LBL_DETAIILS":"Chi ti\u1ebft","ML_LBL_DO_NOT_REMOVE_TABLES":"\u0110\u1eebng x\u00f3a c\u00e1c b\u1ea3ng","ML_LBL_DOCUMENTATION":"T\u00e0i li\u1ec7u","ML_LBL_INSTALL_FROM_LOCAL":"C\u00e0i \u0111\u1eb7t t\u1eeb t\u1eadp tin n\u1ed9i b\u1ed9","ML_LBL_INSTALL_FROM_SERVER":"C\u00e0i \u0111\u1eb7t t\u1eeb CRM","ML_LBL_OVERWRITE_FILES":"Ph\u00e2n h\u1ec7 li\u00ean quan \u0111\u00e3 b\u1ecb thay \u0111\u1ed5i t\u1eeb thao t\u00e1c g\u1ea7n nh\u1ea5t. Thao t\u00e1c b\u1ea1n ch\u1ecdn la g\u00ec?","ML_LBL_REMOVE_TABLES":"Lo\u1ea1i b\u1ecf c\u00e1c b\u1ea3ng","ML_LBL_REVIEWS":"Xem l\u1ea1i","ML_LBL_SCREENSHOTS":"H\u00ecnh ch\u1ee5p","MSG_CONFIG_FILE_READY_FOR_REBUILD":"T\u1eadp tin config.php c\u00f3 th\u1ec3 bi\u00ean d\u1ecbch l\u1ea1i.","MSG_CONFIG_FILE_REBUILD_FAILED":"T\u1eadp tin config.php ch\u01b0a c\u00f3 th\u1ec3 bi\u00ean d\u1ecbch l\u1ea1i.","MSG_CONFIG_FILE_REBUILD_SUCCESS":"T\u1eadp tin config.php \u0111\u00e3 bi\u00ean d\u1ecbch l\u1ea1i th\u00e0nh c\u00f4ng.","MSG_INCREASE_UPLOAD_MAX_FILESIZE":"C\u1ea3nh b\u00e1o:H\u1ec7 th\u1ed1ng ch\u1ec9 cho ph\u00e9p nh\u1eefng t\u1eadp tin c\u00f3 k\u00edch th\u01b0\u1edbc t\u1ed1i \u0111a l\u00e0 6MB.","MSG_MAKE_CONFIG_FILE_WRITABLE":"Vui l\u00f2ng thay \u0111\u1ed5i thu\u1ed9c t\u00ednh \u0111\u1ec3 c\u00f3 th\u1ec3 ghi v\u00e0o t\u1eadp tin config.php v\u00e0 th\u1eed l\u1ea1i.","MSG_REBUILD_EXTENSIONS":"Vui l\u00f2ng \u0111\u1ebfn m\u1ee5c Ch\u1ec9nh s\u1eeda giao di\u1ec7n v\u00e0 ch\u1ecdn Bi\u00ean d\u1ecbch l\u1ea1i Ph\u1ea7n m\u1edf r\u1ed9ng.","MSG_REBUILD_RELATIONSHIPS":"Vui l\u00f2ng \u0111\u1ebfn m\u1ee5c Ch\u1ec9nh s\u1eeda giao di\u1ec7n v\u00e0 ch\u1ecdn Bi\u00ean d\u1ecbch l\u1ea1i Quan h\u1ec7.","NO_ENABLED_OFFLINE_CLIENTS":"Hi\u1ec7n kh\u00f4ng c\u00f3 M\u00e1y tr\u1ea1m Ngo\u1ea1i tuy\u1ebfn \u0111ang ho\u1ea1t \u0111\u1ed9ng.","NTC_DISABLE_OFFLINE_CLIENT_ALERT":"B\u1ea1n ch\u1eafc r\u1eb1ng mu\u1ed1n kh\u00f3a M\u00e1y tr\u1ea1m Ngo\u1ea1i tuy\u1ebfn n\u00e0y?","NTC_ENABLE_OFFLINE_CLIENT_ALERT":"B\u1ea1n ch\u1eafc r\u1eb1ng mu\u1ed1n m\u1edf M\u00e1y tr\u1ea1m Ngo\u1ea1i tuy\u1ebfn n\u00e0y?","NTC_OC_NOT_AVAILABLE":"Kh\u00f4ng c\u00f3 s\u1eb5n","NTC_OC_RESERVED":"\u0110\u1eb7t tr\u01b0\u1edbc","REMOVE_QUESTION":"B\u1ea1n mu\u1ed1n lo\u1ea1i b\u1ecf g\u00f3i \u0111\u01b0\u1ee3c ch\u1ecdn?","WARN_POSSIBLE_JS_OVERWRITE":"\u0110i\u1ec1u n\u00e0y s\u1ebd l\u00e0m thay \u0111\u1ed5i t\u1eadp tin javascript, b\u1ea1n mu\u1ed1n th\u1ef1c hi\u1ec7n?","WARN_INSTALLER_LOCKED":"C\u1ea3nh b\u00e1o: \u0110\u1ec3 \u0111\u1ea3m b\u1ea3o d\u1eef li\u1ec7u b\u1ea1n ph\u1ea3i \u0111\u1eb7t 'installer_locked' th\u00e0nh 'true' trong t\u1eadp tin config.php","WARN_LICENSE_EXPIRED":"Ghi ch\u00fa: B\u1ea3n quy\u1ec1n c\u1ee7a b\u1ea1n s\u1ebd h\u1ebft h\u1ea1n trong","WARN_LICENSE_EXPIRED2":"ng\u00e0y. Vui l\u00f2ng v\u00e0o \"Qu\u1ea3n l\u00fd b\u1ea3n quy\u1ec1n trong m\u00e0n h\u00ecnh Qu\u1ea3n tr\u1ecb.","WARN_LICENSE_SEATS":"C\u1ea3nh b\u00e1o: S\u1ed1 l\u01b0\u1ee3ng b\u1ea3n quy\u1ec1n ng\u01b0\u1eddi d\u00f9ng \u0111\u00e3 v\u01b0\u1ee3t qu\u00e1 do","WARN_LICENSE_SEATS2":". Vui l\u00f2ng li\u00ean h\u1ec7 <EMAIL>.","WARN_LICENSE_SEATS_MAXED":"Warning: The number of active users is already the maximum number of licenses allowed: ","WARN_LICENSE_SEATS_EDIT_USER":"Warning: The number of active users is already the maximum number of licenses allowed","WARN_LICENSE_SEATS_USER_CREATE":"Warning: The number of active users allowed by your license matches the number of active users in the system. You will not be able to create additional active users.","WARN_REPAIR_CONFIG":"C\u1ea3nh b\u00e1o: T\u1eadp tin config.php c\u1ea7n \u0111\u01b0\u1ee3c s\u1eeda ch\u1eefa.","WARN_UPGRADE_APP":"M\u1ed9t phi\u00ean b\u1ea3n c\u1eadp nh\u1eadt \u0111\u00e3 s\u1eb5n s\u00e0ng.","WARN_UPGRADE":"C\u1ea3nh b\u00e1o: Vui l\u00f2ng n\u00e2ng c\u1ea5p","WARN_UPGRADE2":"s\u1eed d\u1ee5ng t\u00ednh n\u0103ng n\u00e2ng c\u1ea5p trong \u00f4 Qu\u1ea3n tr\u1ecb","WARN_VALIDATION_EXPIRED":"Ghi ch\u00fa: Kh\u00f3a x\u00e1c nh\u1eadn c\u1ee7a b\u1ea1n s\u1ebd h\u1ebft h\u1ea1n trong","WARN_VALIDATION_EXPIRED2":"ng\u00e0y. Vui l\u00f2ng n\u00e2ng c\u1ea5p th\u00f4ng tin trong trang \"Qu\u1ea3n l\u00fd b\u1ea3n quy\u1ec1n\" c\u1ee7a ph\u1ea7n Qu\u1ea3n tr\u1ecb.","LBL_MODULEBUILDER":"B\u1ed9 t\u1ea1o Ph\u00e2n h\u1ec7","LBL_MODULEBUILDER_DESC":"T\u1ea1i c\u00e1c ph\u00e2n h\u1ec7 m\u1edbi \u0111\u1ec3 m\u1edf r\u1ed9ng t\u00ednh n\u0103ng cho CRM","LBL_incomCRMPORTAL":"C\u1ed5ng CRM","LBL_incomCRMPORTAL_DESC":"Qu\u1ea3n l\u00fd C\u1ed5ng CRM","LBL_CLEAR_TEMPLATE_DATA_CACHE_DESC":"Lo\u1ea1i b\u1ecf c\u00e1c t\u1eadp tin nh\u1edb t\u1ea1m","LBL_CLEAR_TEMPLATE_DATA_CACHE_TITLE":"D\u1ecdn v\u00f9ng nh\u1edb t\u1ea1m Template","LBL_CLEAR_VARDEFS_DATA_CACHE_TITLE":"D\u1ecdn v\u00f9ng nh\u1edb t\u1ea1m Vardefs","LBL_CLEAR_VARDEFS_DATA_CACHE_DESC":"Lo\u1ea1i b\u1ecf c\u00e1c \u0111\u1ecbnh ngh\u0129a tham s\u1ed1 trong v\u00f9ng nh\u1edb t\u1ea1m","LBL_CLEAR_UNIFIED_SEARCH_CACHE_TITLE":"D\u1ecdn v\u00f9ng nh\u1edb t\u1ea1m t\u00ecm ki\u1ebfm","LBL_CLEAR_UNIFIED_SEARCH_CACHE_DESC":"X\u00f3a t\u1eadp tin unified_search_modules.php trong v\u00f9ng nh\u1edb t\u1ea1m","LBL_QUICK_REPAIR_AND_REBUILD":"Ch\u1ec9nh s\u1eeda nhanh v\u00e0 Bi\u00ean d\u1ecbch l\u1ea1i","LBL_QUICK_REPAIR_TITLE":"Vui l\u00f2ng ch\u1ecdn c\u00e1c ph\u00e2n h\u1ec7 c\u1ea7n ch\u1ec9nh s\u1eeda:","LBL_FAILED_CONNECTION":"K\u1ebft n\u1ed1i b\u1ecb l\u1ed7i :","LBL_QUICK_REPAIR_AND_REBUILD_DESC":"S\u1eeda ch\u1eefa v\u00e0 bi\u00ean d\u1ecbch l\u1ea1i C\u01a1 s\u1edf d\u1eef li\u1ec7u, Ph\u1ea7n m\u1edf r\u1ed9ng, C\u00e1c \u0111\u1ecbnh ngh\u0129a tham s\u1ed1, B\u1ea3ng th\u00f4ng tin CRM v.v... cho c\u00e1c ph\u00e2n h\u1ec7 \u0111\u01b0\u1ee3c ch\u1ecdn","LBL_ALL_MODULES":"T\u1ea5t c\u1ea3 c\u00e1c ph\u00e2n h\u1ec7","LBL_CAMPAIGN_CONFIG_TITLE":"Qu\u1ea3n l\u00fd thi\u1ebft l\u1eadp chi\u1ebfn d\u1ecbch th\u01b0 \u0111i\u1ec7n t\u1eed","LBL_CAMPAIGN_CONFIG_DESC":"Thi\u1ebft l\u1eadp Chi\u1ebfn d\u1ecbch ","LBL_REPAIR_ORACLE_FULLTEXT":"Oracle","LBL_REPAIR_ORACLE_FULLTEXT_DESC":"Oracle","LBL_REPAIR":"Ch\u1ec9nh s\u1eeda","LBL_QR_CBOX_DATAB":"Repair Database","LBL_QR_CBOX_CLEARTPL":"Clear .tpl Files","LBL_QR_CBOX_CLEARJS":"Clear Javascript Files","LBL_QR_CBOX_CLEARVARDEFS":"Clear Vardefs","LBL_QR_CBOX_CLEARJSLANG":"Clear Javascript Language Files","LBL_QR_CBOX_CLEARDASHLET":"Clear CRM Dashlet Cache Data","LBL_QR_CBOX_REBUILDAUDIT":"Rebuild Audit Tables","LBL_QR_CBOX_REBUILDEXT":"Rebuild Extensions","LBL_QR_CBOX_CLEARLANG":"Clear Language Files","LBL_QR_CBOX_CLEARSEARCH":"Clear Unified Search Cache","LBL_QR_REBUILDEXT":"<h3> Rebuilding Extensions...<\/h3>","LBL_QR_CLEARSMARTY":"Clearing Smarty templates from cache...done","LBL_QR_XMLFILES":"Clearing XML files from cache...done","LBL_QR_CLEARDASHLET":"Clearing CRM Dashlet files from cache...done","LBL_QR_CLEARTEMPLATE":"Clearing Template files from cache...done","LBL_QR_CLEARVADEFS":"Clearing Vardefs from cache...done","LBL_QR_CLEARJS":"Clearing JS files from cache...done","LBL_QR_CLEARJSLANG":"Clearing JS Language files from cache...done","LBL_QR_CLEARLANG":"Clearing language files from cache...done","LBL_QR_CLEARSEARCH":"Clearing Unified Search Cache...done","LBL_QR_REBUILDAUDIT":"Rebuilding Audit Tables...","LBL_QR_CBOX_CLEARincomCRMFEEDCACHE":"Clear CRM Feed Cache","LBL_QR_CLEARincomCRMFEEDCACHE":"Clearing CRM Feed Cache...done","LBL_QR_CBOX_CLEARTHEMECACHE":"Clear Theme Cache","LBL_QR_CLEARTHEMECACHE":"Clearing Theme Cache...done","LBL_REPAIR_DB_FOR":"Repairing DB for","LBL_QR_CREATING_TABLE":"creating table","LBL_QR_NOT_AUDIT_ENABLED":" not Audit Enabled...<br\/>","LBL_QR_CBOX_CLEARPDFFONT":"Clear PDF Font Cache File","LBL_QR_CLEARPDFFONT":"Clearing PDF Font Cache File...done","LBL_FOR":"for","LBL_REPAIR_SEED_USERS_TITLE":"M\u1edf\/Kh\u00f3a Ng\u01b0\u1eddi d\u00f9ng","LBL_REPAIR_SEED_USERS_ACTIVATE":"K\u00edch ho\u1ea1t","LBL_REPAIR_SEED_USERS_DECACTIVATE":"\u0110\u00f3ng","LBL_REPAIR_SEED_USERS_DESC":"M\u1edf ho\u1eb7c kh\u00f3a nhanh ng\u01b0\u1eddi d\u00f9ng \u0111\u01b0\u1ee3c t\u1ea1o cho vi\u1ec7c c\u00e0i \u0111\u1eb7t demo","LBL_UW_FILES_REMOVED":"These files will be removed from the system:<br>\n","LBL_TEAM_SETS":"Clean up unused combinations of teams.","LBL_MODULE_UPLOAD_DISABLE_HELP_TEXT":"New uploads for Module Loader are disabled. Installable modules are restricted to the modules pre-loaded below.","ML_PACKAGE_SCANNING":"Scanning {PACKAGE}","ML_INSTALLATION_FAILED":"Installation failed!","ML_PACKAGE_NOT_CONFIRM":"The package you are attempting to install does not conform to the policies established within the CRM Open Cloud or by your system administrator.","ML_TO_RESOLVE":"To resolve this issue:","ML_OBTAIN_NEW_PACKAGE":"CRM Open Cloud customers must obtain a new package from the package provider that addresses the issues described below.","ML_RELAX_LOCAL":"If you are running CRM locally, you can relax your Module Loader restrictions to allow the package to be installed.","ML_incomCRM_LOADING_POLICY":"The CRM Open Cloud package loading policies are detailed in the","ML_incomCRM_KB":"CRM Knowledge Base","ML_incomCRM_DZ":"CRM Developer Zone","ML_AVAIL_RESTRICTION":"The available restrictions and exceptions are detailed in the","ML_OVERRIDE_CORE_FILES":"Overriding of core CRM files is not allowed ","ML_PATH_MAY_NOT_CONTAIN":"File path may not contain","ML_INVALID_ACTION_IN_MANIFEST":"Invalid action in your manifest:","ML_NO_MANIFEST":"This package does not contain a manifest","ML_INVALID_FUNCTION":"Invalid usage of a function ","ML_INVALID_EXT":"Invalid file extension ","ML_ISSUES":"Issues","ML_MANIFEST_ISSUE":"Issue with the manifest","LBL_CURRENT_LANGUAGE_CHANGE":"Your current language is changed to ","LBL_DEFAULT_LANGUAGE_CHANGE":"System's default language is changed to ","ACLActions":"ACLAction","ACLFields":"ACLField","ACLRoles":"ACLRole","Administration":"Administration","Audit":"Audit","CampaignTrackers":"CampaignTracker","Connectors":"Connectors","ContractTypes":"ContractType","Currencies":"Currency","CustomFields":"CustomFields","CustomQueries":"CustomQuery","DataSets":"DataSet","DocumentRevisions":"DocumentRevision","DynamicFields":"DynamicField","EditCustomFields":"FieldsMetaData","EmailMan":"EmailMan","Employees":"Employee","Expressions":"Expression","Feeds":"Feed","ForecastOpportunities":"ForecastOpportunities","ForecastSchedule":"ForecastSchedule","Groups":"Group","Holidays":"Holiday","Import_1":"ImportMap","Import_2":"UsersLastImport","InboundEmail":"InboundEmail","KBContents":"KBContent","KBDocumentKBTags":"KBDocumentKBTag","KBDocumentRevisions":"KBDocumentRevision","KBTags":"KBTag","Manufacturers":"Manufacturer","ProductBundleNotes":"ProductBundleNote","ProductBundles":"ProductBundle","ProjectResources":"ProjectResource","Relationships":"Relationship","Releases":"Release","ReportMaker":"ReportMaker","Reports_1":"SavedReport","Roles":"Role","Schedulers":"Scheduler","SchedulersJobs":"SchedulersJob","Shippers":"Shipper","TaxRates":"TaxRate","TeamHierarchies":"TeamHierarchy","TeamMemberships":"TeamMembership","TeamNotices":"TeamNotice","TeamSetModules":"TeamSetModule","TeamSets":"TeamSet","TimePeriods":"TimePeriod","TrackerPerfs":"TrackerPerf","TrackerQueries":"TrackerQuery","TrackerSessions":"TrackerSession","UserPreferences":"UserPreference","Versions":"Version","WorkFlowActions":"WorkFlowAction","WorkFlowActionShells":"WorkFlowActionShell","WorkFlowAlerts":"WorkFlowAlert","WorkFlowAlertShells":"WorkFlowAlertShell","WorkFlowTriggerShells":"WorkFlowTriggerShell","Worksheet":"Worksheet","LBL_OTHER_TOOLS":"C\u00e1c ch\u1ee9c n\u0103ng kh\u00e1c","LBL_ADMIN_SETTING":"C\u1ea5u h\u00ecnh h\u1ec7 th\u1ed1ng","LBL_ADMIN_SETTING_DESC":"C\u1ea5u h\u00ecnh x\u00e1c nh\u1eadn v\u1ecb tr\u00ed v\u00e0 l\u1ecbch g\u1eedi email","LBL_CAMPAIGN_REMOVE_EMAIL":"Lo\u1ea1i b\u1ecf c\u00e1c email sai","LBL_CAMPAIGN_REMOVE_EMAIL_DESC":"Lo\u1ea1i b\u1ecf c\u00e1c email sai trong Chi\u1ebfn d\u1ecbch Marketing","LBL_SETTING_ROLE_DEFAULT":"Vai tr\u00f2 m\u1eb7c \u0111\u1ecbnh khi t\u1ea1o m\u1edbi ng\u01b0\u1eddi d\u00f9ng","LBL_NEW_USER_ROLE_DEFAULT":"Vai tr\u00f2 m\u1eb7c \u0111\u1ecbnh cho ng\u01b0\u1eddi d\u00f9ng m\u1edbi","LBL_NEW_OWNER_GROUP_ROLE_DEFAULT":"Vai tr\u00f2 m\u1eb7c \u0111\u1ecbnh cho SecurityGroup Owner","LBL_LIST_EMAIL_RECEIVE_ALL":"Danh s\u00e1ch c\u00e1c E-mails nh\u1eadn t\u1ea5t c\u1ea3 c\u00e1c mail khi g\u1eedi ra t\u1eeb incomCRM","LBL_EMAILS_EMAIL_RECEIVE_DESC":"C\u00e1c emails \u0111\u01b0\u1ee3c c\u00e1ch nhau b\u1eb1ng d\u1ea5u ch\u1ea5m ph\u1ea9y (;)","LBL_OTHER_CONFIG":"C\u1ea5u h\u00ecnh kh\u00e1c","LBL_CHOOSE_SALES_PLAN_DISPLAY":"Hi\u1ec7n k\u1ebf ho\u1ea1ch th\u00e1ng theo","LBL_WAREHOUSE_SETTING":"Thi\u1ebft l\u1eadp \u0111\u01a1n h\u00e0ng\/kho h\u00e0ng\/kh\u00e1ch h\u00e0ng","LBL_WAREHOUSE_SETTING_DESC":"C\u00e1c ch\u1ee9c n\u0103ng li\u00ean quan \u0111\u1ebfn \u0111\u01a1n h\u00e0ng, kho h\u00e0ng v\u00e0 kh\u00e1ch h\u00e0ng","LBL_WAREHOUSE_ACCESS_SETTING":"Ph\u00e2n quy\u1ec1n Duy\u1ec7t\/X\u00e1c nh\u1eadn","LBL_WAREHOUSE_ACCESS_SETTING_DESC":"Ph\u00e2n quy\u1ec1n duy\u1ec7t Nh\u1eadp\/Xu\u1ea5t kho h\u00e0ng, xem l\u1ee3i nhu\u1eadn","LBL_INVOICE_SETTING":"C\u00e0i \u0111\u1eb7t in h\u00f3a \u0111\u01a1n","LBL_INVOICE_SETTING_DESC":"C\u00e1c ch\u1ee9c n\u0103ng li\u00ean quan \u0111\u1ebfn in h\u00f3a \u0111\u01a1n","LBL_PROCESS_STEP_SETTING":"Thi\u1ebft l\u1eadp \u0110\u01a1n h\u00e0ng\/Kho\/Thu ti\u1ec1n","LBL_PROCESS_STEP_SETTING_DESC":"C\u00e1c ch\u1ee9c n\u0103ng li\u00ean quan \u0111\u1ebfn quy tr\u00ecnh x\u1eed l\u00fd \u0111\u01a1n h\u00e0ng","LBL_ORDER_COLUMNS_CUSTOMIZE":"T\u00f9y ch\u1ec9nh c\u00e1c c\u1ed9t tr\u00ean \u0110H B\u00c1N","LBL_ORDER_COLUMNS_CUSTOMIZE_DESC":"T\u00f9y ch\u1ec9nh c\u00e1c c\u1ed9t hi\u1ec3n th\u1ecb tr\u00ean m\u00e0n h\u00ecnh b\u00e1n h\u00e0ng","LBL_PURCHASE_COLUMNS_CUSTOMIZE":"T\u00f9y ch\u1ec9nh c\u00e1c c\u1ed9t tr\u00ean \u0110H MUA","LBL_PURCHASE_COLUMNS_CUSTOMIZE_DESC":"T\u00f9y ch\u1ec9nh c\u00e1c c\u1ed9t hi\u1ec3n th\u1ecb tr\u00ean m\u00e0n h\u00ecnh mua h\u00e0ng","LBL_ORDERING_SETTING":"Ph\u00e2n quy\u1ec1n \u0110\u1eb7t h\u00e0ng\/Ch\u1ec9nh gi\u00e1","LBL_ORDERING_SETTING_DESC":"Ph\u00e2n quy\u1ec1n \u0111\u1eb7t h\u00e0ng\/ch\u1ec9nh gi\u00e1 cho t\u1eebng lo\u1ea1i \u0111\u01a1n h\u00e0ng","LBL_ORDER_PROCESSING":"C\u00e1c b\u01b0\u1edbc x\u1eed l\u00fd \u0111\u01a1n h\u00e0ng B\u00c1N","LBL_ORDER_PROCESSING_DESC":"C\u1ea5u h\u00ecnh s\u1ed1 b\u01b0\u1edbc v\u00e0 c\u1ea5p quy\u1ec1n x\u1eed l\u00fd trong \u0111\u01a1n h\u00e0ng B\u00c1N","LBL_PURCHASE_PROCESSING":"C\u00e1c b\u01b0\u1edbc x\u1eed l\u00fd \u0111\u01a1n h\u00e0ng MUA","LBL_PURCHASE_PROCESSING_DESC":"C\u1ea5u h\u00ecnh s\u1ed1 b\u01b0\u1edbc v\u00e0 c\u1ea5p quy\u1ec1n x\u1eed l\u00fd trong \u0111\u01a1n h\u00e0ng MUA","LBL_INTERNET_INVOICE":"Thi\u1ebft l\u1eadp h\u00f3a \u0111\u01a1n \u0111i\u1ec7n t\u1eed","LBL_INTERNET_INVOICE_DESC":"C\u00e1c ch\u1ee9c n\u0103ng li\u00ean quan \u0111\u1ebfn xu\u1ea5t h\u00f3a \u0111\u01a1n \u0111i\u1ec7n t\u1eed","LBL_PROJECT_PROCESSING":"C\u00e1c b\u01b0\u1edbc x\u1eed l\u00fd D\u1ef1 \u00e1n","LBL_PROJECT_PROCESSING_DESC":"C\u1ea5u h\u00ecnh s\u1ed1 b\u01b0\u1edbc v\u00e0 c\u1ea5p quy\u1ec1n x\u1eed l\u00fd trong D\u1ef1 \u00e1n","LBL_EXTERNAL_COLUMNS_CUSTOMIZE":"T\u00f9y ch\u1ec9nh c\u00e1c c\u1ed9t tr\u00ean Mua h\u00e0ng nh\u1eadp kh\u1ea9u","LBL_EXTERNAL_COLUMNS_CUSTOMIZE_DESC":"T\u00f9y ch\u1ec9nh c\u00e1c c\u1ed9t hi\u1ec3n th\u1ecb tr\u00ean Mua h\u00e0ng nh\u1eadp kh\u1ea9u","LBL_DESIGN_PROCESSING":"C\u00e1c b\u01b0\u1edbc x\u1eed l\u00fd Mua n\u01b0\u1edbc ngo\u00e0i","LBL_DESIGN_PROCESSING_DESC":"C\u1ea5u h\u00ecnh s\u1ed1 b\u01b0\u1edbc v\u00e0 c\u1ea5p quy\u1ec1n x\u1eed l\u00fd Mua n\u01b0\u1edbc ngo\u00e0i","LBL_GUARANTEE_PROCESSING":"C\u00e1c b\u01b0\u1edbc x\u1eed l\u00fd B\u1ea3o h\u00e0nh\/b\u1ea3o tr\u00ec","LBL_GUARANTEE_PROCESSING_DESC":"C\u1ea5u h\u00ecnh s\u1ed1 b\u01b0\u1edbc v\u00e0 c\u1ea5p quy\u1ec1n x\u1eed l\u00fd B\u1ea3o h\u00e0nh\/b\u1ea3o tr\u00ec","LBL_MOBILE_APP_CONFIG":"C\u1ea5u h\u00ecnh MobileApp","LBL_MOBILE_APP_CONFIG_DESC":"C\u00e1c ch\u1ee9c n\u0103ng li\u00ean quan \u0111\u1ebfn MobileApp","LBL_DELIVERY_PROCESSING":"C\u00e1c b\u01b0\u1edbc x\u1eed l\u00fd Phi\u1ebfu giao h\u00e0ng","LBL_DELIVERY_PROCESSING_DESC":"C\u1ea5u h\u00ecnh s\u1ed1 b\u01b0\u1edbc v\u00e0 c\u1ea5p quy\u1ec1n x\u1eed l\u00fd Phi\u1ebfu giao h\u00e0ng","LBL_ZALO_OA_SETTING":"C\u00e0i \u0111\u1eb7t k\u1ebft n\u1ed1i Zalo OA","LBL_ZALO_OA_SETTING_DESC":"C\u1ea5u h\u00ecnh k\u1ebft n\u1ed1i v\u00e0 c\u1ea5p quy\u1ec1n Zalo OA","LBL_MANAGE_SECURITYGROUPS_TITLE":"Nh\u00f3m ng\u01b0\u1eddi d\u00f9ng","LBL_MANAGE_SECURITYGROUPS":"C\u00e1c nh\u00f3m ng\u01b0\u1eddi d\u00f9ng","LBL_SECURITYGROUPS":"Security Suite","LBL_CONFIG_SECURITYGROUPS_TITLE":"Security Suite Settings","LBL_CONFIG_SECURITYGROUPS":"Configure Security Suite settings such as group inheritance, additive security, etc","LBL_CONTRACT_APPROVAL_PROCESS":"Quy tr\u00ecnh duy\u1ec7t H\u1ee3p \u0111\u1ed3ng","LBL_CONTRACT_APPROVAL_PROCESS_DESC":"C\u1ea5u h\u00ecnh s\u1ed1 b\u01b0\u1edbc x\u1eed l\u00fd trong quy tr\u00ecnh duy\u1ec7t h\u1ee3p \u0111\u1ed3ng","LBL_APPROVAL_PROCESS_STEP":"S\u1ed1 b\u01b0\u1edbc x\u1eed l\u00fd","LBL_APPROVAL_PROCESS_NAME":"T\u00ean b\u01b0\u1edbc duy\u1ec7t","LBL_APPROVAL_PROCESS_USER":"Ng\u01b0\u1eddi duy\u1ec7t","LBL_APPROVAL_PROCESS_EMAILS":"D.s ng\u01b0\u1eddi nh\u1eadn email","LBL_APPROVAL_PROCESS_NUMBER":"B\u01b0\u1edbc x\u1eed l\u00fd s\u1ed1","LBL_APPROVAL_PROCESS_AREA":"Khu v\u1ef1c","LBL_MANAGE_BRANCHES":"Chi nh\u00e1nh\/Khu v\u1ef1c","LBL_MANAGE_BRANCHES_DESC":"Qu\u1ea3n l\u00fd chi nh\u00e1nh c\u00f4ng ty","LBL_MANAGE_DEPARTMENTS":"Ph\u00f2ng ban\/NPP","LBL_MANAGE_DEPARTMENTS_DESC":"Qu\u1ea3n l\u00fd ph\u00f2ng ban c\u1ee7a chi nh\u00e1nh","LBL_MANAGE_TEAMS_DESC":"Qu\u1ea3n l\u00fd b\u1ed9 ph\u1eadn\/nh\u00f3m c\u1ee7a ph\u00f2ng ban","LBL_MANAGE_LOG_VISITED":"L\u1ecbch s\u1eed \u0111\u0103ng nh\u1eadp","LBL_MANAGE_LOG_VISITED_DESC":"Xem l\u1ecbch s\u1eed \u0111\u0103ng nh\u1eadp c\u1ee7a ng\u01b0\u1eddi d\u00f9ng","LBL_SHOW_PRODUCTS_ON_OPPORTUNITIES":"Ch\u1ecdn S\u1ea3n ph\u1ea9m trong C\u01a1 h\u1ed9i KD?","LBL_SHOW_ACCOUNT_TYPE_REVENUE":"Hi\u1ec7n Doanh thu theo lo\u1ea1i kh\u00e1ch h\u00e0ng","LBL_ACCOUNT_NEW_ASSIGNER_TITLE":"G\u00e1n kh\u00e1ch h\u00e0ng cho nh\u00e2n vi\u00ean m\u1edbi","LBL_ACCOUNT_NEW_ASSIGNER_CONTACTS":"T\u1ea5t c\u1ea3 ng\u01b0\u1eddi li\u00ean h\u1ec7 c\u1ee7a kh\u00e1ch h\u00e0ng n\u00e0y \u0111\u01b0\u1ee3c","LBL_ACCOUNT_NEW_ASSIGNER_TASKS":"T\u1ea5t c\u1ea3 t\u00e1c v\u1ee5 & GD v\u1edbi kh\u00e1ch h\u00e0ng n\u00e0y \u0111\u01b0\u1ee3c","LBL_ACCOUNT_NEW_ASSIGNER_SUBFIX":"cho nh\u00e2n vi\u00ean m\u1edbi","LBL_ACCOUNT_NEW_ASSIGNER_ASSIGN":"G\u00e1n","LBL_ACCOUNT_NEW_ASSIGNER_SHARE":"Chia s\u1ebd","LBL_SELECT_DEFAULT_ACCOUNT":"Ch\u1ecdn CTy ch\u1ee7 s\u1edf h\u1eefu","LBL_SELECT_DEFAULT_EMPLOYEE":"Ch\u1ecdn nh\u00e2n vi\u00ean Nv.CTy","LBL_LIMIT_DAY_SWITCH_ACCOUNT":"Gi\u1edbi h\u1ea1n s\u1ed1 ng\u00e0y chuy\u1ec3n kh\u00e1ch h\u00e0ng cho Nv.CTy","LBL_LIMIT_ASSIGNED_ACCOUNT_PER_DAY":"Gi\u1edbi h\u1ea1n s\u1ed1 l\u01b0\u1ee3ng kh\u00e1ch h\u00e0ng g\u00e1n t\u1eeb Nv.CTy\/1 ng\u00e0y","LBL_LIMIT_CREATE_ACCOUNT_PER_DAY":"Gi\u1edbi h\u1ea1n s\u1ed1 l\u01b0\u1ee3ng kh\u00e1ch h\u00e0ng nh\u1eadp m\u1edbi trong ng\u00e0y\/1 nh\u00e2n vi\u00ean","LBL_DISABLE_APPROVED_INPUT":"Nh\u1eadp <span style=\"color:red; font-weight:bold\">0<\/span> n\u1ebfu kh\u00f4ng \u00e1p d\u1ee5ng","LBL_TELESALES_WORKING_TITLE":"Telesales Working","LBL_TELESALES_WORKING_CHECKBOX":"S\u1ed1 tr\u01b0\u1eddng checkbox ch\u1ee7ng lo\u1ea1i s\u1ea3n ph\u1ea9m","LBL_TELESALES_WORKING_TEXTAREA":"S\u1ed1 tr\u01b0\u1eddng nh\u1eadp text ch\u1ee7ng lo\u1ea1i s\u1ea3n ph\u1ea9m","LBL_BUYING_CYCLE_TITLE":"Chu k\u1ef3 mua h\u00e0ng","LBL_BUYING_CYCLE_REPEAT_BUYING":"C\u1ea3nh b\u00e1o kh\u00e1ch h\u00e0ng s\u1eafp l\u1eb7p l\u1ea1i chu k\u1ef3 mua h\u00e0ng","LBL_BUYING_CYCLE_STOP_BUYING":"C\u1ea3nh b\u00e1o kh\u00e1ch h\u00e0ng ng\u1eebng mua","LBL_BUYING_CYCLE_UNIT":"ng\u00e0y","LBL_ZALO_OA_CONFIG":"C\u1ea5u h\u00ecnh Zalo OA","LBL_ZALO_ACCESS_TOKEN":"Access Token","LBL_ZALO_APP_ID":"App ID","LBL_ZALO_SECRET_KEY":"Secret Key","LBL_ZALO_TEMPLATE_DEFAULT":"Template m\u1eb7c \u0111\u1ecbnh","LBL_ZALO_NOTIFICATION_ENABLED":"B\u1eadt th\u00f4ng b\u00e1o","LBL_ZALO_SYNC_INTERVAL":"Th\u1eddi gian \u0111\u1ed3ng b\u1ed9","LBL_ZALO_LOG_ENABLED":"B\u1eadt log","LBL_ZALO_ACCESS_TOKEN_REQUIRED":"Vui l\u00f2ng nh\u1eadp Access Token","LBL_ZALO_APP_ID_REQUIRED":"Vui l\u00f2ng nh\u1eadp App ID","LBL_ZALO_SECRET_KEY_REQUIRED":"Vui l\u00f2ng nh\u1eadp Secret Key","LBL_ZALO_TOKEN_STATUS":"Tr\u1ea1ng th\u00e1i k\u1ebft n\u1ed1i","LBL_ZALO_TOKEN_ACTIONS":"Thao t\u00e1c","LBL_ZALO_CONNECT":"K\u1ebft n\u1ed1i Zalo OA","LBL_ZALO_RECONNECT":"K\u1ebft n\u1ed1i l\u1ea1i","LBL_ZALO_REFRESH_TOKEN":"L\u00e0m m\u1edbi token","LBL_ZALO_DISCONNECT":"Ng\u1eaft k\u1ebft n\u1ed1i","LBL_ZALO_REFRESH_CONFIRM":"B\u1ea1n c\u00f3 ch\u1eafc ch\u1eafn mu\u1ed1n l\u00e0m m\u1edbi token?","LBL_ZALO_DISCONNECT_CONFIRM":"B\u1ea1n c\u00f3 ch\u1eafc ch\u1eafn mu\u1ed1n ng\u1eaft k\u1ebft n\u1ed1i v\u1edbi Zalo OA?","LBL_ZALO_REFRESH_SUCCESS":"L\u00e0m m\u1edbi token th\u00e0nh c\u00f4ng","LBL_ZALO_REFRESH_ERROR":"L\u1ed7i khi l\u00e0m m\u1edbi token","LBL_ZALO_DISCONNECT_SUCCESS":"\u0110\u00e3 ng\u1eaft k\u1ebft n\u1ed1i th\u00e0nh c\u00f4ng","LBL_ZALO_DISCONNECT_ERROR":"L\u1ed7i khi ng\u1eaft k\u1ebft n\u1ed1i"});