<?php
// created: 2024-08-26 14:24:26
$mod_strings = array (
  'ADVANCED' => 'Advanced',
  'DEFAULT_CURRENCY_ISO4217' => 'ISO 4217 currency code',
  'DEFAULT_CURRENCY_NAME' => 'Currency name',
  'DEFAULT_CURRENCY_SYMBOL' => 'Currency symbol',
  'DEFAULT_CURRENCY' => 'Default Currency',
  'DEFAULT_DATE_FORMAT' => 'Default date format',
  'DEFAULT_DECIMAL_SEP' => 'Decimal symbol',
  'DEFAULT_LANGUAGE' => 'Default language',
  'DEFAULT_NUMBER_GROUPING_SEP' => '1000s separator',
  'DEFAULT_SYSTEM_SETTINGS' => 'User Interface',
  'DEFAULT_THEME' => 'Default theme',
  'DEFAULT_TIME_FORMAT' => 'Default time format',
  'DISPLAY_LOGIN_NAV' => 'Display tabs on login screen',
  'DISPLAY_RESPONSE_TIME' => 'Display server response times',
  'IMAGES' => 'Logos',
  'LBL_CONFIGURE_SETTINGS_TITLE' => 'System Settings',
  'LBL_ENABLE_MAILMERGE' => 'Enable mail merge?',
  'LBL_LOGVIEW' => 'Configure Log Settings',
  'LBL_MAIL_SMTPAUTH_REQ' => 'Use SMTP Authentication?',
  'LBL_MAIL_SMTPPASS' => 'SMTP Password:',
  'LBL_MAIL_SMTPPORT' => 'SMTP Port:',
  'LBL_MAIL_SMTPSERVER' => 'SMTP Server:',
  'LBL_MAIL_SMTPUSER' => 'SMTP Username:',
  'LBL_MAILMERGE_DESC' => 'This flag should be checked only if you have the incomCRM Plug-in for Microsoft&reg; Word&reg;.',
  'LBL_MAILMERGE' => 'Mail Merge',
  'LBL_MODULE_FAVICON' => 'Display module icon as favicon',
  'LBL_MODULE_FAVICON_HELP' => 'If you are in a module with an icon, use the module\'s icon as the favicon, instead of the theme\'s favicon, in the browser tab.',
  'LBL_MODULE_NAME' => 'System Settings',
  'LBL_MODULE_ID' => 'Configurator',
  'LBL_MODULE_TITLE' => 'User Interface',
  'LBL_NOTIFY_FROMADDRESS' => '"From" Address:',
  'LBL_NOTIFY_SUBJECT' => 'Email subject:',
  'LBL_PORTAL_ON_DESC' => 'Allows users to manage portal user information within contact records. Portal users can access Cases, Bugs, Knowledge Base articles and other data through the incomCRM Portal application.',
  'LBL_PORTAL_ON' => 'Enable Portal User Management',
  'LBL_PORTAL_TITLE' => 'Customer Self-Service Portal',
  'LBL_PROXY_AUTH' => 'Authentication?',
  'LBL_PROXY_HOST' => 'Proxy Host',
  'LBL_PROXY_ON_DESC' => 'Configure proxy server address and authentication settings',
  'LBL_PROXY_ON' => 'Use proxy server?',
  'LBL_PROXY_PASSWORD' => 'Password',
  'LBL_PROXY_PORT' => 'Port',
  'LBL_PROXY_TITLE' => 'Proxy Settings',
  'LBL_PROXY_USERNAME' => 'User Name',
  'LBL_RESTORE_BUTTON_LABEL' => 'Restore',
  'LBL_SKYPEOUT_ON_DESC' => 'Allows users to click on phone numbers to call using SkypeOut&reg;. The numbers must be formatted properly to make use of this feature. That is, it must be "+"  "The Country Code" "The Number", like +****************. For more information, see the Skype FAQ at <a href="http://www.skype.com/help/faq/skypeout.html#calling" target="skype">skype&reg; faq</a>	',
  'LBL_SKYPEOUT_ON' => 'Enable SkypeOut&reg; integration?',
  'LBL_SKYPEOUT_TITLE' => 'SkypeOut&reg;',
  'LBL_USE_REAL_NAMES' => 'Show Full Name (not Login)',
  'LIST_ENTRIES_PER_LISTVIEW' => 'Listview items per page',
  'LIST_ENTRIES_PER_SUBPANEL' => 'Subpanel items per page',
  'LOG_MEMORY_USAGE' => 'Log memory usage',
  'LOG_SLOW_QUERIES' => 'Log slow queries',
  'LOCK_HOMEPAGE_HELP' => 'This setting is to prevent<BR> 1) the addition of any dashlets to the home page, <BR>2) customization of dashlet placement in the home pages by dragging and dropping.',
  'CURRENT_LOGO' => 'Current Logo',
  'CURRENT_LOGO_HELP' => 'This logo is displayed at the top left-hand corner of the incomCRM application.',
  'NEW_LOGO' => 'Select New Logo',
  'NEW_LOGO_HELP' => 'The image file format can be either .png or .jpg.<BR>The recommended size is 212x40 px.',
  'NEW_QUOTE_LOGO' => 'Upload new Quotes logo',
  'NEW_QUOTE_LOGO_HELP' => 'The required image file format is .jpg.<BR>The recommended size is 867x74 px.',
  'QUOTES_CURRENT_LOGO' => 'Quotes logo',
  'SLOW_QUERY_TIME_MSEC' => 'Slow query time threshold (msec)',
  'STACK_TRACE_ERRORS' => 'Display stack trace of errors',
  'UPLOAD_MAX_SIZE' => 'Maximum upload size',
  'VERIFY_CLIENT_IP' => 'Validate user IP address',
  'LOCK_HOMEPAGE' => 'Prevent user customizable Homepage layout',
  'LOCK_SUBPANELS' => 'Prevent user customizable subpanel layout',
  'MAX_DASHLETS' => 'Maximum number of incomCRM Dashlets on Homepage',
  'SYSTEM_NAME' => 'System Name',
  'LBL_LDAP_TITLE' => 'LDAP Authentication Support',
  'LBL_LDAP_ENABLE' => 'Enable LDAP',
  'LBL_LDAP_SERVER_HOSTNAME' => 'Server:',
  'LBL_LDAP_SERVER_PORT' => 'Port Number:',
  'LBL_LDAP_ADMIN_USER' => 'User Name:',
  'LBL_LDAP_ADMIN_USER_DESC' => 'Used to search for the incomCRM user. [May need to be fully qualified] It will bind anonymously if not provided.',
  'LBL_LDAP_ADMIN_PASSWORD' => 'Password:',
  'LBL_LDAP_AUTHENTICATION' => 'Authentication:',
  'LBL_LDAP_AUTHENTICATION_DESC' => 'Bind to the LDAP server using a specific users credentials',
  'LBL_LDAP_AUTO_CREATE_USERS' => 'Auto Create Users:',
  'LBL_LDAP_USER_DN' => 'User DN:',
  'LBL_LDAP_GROUP_DN' => 'Group DN:',
  'LBL_LDAP_GROUP_DN_DESC' => 'Example: <em>ou=groups,dc=example,dc=com</em>',
  'LBL_LDAP_USER_FILTER' => 'User Filter:',
  'LBL_LDAP_GROUP_MEMBERSHIP' => 'Group Membership:',
  'LBL_LDAP_GROUP_MEMBERSHIP_DESC' => 'Users must be a member of a specific group',
  'LBL_LDAP_GROUP_USER_ATTR' => 'User Attribute:',
  'LBL_LDAP_GROUP_USER_ATTR_DESC' => 'The unique identifier of the person that will be used to check if they are a member of the group Example: <em>uid</em>',
  'LBL_LDAP_GROUP_ATTR_DESC' => 'The attribute of the Group that will be used to filter against the User Attribute Example: <em>memberUid</em>',
  'LBL_LDAP_GROUP_ATTR' => 'Group Attribute:',
  'LBL_LDAP_USER_FILTER_DESC' => 'Any additional filter params to apply when authenticating users e.g.<em>is_incomCRM_user=1 or (is_incomCRM_user=1)(is_sales=1)</em>',
  'LBL_LDAP_LOGIN_ATTRIBUTE' => 'Login Attribute:',
  'LBL_LDAP_BIND_ATTRIBUTE' => 'Bind Attribute:',
  'LBL_LDAP_BIND_ATTRIBUTE_DESC' => 'For Binding the LDAP User Examples:[<b>AD:</b>&nbsp;userPrincipalName] [<b>openLDAP:</b>&nbsp;userPrincipalName] [<b>Mac&nbsp;OS&nbsp;X:</b>&nbsp;uid] ',
  'LBL_LDAP_LOGIN_ATTRIBUTE_DESC' => 'For searching for the LDAP User Examples:[<b>AD:</b>&nbsp;userPrincipalName] [<b>openLDAP:</b>&nbsp;dn] [<b>Mac&nbsp;OS&nbsp;X:</b>&nbsp;dn] ',
  'LBL_LDAP_SERVER_HOSTNAME_DESC' => 'Example: ldap.example.com or ldaps://ldap.example.com for SSL',
  'LBL_LDAP_SERVER_PORT_DESC' => 'Example: <em>389 or 636 for SSL</em>',
  'LBL_LDAP_GROUP_NAME' => 'Group Name:',
  'LBL_LDAP_GROUP_NAME_DESC' => 'Example <em>cn=incomCRM</em>',
  'LBL_LDAP_USER_DN_DESC' => 'Example: <em>ou=people,dc=example,dc=com</eM>',
  'LBL_LDAP_AUTO_CREATE_USERS_DESC' => 'If an authenticated user does not exist one will be created in incomCRM.',
  'LBL_LDAP_ENC_KEY' => 'Encryption Key:',
  'DEVELOPER_MODE' => 'Developer Mode',
  'LBL_LDAP_ENC_KEY_DESC' => 'For SOAP authentication when using LDAP.',
  'LDAP_ENC_KEY_NO_FUNC_DESC' => 'The php_mcrypt extension must be enabled in your php.ini file.',
  'LBL_ALL' => 'All',
  'LBL_MARK_POINT' => 'Mark Point',
  'LBL_NEXT_' => 'Next>>',
  'LBL_REFRESH_FROM_MARK' => 'Refresh From Mark',
  'LBL_SEARCH' => 'Search:',
  'LBL_REG_EXP' => 'Reg Exp:',
  'LBL_IGNORE_SELF' => 'Ignore Self:',
  'LBL_MARKING_WHERE_START_LOGGING' => 'Marking Where To Start Logging From',
  'LBL_DISPLAYING_LOG' => 'Displaying Log',
  'LBL_YOUR_PROCESS_ID' => 'Your process ID',
  'LBL_YOUR_IP_ADDRESS' => 'Your IP Address is',
  'LBL_IT_WILL_BE_IGNORED' => ' It will be ignored ',
  'LBL_LOG_NOT_CHANGED' => 'Log has not changed',
  'LBL_ALERT_JPG_IMAGE' => 'The file format of the image must be JPEG.  Upload a new file with the file extension .jpg.',
  'LBL_ALERT_TYPE_IMAGE' => 'The file format of the image must be JPEG or PNG.  Upload a new file with the file extension .jpg or .png.',
  'LBL_ALERT_SIZE_RATIO' => 'The aspect ratio of the image should be between 1:1 and 10:1.  The image will be resized.',
  'LBL_ALERT_SIZE_RATIO_QUOTES' => 'The aspect ratio of the image must be between 3:1 and 20:1.  Upload a new file with this ratio.',
  'ERR_ALERT_FILE_UPLOAD' => 'Error during the upload of the image.',
  'LBL_LOGGER' => 'Logger Settings',
  'LBL_LOGGER_FILENAME' => 'Log File Name',
  'LBL_LOGGER_FILE_EXTENSION' => 'Extension',
  'LBL_LOGGER_MAX_LOG_SIZE' => 'Maximum log size',
  'LBL_LOGGER_DEFAULT_DATE_FORMAT' => 'Default date format',
  'LBL_LOGGER_LOG_LEVEL' => 'Log Level',
  'LBL_LOGGER_MAX_LOGS' => 'Maximum number of logs (before rolling)',
  'LBL_LOGGER_FILENAME_SUFFIX' => 'Append after filename',
  'LBL_VCAL_PERIOD' => 'vCal Updates Time Period:',
  'vCAL_HELP' => 'Use this setting to determine the number of months in advance of the current date that Free/Busy information for calls and meetings is published.<BR>To turn Free/Busy publishing off, enter "0".  The minimum is 1 month; the maximum is 12 months.',
  'LBL_PDFMODULE_NAME' => 'PDF Settings',
  'incomCRMPDF_BASIC_SETTINGS' => 'Document Properties',
  'incomCRMPDF_ADVANCED_SETTINGS' => 'Advanced Settings',
  'incomCRMPDF_LOGO_SETTINGS' => 'Images',
  'PDF_CREATOR' => 'PDF Creator',
  'PDF_CREATOR_INFO' => 'Defines the creator of the document. <br>This is typically the name of the application that generates the PDF.',
  'PDF_AUTHOR' => 'Author',
  'PDF_AUTHOR_INFO' => 'The Author appears in the document properties.',
  'PDF_HEADER_LOGO' => 'For Quotes PDF Documents',
  'PDF_HEADER_LOGO_INFO' => 'This image appears in the default Header in Quotes PDF Documents.',
  'PDF_NEW_HEADER_LOGO' => 'Select New Image for Quotes',
  'PDF_NEW_HEADER_LOGO_INFO' => 'The file format can be either .jpg or .png. (Only .jpg for EZPDF)<BR>The recommended size is 867x74 px.',
  'PDF_HEADER_LOGO_WIDTH' => 'Quotes Image Width',
  'PDF_HEADER_LOGO_WIDTH_INFO' => 'Change the scale of the uploaded image that appears in Quotes PDF Documents. (TCPDF only)',
  'PDF_SMALL_HEADER_LOGO' => 'For Reports PDF Documents',
  'PDF_SMALL_HEADER_LOGO_INFO' => 'This image appears in the default Header in Reports PDF Documents.<br> This image also appears in the top left-hand corner of the incomCRM application.',
  'PDF_NEW_SMALL_HEADER_LOGO' => 'Select New Image for Reports',
  'PDF_NEW_SMALL_HEADER_LOGO_INFO' => 'The file format can be either .jpg or .png. (Only .jpg for EZPDF)<BR>The recommended size is 212x40 px.',
  'PDF_SMALL_HEADER_LOGO_WIDTH' => 'Reports Image Width',
  'PDF_SMALL_HEADER_LOGO_WIDTH_INFO' => 'Change the scale of the uploaded image that appears in Reports PDF Documents. (TCPDF only)',
  'PDF_HEADER_STRING' => 'Header String',
  'PDF_HEADER_STRING_INFO' => 'Header description string',
  'PDF_HEADER_TITLE' => 'Header Title',
  'PDF_HEADER_TITLE_INFO' => 'String to print as title on document header',
  'PDF_FILENAME' => 'Default Filename',
  'PDF_FILENAME_INFO' => 'Default filename for the generated PDF files',
  'PDF_TITLE' => 'Title',
  'PDF_TITLE_INFO' => 'The Title appears in the document properties.',
  'PDF_SUBJECT' => 'Subject',
  'PDF_SUBJECT_INFO' => 'The Subject appears in the document properties.',
  'PDF_KEYWORDS' => 'Keyword(s)',
  'PDF_KEYWORDS_INFO' => 'Associate Keywords with the document, generally in the form "keyword1 keyword2..."',
  'PDF_COMPRESSION' => 'Compression',
  'PDF_COMPRESSION_INFO' => 'Activates or deactivates page compression. <br>When activated, the internal representation of each page is compressed, which leads to a compression ratio of about 2 for the resulting document.',
  'PDF_JPEG_QUALITY' => 'JPEG Quality (1-100)',
  'PDF_JPEG_QUALITY_INFO' => 'Set the default JPEG compression quality (1-100)',
  'PDF_PDF_VERSION' => 'PDF Version',
  'PDF_PDF_VERSION_INFO' => 'Set the PDF version (check PDF reference for valid values).',
  'PDF_PROTECTION' => 'Document Protection',
  'PDF_PROTECTION_INFO' => 'Set document protection<br>- copy: copy text and images to the clipboard<br>- print: print the document<br>- modify: modify it (except for annotations and forms)<br>- annot-forms: add annotations and forms<br>Note: the protection against modification is for people who have the full Acrobat product.',
  'PDF_USER_PASSWORD' => 'User Password',
  'PDF_USER_PASSWORD_INFO' => 'If you don\\\'t set any password, the document will open as usual. <br>If you set a user password, the PDF viewer will ask for it before displaying the document. <br>The master password, if different from the user one, can be used to get full access.',
  'PDF_OWNER_PASSWORD' => 'Owner Password',
  'PDF_OWNER_PASSWORD_INFO' => 'If you don\\\'t set any password, the document will open as usual. <br>If you set a user password, the PDF viewer will ask for it before displaying the document. <br>The master password, if different from the user one, can be used to get full access.',
  'PDF_ACL_ACCESS' => 'Access Control',
  'PDF_ACL_ACCESS_INFO' => 'Default Access Control for the PDF generation.',
  'K_CELL_HEIGHT_RATIO' => 'Cell Height Ratio',
  'K_CELL_HEIGHT_RATIO_INFO' => 'If the height of a cell is smaller than (Font Height x Cell Height Ratio), then (Font Height x Cell Height Ratio) is used as the cell height.<br>(Font Height x Cell Height Ratio) is also used as the height of the cell when no height is define.',
  'K_TITLE_MAGNIFICATION' => 'Title Magnification',
  'K_TITLE_MAGNIFICATION_INFO' => 'Title magnification respect main font size.',
  'K_SMALL_RATIO' => 'Small Font Factor',
  'K_SMALL_RATIO_INFO' => 'Reduction factor for small font.',
  'HEAD_MAGNIFICATION' => 'Head Magnification',
  'HEAD_MAGNIFICATION_INFO' => 'Magnification factor for titles.',
  'PDF_IMAGE_SCALE_RATIO' => 'Image scale ratio',
  'PDF_IMAGE_SCALE_RATIO_INFO' => 'Ratio used to scale the images',
  'PDF_UNIT' => 'Unit',
  'PDF_UNIT_INFO' => 'document unit of measure',
  'PDF_GD_WARNING' => 'You do not have the GD library installed for PHP. Without the GD library installed, only JPEG logos can be displayed in PDF documents.',
  'ERR_EZPDF_DISABLE' => 'Warning : The EZPDF class is disabled from the config table and it set as the PDF class. Please "Save" this form to set TCPDF as the PDF Class and return in a stable state.',
  'LBL_IMG_RESIZED' => '(resized for display)',
  'LBL_FONTMANAGER_BUTTON' => 'PDF Font Manager',
  'LBL_FONTMANAGER_TITLE' => 'PDF Font Manager',
  'LBL_FONT_BOLD' => 'Bold',
  'LBL_FONT_ITALIC' => 'Italic',
  'LBL_FONT_BOLDITALIC' => 'Bold/Italic',
  'LBL_FONT_REGULAR' => 'Regular',
  'LBL_FONT_TYPE_CID0' => 'CID-0',
  'LBL_FONT_TYPE_CORE' => 'Core',
  'LBL_FONT_TYPE_TRUETYPE' => 'TrueType',
  'LBL_FONT_TYPE_TYPE1' => 'Type1',
  'LBL_FONT_TYPE_TRUETYPEU' => 'TrueTypeUnicode',
  'LBL_FONT_LIST_NAME' => 'Name',
  'LBL_FONT_LIST_FILENAME' => 'Filename',
  'LBL_FONT_LIST_TYPE' => 'Type',
  'LBL_FONT_LIST_STYLE' => 'Style',
  'LBL_FONT_LIST_STYLE_INFO' => 'Style of the font',
  'LBL_FONT_LIST_ENC' => 'Encoding',
  'LBL_FONT_LIST_EMBEDDED' => 'Embedded',
  'LBL_FONT_LIST_EMBEDDED_INFO' => 'Check to embed the font into the PDF file',
  'LBL_FONT_LIST_CIDINFO' => 'CID Information',
  'LBL_FONT_LIST_CIDINFO_INFO' => 'Examples :<ul><li>Chinese Traditional :<br><pre>$enc=\\\'UniCNS-UTF16-H\\\';<br>$cidinfo=array(\\\'Registry\\\'=>\\\'Adobe\\\', \\\'Ordering\\\'=>\\\'CNS1\\\',\\\'Supplement\\\'=>0);<br>include(\\\'include/tcpdf/fonts/uni2cid_ac15.php\\\');</pre></li><li>Chinese Simplified :<br><pre>$enc=\\\'UniGB-UTF16-H\\\';<br>$cidinfo=array(\\\'Registry\\\'=>\\\'Adobe\\\', \\\'Ordering\\\'=>\\\'GB1\\\',\\\'Supplement\\\'=>2);<br>include(\\\'include/tcpdf/fonts/uni2cid_ag15.php\\\');</pre></li><li>Korean :<br><pre>$enc=\\\'UniKS-UTF16-H\\\';<br>$cidinfo=array(\\\'Registry\\\'=>\\\'Adobe\\\', \\\'Ordering\\\'=>\\\'Korea1\\\',\\\'Supplement\\\'=>0);<br>include(\\\'include/tcpdf/fonts/uni2cid_ak12.php\\\');</pre></li><li>Japanese :<br><pre>$enc=\\\'UniJIS-UTF16-H\\\';<br>$cidinfo=array(\\\'Registry\\\'=>\\\'Adobe\\\', \\\'Ordering\\\'=>\\\'Japan1\\\',\\\'Supplement\\\'=>5);<br>include(\\\'include/tcpdf/fonts/uni2cid_aj16.php\\\');</pre></li></ul>More help : www.tcpdf.org',
  'LBL_FONT_LIST_FILESIZE' => 'Font Size (KB)',
  'LBL_ADD_FONT' => 'Add a font',
  'LBL_BACK' => 'Back',
  'LBL_REMOVE' => 'rem',
  'LBL_JS_CONFIRM_DELETE_FONT' => 'Are you sure that you want to delete this font?',
  'LBL_ADDFONT_TITLE' => 'Add a PDF Font',
  'LBL_PDF_PATCH' => 'Patch',
  'LBL_PDF_PATCH_INFO' => 'Custom modification of the encoding. Write a PHP array.<br>Example :<br>ISO-8859-1 does not contain the euro symbol. To add it at position 164, write "array(164=>\\\'Euro\\\')".',
  'LBL_PDF_ENCODING_TABLE' => 'Encoding Table',
  'LBL_PDF_ENCODING_TABLE_INFO' => 'Name of the encoding table.<br>This option is ignored for TrueType Unicode, OpenType Unicode and symbolic fonts.<br>The encoding defines the association between a code (from 0 to 255) and a character contained in the font.<br>The first 128 are fixed and correspond to ASCII.',
  'LBL_PDF_FONT_FILE' => 'Font File',
  'LBL_PDF_FONT_FILE_INFO' => '.ttf or .otf or .pfb file',
  'LBL_PDF_METRIC_FILE' => 'Metric File',
  'LBL_PDF_METRIC_FILE_INFO' => '.afm or .ufm file',
  'LBL_ADD_FONT_BUTTON' => 'Add',
  'JS_ALERT_PDF_WRONG_EXTENSION' => 'This file do not have a good file extension.',
  'LBL_PDF_INSTRUCTIONS' => 'Instructions',
  'PDF_INSTRUCTIONS_ADD_FONT' => 'Fonts supported by incomCRMPDF :
<ul>
<li>TrueTypeUnicode (UTF-8 Unicode)</li>
<li>OpenTypeUnicode</li>
<li>TrueType</li>
<li>OpenType</li>
<li>Type1</li>
<li>CID-0</li>
</ul>
<br>
If you choose to not embed your font in the PDF, the generated PDF file will be lighter but a substitution will be use if the font is not available in the system of your reader.
<br><br>
Adding a PDF font to incomCRM requires to follow steps 1 and 2 of the TCPDF Fonts documentation available in the "DOCS" section of the <a href="http://www.tcpdf.org" target="_blank">TCPDF website</a>.
<br><br>The pfm2afm and ttf2ufm utils are available in fonts/utils in the TCPDF package that you can download on the "DOWNLOAD" section of the <a href="http://www.tcpdf.org" target="_blank">TCPDF website</a>.
<br><br>Load the metric file generated in step 2 and your font file below.',
  'ERR_MISSING_CIDINFO' => 'The field CID Information cannot be empty.',
  'LBL_ADDFONTRESULT_TITLE' => 'Result of the add font process',
  'LBL_STATUS_FONT_SUCCESS' => 'SUCCESS : The font has been added to incomCRM.',
  'LBL_STATUS_FONT_ERROR' => 'ERROR : The font has not been added. Look at the log below.',
  'LBL_FONT_MOVE_DEFFILE' => 'Font definition file move to : ',
  'LBL_FONT_MOVE_FILE' => 'Font file move to : ',
  'ERR_LOADFONTFILE' => 'ERROR: LoadFontFile error!',
  'ERR_FONT_EMPTYFILE' => 'ERROR: Empty filename!',
  'ERR_FONT_UNKNOW_TYPE' => 'ERROR: Unknow font type:',
  'ERR_DELETE_CORE_FILE' => 'ERROR: It is not possible to delete a core font.',
  'ERR_NO_FONT_PATH' => 'ERROR: No font path available!',
  'ERR_NO_CUSTOM_FONT_PATH' => 'ERROR: No custom font path available!',
  'ERR_FONT_NOT_WRITABLE' => 'is not writable.',
  'ERR_FONT_FILE_DO_NOT_EXIST' => 'doesn\'t exist or is not a directory.',
  'ERR_FONT_MAKEFONT' => 'ERROR: MakeFont error',
  'ERR_FONT_ALREADY_EXIST' => 'ERROR : This font already exist. Rollback...',
  'ERR_PDF_NO_UPLOAD' => 'Error during the upload of the font or metric file.',
);
?>
