

{math equation="floor(left / right)"
	left=12
		right=$templateMeta.maxColumns
			assign=colClass
	}

<section class="container2 search-form ml-0 search_form_basic">
	<div class="row">
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12">
			<div class="row">
				<label class="col-5 pr-0" for="assigned_user_id_basic">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_ASSIGNED_TO' module='Checkin_Dates'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="assigned_user_id_basic[]" id="{$fields.assigned_user_id_basic.name}" size="1"   >
{html_options options=$fields.assigned_user_id_basic.options selected=$fields.assigned_user_id_basic.value}
</select>
									</div>
			</div>
		</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12">
			<div class="row">
				<label class="col-5 pr-0" for="checkin_type_basic">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_CHECKIN_TYPE' module='Checkin_Dates'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="checkin_type_basic[]" id="{$fields.checkin_type_basic.name}" size="1"   >
{html_options options=$fields.checkin_type_basic.options selected=$fields.checkin_type_basic.value}
</select>
									</div>
			</div>
		</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12">
			<div class="row">
				<label class="col-5 pr-0" for="date_perform_from_basic">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_DATE_PERFORM_FROM' module='Checkin_Dates'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{assign var=date_value value=$fields.date_perform_from_basic.value }
<input autocomplete="off" type="text" name="{$fields.date_perform_from_basic.name}" id="{$fields.date_perform_from_basic.name}" value="{$date_value}" title=''  tabindex='' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.date_perform_from_basic.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.date_perform_from_basic.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.date_perform_from_basic.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>

									</div>
			</div>
		</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12">
			<div class="row">
				<label class="col-5 pr-0" for="date_perform_to_basic">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_RPT_DATE_TO' module='Checkin_Dates'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{assign var=date_value value=$fields.date_perform_to_basic.value }
<input autocomplete="off" type="text" name="{$fields.date_perform_to_basic.name}" id="{$fields.date_perform_to_basic.name}" value="{$date_value}" title=''  tabindex='' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.date_perform_to_basic.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.date_perform_to_basic.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.date_perform_to_basic.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>

									</div>
			</div>
		</div>
	</div>
</section>

{if $HAS_ADVANCED_SEARCH}
<table cellspacing="0" cellpadding="0" border="0" class="mt-10">
<tr>
	<td scope="row" nowrap="nowrap" class="pt-10">
	<a style="font-size:10pt; text-decoration:none;" onclick="incomCRM.searchForm.searchFormSelect('{$module}|advanced_search','{$module}|basic_search')" href="#">[ {$APP.LNK_ADVANCED_SEARCH} ]</a>
	</td>
</tr>
</table>
{/if}

{literal}<script type="text/javascript">if(typeof sqs_objects == 'undefined'){var sqs_objects = new Array;}
sqs_objects['search_form_modified_by_name_basic'] = {"form":"search_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["modified_by_name_basic","modified_user_id_basic"],"required_list":["modified_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['search_form_created_by_name_basic'] = {"form":"search_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["created_by_name_basic","created_by_basic"],"required_list":["created_by"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['search_form_assigned_user_name_basic'] = {"form":"search_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["assigned_user_name_basic","assigned_user_id_basic"],"required_list":["assigned_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
</script>{/literal}