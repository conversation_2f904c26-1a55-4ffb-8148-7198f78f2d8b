<?php $relationships=array (
  'leads_modified_user' => 
  array (
    'id' => 'bcaa810a-ca2b-c047-ce0b-67becc195054',
    'relationship_name' => 'leads_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Leads',
    'rhs_table' => 'leads',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'leads_created_by' => 
  array (
    'id' => 'bcf0bdd1-1a5f-dc5a-821b-67becc975668',
    'relationship_name' => 'leads_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Leads',
    'rhs_table' => 'leads',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'leads_assigned_user' => 
  array (
    'id' => 'bd21720e-38ba-9c36-ce20-67becced5c43',
    'relationship_name' => 'leads_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Leads',
    'rhs_table' => 'leads',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'leads_email_addresses' => 
  array (
    'id' => 'bd444d57-9275-1a1a-10e4-67becc7fb5aa',
    'relationship_name' => 'leads_email_addresses',
    'lhs_module' => 'Leads',
    'lhs_table' => 'leads',
    'lhs_key' => 'id',
    'rhs_module' => 'EmailAddresses',
    'rhs_table' => 'email_addresses',
    'rhs_key' => 'id',
    'join_table' => 'email_addr_bean_rel',
    'join_key_lhs' => 'bean_id',
    'join_key_rhs' => 'email_address_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'bean_module',
    'relationship_role_column_value' => 'Leads',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'leads_email_addresses_primary' => 
  array (
    'id' => 'bd6bb85d-3213-85aa-ebc0-67becc281b1b',
    'relationship_name' => 'leads_email_addresses_primary',
    'lhs_module' => 'Leads',
    'lhs_table' => 'leads',
    'lhs_key' => 'id',
    'rhs_module' => 'EmailAddresses',
    'rhs_table' => 'email_addresses',
    'rhs_key' => 'id',
    'join_table' => 'email_addr_bean_rel',
    'join_key_lhs' => 'bean_id',
    'join_key_rhs' => 'email_address_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'primary_address',
    'relationship_role_column_value' => '1',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'lead_direct_reports' => 
  array (
    'id' => 'bd8fb6a3-e742-ce62-3c0f-67becc8e6903',
    'relationship_name' => 'lead_direct_reports',
    'lhs_module' => 'Leads',
    'lhs_table' => 'leads',
    'lhs_key' => 'id',
    'rhs_module' => 'Leads',
    'rhs_table' => 'leads',
    'rhs_key' => 'reports_to_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'lead_tasks' => 
  array (
    'id' => 'bdb32ceb-c82a-9e9c-fc97-67becc7a42a3',
    'relationship_name' => 'lead_tasks',
    'lhs_module' => 'Leads',
    'lhs_table' => 'leads',
    'lhs_key' => 'id',
    'rhs_module' => 'Tasks',
    'rhs_table' => 'tasks',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Leads',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'lead_notes' => 
  array (
    'id' => 'bde74831-0412-6f1e-510d-67beccf75d92',
    'relationship_name' => 'lead_notes',
    'lhs_module' => 'Leads',
    'lhs_table' => 'leads',
    'lhs_key' => 'id',
    'rhs_module' => 'Notes',
    'rhs_table' => 'notes',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Leads',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'lead_meetings' => 
  array (
    'id' => 'be08fe8a-014c-b1a8-f854-67beccd09578',
    'relationship_name' => 'lead_meetings',
    'lhs_module' => 'Leads',
    'lhs_table' => 'leads',
    'lhs_key' => 'id',
    'rhs_module' => 'Meetings',
    'rhs_table' => 'meetings',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Leads',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'lead_calls' => 
  array (
    'id' => 'be27bcea-9ba0-4d08-f35d-67becc7b0f6b',
    'relationship_name' => 'lead_calls',
    'lhs_module' => 'Leads',
    'lhs_table' => 'leads',
    'lhs_key' => 'id',
    'rhs_module' => 'Calls',
    'rhs_table' => 'calls',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Leads',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'lead_emails' => 
  array (
    'id' => 'be46f54d-47ae-1352-9680-67beccc6a799',
    'relationship_name' => 'lead_emails',
    'lhs_module' => 'Leads',
    'lhs_table' => 'leads',
    'lhs_key' => 'id',
    'rhs_module' => 'Emails',
    'rhs_table' => 'emails',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Leads',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'lead_campaign_log' => 
  array (
    'id' => 'be67dbcf-a539-e99e-2a87-67beccccaa7e',
    'relationship_name' => 'lead_campaign_log',
    'lhs_module' => 'Leads',
    'lhs_table' => 'leads',
    'lhs_key' => 'id',
    'rhs_module' => 'CampaignLog',
    'rhs_table' => 'campaign_log',
    'rhs_key' => 'target_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contacts_modified_user' => 
  array (
    'id' => 'bf794c80-71d4-3750-195a-67beccdd5ebc',
    'relationship_name' => 'contacts_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Contacts',
    'rhs_table' => 'contacts',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contacts_created_by' => 
  array (
    'id' => 'bfa0feb0-af0e-adc2-761b-67beccf598f1',
    'relationship_name' => 'contacts_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Contacts',
    'rhs_table' => 'contacts',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contacts_assigned_user' => 
  array (
    'id' => 'bfcac356-231c-828e-0316-67beccf33655',
    'relationship_name' => 'contacts_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Contacts',
    'rhs_table' => 'contacts',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contacts_email_addresses' => 
  array (
    'id' => 'bff7e99d-146e-9158-9010-67becc00c5da',
    'relationship_name' => 'contacts_email_addresses',
    'lhs_module' => 'Contacts',
    'lhs_table' => 'contacts',
    'lhs_key' => 'id',
    'rhs_module' => 'EmailAddresses',
    'rhs_table' => 'email_addresses',
    'rhs_key' => 'id',
    'join_table' => 'email_addr_bean_rel',
    'join_key_lhs' => 'bean_id',
    'join_key_rhs' => 'email_address_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'bean_module',
    'relationship_role_column_value' => 'Contacts',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contacts_email_addresses_primary' => 
  array (
    'id' => 'c02383e9-b29c-d619-d58e-67becc3a44c0',
    'relationship_name' => 'contacts_email_addresses_primary',
    'lhs_module' => 'Contacts',
    'lhs_table' => 'contacts',
    'lhs_key' => 'id',
    'rhs_module' => 'EmailAddresses',
    'rhs_table' => 'email_addresses',
    'rhs_key' => 'id',
    'join_table' => 'email_addr_bean_rel',
    'join_key_lhs' => 'bean_id',
    'join_key_rhs' => 'email_address_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'primary_address',
    'relationship_role_column_value' => '1',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contact_direct_reports' => 
  array (
    'id' => 'c05078ae-3daa-d128-e128-67becc29e278',
    'relationship_name' => 'contact_direct_reports',
    'lhs_module' => 'Contacts',
    'lhs_table' => 'contacts',
    'lhs_key' => 'id',
    'rhs_module' => 'Contacts',
    'rhs_table' => 'contacts',
    'rhs_key' => 'reports_to_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contact_leads' => 
  array (
    'id' => 'c07b5b46-0b02-73b9-538e-67beccf478ce',
    'relationship_name' => 'contact_leads',
    'lhs_module' => 'Contacts',
    'lhs_table' => 'contacts',
    'lhs_key' => 'id',
    'rhs_module' => 'Leads',
    'rhs_table' => 'leads',
    'rhs_key' => 'contact_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contact_notes' => 
  array (
    'id' => 'c0a53c1e-cc9d-4e69-38e9-67becc300714',
    'relationship_name' => 'contact_notes',
    'lhs_module' => 'Contacts',
    'lhs_table' => 'contacts',
    'lhs_key' => 'id',
    'rhs_module' => 'Notes',
    'rhs_table' => 'notes',
    'rhs_key' => 'contact_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contact_tasks' => 
  array (
    'id' => 'c0cfa002-bbd3-57b3-f7ce-67beccc8ace1',
    'relationship_name' => 'contact_tasks',
    'lhs_module' => 'Contacts',
    'lhs_table' => 'contacts',
    'lhs_key' => 'id',
    'rhs_module' => 'Tasks',
    'rhs_table' => 'tasks',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Contacts',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contact_campaign_log' => 
  array (
    'id' => 'c0fb511f-5905-fb35-32df-67beccda270d',
    'relationship_name' => 'contact_campaign_log',
    'lhs_module' => 'Contacts',
    'lhs_table' => 'contacts',
    'lhs_key' => 'id',
    'rhs_module' => 'CampaignLog',
    'rhs_table' => 'campaign_log',
    'rhs_key' => 'target_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_modified_user' => 
  array (
    'id' => 'c21b2ee9-2116-b6cb-2a78-67becc100fe9',
    'relationship_name' => 'accounts_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Accounts',
    'rhs_table' => 'accounts',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_created_by' => 
  array (
    'id' => 'c245a722-f796-cc2e-f8e9-67becc542187',
    'relationship_name' => 'accounts_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Accounts',
    'rhs_table' => 'accounts',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_assigned_user' => 
  array (
    'id' => 'c269371c-0af5-6ba6-9af1-67becc47fc0a',
    'relationship_name' => 'accounts_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Accounts',
    'rhs_table' => 'accounts',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_email_addresses' => 
  array (
    'id' => 'c28fd64e-b473-dd7a-2d28-67becce3cbb6',
    'relationship_name' => 'accounts_email_addresses',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'EmailAddresses',
    'rhs_table' => 'email_addresses',
    'rhs_key' => 'id',
    'join_table' => 'email_addr_bean_rel',
    'join_key_lhs' => 'bean_id',
    'join_key_rhs' => 'email_address_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'bean_module',
    'relationship_role_column_value' => 'Accounts',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_email_addresses_primary' => 
  array (
    'id' => 'c2b5d0fa-c617-da94-9dc6-67becc440083',
    'relationship_name' => 'accounts_email_addresses_primary',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'EmailAddresses',
    'rhs_table' => 'email_addresses',
    'rhs_key' => 'id',
    'join_table' => 'email_addr_bean_rel',
    'join_key_lhs' => 'bean_id',
    'join_key_rhs' => 'email_address_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'primary_address',
    'relationship_role_column_value' => '1',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'member_accounts' => 
  array (
    'id' => 'c2dd3f80-fcba-e3da-66b2-67becc1fccc2',
    'relationship_name' => 'member_accounts',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'Accounts',
    'rhs_table' => 'accounts',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'account_cases' => 
  array (
    'id' => 'c3003e91-de06-5169-e043-67becc48315b',
    'relationship_name' => 'account_cases',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'Cases',
    'rhs_table' => 'cases',
    'rhs_key' => 'id',
    'join_table' => 'accounts_cases',
    'join_key_lhs' => 'account_id',
    'join_key_rhs' => 'case_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'account_tasks' => 
  array (
    'id' => 'c323f54b-c155-a648-cc56-67beccb8ea28',
    'relationship_name' => 'account_tasks',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'Tasks',
    'rhs_table' => 'tasks',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Accounts',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'account_notes' => 
  array (
    'id' => 'c345b081-559f-54b5-8476-67becc21f0b5',
    'relationship_name' => 'account_notes',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'Notes',
    'rhs_table' => 'notes',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Accounts',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'account_meetings' => 
  array (
    'id' => 'c36865e2-c7aa-f6ea-b240-67becc4de323',
    'relationship_name' => 'account_meetings',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'Meetings',
    'rhs_table' => 'meetings',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Accounts',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'account_calls' => 
  array (
    'id' => 'c38fe851-50d1-af6b-fea7-67becc1c135b',
    'relationship_name' => 'account_calls',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'Calls',
    'rhs_table' => 'calls',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Accounts',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'account_emails' => 
  array (
    'id' => 'c3b2c241-8d40-ddb1-1eaf-67becc1e572f',
    'relationship_name' => 'account_emails',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'Emails',
    'rhs_table' => 'emails',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Accounts',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'account_leads' => 
  array (
    'id' => 'c3d58058-7419-7a13-4c6f-67beccb61d6e',
    'relationship_name' => 'account_leads',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'Leads',
    'rhs_table' => 'leads',
    'rhs_key' => 'account_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_documents' => 
  array (
    'id' => 'c3f7b66b-a380-d79a-1d5a-67beccb83b43',
    'relationship_name' => 'accounts_documents',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'Documents',
    'rhs_table' => 'documents',
    'rhs_key' => 'id',
    'join_table' => 'linked_documents',
    'join_key_lhs' => 'parent_id',
    'join_key_rhs' => 'document_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Accounts',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_trackreview' => 
  array (
    'id' => 'c41b5b66-48dc-8f43-3fbe-67becce26bd2',
    'relationship_name' => 'accounts_trackreview',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'TrackReview',
    'rhs_table' => 'trackreview',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Accounts',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'debt_users_accounts' => 
  array (
    'id' => 'c43f959e-06af-3464-df38-67becc9c4e0c',
    'relationship_name' => 'debt_users_accounts',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Accounts',
    'rhs_table' => 'accounts',
    'rhs_key' => 'debt_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'support_users_accounts' => 
  array (
    'id' => 'c4622a43-e3f3-c70b-6f91-67becc48e089',
    'relationship_name' => 'support_users_accounts',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Accounts',
    'rhs_table' => 'accounts',
    'rhs_key' => 'support_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_modified_user' => 
  array (
    'id' => 'c53aa33c-f1fe-0250-405e-67becc3771a5',
    'relationship_name' => 'opportunities_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities',
    'rhs_table' => 'opportunities',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_created_by' => 
  array (
    'id' => 'c5602e85-eb67-e7a1-76e1-67becc89a99e',
    'relationship_name' => 'opportunities_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities',
    'rhs_table' => 'opportunities',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_assigned_user' => 
  array (
    'id' => 'c585eba7-cd68-fa62-ac84-67beccb204ad',
    'relationship_name' => 'opportunities_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities',
    'rhs_table' => 'opportunities',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunity_calls' => 
  array (
    'id' => 'c5a952f5-ebec-23ed-b1a9-67becc14a376',
    'relationship_name' => 'opportunity_calls',
    'lhs_module' => 'Opportunities',
    'lhs_table' => 'opportunities',
    'lhs_key' => 'id',
    'rhs_module' => 'Calls',
    'rhs_table' => 'calls',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Opportunities',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunity_meetings' => 
  array (
    'id' => 'c5cbe496-d7d7-d7ca-3cd5-67becc405ac4',
    'relationship_name' => 'opportunity_meetings',
    'lhs_module' => 'Opportunities',
    'lhs_table' => 'opportunities',
    'lhs_key' => 'id',
    'rhs_module' => 'Meetings',
    'rhs_table' => 'meetings',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Opportunities',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunity_tasks' => 
  array (
    'id' => 'c5efcf18-01dc-69b0-5547-67beccb49bcf',
    'relationship_name' => 'opportunity_tasks',
    'lhs_module' => 'Opportunities',
    'lhs_table' => 'opportunities',
    'lhs_key' => 'id',
    'rhs_module' => 'Tasks',
    'rhs_table' => 'tasks',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Opportunities',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunity_notes' => 
  array (
    'id' => 'c60fdd3d-c9f4-bc99-8858-67beccc6c50a',
    'relationship_name' => 'opportunity_notes',
    'lhs_module' => 'Opportunities',
    'lhs_table' => 'opportunities',
    'lhs_key' => 'id',
    'rhs_module' => 'Notes',
    'rhs_table' => 'notes',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Opportunities',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunity_emails' => 
  array (
    'id' => 'c631e4a0-bc86-91eb-a3b9-67becc37ebee',
    'relationship_name' => 'opportunity_emails',
    'lhs_module' => 'Opportunities',
    'lhs_table' => 'opportunities',
    'lhs_key' => 'id',
    'rhs_module' => 'Emails',
    'rhs_table' => 'emails',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Opportunities',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunity_leads' => 
  array (
    'id' => 'c653119f-a1a4-a9a1-c3ba-67becc83c6a8',
    'relationship_name' => 'opportunity_leads',
    'lhs_module' => 'Opportunities',
    'lhs_table' => 'opportunities',
    'lhs_key' => 'id',
    'rhs_module' => 'Leads',
    'rhs_table' => 'leads',
    'rhs_key' => 'opportunity_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunity_currencies' => 
  array (
    'id' => 'c6768902-5b8c-75a5-0093-67becc66f4cf',
    'relationship_name' => 'opportunity_currencies',
    'lhs_module' => 'Opportunities',
    'lhs_table' => 'opportunities',
    'lhs_key' => 'currency_id',
    'rhs_module' => 'Currencies',
    'rhs_table' => 'currencies',
    'rhs_key' => 'id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_campaign' => 
  array (
    'id' => 'c697c6b7-9ab2-76e0-e66c-67beccedbbb8',
    'relationship_name' => 'opportunities_campaign',
    'lhs_module' => 'Campaigns',
    'lhs_table' => 'campaigns',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities',
    'rhs_table' => 'opportunities',
    'rhs_key' => 'campaign_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_documents' => 
  array (
    'id' => 'c6b877e9-14b8-0c40-f64f-67becc36cff9',
    'relationship_name' => 'opportunities_documents',
    'lhs_module' => 'Opportunities',
    'lhs_table' => 'opportunities',
    'lhs_key' => 'id',
    'rhs_module' => 'Documents',
    'rhs_table' => 'documents',
    'rhs_key' => 'id',
    'join_table' => 'linked_documents',
    'join_key_lhs' => 'parent_id',
    'join_key_rhs' => 'document_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Opportunities',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'project_opportunity_related' => 
  array (
    'id' => 'c6dae41a-c4ee-2efa-d8a5-67becc17743c',
    'relationship_name' => 'project_opportunity_related',
    'lhs_module' => 'Project',
    'lhs_table' => 'project',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities',
    'rhs_table' => 'opportunities',
    'rhs_key' => 'project_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'purchase_orders_tasks_related' => 
  array (
    'id' => 'c70039d9-df94-180d-ec54-67becc1e8486',
    'relationship_name' => 'purchase_orders_tasks_related',
    'lhs_module' => 'Opportunities',
    'lhs_table' => 'opportunities',
    'lhs_key' => 'id',
    'rhs_module' => 'Purchase_Orders_Tasks',
    'rhs_table' => 'opportunities_tasks',
    'rhs_key' => 'opportunity_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'handle_users_opportunities' => 
  array (
    'id' => 'c720b81b-ff14-4b98-3b56-67becc48acca',
    'relationship_name' => 'handle_users_opportunities',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities',
    'rhs_table' => 'opportunities',
    'rhs_key' => 'handle_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'member_opportunities' => 
  array (
    'id' => 'c74575e4-2fb2-c489-f4fb-67beccd9166f',
    'relationship_name' => 'member_opportunities',
    'lhs_module' => 'Opportunities',
    'lhs_table' => 'opportunities',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities',
    'rhs_table' => 'opportunities',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'cases_modified_user' => 
  array (
    'id' => 'c7db3ef8-aa07-2918-c8ce-67becc5c3ae1',
    'relationship_name' => 'cases_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Cases',
    'rhs_table' => 'cases',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'cases_created_by' => 
  array (
    'id' => 'c7ffc59e-eb6a-9d4e-e82e-67beccc90214',
    'relationship_name' => 'cases_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Cases',
    'rhs_table' => 'cases',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'cases_assigned_user' => 
  array (
    'id' => 'c822c7ba-5358-2dbf-8723-67becc8af7a1',
    'relationship_name' => 'cases_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Cases',
    'rhs_table' => 'cases',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'case_calls' => 
  array (
    'id' => 'c8452215-5bd1-86d7-8c17-67becc60407f',
    'relationship_name' => 'case_calls',
    'lhs_module' => 'Cases',
    'lhs_table' => 'cases',
    'lhs_key' => 'id',
    'rhs_module' => 'Calls',
    'rhs_table' => 'calls',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Cases',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'case_tasks' => 
  array (
    'id' => 'c86acbe1-45ce-a6d1-3bdd-67becc0a15f8',
    'relationship_name' => 'case_tasks',
    'lhs_module' => 'Cases',
    'lhs_table' => 'cases',
    'lhs_key' => 'id',
    'rhs_module' => 'Tasks',
    'rhs_table' => 'tasks',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Cases',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'case_notes' => 
  array (
    'id' => 'c88c5e6d-430b-73dd-f663-67becc87f578',
    'relationship_name' => 'case_notes',
    'lhs_module' => 'Cases',
    'lhs_table' => 'cases',
    'lhs_key' => 'id',
    'rhs_module' => 'Notes',
    'rhs_table' => 'notes',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Cases',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'case_meetings' => 
  array (
    'id' => 'c8ada398-c995-dc7f-a12d-67becc7885b9',
    'relationship_name' => 'case_meetings',
    'lhs_module' => 'Cases',
    'lhs_table' => 'cases',
    'lhs_key' => 'id',
    'rhs_module' => 'Meetings',
    'rhs_table' => 'meetings',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Cases',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'case_emails' => 
  array (
    'id' => 'c8cebc07-b941-7aac-254c-67becca012c3',
    'relationship_name' => 'case_emails',
    'lhs_module' => 'Cases',
    'lhs_table' => 'cases',
    'lhs_key' => 'id',
    'rhs_module' => 'Emails',
    'rhs_table' => 'emails',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Cases',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'notes_modified_user' => 
  array (
    'id' => 'c95a5cfb-2471-18ca-85e0-67becc4696fd',
    'relationship_name' => 'notes_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Notes',
    'rhs_table' => 'notes',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'notes_created_by' => 
  array (
    'id' => 'c9853a38-bac9-f6a0-58db-67becc895e80',
    'relationship_name' => 'notes_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Notes',
    'rhs_table' => 'notes',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'approve_users_notes' => 
  array (
    'id' => 'c9b141cc-a2ea-db22-0efd-67becc1c01a9',
    'relationship_name' => 'approve_users_notes',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Notes',
    'rhs_table' => 'notes',
    'rhs_key' => 'approve_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'calls_modified_user' => 
  array (
    'id' => 'ca7a688f-936b-b68e-8084-67becc0a6303',
    'relationship_name' => 'calls_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Calls',
    'rhs_table' => 'calls',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'calls_created_by' => 
  array (
    'id' => 'caa56d8f-6a37-05d6-f40b-67becce7a8a9',
    'relationship_name' => 'calls_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Calls',
    'rhs_table' => 'calls',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'calls_assigned_user' => 
  array (
    'id' => 'cacc684c-554b-196a-b455-67becc069469',
    'relationship_name' => 'calls_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Calls',
    'rhs_table' => 'calls',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'calls_notes' => 
  array (
    'id' => 'caeeea31-07ff-2461-b76d-67becc384c73',
    'relationship_name' => 'calls_notes',
    'lhs_module' => 'Calls',
    'lhs_table' => 'calls',
    'lhs_key' => 'id',
    'rhs_module' => 'Notes',
    'rhs_table' => 'notes',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'calls_trackreview' => 
  array (
    'id' => 'cb1130e7-ad4c-7580-0376-67becc5e96c2',
    'relationship_name' => 'calls_trackreview',
    'lhs_module' => 'Calls',
    'lhs_table' => 'calls',
    'lhs_key' => 'id',
    'rhs_module' => 'TrackReview',
    'rhs_table' => 'trackreview',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Calls',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'conts_calls' => 
  array (
    'id' => 'cb33c97a-f8f1-c93d-22f1-67becc9eb90c',
    'relationship_name' => 'conts_calls',
    'lhs_module' => 'Contacts',
    'lhs_table' => 'contacts',
    'lhs_key' => 'id',
    'rhs_module' => 'Calls',
    'rhs_table' => 'calls',
    'rhs_key' => 'cont_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'emails_assigned_user' => 
  array (
    'id' => 'cba73254-1bc5-1178-1ef6-67beccc952af',
    'relationship_name' => 'emails_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Emails',
    'rhs_table' => 'emails',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'emails_modified_user' => 
  array (
    'id' => 'cbd6203b-5ade-b9a2-22ef-67beccb77cfe',
    'relationship_name' => 'emails_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Emails',
    'rhs_table' => 'emails',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'emails_created_by' => 
  array (
    'id' => 'cbf89b78-d5fb-ef69-dd65-67becc4e932f',
    'relationship_name' => 'emails_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Emails',
    'rhs_table' => 'emails',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'emails_notes_rel' => 
  array (
    'id' => 'cc1b0612-f5a6-9d4e-ec96-67becc7eeb8a',
    'relationship_name' => 'emails_notes_rel',
    'lhs_module' => 'Emails',
    'lhs_table' => 'emails',
    'lhs_key' => 'id',
    'rhs_module' => 'Notes',
    'rhs_table' => 'notes',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'meetings_modified_user' => 
  array (
    'id' => 'cc96457a-eb39-46a1-6309-67becca12073',
    'relationship_name' => 'meetings_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Meetings',
    'rhs_table' => 'meetings',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'meetings_created_by' => 
  array (
    'id' => 'ccb784f6-a3d4-8082-867e-67becc18e4dc',
    'relationship_name' => 'meetings_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Meetings',
    'rhs_table' => 'meetings',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'meetings_assigned_user' => 
  array (
    'id' => 'cce941e9-bc37-a476-728a-67becc373ef2',
    'relationship_name' => 'meetings_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Meetings',
    'rhs_table' => 'meetings',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'meetings_notes' => 
  array (
    'id' => 'cd14e0f6-bb73-fc38-ece9-67becc1b3b8c',
    'relationship_name' => 'meetings_notes',
    'lhs_module' => 'Meetings',
    'lhs_table' => 'meetings',
    'lhs_key' => 'id',
    'rhs_module' => 'Notes',
    'rhs_table' => 'notes',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Meetings',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'conts_meetings' => 
  array (
    'id' => 'cd343511-bc2a-5bf2-4321-67beccc7661f',
    'relationship_name' => 'conts_meetings',
    'lhs_module' => 'Contacts',
    'lhs_table' => 'contacts',
    'lhs_key' => 'id',
    'rhs_module' => 'Meetings',
    'rhs_table' => 'meetings',
    'rhs_key' => 'cont_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'meetings_trackreview' => 
  array (
    'id' => 'cd6098de-bffc-0464-83a7-67becc55c7b7',
    'relationship_name' => 'meetings_trackreview',
    'lhs_module' => 'Meetings',
    'lhs_table' => 'meetings',
    'lhs_key' => 'id',
    'rhs_module' => 'TrackReview',
    'rhs_table' => 'trackreview',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Meetings',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tasks_modified_user' => 
  array (
    'id' => 'ce1ed8b3-ea16-ada9-70a6-67beccccbfd7',
    'relationship_name' => 'tasks_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Tasks',
    'rhs_table' => 'tasks',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tasks_created_by' => 
  array (
    'id' => 'ce44083b-5a46-62ab-244f-67becc4b8e64',
    'relationship_name' => 'tasks_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Tasks',
    'rhs_table' => 'tasks',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tasks_assigned_user' => 
  array (
    'id' => 'ce67119a-f5b8-7633-8bc8-67becc9f4f34',
    'relationship_name' => 'tasks_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Tasks',
    'rhs_table' => 'tasks',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'user_errs_tasks' => 
  array (
    'id' => 'ce8cc754-0a0d-7ccf-b930-67becc21114f',
    'relationship_name' => 'user_errs_tasks',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Tasks',
    'rhs_table' => 'tasks',
    'rhs_key' => 'user_err_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tasks_notes' => 
  array (
    'id' => 'ceb17a12-1ad7-b928-da17-67becc582c34',
    'relationship_name' => 'tasks_notes',
    'lhs_module' => 'Tasks',
    'lhs_table' => 'tasks',
    'lhs_key' => 'id',
    'rhs_module' => 'Notes',
    'rhs_table' => 'notes',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contacts_tasks_direct' => 
  array (
    'id' => 'ced391a4-2007-bda7-6549-67becca4a992',
    'relationship_name' => 'contacts_tasks_direct',
    'lhs_module' => 'Contacts',
    'lhs_table' => 'contacts',
    'lhs_key' => 'id',
    'rhs_module' => 'Tasks',
    'rhs_table' => 'tasks',
    'rhs_key' => 'contact_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'member_tasks' => 
  array (
    'id' => 'cef4aa5b-649b-305d-9ce6-67becce0ba62',
    'relationship_name' => 'member_tasks',
    'lhs_module' => 'Tasks',
    'lhs_table' => 'tasks',
    'lhs_key' => 'id',
    'rhs_module' => 'Tasks',
    'rhs_table' => 'tasks',
    'rhs_key' => 'main_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tasks_trackreview' => 
  array (
    'id' => 'cf13d140-92d1-dbaa-de6d-67beccfb1c3c',
    'relationship_name' => 'tasks_trackreview',
    'lhs_module' => 'Tasks',
    'lhs_table' => 'tasks',
    'lhs_key' => 'id',
    'rhs_module' => 'TrackReview',
    'rhs_table' => 'trackreview',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Tasks',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_tasks_direct' => 
  array (
    'id' => 'cf34d3ac-55a9-7e6c-8ab8-67becc4caa25',
    'relationship_name' => 'opportunities_tasks_direct',
    'lhs_module' => 'Opportunities',
    'lhs_table' => 'opportunities',
    'lhs_key' => 'id',
    'rhs_module' => 'Tasks',
    'rhs_table' => 'tasks',
    'rhs_key' => 'opportunity_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tasks_groups_tasks' => 
  array (
    'id' => 'cf52e6d8-b053-5d70-84d6-67beccabd3c5',
    'relationship_name' => 'tasks_groups_tasks',
    'lhs_module' => 'Tasks_Groups',
    'lhs_table' => 'tasks_groups',
    'lhs_key' => 'id',
    'rhs_module' => 'Tasks',
    'rhs_table' => 'tasks',
    'rhs_key' => 'group_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'forward_users_tasks' => 
  array (
    'id' => 'cf713dbf-0cdd-9514-13af-67becc3b5654',
    'relationship_name' => 'forward_users_tasks',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Tasks',
    'rhs_table' => 'tasks',
    'rhs_key' => 'forward_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'approval_levels_tasks' => 
  array (
    'id' => 'cf8ecae7-51e1-d830-af13-67beccdec597',
    'relationship_name' => 'approval_levels_tasks',
    'lhs_module' => 'Approval_Levels',
    'lhs_table' => 'approval_levels',
    'lhs_key' => 'id',
    'rhs_module' => 'Tasks',
    'rhs_table' => 'tasks',
    'rhs_key' => 'approval_level_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'user_direct_reports' => 
  array (
    'id' => 'd0136772-0fc9-a1e2-5915-67becccbbd09',
    'relationship_name' => 'user_direct_reports',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Users',
    'rhs_table' => 'users',
    'rhs_key' => 'reports_to_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'users_email_addresses' => 
  array (
    'id' => 'd036165c-bcca-eea1-0b16-67becc8d0132',
    'relationship_name' => 'users_email_addresses',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'EmailAddresses',
    'rhs_table' => 'email_addresses',
    'rhs_key' => 'id',
    'join_table' => 'email_addr_bean_rel',
    'join_key_lhs' => 'bean_id',
    'join_key_rhs' => 'email_address_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'bean_module',
    'relationship_role_column_value' => 'Users',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'users_email_addresses_primary' => 
  array (
    'id' => 'd057408d-2904-ddab-161a-67becc3f7085',
    'relationship_name' => 'users_email_addresses_primary',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'EmailAddresses',
    'rhs_table' => 'email_addresses',
    'rhs_key' => 'id',
    'join_table' => 'email_addr_bean_rel',
    'join_key_lhs' => 'bean_id',
    'join_key_rhs' => 'email_address_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'primary_address',
    'relationship_role_column_value' => '1',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'bugs_modified_user' => 
  array (
    'id' => 'd1d013d7-b587-ff94-6292-67becc570301',
    'relationship_name' => 'bugs_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Bugs',
    'rhs_table' => 'bugs',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'bugs_created_by' => 
  array (
    'id' => 'd1f65312-b2be-0bb7-de25-67becc385cd6',
    'relationship_name' => 'bugs_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Bugs',
    'rhs_table' => 'bugs',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'bugs_assigned_user' => 
  array (
    'id' => 'd2129847-d2f7-8e16-a21d-67beccbcf5a6',
    'relationship_name' => 'bugs_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Bugs',
    'rhs_table' => 'bugs',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'bug_tasks' => 
  array (
    'id' => 'd2329335-1084-65ad-8db7-67becc2c0983',
    'relationship_name' => 'bug_tasks',
    'lhs_module' => 'Bugs',
    'lhs_table' => 'bugs',
    'lhs_key' => 'id',
    'rhs_module' => 'Tasks',
    'rhs_table' => 'tasks',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Bugs',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'bug_meetings' => 
  array (
    'id' => 'd255265a-5c28-ead9-d1bd-67becc4beebe',
    'relationship_name' => 'bug_meetings',
    'lhs_module' => 'Bugs',
    'lhs_table' => 'bugs',
    'lhs_key' => 'id',
    'rhs_module' => 'Meetings',
    'rhs_table' => 'meetings',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Bugs',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'bug_calls' => 
  array (
    'id' => 'd279f73c-2b06-ec87-409f-67beccea8324',
    'relationship_name' => 'bug_calls',
    'lhs_module' => 'Bugs',
    'lhs_table' => 'bugs',
    'lhs_key' => 'id',
    'rhs_module' => 'Calls',
    'rhs_table' => 'calls',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Bugs',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'bug_emails' => 
  array (
    'id' => 'd29dda2c-d046-0437-d538-67becc6e6caa',
    'relationship_name' => 'bug_emails',
    'lhs_module' => 'Bugs',
    'lhs_table' => 'bugs',
    'lhs_key' => 'id',
    'rhs_module' => 'Emails',
    'rhs_table' => 'emails',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Bugs',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'bug_notes' => 
  array (
    'id' => 'd2bf920b-1e21-fd0e-642f-67becc0937da',
    'relationship_name' => 'bug_notes',
    'lhs_module' => 'Bugs',
    'lhs_table' => 'bugs',
    'lhs_key' => 'id',
    'rhs_module' => 'Notes',
    'rhs_table' => 'notes',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Bugs',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'bugs_release' => 
  array (
    'id' => 'd2dfd8f8-9b40-f99d-72cd-67becc018a8d',
    'relationship_name' => 'bugs_release',
    'lhs_module' => 'Releases',
    'lhs_table' => 'releases',
    'lhs_key' => 'id',
    'rhs_module' => 'Bugs',
    'rhs_table' => 'bugs',
    'rhs_key' => 'found_in_release',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'bugs_fixed_in_release' => 
  array (
    'id' => 'd2ff32b1-1be8-75c5-6c11-67becc4dad93',
    'relationship_name' => 'bugs_fixed_in_release',
    'lhs_module' => 'Releases',
    'lhs_table' => 'releases',
    'lhs_key' => 'id',
    'rhs_module' => 'Bugs',
    'rhs_table' => 'bugs',
    'rhs_key' => 'fixed_in_release',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'feeds_assigned_user' => 
  array (
    'id' => 'd36a4606-f761-57a7-a9bb-67becc596532',
    'relationship_name' => 'feeds_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Feeds',
    'rhs_table' => 'feeds',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'feeds_modified_user' => 
  array (
    'id' => 'd3879c22-ca38-9032-12a0-67becc50b65b',
    'relationship_name' => 'feeds_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Feeds',
    'rhs_table' => 'feeds',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'feeds_created_by' => 
  array (
    'id' => 'd3a9b32f-b1fb-a028-f6dd-67becc372c70',
    'relationship_name' => 'feeds_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Feeds',
    'rhs_table' => 'feeds',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'project_modified_user' => 
  array (
    'id' => 'd41d5316-4265-27aa-f151-67becc7c43e7',
    'relationship_name' => 'project_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Project',
    'rhs_table' => 'project',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'project_created_by' => 
  array (
    'id' => 'd4410a0e-cece-ae77-30c3-67becc9fb3c3',
    'relationship_name' => 'project_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Project',
    'rhs_table' => 'project',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'project_assigned_user' => 
  array (
    'id' => 'd4622ede-68eb-d928-434e-67becc1aaeb6',
    'relationship_name' => 'project_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Project',
    'rhs_table' => 'project',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'projects_notes' => 
  array (
    'id' => 'd480f9f6-33d7-a78f-c022-67beccbfb91c',
    'relationship_name' => 'projects_notes',
    'lhs_module' => 'Project',
    'lhs_table' => 'project',
    'lhs_key' => 'id',
    'rhs_module' => 'Notes',
    'rhs_table' => 'notes',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Project',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'projects_tasks' => 
  array (
    'id' => 'd4a242ca-d806-c6e9-26da-67becca39d84',
    'relationship_name' => 'projects_tasks',
    'lhs_module' => 'Project',
    'lhs_table' => 'project',
    'lhs_key' => 'id',
    'rhs_module' => 'Tasks',
    'rhs_table' => 'tasks',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Project',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'projects_meetings' => 
  array (
    'id' => 'd4bfea77-7f6e-6af5-ce05-67beccb5a818',
    'relationship_name' => 'projects_meetings',
    'lhs_module' => 'Project',
    'lhs_table' => 'project',
    'lhs_key' => 'id',
    'rhs_module' => 'Meetings',
    'rhs_table' => 'meetings',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Project',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'projects_calls' => 
  array (
    'id' => 'd4e09b7f-630d-b1a0-51cf-67becc2ccc49',
    'relationship_name' => 'projects_calls',
    'lhs_module' => 'Project',
    'lhs_table' => 'project',
    'lhs_key' => 'id',
    'rhs_module' => 'Calls',
    'rhs_table' => 'calls',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Project',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'projects_emails' => 
  array (
    'id' => 'd5002caa-a31e-09f7-6537-67becc0bd942',
    'relationship_name' => 'projects_emails',
    'lhs_module' => 'Project',
    'lhs_table' => 'project',
    'lhs_key' => 'id',
    'rhs_module' => 'Emails',
    'rhs_table' => 'emails',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Project',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'projects_project_tasks' => 
  array (
    'id' => 'd525f54e-8b2a-3d15-d456-67beccccbe86',
    'relationship_name' => 'projects_project_tasks',
    'lhs_module' => 'Project',
    'lhs_table' => 'project',
    'lhs_key' => 'id',
    'rhs_module' => 'ProjectTask',
    'rhs_table' => 'project_task',
    'rhs_key' => 'project_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'project_tasks_notes' => 
  array (
    'id' => 'd597055e-4ac4-5b81-1563-67becc9df4af',
    'relationship_name' => 'project_tasks_notes',
    'lhs_module' => 'ProjectTask',
    'lhs_table' => 'project_task',
    'lhs_key' => 'id',
    'rhs_module' => 'Notes',
    'rhs_table' => 'notes',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'ProjectTask',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'project_tasks_tasks' => 
  array (
    'id' => 'd5bb680b-ef2a-c257-c605-67beccd89cb5',
    'relationship_name' => 'project_tasks_tasks',
    'lhs_module' => 'ProjectTask',
    'lhs_table' => 'project_task',
    'lhs_key' => 'id',
    'rhs_module' => 'Tasks',
    'rhs_table' => 'tasks',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'ProjectTask',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'project_tasks_meetings' => 
  array (
    'id' => 'd5e18c2c-473f-a762-1500-67beccd07311',
    'relationship_name' => 'project_tasks_meetings',
    'lhs_module' => 'ProjectTask',
    'lhs_table' => 'project_task',
    'lhs_key' => 'id',
    'rhs_module' => 'Meetings',
    'rhs_table' => 'meetings',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'ProjectTask',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'project_tasks_calls' => 
  array (
    'id' => 'd5ffb085-8840-3fbe-05a1-67beccbea32a',
    'relationship_name' => 'project_tasks_calls',
    'lhs_module' => 'ProjectTask',
    'lhs_table' => 'project_task',
    'lhs_key' => 'id',
    'rhs_module' => 'Calls',
    'rhs_table' => 'calls',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'ProjectTask',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'project_tasks_emails' => 
  array (
    'id' => 'd6215eb1-d146-9d51-27ac-67becc697567',
    'relationship_name' => 'project_tasks_emails',
    'lhs_module' => 'ProjectTask',
    'lhs_table' => 'project_task',
    'lhs_key' => 'id',
    'rhs_module' => 'Emails',
    'rhs_table' => 'emails',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'ProjectTask',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'project_tasks_assigned_user' => 
  array (
    'id' => 'd63ffc5f-4ea9-63cb-e654-67becce16275',
    'relationship_name' => 'project_tasks_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'ProjectTask',
    'rhs_table' => 'project_task',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'project_tasks_modified_user' => 
  array (
    'id' => 'd661712b-1362-fe26-44d6-67becc44c3bf',
    'relationship_name' => 'project_tasks_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'ProjectTask',
    'rhs_table' => 'project_task',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'project_tasks_created_by' => 
  array (
    'id' => 'd67f56db-5692-afb3-4815-67becced9d7d',
    'relationship_name' => 'project_tasks_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'ProjectTask',
    'rhs_table' => 'project_task',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'email_template_email_marketings' => 
  array (
    'id' => 'd6cd7683-f53d-0618-9608-67becc9bda5c',
    'relationship_name' => 'email_template_email_marketings',
    'lhs_module' => 'EmailTemplates',
    'lhs_table' => 'email_templates',
    'lhs_key' => 'id',
    'rhs_module' => 'EmailMarketing',
    'rhs_table' => 'email_marketing',
    'rhs_key' => 'template_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'campaigns_modified_user' => 
  array (
    'id' => 'd753a1a3-dcd4-1ed0-ed37-67beccf43771',
    'relationship_name' => 'campaigns_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Campaigns',
    'rhs_table' => 'campaigns',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'campaigns_created_by' => 
  array (
    'id' => 'd775b7cc-6c81-aeff-75b8-67becc7814cf',
    'relationship_name' => 'campaigns_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Campaigns',
    'rhs_table' => 'campaigns',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'campaigns_assigned_user' => 
  array (
    'id' => 'd7951023-1529-d1b3-8880-67becc14e2e7',
    'relationship_name' => 'campaigns_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Campaigns',
    'rhs_table' => 'campaigns',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'campaign_accounts' => 
  array (
    'id' => 'd7cdd33e-0cde-1183-c3f5-67becc4425a8',
    'relationship_name' => 'campaign_accounts',
    'lhs_module' => 'Campaigns',
    'lhs_table' => 'campaigns',
    'lhs_key' => 'id',
    'rhs_module' => 'Accounts',
    'rhs_table' => 'accounts',
    'rhs_key' => 'campaign_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'campaign_contacts' => 
  array (
    'id' => 'd7fbff73-255f-7841-66cf-67beccf66538',
    'relationship_name' => 'campaign_contacts',
    'lhs_module' => 'Campaigns',
    'lhs_table' => 'campaigns',
    'lhs_key' => 'id',
    'rhs_module' => 'Contacts',
    'rhs_table' => 'contacts',
    'rhs_key' => 'campaign_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'campaign_leads' => 
  array (
    'id' => 'd81cc5f5-f945-043a-ae16-67becc65590c',
    'relationship_name' => 'campaign_leads',
    'lhs_module' => 'Campaigns',
    'lhs_table' => 'campaigns',
    'lhs_key' => 'id',
    'rhs_module' => 'Leads',
    'rhs_table' => 'leads',
    'rhs_key' => 'campaign_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'campaign_prospects' => 
  array (
    'id' => 'd87e2d28-4ba8-52af-552a-67becc03f95e',
    'relationship_name' => 'campaign_prospects',
    'lhs_module' => 'Campaigns',
    'lhs_table' => 'campaigns',
    'lhs_key' => 'id',
    'rhs_module' => 'Prospects',
    'rhs_table' => 'prospects',
    'rhs_key' => 'campaign_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'campaign_opportunities' => 
  array (
    'id' => 'd8a1d327-e634-482e-ccaf-67beccf7fc30',
    'relationship_name' => 'campaign_opportunities',
    'lhs_module' => 'Campaigns',
    'lhs_table' => 'campaigns',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities',
    'rhs_table' => 'opportunities',
    'rhs_key' => 'campaign_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'campaign_email_marketing' => 
  array (
    'id' => 'd8c29f45-2834-a42a-5e13-67becce6f129',
    'relationship_name' => 'campaign_email_marketing',
    'lhs_module' => 'Campaigns',
    'lhs_table' => 'campaigns',
    'lhs_key' => 'id',
    'rhs_module' => 'EmailMarketing',
    'rhs_table' => 'email_marketing',
    'rhs_key' => 'campaign_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'campaign_emailman' => 
  array (
    'id' => 'd8e2aa0f-b73a-40b0-ce1f-67becc61262e',
    'relationship_name' => 'campaign_emailman',
    'lhs_module' => 'Campaigns',
    'lhs_table' => 'campaigns',
    'lhs_key' => 'id',
    'rhs_module' => 'EmailMan',
    'rhs_table' => 'emailman',
    'rhs_key' => 'campaign_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'campaign_campaignlog' => 
  array (
    'id' => 'd902bf5e-bc68-5391-d2a7-67beccafabd1',
    'relationship_name' => 'campaign_campaignlog',
    'lhs_module' => 'Campaigns',
    'lhs_table' => 'campaigns',
    'lhs_key' => 'id',
    'rhs_module' => 'CampaignLog',
    'rhs_table' => 'campaign_log',
    'rhs_key' => 'campaign_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'campaign_assigned_user' => 
  array (
    'id' => 'd92374e5-1af4-9e62-9f00-67beccafa9be',
    'relationship_name' => 'campaign_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Campaigns',
    'rhs_table' => 'campaigns',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'campaign_modified_user' => 
  array (
    'id' => 'd94320d7-2cc6-7e69-2eca-67beccc16358',
    'relationship_name' => 'campaign_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Campaigns',
    'rhs_table' => 'campaigns',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'campaign_documents' => 
  array (
    'id' => 'd966099c-3d16-fbb8-8f4e-67becc13d2f2',
    'relationship_name' => 'campaign_documents',
    'lhs_module' => 'Campaigns',
    'lhs_table' => 'campaigns',
    'lhs_key' => 'id',
    'rhs_module' => 'Documents',
    'rhs_table' => 'documents',
    'rhs_key' => 'id',
    'join_table' => 'campaign_documents',
    'join_key_lhs' => 'campaign_id',
    'join_key_rhs' => 'document_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'campaign_parent_campaigns' => 
  array (
    'id' => 'd989d87b-908b-a27a-b913-67becc0b9c43',
    'relationship_name' => 'campaign_parent_campaigns',
    'lhs_module' => 'Campaigns',
    'lhs_table' => 'campaigns',
    'lhs_key' => 'parent_campaign_id',
    'rhs_module' => 'Campaigns',
    'rhs_table' => 'campaigns',
    'rhs_key' => 'id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'prospects_modified_user' => 
  array (
    'id' => 'da6206c7-73a8-a38a-8a1c-67becc9f2d02',
    'relationship_name' => 'prospects_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Prospects',
    'rhs_table' => 'prospects',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'prospects_created_by' => 
  array (
    'id' => 'da88efd5-d727-8a6f-0523-67becca89b8a',
    'relationship_name' => 'prospects_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Prospects',
    'rhs_table' => 'prospects',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'prospects_assigned_user' => 
  array (
    'id' => 'dab52210-c377-8185-3495-67becc9ec517',
    'relationship_name' => 'prospects_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Prospects',
    'rhs_table' => 'prospects',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'prospects_email_addresses' => 
  array (
    'id' => 'dad70286-fa0f-e050-cfc1-67becc852d2f',
    'relationship_name' => 'prospects_email_addresses',
    'lhs_module' => 'Prospects',
    'lhs_table' => 'prospects',
    'lhs_key' => 'id',
    'rhs_module' => 'EmailAddresses',
    'rhs_table' => 'email_addresses',
    'rhs_key' => 'id',
    'join_table' => 'email_addr_bean_rel',
    'join_key_lhs' => 'bean_id',
    'join_key_rhs' => 'email_address_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'bean_module',
    'relationship_role_column_value' => 'Prospects',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'prospects_email_addresses_primary' => 
  array (
    'id' => 'dafeb17f-d8a7-5cb8-65f5-67beccaca389',
    'relationship_name' => 'prospects_email_addresses_primary',
    'lhs_module' => 'Prospects',
    'lhs_table' => 'prospects',
    'lhs_key' => 'id',
    'rhs_module' => 'EmailAddresses',
    'rhs_table' => 'email_addresses',
    'rhs_key' => 'id',
    'join_table' => 'email_addr_bean_rel',
    'join_key_lhs' => 'bean_id',
    'join_key_rhs' => 'email_address_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'primary_address',
    'relationship_role_column_value' => '1',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'prospect_campaign_log' => 
  array (
    'id' => 'db2035aa-5954-4653-63e5-67beccc073f5',
    'relationship_name' => 'prospect_campaign_log',
    'lhs_module' => 'Prospects',
    'lhs_table' => 'prospects',
    'lhs_key' => 'id',
    'rhs_module' => 'CampaignLog',
    'rhs_table' => 'campaign_log',
    'rhs_key' => 'target_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'documents_modified_user' => 
  array (
    'id' => 'dbab17ef-9b5c-f045-0e6f-67becca9d576',
    'relationship_name' => 'documents_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Documents',
    'rhs_table' => 'documents',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'documents_created_by' => 
  array (
    'id' => 'dbcd0675-27fa-e866-a6ce-67beccb61276',
    'relationship_name' => 'documents_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Documents',
    'rhs_table' => 'documents',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'documents_assigned_user' => 
  array (
    'id' => 'dbec6a73-8be4-c61c-28ca-67becc48f298',
    'relationship_name' => 'documents_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Documents',
    'rhs_table' => 'documents',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'document_revisions' => 
  array (
    'id' => 'dc0d0a97-af42-f9f7-f2f1-67becc18136c',
    'relationship_name' => 'document_revisions',
    'lhs_module' => 'Documents',
    'lhs_table' => 'documents',
    'lhs_key' => 'id',
    'rhs_module' => 'Documents',
    'rhs_table' => 'document_revisions',
    'rhs_key' => 'document_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'revisions_created_by' => 
  array (
    'id' => 'dc486706-42e2-e976-49e8-67becc39a618',
    'relationship_name' => 'revisions_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'DocumentRevisions',
    'rhs_table' => 'document_revisions',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'schedulers_created_by_rel' => 
  array (
    'id' => 'dd0e4d40-bedd-a7dc-b067-67becce289ce',
    'relationship_name' => 'schedulers_created_by_rel',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Schedulers',
    'rhs_table' => 'schedulers',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-one',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'schedulers_modified_user_id_rel' => 
  array (
    'id' => 'dd323501-2f85-9b8b-b9df-67beccfdca58',
    'relationship_name' => 'schedulers_modified_user_id_rel',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Schedulers',
    'rhs_table' => 'schedulers',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-one',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'schedulers_jobs_rel' => 
  array (
    'id' => 'dd4f2e1b-d5c8-c821-1a1c-67becc03d8ea',
    'relationship_name' => 'schedulers_jobs_rel',
    'lhs_module' => 'Schedulers',
    'lhs_table' => 'schedulers',
    'lhs_key' => 'id',
    'rhs_module' => 'SchedulersJobs',
    'rhs_table' => 'schedulers_times',
    'rhs_key' => 'scheduler_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'inbound_email_created_by' => 
  array (
    'id' => 'de1b2a17-19cf-9c3c-3163-67becc0b698f',
    'relationship_name' => 'inbound_email_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'InboundEmail',
    'rhs_table' => 'inbound_email',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-one',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'inbound_email_modified_user_id' => 
  array (
    'id' => 'de3c0ff7-34e6-1ec0-793a-67becc101fdd',
    'relationship_name' => 'inbound_email_modified_user_id',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'InboundEmail',
    'rhs_table' => 'inbound_email',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-one',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'campaignlog_contact' => 
  array (
    'id' => 'df06a7f2-8c79-c488-64d7-67becce47ab1',
    'relationship_name' => 'campaignlog_contact',
    'lhs_module' => 'CampaignLog',
    'lhs_table' => 'campaign_log',
    'lhs_key' => 'related_id',
    'rhs_module' => 'Contacts',
    'rhs_table' => 'contacts',
    'rhs_key' => 'id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'campaignlog_lead' => 
  array (
    'id' => 'df2aeae2-e88c-afbe-d5f0-67becc6376fe',
    'relationship_name' => 'campaignlog_lead',
    'lhs_module' => 'CampaignLog',
    'lhs_table' => 'campaign_log',
    'lhs_key' => 'related_id',
    'rhs_module' => 'Leads',
    'rhs_table' => 'leads',
    'rhs_key' => 'id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'dashboard_modified_user' => 
  array (
    'id' => 'df94d82d-911e-2bc0-ab6f-67becc35a16a',
    'relationship_name' => 'dashboard_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Dashboard',
    'rhs_table' => 'dashboards',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'dashboard_created_by' => 
  array (
    'id' => 'dfb538e3-c19c-8b1c-5027-67becccd94b3',
    'relationship_name' => 'dashboard_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Dashboard',
    'rhs_table' => 'dashboards',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'dashboard_assigned_user' => 
  array (
    'id' => 'dfd4093b-d43b-01e8-5991-67becc251d80',
    'relationship_name' => 'dashboard_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Dashboard',
    'rhs_table' => 'dashboards',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'campaign_campaigntrakers' => 
  array (
    'id' => 'e00865ba-a413-45d4-3035-67becc72d068',
    'relationship_name' => 'campaign_campaigntrakers',
    'lhs_module' => 'Campaigns',
    'lhs_table' => 'campaigns',
    'lhs_key' => 'id',
    'rhs_module' => 'CampaignTrackers',
    'rhs_table' => 'campaign_trkrs',
    'rhs_key' => 'campaign_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'saved_search_assigned_user' => 
  array (
    'id' => 'e0463bee-f5fa-02f0-4f1a-67becc10ebaf',
    'relationship_name' => 'saved_search_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'SavedSearch',
    'rhs_table' => 'saved_search',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'incomcrmfeed_modified_user' => 
  array (
    'id' => 'e12363e2-cea1-fe17-0aaf-67beccba08bf',
    'relationship_name' => 'incomcrmfeed_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'incomCRMFeed',
    'rhs_table' => 'incomcrmfeed',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'incomcrmfeed_created_by' => 
  array (
    'id' => 'e14f6f89-837d-1d15-6785-67beccd47202',
    'relationship_name' => 'incomcrmfeed_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'incomCRMFeed',
    'rhs_table' => 'incomcrmfeed',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'incomcrmfeed_assigned_user' => 
  array (
    'id' => 'e17a7ed4-819c-557d-dcb3-67becc758eaa',
    'relationship_name' => 'incomcrmfeed_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'incomCRMFeed',
    'rhs_table' => 'incomcrmfeed',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'branches_modified_user' => 
  array (
    'id' => 'e20dc140-ca0c-152b-4b45-67becc31e438',
    'relationship_name' => 'branches_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Branches',
    'rhs_table' => 'branches',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'branches_created_by' => 
  array (
    'id' => 'e23c9d7f-096e-6f28-8829-67beccf24b98',
    'relationship_name' => 'branches_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Branches',
    'rhs_table' => 'branches',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'branch_users' => 
  array (
    'id' => 'e26649a7-59b7-5417-d3f9-67becc348cff',
    'relationship_name' => 'branch_users',
    'lhs_module' => 'Branches',
    'lhs_table' => 'branches',
    'lhs_key' => 'id',
    'rhs_module' => 'Users',
    'rhs_table' => 'users',
    'rhs_key' => 'branch_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_branches' => 
  array (
    'id' => 'e298fb04-aa9b-310c-c49a-67becc4ef2fa',
    'relationship_name' => 'accounts_branches',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'Branches',
    'rhs_table' => 'branches',
    'rhs_key' => 'account_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'companies_branches' => 
  array (
    'id' => 'e2c5f915-42e7-fab3-c96f-67becc8628f9',
    'relationship_name' => 'companies_branches',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'Branches',
    'rhs_table' => 'branches',
    'rhs_key' => 'company_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'warehouses_branches' => 
  array (
    'id' => 'e2eea76b-0d2f-c613-39d7-67becc060f1f',
    'relationship_name' => 'warehouses_branches',
    'lhs_module' => 'TQT_Warehouses',
    'lhs_table' => 'tqt_warehouses',
    'lhs_key' => 'id',
    'rhs_module' => 'Branches',
    'rhs_table' => 'branches',
    'rhs_key' => 'warehouse_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'checkin_dates_modified_user' => 
  array (
    'id' => 'e39ef257-ea34-09b7-845c-67becc1de7c6',
    'relationship_name' => 'checkin_dates_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Checkin_Dates',
    'rhs_table' => 'checkin_dates',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'checkin_dates_created_by' => 
  array (
    'id' => 'e3c771e0-8f23-8d28-d948-67becc7d6ebc',
    'relationship_name' => 'checkin_dates_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Checkin_Dates',
    'rhs_table' => 'checkin_dates',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'checkin_dates_assigned_user' => 
  array (
    'id' => 'e3e7df56-44da-3d4a-d413-67beccec4bab',
    'relationship_name' => 'checkin_dates_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Checkin_Dates',
    'rhs_table' => 'checkin_dates',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_tasks_modified_user' => 
  array (
    'id' => 'e42ed91d-92f2-0ed2-4892-67becc816a88',
    'relationship_name' => 'opportunities_tasks_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities_Tasks',
    'rhs_table' => 'opportunities_tasks',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_tasks_created_by' => 
  array (
    'id' => 'e4526611-5275-41c7-ac3c-67becc9be5a7',
    'relationship_name' => 'opportunities_tasks_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities_Tasks',
    'rhs_table' => 'opportunities_tasks',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_tasks_related' => 
  array (
    'id' => 'e47769c7-a3d9-331b-0e9e-67becc8c35f6',
    'relationship_name' => 'opportunities_tasks_related',
    'lhs_module' => 'Opportunities',
    'lhs_table' => 'opportunities',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities_Tasks',
    'rhs_table' => 'opportunities_tasks',
    'rhs_key' => 'opportunity_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'cycle_plans_modified_user' => 
  array (
    'id' => 'e51a75d5-c836-990d-47ef-67beccdb05ad',
    'relationship_name' => 'cycle_plans_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Cycle_Plans',
    'rhs_table' => 'cycle_plans',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'cycle_plans_created_by' => 
  array (
    'id' => 'e5426742-b51d-8e91-dbe7-67beccf4f937',
    'relationship_name' => 'cycle_plans_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Cycle_Plans',
    'rhs_table' => 'cycle_plans',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'cycle_plans_assigned_user' => 
  array (
    'id' => 'e568a536-5b7c-29a6-551b-67becc3eee60',
    'relationship_name' => 'cycle_plans_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Cycle_Plans',
    'rhs_table' => 'cycle_plans',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'product_units_modified_user' => 
  array (
    'id' => 'e5cc48f0-adf2-7eb7-b155-67becc476e75',
    'relationship_name' => 'product_units_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Product_Units',
    'rhs_table' => 'product_units',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'product_units_created_by' => 
  array (
    'id' => 'e5f41fd6-e42a-ee22-79de-67becc773960',
    'relationship_name' => 'product_units_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Product_Units',
    'rhs_table' => 'product_units',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'product_units_assigned_user' => 
  array (
    'id' => 'e61a17ca-b2cb-c4d6-d16e-67beccbdc736',
    'relationship_name' => 'product_units_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Product_Units',
    'rhs_table' => 'product_units',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'product_units_related' => 
  array (
    'id' => 'e64136b9-9d59-25c6-4192-67becc296cf1',
    'relationship_name' => 'product_units_related',
    'lhs_module' => 'Product_Units',
    'lhs_table' => 'product_units',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Products',
    'rhs_table' => 'tqt_products',
    'rhs_key' => 'product_unit_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'trackreview_modified_user' => 
  array (
    'id' => 'e6f3a2f1-c497-a552-a432-67becc13c84c',
    'relationship_name' => 'trackreview_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TrackReview',
    'rhs_table' => 'trackreview',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'trackreview_created_by' => 
  array (
    'id' => 'e71f4eac-5dfd-6cfb-a95c-67becc94dafb',
    'relationship_name' => 'trackreview_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TrackReview',
    'rhs_table' => 'trackreview',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'trackreview_assigned_user' => 
  array (
    'id' => 'e743f99d-d801-b9eb-4431-67becc0e2101',
    'relationship_name' => 'trackreview_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TrackReview',
    'rhs_table' => 'trackreview',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'product_promotions_modified_user' => 
  array (
    'id' => 'e7cc0ebb-ffdc-38ed-a29d-67beccd661ae',
    'relationship_name' => 'product_promotions_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Product_Promotions',
    'rhs_table' => 'product_promotions',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'product_promotions_created_by' => 
  array (
    'id' => 'e7f4e763-fa75-714b-8916-67becc9af5b2',
    'relationship_name' => 'product_promotions_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Product_Promotions',
    'rhs_table' => 'product_promotions',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'product_promotions_assigned_user' => 
  array (
    'id' => 'e81f4c84-1156-caa3-6795-67becc452b24',
    'relationship_name' => 'product_promotions_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Product_Promotions',
    'rhs_table' => 'product_promotions',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'approval_levels_modified_user' => 
  array (
    'id' => 'e8d65031-e359-c942-c54d-67becc09488d',
    'relationship_name' => 'approval_levels_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Approval_Levels',
    'rhs_table' => 'approval_levels',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'approval_levels_created_by' => 
  array (
    'id' => 'e8fe1b41-27cc-017e-51ed-67becc360554',
    'relationship_name' => 'approval_levels_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Approval_Levels',
    'rhs_table' => 'approval_levels',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'approval_levels_assigned_user' => 
  array (
    'id' => 'e922b08e-992a-f602-1261-67becc2ff19d',
    'relationship_name' => 'approval_levels_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Approval_Levels',
    'rhs_table' => 'approval_levels',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'invoices_modified_user' => 
  array (
    'id' => 'ea1d9c49-e118-0177-93f1-67becc4e84db',
    'relationship_name' => 'invoices_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Invoices',
    'rhs_table' => 'invoices',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'invoices_created_by' => 
  array (
    'id' => 'ea43e61f-7960-fcf0-1eb6-67becc99c388',
    'relationship_name' => 'invoices_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Invoices',
    'rhs_table' => 'invoices',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'invoices_assigned_user' => 
  array (
    'id' => 'ea6a0902-6fcf-2c21-6995-67becc170593',
    'relationship_name' => 'invoices_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Invoices',
    'rhs_table' => 'invoices',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_invoices' => 
  array (
    'id' => 'ea96c3de-94a5-c403-6c2b-67becc0d7e5c',
    'relationship_name' => 'accounts_invoices',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'Invoices',
    'rhs_table' => 'invoices',
    'rhs_key' => 'account_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'product_lots_modified_user' => 
  array (
    'id' => 'eb4b5e43-04cd-a3fa-a3d9-67beccafaf7a',
    'relationship_name' => 'product_lots_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Product_Lots',
    'rhs_table' => 'product_lots',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'product_lots_created_by' => 
  array (
    'id' => 'eb723482-ffb6-3bc3-ae9d-67becce6a059',
    'relationship_name' => 'product_lots_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Product_Lots',
    'rhs_table' => 'product_lots',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'product_lots_assigned_user' => 
  array (
    'id' => 'eb998139-ddf5-8b97-ce0f-67beccd1189f',
    'relationship_name' => 'product_lots_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Product_Lots',
    'rhs_table' => 'product_lots',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_productgroup_product_lots' => 
  array (
    'id' => 'ebbb53e6-a5cf-2ddc-2f75-67becc7a22d8',
    'relationship_name' => 'tqt_productgroup_product_lots',
    'lhs_module' => 'TQT_ProductGroup',
    'lhs_table' => 'tqt_productgroup',
    'lhs_key' => 'id',
    'rhs_module' => 'Product_Lots',
    'rhs_table' => 'product_lots',
    'rhs_key' => 'tqt_productgroup_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'warehouse_types_modified_user' => 
  array (
    'id' => 'ec90e0df-035a-c57e-4bb7-67becc6a5688',
    'relationship_name' => 'warehouse_types_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Types',
    'rhs_table' => 'warehouse_types',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'warehouse_types_created_by' => 
  array (
    'id' => 'ecbe80b4-5a35-f78b-e0d4-67becc640f77',
    'relationship_name' => 'warehouse_types_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Types',
    'rhs_table' => 'warehouse_types',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'custom_units_modified_user' => 
  array (
    'id' => 'edffb9a3-7c4a-a15d-466a-67becc71dae1',
    'relationship_name' => 'custom_units_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Custom_Units',
    'rhs_table' => 'custom_units',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'custom_units_created_by' => 
  array (
    'id' => 'ee324d3c-7b9c-c735-1985-67becc7c8c55',
    'relationship_name' => 'custom_units_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Custom_Units',
    'rhs_table' => 'custom_units',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'custom_units_assigned_user' => 
  array (
    'id' => 'ee58cff7-882e-607a-fe7f-67beccb2c212',
    'relationship_name' => 'custom_units_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Custom_Units',
    'rhs_table' => 'custom_units',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'approval_histories_modified_user' => 
  array (
    'id' => 'eeec9c68-4848-6769-b098-67becc6d8990',
    'relationship_name' => 'approval_histories_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Approval_Histories',
    'rhs_table' => 'approval_histories',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'approval_histories_created_by' => 
  array (
    'id' => 'ef132bd0-4cf3-8417-0674-67becc7056e8',
    'relationship_name' => 'approval_histories_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Approval_Histories',
    'rhs_table' => 'approval_histories',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'approval_histories_assigned_user' => 
  array (
    'id' => 'ef3972be-94f2-fb2c-55dc-67beccf7dd81',
    'relationship_name' => 'approval_histories_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Approval_Histories',
    'rhs_table' => 'approval_histories',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'approve_users_task' => 
  array (
    'id' => 'ef5ea6af-40f5-39da-3118-67beccead4d7',
    'relationship_name' => 'approve_users_task',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Approval_Histories',
    'rhs_table' => 'approval_histories',
    'rhs_key' => 'approve_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'saleplans_modified_user' => 
  array (
    'id' => 'effedce6-6fda-66e5-1bbf-67beccf53c72',
    'relationship_name' => 'saleplans_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'SalePlans',
    'rhs_table' => 'saleplans',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'saleplans_created_by' => 
  array (
    'id' => 'f02196a2-83f4-b709-44c4-67beccb1b1ba',
    'relationship_name' => 'saleplans_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'SalePlans',
    'rhs_table' => 'saleplans',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'saleplans_assigned_user' => 
  array (
    'id' => 'f0467dc6-1d5d-6a0c-e550-67becce01c2a',
    'relationship_name' => 'saleplans_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'SalePlans',
    'rhs_table' => 'saleplans',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_comments_modified_user' => 
  array (
    'id' => 'f0e97358-9790-e036-27ff-67beccc22b31',
    'relationship_name' => 'tqt_comments_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Comments',
    'rhs_table' => 'tqt_comments',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_comments_created_by' => 
  array (
    'id' => 'f12059ec-e341-0bbd-cea1-67becc5fb64e',
    'relationship_name' => 'tqt_comments_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Comments',
    'rhs_table' => 'tqt_comments',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_comments_assigned_user' => 
  array (
    'id' => 'f1545dd8-3640-87be-5c3f-67becce1eac5',
    'relationship_name' => 'tqt_comments_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Comments',
    'rhs_table' => 'tqt_comments',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_tqt_comments' => 
  array (
    'id' => 'f17fa987-7b2e-efb3-4e9f-67beccc63efb',
    'relationship_name' => 'accounts_tqt_comments',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Comments',
    'rhs_table' => 'tqt_comments',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Accounts',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tasks_tqt_comments' => 
  array (
    'id' => 'f1b3657a-d48d-ebef-d472-67becc7808ae',
    'relationship_name' => 'tasks_tqt_comments',
    'lhs_module' => 'Tasks',
    'lhs_table' => 'tasks',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Comments',
    'rhs_table' => 'tqt_comments',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Tasks',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_comments_notes' => 
  array (
    'id' => 'f1e3bdef-38b9-2eb9-3d4d-67beccc32a51',
    'relationship_name' => 'tqt_comments_notes',
    'lhs_module' => 'TQT_Comments',
    'lhs_table' => 'tqt_comments',
    'lhs_key' => 'id',
    'rhs_module' => 'Notes',
    'rhs_table' => 'notes',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'TQT_Comments',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_tqt_comments' => 
  array (
    'id' => 'f21bbbe1-a5df-e01b-08c1-67beccb1213c',
    'relationship_name' => 'opportunities_tqt_comments',
    'lhs_module' => 'Opportunities',
    'lhs_table' => 'opportunities',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Comments',
    'rhs_table' => 'tqt_comments',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Opportunities',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_announcement_tqt_comments' => 
  array (
    'id' => 'f243e251-ddfd-5f51-942b-67becc69471f',
    'relationship_name' => 'tqt_announcement_tqt_comments',
    'lhs_module' => 'TQT_Announcement',
    'lhs_table' => 'tqt_announcement',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Comments',
    'rhs_table' => 'tqt_comments',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'TQT_Announcement',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_announcement_modified_user' => 
  array (
    'id' => 'f33a62ce-8df6-2652-7183-67becc245593',
    'relationship_name' => 'tqt_announcement_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Announcement',
    'rhs_table' => 'tqt_announcement',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_announcement_created_by' => 
  array (
    'id' => 'f367f26a-fb78-945d-307b-67becc3bc006',
    'relationship_name' => 'tqt_announcement_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Announcement',
    'rhs_table' => 'tqt_announcement',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_announcement_assigned_user' => 
  array (
    'id' => 'f3912684-99f5-d9fd-43e4-67becc94ef6d',
    'relationship_name' => 'tqt_announcement_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Announcement',
    'rhs_table' => 'tqt_announcement',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'design_orders_tasks_modified_user' => 
  array (
    'id' => '73100eec-7e44-9b53-8f4c-67becc6bad91',
    'relationship_name' => 'design_orders_tasks_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Design_Orders_Tasks',
    'rhs_table' => 'design_orders_tasks',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'design_orders_tasks_created_by' => 
  array (
    'id' => 'a5000594-6737-863e-698e-67becc6d6e9b',
    'relationship_name' => 'design_orders_tasks_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Design_Orders_Tasks',
    'rhs_table' => 'design_orders_tasks',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'design_orders_tasks_related' => 
  array (
    'id' => 'd47007d6-bf9d-0428-45fe-67beccbc8de4',
    'relationship_name' => 'design_orders_tasks_related',
    'lhs_module' => 'Opportunities',
    'lhs_table' => 'opportunities',
    'lhs_key' => 'id',
    'rhs_module' => 'Design_Orders_Tasks',
    'rhs_table' => 'design_orders_tasks',
    'rhs_key' => 'opportunity_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'map_points_modified_user' => 
  array (
    'id' => '19740670-1a7a-2646-bd67-67becc7d0eb7',
    'relationship_name' => 'map_points_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Map_Points',
    'rhs_table' => 'map_points',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'map_points_created_by' => 
  array (
    'id' => '1c450188-4d01-6d28-1437-67becc32e44d',
    'relationship_name' => 'map_points_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Map_Points',
    'rhs_table' => 'map_points',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'map_points_assigned_user' => 
  array (
    'id' => '1f1b0937-66ee-7af6-50cc-67becc55cb3e',
    'relationship_name' => 'map_points_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Map_Points',
    'rhs_table' => 'map_points',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_modified_user' => 
  array (
    'id' => '2cef0aec-f8cf-d9c1-43ef-67beccd2bb53',
    'relationship_name' => 'securitygroups_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'SecurityGroups',
    'rhs_table' => 'securitygroups',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_created_by' => 
  array (
    'id' => '2fc1088a-8a95-e606-9060-67becc7d230f',
    'relationship_name' => 'securitygroups_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'SecurityGroups',
    'rhs_table' => 'securitygroups',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_assigned_user' => 
  array (
    'id' => '323103ea-2df0-2044-acbb-67beccdb2971',
    'relationship_name' => 'securitygroups_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'SecurityGroups',
    'rhs_table' => 'securitygroups',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'sync_data_modified_user' => 
  array (
    'id' => '3fd10932-5121-4d2f-e0d5-67becc6c5e9f',
    'relationship_name' => 'sync_data_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Sync_Data',
    'rhs_table' => 'sync_data',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'sync_data_created_by' => 
  array (
    'id' => '43440dbe-8e36-282e-d34e-67becc7d8542',
    'relationship_name' => 'sync_data_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Sync_Data',
    'rhs_table' => 'sync_data',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'product_gifts_modified_user' => 
  array (
    'id' => '4b000951-2354-f0b6-1c54-67becc68acaf',
    'relationship_name' => 'product_gifts_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Product_Gifts',
    'rhs_table' => 'product_gifts',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'product_gifts_created_by' => 
  array (
    'id' => '4ed9061c-bdd4-215f-82fe-67beccb55d13',
    'relationship_name' => 'product_gifts_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Product_Gifts',
    'rhs_table' => 'product_gifts',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'product_gifts_assigned_user' => 
  array (
    'id' => '520309d2-a30c-7b56-6407-67becce62cb4',
    'relationship_name' => 'product_gifts_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Product_Gifts',
    'rhs_table' => 'product_gifts',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'product_gifts_related' => 
  array (
    'id' => '55d4020a-2d70-7dc3-f6ac-67beccb09712',
    'relationship_name' => 'product_gifts_related',
    'lhs_module' => 'TQT_Products',
    'lhs_table' => 'tqt_products',
    'lhs_key' => 'id',
    'rhs_module' => 'Product_Gifts',
    'rhs_table' => 'product_gifts',
    'rhs_key' => 'tqt_product_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'payrolls_modified_user' => 
  array (
    'id' => '64450c02-fb3c-60a6-a309-67becc9ab758',
    'relationship_name' => 'payrolls_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'PayRolls',
    'rhs_table' => 'payrolls',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'payrolls_created_by' => 
  array (
    'id' => '675f0a5b-4d0d-7e3c-829e-67beccf482fc',
    'relationship_name' => 'payrolls_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'PayRolls',
    'rhs_table' => 'payrolls',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'payrolls_assigned_user' => 
  array (
    'id' => '6a300119-f0e5-98e3-b235-67becc2ba38b',
    'relationship_name' => 'payrolls_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'PayRolls',
    'rhs_table' => 'payrolls',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_payrolls' => 
  array (
    'id' => '6cbb0d79-efa7-461a-08a1-67becc19f5f3',
    'relationship_name' => 'accounts_payrolls',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'PayRolls',
    'rhs_table' => 'payrolls',
    'rhs_key' => 'account_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_modified_user' => 
  array (
    'id' => '98190dec-e3d0-b4ad-1bcb-67becc8458ce',
    'relationship_name' => 'contracts_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts',
    'rhs_table' => 'contracts',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_created_by' => 
  array (
    'id' => 'a7010b18-bb0c-ce80-a0a9-67becc085ee1',
    'relationship_name' => 'contracts_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts',
    'rhs_table' => 'contracts',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_assigned_user' => 
  array (
    'id' => 'bdf30635-2142-d4f6-1663-67becc84927d',
    'relationship_name' => 'contracts_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts',
    'rhs_table' => 'contracts',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'member_contracts' => 
  array (
    'id' => 'c2160cae-d768-833d-13d6-67becc96097b',
    'relationship_name' => 'member_contracts',
    'lhs_module' => 'Contracts',
    'lhs_table' => 'contracts',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts',
    'rhs_table' => 'contracts',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_contracts' => 
  array (
    'id' => 'c61c0da9-cba7-9857-f592-67becc7c48db',
    'relationship_name' => 'accounts_contracts',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts',
    'rhs_table' => 'contracts',
    'rhs_key' => 'account_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'debt_users_contracts' => 
  array (
    'id' => 'c9160fee-1210-e9b0-c315-67beccf96165',
    'relationship_name' => 'debt_users_contracts',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts',
    'rhs_table' => 'contracts',
    'rhs_key' => 'debt_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_tasks' => 
  array (
    'id' => 'ccf708e7-4472-f430-4178-67beccb10a71',
    'relationship_name' => 'contracts_tasks',
    'lhs_module' => 'Contracts',
    'lhs_table' => 'contracts',
    'lhs_key' => 'id',
    'rhs_module' => 'Tasks',
    'rhs_table' => 'tasks',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Contracts',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_calls' => 
  array (
    'id' => 'd038049d-7869-b4b2-0b79-67becc5e35f0',
    'relationship_name' => 'contracts_calls',
    'lhs_module' => 'Contracts',
    'lhs_table' => 'contracts',
    'lhs_key' => 'id',
    'rhs_module' => 'Calls',
    'rhs_table' => 'calls',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Contracts',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_meetings' => 
  array (
    'id' => 'd4110e7b-5fe8-b39a-10f8-67becce0c21c',
    'relationship_name' => 'contracts_meetings',
    'lhs_module' => 'Contracts',
    'lhs_table' => 'contracts',
    'lhs_key' => 'id',
    'rhs_module' => 'Meetings',
    'rhs_table' => 'meetings',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Contracts',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_notes' => 
  array (
    'id' => 'd728096d-7df9-f283-f9e8-67beccf2f63f',
    'relationship_name' => 'contracts_notes',
    'lhs_module' => 'Contracts',
    'lhs_table' => 'contracts',
    'lhs_key' => 'id',
    'rhs_module' => 'Notes',
    'rhs_table' => 'notes',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Contracts',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_documents' => 
  array (
    'id' => 'daa20a04-fe36-fe38-4944-67beccdc1639',
    'relationship_name' => 'contracts_documents',
    'lhs_module' => 'Contracts',
    'lhs_table' => 'contracts',
    'lhs_key' => 'id',
    'rhs_module' => 'Documents',
    'rhs_table' => 'documents',
    'rhs_key' => 'id',
    'join_table' => 'linked_documents',
    'join_key_lhs' => 'parent_id',
    'join_key_rhs' => 'document_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Contracts',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'handle_users_contracts' => 
  array (
    'id' => 'dd9309a2-e0ed-6876-26d2-67beccb75e73',
    'relationship_name' => 'handle_users_contracts',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts',
    'rhs_table' => 'contracts',
    'rhs_key' => 'handle_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'engineer_users_contracts' => 
  array (
    'id' => 'e06701fb-8cbe-a2f2-d07b-67becccda608',
    'relationship_name' => 'engineer_users_contracts',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts',
    'rhs_table' => 'contracts',
    'rhs_key' => 'engineer_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_contracts' => 
  array (
    'id' => 'e3240909-2752-1b34-7bd4-67becc6cde83',
    'relationship_name' => 'opportunities_contracts',
    'lhs_module' => 'Opportunities',
    'lhs_table' => 'opportunities',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts',
    'rhs_table' => 'contracts',
    'rhs_key' => 'opportunity_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_approves_modified_user' => 
  array (
    'id' => 'f2d702d6-89fa-b664-e3b2-67becc05d572',
    'relationship_name' => 'contracts_approves_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_Approves',
    'rhs_table' => 'contracts_approves',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_approves_created_by' => 
  array (
    'id' => 'f5f90737-1399-8302-f5c9-67beccc6195d',
    'relationship_name' => 'contracts_approves_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_Approves',
    'rhs_table' => 'contracts_approves',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_approves_assigned_user' => 
  array (
    'id' => 'f9c00441-6515-ca02-69c4-67beccb1fcb7',
    'relationship_name' => 'contracts_approves_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_Approves',
    'rhs_table' => 'contracts_approves',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_contracts_approves' => 
  array (
    'id' => 'fc6407bc-d4af-e77e-f6ce-67becca51f6c',
    'relationship_name' => 'contracts_contracts_approves',
    'lhs_module' => 'Contracts',
    'lhs_table' => 'contracts',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_Approves',
    'rhs_table' => 'contracts_approves',
    'rhs_key' => 'contract_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'process_contracts_approves' => 
  array (
    'id' => 'fefd0e78-a915-4f65-fca1-67beccd21639',
    'relationship_name' => 'process_contracts_approves',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_Approves',
    'rhs_table' => 'contracts_approves',
    'rhs_key' => 'process_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_locks_modified_user' => 
  array (
    'id' => '10944d50-7d8c-26dc-a405-67beccb81f60',
    'relationship_name' => 'contracts_locks_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_Locks',
    'rhs_table' => 'contracts_locks',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_locks_created_by' => 
  array (
    'id' => '10c0d930-ec66-f4b9-4ca5-67beccbf7c6c',
    'relationship_name' => 'contracts_locks_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_Locks',
    'rhs_table' => 'contracts_locks',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_locks_assigned_user' => 
  array (
    'id' => '10ea4eb6-bc3c-5f79-3528-67becc09a719',
    'relationship_name' => 'contracts_locks_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_Locks',
    'rhs_table' => 'contracts_locks',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'FK_CONTRACTS_LOCKS_CONTRACT_ID' => 
  array (
    'id' => '11266926-3a20-a0e2-0cde-67becce217fd',
    'relationship_name' => 'FK_CONTRACTS_LOCKS_CONTRACT_ID',
    'lhs_module' => 'Contracts',
    'lhs_table' => 'contracts',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_Locks',
    'rhs_table' => 'contracts_locks',
    'rhs_key' => 'contract_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_payments_modified_user' => 
  array (
    'id' => '14630b60-d5fb-ade6-6027-67becc855f13',
    'relationship_name' => 'contracts_payments_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_Payments',
    'rhs_table' => 'contracts_payments',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_payments_created_by' => 
  array (
    'id' => '152bc0d2-cf58-18fe-5072-67beccfe432b',
    'relationship_name' => 'contracts_payments_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_Payments',
    'rhs_table' => 'contracts_payments',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_payments_assigned_user' => 
  array (
    'id' => '16382a59-a87e-2575-688b-67becc6919f3',
    'relationship_name' => 'contracts_payments_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_Payments',
    'rhs_table' => 'contracts_payments',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'FK_PAYMENT_CONTRACT_ID' => 
  array (
    'id' => '17139efc-c301-d2c4-a305-67becc25b10c',
    'relationship_name' => 'FK_PAYMENT_CONTRACT_ID',
    'lhs_module' => 'Contracts',
    'lhs_table' => 'contracts',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_Payments',
    'rhs_table' => 'contracts_payments',
    'rhs_key' => 'contract_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'FK_ACCOUNTING_USER_ID' => 
  array (
    'id' => '1750f141-1813-007e-6276-67becceffdfc',
    'relationship_name' => 'FK_ACCOUNTING_USER_ID',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_Payments',
    'rhs_table' => 'contracts_payments',
    'rhs_key' => 'accounting_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_collections_modified_user' => 
  array (
    'id' => '184cbc32-48ed-1908-f147-67becc8d322d',
    'relationship_name' => 'contracts_collections_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_Collections',
    'rhs_table' => 'contracts_collections',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_collections_created_by' => 
  array (
    'id' => '18e8fbe0-dfa3-2567-0651-67becc391509',
    'relationship_name' => 'contracts_collections_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_Collections',
    'rhs_table' => 'contracts_collections',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_collections_assigned_user' => 
  array (
    'id' => '1942d341-950a-a5d3-f08b-67becccaee79',
    'relationship_name' => 'contracts_collections_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_Collections',
    'rhs_table' => 'contracts_collections',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_contracts_collections' => 
  array (
    'id' => '19e16617-be3b-045a-b134-67beccaac936',
    'relationship_name' => 'contracts_contracts_collections',
    'lhs_module' => 'Contracts',
    'lhs_table' => 'contracts',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_Collections',
    'rhs_table' => 'contracts_collections',
    'rhs_key' => 'contract_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_contracts_collections' => 
  array (
    'id' => '1a361e7e-5b36-b90c-9fc3-67becc590033',
    'relationship_name' => 'accounts_contracts_collections',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_Collections',
    'rhs_table' => 'contracts_collections',
    'rhs_key' => 'account_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_invoice_modified_user' => 
  array (
    'id' => '1bfd7c2e-0e4a-5223-5bde-67becc3be8cb',
    'relationship_name' => 'contracts_invoice_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_Invoice',
    'rhs_table' => 'contracts_invoice',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_invoice_created_by' => 
  array (
    'id' => '1cba7081-f26a-70a3-8317-67becc111d5b',
    'relationship_name' => 'contracts_invoice_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_Invoice',
    'rhs_table' => 'contracts_invoice',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_invoice_assigned_user' => 
  array (
    'id' => '1cf19b7a-1005-21df-7372-67beccd3641e',
    'relationship_name' => 'contracts_invoice_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_Invoice',
    'rhs_table' => 'contracts_invoice',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_contracts_invoice' => 
  array (
    'id' => '1d2aedc3-00e1-7862-dde3-67becc4ebb00',
    'relationship_name' => 'contracts_contracts_invoice',
    'lhs_module' => 'Contracts',
    'lhs_table' => 'contracts',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_Invoice',
    'rhs_table' => 'contracts_invoice',
    'rhs_key' => 'contract_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_revenuereduces_modified_user' => 
  array (
    'id' => '1e038cd0-9ca0-f321-23c3-67becc3fc1fd',
    'relationship_name' => 'contracts_revenuereduces_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_RevenueReduces',
    'rhs_table' => 'contracts_revenuereduces',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_revenuereduces_created_by' => 
  array (
    'id' => '1e329957-bca5-6fb8-9c06-67beccfa48da',
    'relationship_name' => 'contracts_revenuereduces_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_RevenueReduces',
    'rhs_table' => 'contracts_revenuereduces',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_revenuereduces_assigned_user' => 
  array (
    'id' => '1e783ca1-516b-181c-4f37-67beccecfb3e',
    'relationship_name' => 'contracts_revenuereduces_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_RevenueReduces',
    'rhs_table' => 'contracts_revenuereduces',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_contracts_revenuereduces' => 
  array (
    'id' => '1e9af842-8940-1e10-2788-67becccb1672',
    'relationship_name' => 'contracts_contracts_revenuereduces',
    'lhs_module' => 'Contracts',
    'lhs_table' => 'contracts',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_RevenueReduces',
    'rhs_table' => 'contracts_revenuereduces',
    'rhs_key' => 'contract_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_maintasks_modified_user' => 
  array (
    'id' => '204e98a4-df8c-1c6e-4c83-67becc8e4fa2',
    'relationship_name' => 'tqt_maintasks_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_MainTasks',
    'rhs_table' => 'tqt_maintasks',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_maintasks_created_by' => 
  array (
    'id' => '2077b340-a516-4c82-6a7d-67becc4c8b51',
    'relationship_name' => 'tqt_maintasks_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_MainTasks',
    'rhs_table' => 'tqt_maintasks',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_maintasks_assigned_user' => 
  array (
    'id' => '209ee7ad-d220-a2ee-4404-67becc6e7558',
    'relationship_name' => 'tqt_maintasks_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_MainTasks',
    'rhs_table' => 'tqt_maintasks',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'handle_users_tqt_maintasks' => 
  array (
    'id' => '20c62c49-5e91-a5e8-d494-67becc6fe320',
    'relationship_name' => 'handle_users_tqt_maintasks',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_MainTasks',
    'rhs_table' => 'tqt_maintasks',
    'rhs_key' => 'handle_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'engineer_users_tqt_maintasks' => 
  array (
    'id' => '20eb09bb-da63-2632-9d62-67becce3e764',
    'relationship_name' => 'engineer_users_tqt_maintasks',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_MainTasks',
    'rhs_table' => 'tqt_maintasks',
    'rhs_key' => 'engineer_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_tqt_maintasks' => 
  array (
    'id' => '210d5197-f6d0-9d3c-72e6-67beccb0d615',
    'relationship_name' => 'opportunities_tqt_maintasks',
    'lhs_module' => 'Opportunities',
    'lhs_table' => 'opportunities',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_MainTasks',
    'rhs_table' => 'tqt_maintasks',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Opportunities',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_tqt_maintasks' => 
  array (
    'id' => '212fc3f6-fee9-5cc7-68e4-67becc87c634',
    'relationship_name' => 'contracts_tqt_maintasks',
    'lhs_module' => 'Contracts',
    'lhs_table' => 'contracts',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_MainTasks',
    'rhs_table' => 'tqt_maintasks',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Contracts',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_maintasks_tasks' => 
  array (
    'id' => '21528b0e-15f9-c81b-af57-67becc89b5e8',
    'relationship_name' => 'tqt_maintasks_tasks',
    'lhs_module' => 'TQT_MainTasks',
    'lhs_table' => 'tqt_maintasks',
    'lhs_key' => 'id',
    'rhs_module' => 'Tasks',
    'rhs_table' => 'tasks',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'TQT_MainTasks',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_maintasks_meetings' => 
  array (
    'id' => '2172ea47-5f93-75bd-c7ac-67becc5f8492',
    'relationship_name' => 'tqt_maintasks_meetings',
    'lhs_module' => 'TQT_MainTasks',
    'lhs_table' => 'tqt_maintasks',
    'lhs_key' => 'id',
    'rhs_module' => 'Meetings',
    'rhs_table' => 'meetings',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'TQT_MainTasks',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_maintasks_calls' => 
  array (
    'id' => '2193756c-ba4a-d0b5-34b9-67becc616b06',
    'relationship_name' => 'tqt_maintasks_calls',
    'lhs_module' => 'TQT_MainTasks',
    'lhs_table' => 'tqt_maintasks',
    'lhs_key' => 'id',
    'rhs_module' => 'Calls',
    'rhs_table' => 'calls',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'TQT_MainTasks',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_maintasks_documents' => 
  array (
    'id' => '21b89cff-2a93-5303-5f24-67beccd43184',
    'relationship_name' => 'tqt_maintasks_documents',
    'lhs_module' => 'TQT_MainTasks',
    'lhs_table' => 'tqt_maintasks',
    'lhs_key' => 'id',
    'rhs_module' => 'Documents',
    'rhs_table' => 'documents',
    'rhs_key' => 'id',
    'join_table' => 'linked_documents',
    'join_key_lhs' => 'parent_id',
    'join_key_rhs' => 'document_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'TQT_MainTasks',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_subtasks_modified_user' => 
  array (
    'id' => '227f9ed5-5eba-6ba6-ee8b-67becc63d9e1',
    'relationship_name' => 'tqt_subtasks_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_SubTasks',
    'rhs_table' => 'tqt_subtasks',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_subtasks_created_by' => 
  array (
    'id' => '22a98569-5157-082c-a5d7-67becc744b7a',
    'relationship_name' => 'tqt_subtasks_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_SubTasks',
    'rhs_table' => 'tqt_subtasks',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_subtasks_assigned_user' => 
  array (
    'id' => '22ca71b8-90ed-8a7f-4535-67becca57b46',
    'relationship_name' => 'tqt_subtasks_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_SubTasks',
    'rhs_table' => 'tqt_subtasks',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_tqt_subtasks' => 
  array (
    'id' => '22f766eb-00d7-1cd7-e492-67beccfc8d83',
    'relationship_name' => 'opportunities_tqt_subtasks',
    'lhs_module' => 'Opportunities',
    'lhs_table' => 'opportunities',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_SubTasks',
    'rhs_table' => 'tqt_subtasks',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Opportunities',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_tqt_subtasks' => 
  array (
    'id' => '23235bc7-6797-efec-e6c7-67becccf0e5a',
    'relationship_name' => 'contracts_tqt_subtasks',
    'lhs_module' => 'Contracts',
    'lhs_table' => 'contracts',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_SubTasks',
    'rhs_table' => 'tqt_subtasks',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Contracts',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'cases_tqt_subtasks' => 
  array (
    'id' => '2351b35f-2a4f-b638-c472-67beccb89ed4',
    'relationship_name' => 'cases_tqt_subtasks',
    'lhs_module' => 'Cases',
    'lhs_table' => 'cases',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_SubTasks',
    'rhs_table' => 'tqt_subtasks',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Cases',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_maintasks_tqt_subtasks' => 
  array (
    'id' => '237a946f-f052-2771-670a-67becc538671',
    'relationship_name' => 'tqt_maintasks_tqt_subtasks',
    'lhs_module' => 'TQT_MainTasks',
    'lhs_table' => 'tqt_maintasks',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_SubTasks',
    'rhs_table' => 'tqt_subtasks',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'TQT_MainTasks',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_subtasks_tasks' => 
  array (
    'id' => '23a9dbd4-ded0-a96e-0ffe-67becc2250e1',
    'relationship_name' => 'tqt_subtasks_tasks',
    'lhs_module' => 'TQT_SubTasks',
    'lhs_table' => 'tqt_subtasks',
    'lhs_key' => 'id',
    'rhs_module' => 'Tasks',
    'rhs_table' => 'tasks',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'TQT_SubTasks',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_subtasks_meetings' => 
  array (
    'id' => '23df0f75-0c4a-c55d-68f9-67becc1876ac',
    'relationship_name' => 'tqt_subtasks_meetings',
    'lhs_module' => 'TQT_SubTasks',
    'lhs_table' => 'tqt_subtasks',
    'lhs_key' => 'id',
    'rhs_module' => 'Meetings',
    'rhs_table' => 'meetings',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'TQT_SubTasks',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_subtasks_calls' => 
  array (
    'id' => '24074ac3-158a-74e0-7c5a-67becc3a252f',
    'relationship_name' => 'tqt_subtasks_calls',
    'lhs_module' => 'TQT_SubTasks',
    'lhs_table' => 'tqt_subtasks',
    'lhs_key' => 'id',
    'rhs_module' => 'Calls',
    'rhs_table' => 'calls',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'TQT_SubTasks',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_summaryweeks_modified_user' => 
  array (
    'id' => '24cb479d-1d0f-dc9d-d0ca-67becc38cacd',
    'relationship_name' => 'tqt_summaryweeks_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_SummaryWeeks',
    'rhs_table' => 'tqt_summaryweeks',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_summaryweeks_created_by' => 
  array (
    'id' => '24f2b54d-824e-e8e6-3dc9-67beccfcca2f',
    'relationship_name' => 'tqt_summaryweeks_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_SummaryWeeks',
    'rhs_table' => 'tqt_summaryweeks',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_summaryweeks_assigned_user' => 
  array (
    'id' => '25136ffe-b0a7-5414-1035-67becccb8cab',
    'relationship_name' => 'tqt_summaryweeks_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_SummaryWeeks',
    'rhs_table' => 'tqt_summaryweeks',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_tqt_summaryweeks' => 
  array (
    'id' => '25397137-c254-dd0f-5a10-67becc7ddf87',
    'relationship_name' => 'opportunities_tqt_summaryweeks',
    'lhs_module' => 'Opportunities',
    'lhs_table' => 'opportunities',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_SummaryWeeks',
    'rhs_table' => 'tqt_summaryweeks',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Opportunities',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_tqt_summaryweeks' => 
  array (
    'id' => '2563d0e3-a019-e8a6-9bec-67becc207a1a',
    'relationship_name' => 'contracts_tqt_summaryweeks',
    'lhs_module' => 'Contracts',
    'lhs_table' => 'contracts',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_SummaryWeeks',
    'rhs_table' => 'tqt_summaryweeks',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Contracts',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_maintasks_tqt_summaryweeks' => 
  array (
    'id' => '25899d70-a076-69a3-e503-67beccee51b8',
    'relationship_name' => 'tqt_maintasks_tqt_summaryweeks',
    'lhs_module' => 'TQT_MainTasks',
    'lhs_table' => 'tqt_maintasks',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_SummaryWeeks',
    'rhs_table' => 'tqt_summaryweeks',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'TQT_MainTasks',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_productgroup_modified_user' => 
  array (
    'id' => '29d818e0-3d62-482d-ae12-67becc4a77f3',
    'relationship_name' => 'tqt_productgroup_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_ProductGroup',
    'rhs_table' => 'tqt_productgroup',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_productgroup_created_by' => 
  array (
    'id' => '2a275b3c-d6d1-d2dc-36ee-67beccaa2105',
    'relationship_name' => 'tqt_productgroup_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_ProductGroup',
    'rhs_table' => 'tqt_productgroup',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_productgroup_assigned_user' => 
  array (
    'id' => '2a633038-83c4-cfcf-8a86-67becca1d4b0',
    'relationship_name' => 'tqt_productgroup_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_ProductGroup',
    'rhs_table' => 'tqt_productgroup',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_productseries_tqt_productgroup' => 
  array (
    'id' => '2a935269-134c-eaf9-2391-67becc5f3e96',
    'relationship_name' => 'tqt_productseries_tqt_productgroup',
    'lhs_module' => 'TQT_ProductSeries',
    'lhs_table' => 'tqt_productseries',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_ProductGroup',
    'rhs_table' => 'tqt_productgroup',
    'rhs_key' => 'tqt_productseries_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_guarantee_related' => 
  array (
    'id' => '2b604772-e68d-e465-5586-67becc19d016',
    'relationship_name' => 'opportunities_guarantee_related',
    'lhs_module' => 'Opportunities',
    'lhs_table' => 'opportunities',
    'lhs_key' => 'id',
    'rhs_module' => 'Guarantee_Orders_Tasks',
    'rhs_table' => 'opportunities_tasks',
    'rhs_key' => 'opportunity_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_orderdetails_modified_user' => 
  array (
    'id' => '2cc34c9d-d90a-a4be-36e5-67becc75123f',
    'relationship_name' => 'tqt_orderdetails_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_OrderDetails',
    'rhs_table' => 'tqt_orderdetails',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_orderdetails_created_by' => 
  array (
    'id' => '2cfcd2e6-d786-dc01-0796-67beccfc41bd',
    'relationship_name' => 'tqt_orderdetails_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_OrderDetails',
    'rhs_table' => 'tqt_orderdetails',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_orderdetails_assigned_user' => 
  array (
    'id' => '2d27b6b3-a30e-1e8f-4440-67becc1b57f2',
    'relationship_name' => 'tqt_orderdetails_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_OrderDetails',
    'rhs_table' => 'tqt_orderdetails',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_tqt_orderdetails' => 
  array (
    'id' => '2d69fdb8-42c0-ad5d-85b8-67beccad8a5c',
    'relationship_name' => 'opportunities_tqt_orderdetails',
    'lhs_module' => 'Opportunities',
    'lhs_table' => 'opportunities',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_OrderDetails',
    'rhs_table' => 'tqt_orderdetails',
    'rhs_key' => 'opportunity_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_tqt_orderdetails' => 
  array (
    'id' => '2d972ff6-af02-4eea-a141-67becc9d9b78',
    'relationship_name' => 'accounts_tqt_orderdetails',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_OrderDetails',
    'rhs_table' => 'tqt_orderdetails',
    'rhs_key' => 'account_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_targets_modified_user' => 
  array (
    'id' => '2e753bc3-c665-889f-5e17-67beccfcfe4f',
    'relationship_name' => 'accounts_targets_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Accounts_Targets',
    'rhs_table' => 'accounts_targets',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_targets_created_by' => 
  array (
    'id' => '2eadf639-5d38-4630-6468-67beccc1f1a1',
    'relationship_name' => 'accounts_targets_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Accounts_Targets',
    'rhs_table' => 'accounts_targets',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_targets_assigned_user' => 
  array (
    'id' => '2edb353b-53b1-6561-91e3-67becc272aae',
    'relationship_name' => 'accounts_targets_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Accounts_Targets',
    'rhs_table' => 'accounts_targets',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_accounts_targets' => 
  array (
    'id' => '2f07ec8f-2da3-f6ef-7e5c-67becc8988b5',
    'relationship_name' => 'accounts_accounts_targets',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'Accounts_Targets',
    'rhs_table' => 'accounts_targets',
    'rhs_key' => 'account_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_productseries_accounts_targets' => 
  array (
    'id' => '2f365af5-f00a-b8e7-d22f-67beccde2610',
    'relationship_name' => 'tqt_productseries_accounts_targets',
    'lhs_module' => 'TQT_ProductSeries',
    'lhs_table' => 'tqt_productseries',
    'lhs_key' => 'id',
    'rhs_module' => 'Accounts_Targets',
    'rhs_table' => 'accounts_targets',
    'rhs_key' => 'tqt_productseries_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_product_names_modified_user' => 
  array (
    'id' => '300436eb-a57b-ae04-452a-67becca11964',
    'relationship_name' => 'tqt_product_names_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Product_Names',
    'rhs_table' => 'tqt_product_names',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_product_names_created_by' => 
  array (
    'id' => '30345266-b688-8ef2-3f93-67beccd581ab',
    'relationship_name' => 'tqt_product_names_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Product_Names',
    'rhs_table' => 'tqt_product_names',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_products_names_related' => 
  array (
    'id' => '305f3b74-a9fb-a719-4219-67becce2a91f',
    'relationship_name' => 'tqt_products_names_related',
    'lhs_module' => 'TQT_Products',
    'lhs_table' => 'tqt_products',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Product_Names',
    'rhs_table' => 'tqt_product_names',
    'rhs_key' => 'tqt_product_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_tqt_product_names' => 
  array (
    'id' => '308898a9-1f71-a428-2558-67becc5f61f1',
    'relationship_name' => 'accounts_tqt_product_names',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Product_Names',
    'rhs_table' => 'tqt_product_names',
    'rhs_key' => 'account_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'warehouse_ins_tqt_product_names' => 
  array (
    'id' => '30b11a22-d542-22a7-4e97-67beccfe584f',
    'relationship_name' => 'warehouse_ins_tqt_product_names',
    'lhs_module' => 'Warehouse_Ins',
    'lhs_table' => 'warehouse_ins',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Product_Names',
    'rhs_table' => 'tqt_product_names',
    'rhs_key' => 'warehouse_in_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'warehouse_outs_tqt_product_names' => 
  array (
    'id' => '30dae8e6-b6f4-f588-a789-67beccfc18fd',
    'relationship_name' => 'warehouse_outs_tqt_product_names',
    'lhs_module' => 'Warehouse_Outs',
    'lhs_table' => 'warehouse_outs',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Product_Names',
    'rhs_table' => 'tqt_product_names',
    'rhs_key' => 'warehouse_out_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'account_groups_modified_user' => 
  array (
    'id' => '317e57e7-1246-df26-7f46-67beccf0f50f',
    'relationship_name' => 'account_groups_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Account_Groups',
    'rhs_table' => 'account_groups',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'account_groups_created_by' => 
  array (
    'id' => '31ae5b4c-8ad5-6723-95b2-67becc1729b7',
    'relationship_name' => 'account_groups_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Account_Groups',
    'rhs_table' => 'account_groups',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'account_groups_assigned_user' => 
  array (
    'id' => '31dc7cc8-41fe-cadb-5707-67becc146573',
    'relationship_name' => 'account_groups_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Account_Groups',
    'rhs_table' => 'account_groups',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_account_groups' => 
  array (
    'id' => '320b12a2-7ce4-f1eb-26a6-67beccbb334c',
    'relationship_name' => 'accounts_account_groups',
    'lhs_module' => 'Account_Groups',
    'lhs_table' => 'account_groups',
    'lhs_key' => 'id',
    'rhs_module' => 'Accounts',
    'rhs_table' => 'accounts',
    'rhs_key' => 'account_group_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'telesales_modified_user' => 
  array (
    'id' => '3eec4596-e731-f870-0e82-67becc2cab2c',
    'relationship_name' => 'telesales_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Telesales',
    'rhs_table' => 'telesales',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'telesales_created_by' => 
  array (
    'id' => '3f17df1f-830e-bfdc-be79-67becc0b75f3',
    'relationship_name' => 'telesales_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Telesales',
    'rhs_table' => 'telesales',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'telesales_assigned_user' => 
  array (
    'id' => '3f4cc79e-2f24-e4ac-cfc2-67becc7be6df',
    'relationship_name' => 'telesales_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Telesales',
    'rhs_table' => 'telesales',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_telesales' => 
  array (
    'id' => '3f719d3f-159a-0ea2-7386-67becc9a30cc',
    'relationship_name' => 'accounts_telesales',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'Telesales',
    'rhs_table' => 'telesales',
    'rhs_key' => 'account_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-one',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_productseries_modified_user' => 
  array (
    'id' => '4078af53-65b7-248f-7b92-67becc2aad2b',
    'relationship_name' => 'tqt_productseries_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_ProductSeries',
    'rhs_table' => 'tqt_productseries',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_productseries_created_by' => 
  array (
    'id' => '409b79cd-6633-e42e-94dc-67beccf5a677',
    'relationship_name' => 'tqt_productseries_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_ProductSeries',
    'rhs_table' => 'tqt_productseries',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_productseries_assigned_user' => 
  array (
    'id' => '40bb09cb-89f9-0fde-4843-67becc7b58af',
    'relationship_name' => 'tqt_productseries_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_ProductSeries',
    'rhs_table' => 'tqt_productseries',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tasks_groups_modified_user' => 
  array (
    'id' => '414c91b1-5fe8-0e64-3a46-67becc9c7e89',
    'relationship_name' => 'tasks_groups_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Tasks_Groups',
    'rhs_table' => 'tasks_groups',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tasks_groups_created_by' => 
  array (
    'id' => '4172686c-6f4e-923a-e214-67beccb9d265',
    'relationship_name' => 'tasks_groups_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Tasks_Groups',
    'rhs_table' => 'tasks_groups',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tasks_groups_assigned_user' => 
  array (
    'id' => '419988be-eee6-17ca-373d-67becc69e08f',
    'relationship_name' => 'tasks_groups_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Tasks_Groups',
    'rhs_table' => 'tasks_groups',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'warehouse_invs_modified_user' => 
  array (
    'id' => '42557213-3038-4779-80de-67becc4a4c7c',
    'relationship_name' => 'warehouse_invs_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Invs',
    'rhs_table' => 'warehouse_invs',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'warehouse_invs_created_by' => 
  array (
    'id' => '42842933-3dfb-2e74-12a0-67becc707f96',
    'relationship_name' => 'warehouse_invs_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Invs',
    'rhs_table' => 'warehouse_invs',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'warehouse_invs_assigned_user' => 
  array (
    'id' => '42cd5e1e-0b8f-4501-64ce-67becc53b696',
    'relationship_name' => 'warehouse_invs_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Invs',
    'rhs_table' => 'warehouse_invs',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_warehouse_inventories_related' => 
  array (
    'id' => '42fdc8c8-9594-48b2-e9b1-67beccc36053',
    'relationship_name' => 'tqt_warehouse_inventories_related',
    'lhs_module' => 'TQT_Warehouses',
    'lhs_table' => 'tqt_warehouses',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Invs',
    'rhs_table' => 'warehouse_invs',
    'rhs_key' => 'tqt_warehouse_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'warehouse_ins_inventories_related' => 
  array (
    'id' => '432ca8e9-b538-dcdc-bebf-67becce5818d',
    'relationship_name' => 'warehouse_ins_inventories_related',
    'lhs_module' => 'Warehouse_Ins',
    'lhs_table' => 'warehouse_ins',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Invs',
    'rhs_table' => 'warehouse_invs',
    'rhs_key' => 'warehouse_in_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'warehouse_outs_inventories_related' => 
  array (
    'id' => '4351169b-d8a3-b136-7e98-67becc437ecd',
    'relationship_name' => 'warehouse_outs_inventories_related',
    'lhs_module' => 'Warehouse_Outs',
    'lhs_table' => 'warehouse_outs',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Invs',
    'rhs_table' => 'warehouse_invs',
    'rhs_key' => 'warehouse_out_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_products_modified_user' => 
  array (
    'id' => '4436975d-aeaf-12ad-5ca4-67beccadac71',
    'relationship_name' => 'tqt_products_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Products',
    'rhs_table' => 'tqt_products',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_products_created_by' => 
  array (
    'id' => '445e440c-919b-1be3-e041-67beccdec4d4',
    'relationship_name' => 'tqt_products_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Products',
    'rhs_table' => 'tqt_products',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_products_assigned_user' => 
  array (
    'id' => '447b64b5-c5ff-ba3a-03fc-67beccdc82b8',
    'relationship_name' => 'tqt_products_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Products',
    'rhs_table' => 'tqt_products',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_productgroup_tqt_products' => 
  array (
    'id' => '449a6216-293c-de9b-4659-67becc7a352f',
    'relationship_name' => 'tqt_productgroup_tqt_products',
    'lhs_module' => 'TQT_ProductGroup',
    'lhs_table' => 'tqt_productgroup',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Products',
    'rhs_table' => 'tqt_products',
    'rhs_key' => 'tqt_productgroup_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_productseries_tqt_products' => 
  array (
    'id' => '44b94358-dfe8-89b1-f818-67beccfd38c1',
    'relationship_name' => 'tqt_productseries_tqt_products',
    'lhs_module' => 'TQT_ProductSeries',
    'lhs_table' => 'tqt_productseries',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Products',
    'rhs_table' => 'tqt_products',
    'rhs_key' => 'tqt_productseries_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'time_keepings_modified_user' => 
  array (
    'id' => '45afdac7-5bf3-fc56-3633-67becc8cda22',
    'relationship_name' => 'time_keepings_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Time_Keepings',
    'rhs_table' => 'time_keepings',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'time_keepings_created_by' => 
  array (
    'id' => '45e0fbbe-ab3b-c982-6061-67becc4fe9ce',
    'relationship_name' => 'time_keepings_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Time_Keepings',
    'rhs_table' => 'time_keepings',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'time_keepings_assigned_user' => 
  array (
    'id' => '460b2e1d-9bc4-d0c1-baa8-67becccbc413',
    'relationship_name' => 'time_keepings_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Time_Keepings',
    'rhs_table' => 'time_keepings',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'handle_users_time_keepings' => 
  array (
    'id' => '46310036-24c5-35ac-7e99-67beccb6eb2d',
    'relationship_name' => 'handle_users_time_keepings',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Time_Keepings',
    'rhs_table' => 'time_keepings',
    'rhs_key' => 'handle_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_product_vats_modified_user' => 
  array (
    'id' => '473d02ce-4fe8-0dcc-12fb-67becce9284e',
    'relationship_name' => 'tqt_product_vats_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Product_VATs',
    'rhs_table' => 'tqt_product_vats',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_product_vats_created_by' => 
  array (
    'id' => '47658ff1-1822-dda1-ac83-67beccc836d7',
    'relationship_name' => 'tqt_product_vats_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Product_VATs',
    'rhs_table' => 'tqt_product_vats',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_product_vats_assigned_user' => 
  array (
    'id' => '479adde9-0587-5f4e-a96e-67beccc482de',
    'relationship_name' => 'tqt_product_vats_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Product_VATs',
    'rhs_table' => 'tqt_product_vats',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_products_vats_related' => 
  array (
    'id' => '47bb6c67-c93d-3a54-8aee-67becc15bbfa',
    'relationship_name' => 'tqt_products_vats_related',
    'lhs_module' => 'TQT_Products',
    'lhs_table' => 'tqt_products',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Product_VATs',
    'rhs_table' => 'tqt_product_vats',
    'rhs_key' => 'tqt_product_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_warehouses_stocks_vats_related' => 
  array (
    'id' => '47e4f04f-4196-72d4-7ddf-67becce21c7e',
    'relationship_name' => 'tqt_warehouses_stocks_vats_related',
    'lhs_module' => 'TQT_Warehouses',
    'lhs_table' => 'tqt_warehouses',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Product_VATs',
    'rhs_table' => 'tqt_product_vats',
    'rhs_key' => 'tqt_warehouse_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_stocks_vats_related' => 
  array (
    'id' => '48069505-2dc4-f3c2-584d-67becc56eb6c',
    'relationship_name' => 'opportunities_stocks_vats_related',
    'lhs_module' => 'Opportunities',
    'lhs_table' => 'opportunities',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Product_VATs',
    'rhs_table' => 'tqt_product_vats',
    'rhs_key' => 'opportunity_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_stocks_vats_related' => 
  array (
    'id' => '482c17cf-dc99-95d7-c445-67becc38ea2e',
    'relationship_name' => 'accounts_stocks_vats_related',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Product_VATs',
    'rhs_table' => 'tqt_product_vats',
    'rhs_key' => 'account_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'vat_warehouse_outs' => 
  array (
    'id' => '485066e9-4843-18c6-6544-67beccd81e4a',
    'relationship_name' => 'vat_warehouse_outs',
    'lhs_module' => 'TQT_Product_VATs',
    'lhs_table' => 'tqt_product_vats',
    'lhs_key' => 'warehouse_out_id',
    'rhs_module' => 'Warehouse_Outs',
    'rhs_table' => '	warehouse_outs',
    'rhs_key' => 'id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'vat_warehouse_ins' => 
  array (
    'id' => '4875eddf-cece-1882-c74c-67beccf53327',
    'relationship_name' => 'vat_warehouse_ins',
    'lhs_module' => 'TQT_Product_VATs',
    'lhs_table' => 'tqt_product_vats',
    'lhs_key' => 'warehouse_in_id',
    'rhs_module' => 'Warehouse_Ins',
    'rhs_table' => '	warehouse_ins',
    'rhs_key' => 'id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_growthabilities_modified_user' => 
  array (
    'id' => '49bcaa37-63a4-b5db-0a82-67becc4ddc51',
    'relationship_name' => 'tqt_growthabilities_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_GrowthAbilities',
    'rhs_table' => 'tqt_growthabilities',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_growthabilities_created_by' => 
  array (
    'id' => '49eb10ef-f576-e70a-fffd-67becc4baf77',
    'relationship_name' => 'tqt_growthabilities_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_GrowthAbilities',
    'rhs_table' => 'tqt_growthabilities',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_growthabilities_assigned_user' => 
  array (
    'id' => '4a0ec915-29ba-cf3d-e17c-67becccf2387',
    'relationship_name' => 'tqt_growthabilities_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_GrowthAbilities',
    'rhs_table' => 'tqt_growthabilities',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_tqt_growthabilities' => 
  array (
    'id' => '4a35b04e-424f-c701-d8c4-67becc924ded',
    'relationship_name' => 'accounts_tqt_growthabilities',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_GrowthAbilities',
    'rhs_table' => 'tqt_growthabilities',
    'rhs_key' => 'account_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_productgroup_tqt_growthabilities' => 
  array (
    'id' => '4a56e2c4-2c40-1283-8377-67becc1b586d',
    'relationship_name' => 'tqt_productgroup_tqt_growthabilities',
    'lhs_module' => 'TQT_ProductGroup',
    'lhs_table' => 'tqt_productgroup',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_GrowthAbilities',
    'rhs_table' => 'tqt_growthabilities',
    'rhs_key' => 'tqt_productgroup_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_productseries_tqt_growthabilities' => 
  array (
    'id' => '4a7bf38a-3722-ae58-2b7f-67becc3eded3',
    'relationship_name' => 'tqt_productseries_tqt_growthabilities',
    'lhs_module' => 'TQT_ProductSeries',
    'lhs_table' => 'tqt_productseries',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_GrowthAbilities',
    'rhs_table' => 'tqt_growthabilities',
    'rhs_key' => 'tqt_productseries_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'cash_reasons_modified_user' => 
  array (
    'id' => '50c0a4d5-fcff-05ea-a7f9-67becc54ac9a',
    'relationship_name' => 'cash_reasons_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Cash_Reasons',
    'rhs_table' => 'cash_reasons',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'cash_reasons_created_by' => 
  array (
    'id' => '510ff724-a2c5-277c-cf18-67becc1cd084',
    'relationship_name' => 'cash_reasons_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Cash_Reasons',
    'rhs_table' => 'cash_reasons',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'team_groups_modified_user' => 
  array (
    'id' => '51f09f67-9453-30e0-ba70-67becc25e523',
    'relationship_name' => 'team_groups_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Team_Groups',
    'rhs_table' => 'team_groups',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'team_groups_created_by' => 
  array (
    'id' => '521cfd14-f6e7-ea7a-13b8-67becc6d1038',
    'relationship_name' => 'team_groups_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Team_Groups',
    'rhs_table' => 'team_groups',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'branches_team_groups' => 
  array (
    'id' => '5245cc9c-8b78-3e26-4e42-67becc7facad',
    'relationship_name' => 'branches_team_groups',
    'lhs_module' => 'Branches',
    'lhs_table' => 'branches',
    'lhs_key' => 'id',
    'rhs_module' => 'Team_Groups',
    'rhs_table' => 'team_groups',
    'rhs_key' => 'branch_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'departments_team_groups' => 
  array (
    'id' => '527675ea-b8fd-e71e-a9fe-67becc8a86df',
    'relationship_name' => 'departments_team_groups',
    'lhs_module' => 'Departments',
    'lhs_table' => 'departments',
    'lhs_key' => 'id',
    'rhs_module' => 'Team_Groups',
    'rhs_table' => 'team_groups',
    'rhs_key' => 'department_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'team_groups_users' => 
  array (
    'id' => '5334826e-c4cf-1cf2-e4fe-67beccc8ee14',
    'relationship_name' => 'team_groups_users',
    'lhs_module' => 'Team_Groups',
    'lhs_table' => 'team_groups',
    'lhs_key' => 'id',
    'rhs_module' => 'Users',
    'rhs_table' => 'users',
    'rhs_key' => 'teams',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'monthlytargets_modified_user' => 
  array (
    'id' => '541386ec-d8b8-e610-71a9-67becc206873',
    'relationship_name' => 'monthlytargets_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'MonthlyTargets',
    'rhs_table' => 'monthlytargets',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'monthlytargets_created_by' => 
  array (
    'id' => '544a93ad-9fdb-f3e4-71c4-67becc9c58f1',
    'relationship_name' => 'monthlytargets_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'MonthlyTargets',
    'rhs_table' => 'monthlytargets',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'monthlytargets_assigned_user' => 
  array (
    'id' => '54790a3d-f50c-14e9-2548-67becc4a0af2',
    'relationship_name' => 'monthlytargets_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'MonthlyTargets',
    'rhs_table' => 'monthlytargets',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'bank_accounts_modified_user' => 
  array (
    'id' => '554a0b8c-405c-78fb-18a1-67beccb6b5a8',
    'relationship_name' => 'bank_accounts_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Bank_Accounts',
    'rhs_table' => 'bank_accounts',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'bank_accounts_created_by' => 
  array (
    'id' => '5591da93-cab1-69be-e8d9-67becc41512e',
    'relationship_name' => 'bank_accounts_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Bank_Accounts',
    'rhs_table' => 'bank_accounts',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'bank_accounts_assigned_user' => 
  array (
    'id' => '55c5ea59-2883-95f0-fb4a-67becc83363f',
    'relationship_name' => 'bank_accounts_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Bank_Accounts',
    'rhs_table' => 'bank_accounts',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'bank_accounts_cashes' => 
  array (
    'id' => '55fa36a1-a856-aaa7-8a26-67becc961064',
    'relationship_name' => 'bank_accounts_cashes',
    'lhs_module' => 'Bank_Accounts',
    'lhs_table' => 'bank_accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'Cashes',
    'rhs_table' => 'cashes',
    'rhs_key' => 'bank_account_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'bank_accounts_opportunities' => 
  array (
    'id' => '********-a767-d4ba-7884-67beccee11ec',
    'relationship_name' => 'bank_accounts_opportunities',
    'lhs_module' => 'Bank_Accounts',
    'lhs_table' => 'bank_accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities',
    'rhs_table' => 'opportunities',
    'rhs_key' => 'bank_account_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'list_expenses_modified_user' => 
  array (
    'id' => '571ad8ed-8f9c-4bad-c687-67becc955c72',
    'relationship_name' => 'list_expenses_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'List_Expenses',
    'rhs_table' => 'list_expenses',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'list_expenses_created_by' => 
  array (
    'id' => '57505b6b-7e77-02c3-2b56-67becc709e2c',
    'relationship_name' => 'list_expenses_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'List_Expenses',
    'rhs_table' => 'list_expenses',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'list_expenses_assigned_user' => 
  array (
    'id' => '577d4f55-a234-4ec9-d646-67becc9fce9c',
    'relationship_name' => 'list_expenses_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'List_Expenses',
    'rhs_table' => 'list_expenses',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'revenue_promotions_modified_user' => 
  array (
    'id' => '58f13539-a003-06d0-afe9-67becc6d132d',
    'relationship_name' => 'revenue_promotions_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Revenue_Promotions',
    'rhs_table' => 'revenue_promotions',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'revenue_promotions_created_by' => 
  array (
    'id' => '5944e8a7-930a-7616-269c-67becced6027',
    'relationship_name' => 'revenue_promotions_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Revenue_Promotions',
    'rhs_table' => 'revenue_promotions',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'revenue_promotions_assigned_user' => 
  array (
    'id' => '597112dc-d621-b7f4-e528-67becc1eada3',
    'relationship_name' => 'revenue_promotions_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Revenue_Promotions',
    'rhs_table' => 'revenue_promotions',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_productseries_revenue_promotion' => 
  array (
    'id' => '599fc53d-06d5-5265-bb74-67becca4b42a',
    'relationship_name' => 'tqt_productseries_revenue_promotion',
    'lhs_module' => 'TQT_ProductSeries',
    'lhs_table' => 'tqt_productseries',
    'lhs_key' => 'id',
    'rhs_module' => 'Revenue_Promotions',
    'rhs_table' => 'revenue_promotions',
    'rhs_key' => 'tqt_productseries_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-one',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_businessfields_modified_user' => 
  array (
    'id' => '5b4bdd5d-5d82-e581-9d47-67becca07e0b',
    'relationship_name' => 'tqt_businessfields_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_BusinessFields',
    'rhs_table' => 'tqt_businessfields',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_businessfields_created_by' => 
  array (
    'id' => '5b80404a-70d7-e46b-ea9f-67becca7f260',
    'relationship_name' => 'tqt_businessfields_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_BusinessFields',
    'rhs_table' => 'tqt_businessfields',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_businessfields_assigned_user' => 
  array (
    'id' => '5bb502d1-c4df-a408-bdc3-67becce84cb5',
    'relationship_name' => 'tqt_businessfields_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_BusinessFields',
    'rhs_table' => 'tqt_businessfields',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_tqt_businessfields' => 
  array (
    'id' => '5be0c410-7de4-86da-9051-67becc6fe343',
    'relationship_name' => 'accounts_tqt_businessfields',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_BusinessFields',
    'rhs_table' => 'tqt_businessfields',
    'rhs_key' => 'account_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_productprices_modified_user' => 
  array (
    'id' => '5cb979f8-a27c-035d-d20e-67beccf3b185',
    'relationship_name' => 'tqt_productprices_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_ProductPrices',
    'rhs_table' => 'tqt_productprices',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_productprices_created_by' => 
  array (
    'id' => '5cf387c0-0347-2a14-aa2d-67becc2d49a8',
    'relationship_name' => 'tqt_productprices_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_ProductPrices',
    'rhs_table' => 'tqt_productprices',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_productprices_assigned_user' => 
  array (
    'id' => '5d1d9a16-6f5b-a5f7-76fe-67becc5b0b13',
    'relationship_name' => 'tqt_productprices_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_ProductPrices',
    'rhs_table' => 'tqt_productprices',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_products_tqt_product_prices' => 
  array (
    'id' => '5d50918c-c976-ba4c-7d82-67becc0dabc3',
    'relationship_name' => 'tqt_products_tqt_product_prices',
    'lhs_module' => 'TQT_Products',
    'lhs_table' => 'tqt_products',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_ProductPrices',
    'rhs_table' => 'tqt_productprices',
    'rhs_key' => 'tqt_product_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_tqt_product_prices' => 
  array (
    'id' => '5d7cccc8-bca6-ddf5-6a5d-67becc8f3187',
    'relationship_name' => 'accounts_tqt_product_prices',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_ProductPrices',
    'rhs_table' => 'tqt_productprices',
    'rhs_key' => 'account_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'cashes_modified_user' => 
  array (
    'id' => '5f34955b-5391-321e-b880-67beccb6a81e',
    'relationship_name' => 'cashes_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Cashes',
    'rhs_table' => 'cashes',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'cashes_created_by' => 
  array (
    'id' => '5f74de0b-26f7-a4ea-0816-67becc85474c',
    'relationship_name' => 'cashes_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Cashes',
    'rhs_table' => 'cashes',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'cashes_assigned_user' => 
  array (
    'id' => '5f9ec68a-9338-fdd2-7a8b-67beccad2dfd',
    'relationship_name' => 'cashes_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Cashes',
    'rhs_table' => 'cashes',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_cashes' => 
  array (
    'id' => '5fcc63ec-feed-bf09-c0f2-67becc801f68',
    'relationship_name' => 'accounts_cashes',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'Cashes',
    'rhs_table' => 'cashes',
    'rhs_key' => 'account_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'cash_reasons_related' => 
  array (
    'id' => '5ff76c12-0f35-1a79-c55d-67beccb4061d',
    'relationship_name' => 'cash_reasons_related',
    'lhs_module' => 'Cash_Reasons',
    'lhs_table' => 'cash_reasons',
    'lhs_key' => 'id',
    'rhs_module' => 'Cashes',
    'rhs_table' => 'cashes',
    'rhs_key' => 'cash_reason_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'projects_cashes' => 
  array (
    'id' => '6023a241-1a2b-52a4-9093-67becc873d75',
    'relationship_name' => 'projects_cashes',
    'lhs_module' => 'Project',
    'lhs_table' => 'project',
    'lhs_key' => 'id',
    'rhs_module' => 'Cashes',
    'rhs_table' => 'cashes',
    'rhs_key' => 'project_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'cashes_notes' => 
  array (
    'id' => '60510273-bde4-dff2-6609-67beccf3d019',
    'relationship_name' => 'cashes_notes',
    'lhs_module' => 'Cashes',
    'lhs_table' => 'cashes',
    'lhs_key' => 'id',
    'rhs_module' => 'Notes',
    'rhs_table' => 'notes',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Cashes',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'sale_users_cashes' => 
  array (
    'id' => '6086fc42-5822-d0d0-82dd-67becc16eaa1',
    'relationship_name' => 'sale_users_cashes',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Cashes',
    'rhs_table' => 'cashes',
    'rhs_key' => 'sale_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_commissions_modified_user' => 
  array (
    'id' => '61577c46-8f7a-8fe0-815f-67beccd76118',
    'relationship_name' => 'opportunities_commissions_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities_Commissions',
    'rhs_table' => 'opportunities_commissions',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_commissions_created_by' => 
  array (
    'id' => '61847b21-43dc-533e-5b88-67beccf4cfc8',
    'relationship_name' => 'opportunities_commissions_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities_Commissions',
    'rhs_table' => 'opportunities_commissions',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_commissions_assigned_user' => 
  array (
    'id' => '61aff6a0-0b7d-450f-a0dc-67becc30db3b',
    'relationship_name' => 'opportunities_commissions_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities_Commissions',
    'rhs_table' => 'opportunities_commissions',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_commissions_related' => 
  array (
    'id' => '61dae9ee-3ddb-7b6b-7526-67beccff32ed',
    'relationship_name' => 'opportunities_commissions_related',
    'lhs_module' => 'Opportunities',
    'lhs_table' => 'opportunities',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities_Commissions',
    'rhs_table' => 'opportunities_commissions',
    'rhs_key' => 'opportunity_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_product_stocks_modified_user' => 
  array (
    'id' => '631ded24-3295-a157-d433-67beccb6af13',
    'relationship_name' => 'tqt_product_stocks_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Product_Stocks',
    'rhs_table' => 'tqt_product_stocks',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_product_stocks_created_by' => 
  array (
    'id' => '634fbf38-b3ba-6473-4994-67becc0a3016',
    'relationship_name' => 'tqt_product_stocks_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Product_Stocks',
    'rhs_table' => 'tqt_product_stocks',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_product_stocks_assigned_user' => 
  array (
    'id' => '637f466c-a509-0fbc-3476-67beccb6437a',
    'relationship_name' => 'tqt_product_stocks_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Product_Stocks',
    'rhs_table' => 'tqt_product_stocks',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_products_stocks_related' => 
  array (
    'id' => '63addb67-5e41-4d72-ed35-67becc705295',
    'relationship_name' => 'tqt_products_stocks_related',
    'lhs_module' => 'TQT_Products',
    'lhs_table' => 'tqt_products',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Product_Stocks',
    'rhs_table' => 'tqt_product_stocks',
    'rhs_key' => 'tqt_product_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_warehouses_stocks_related' => 
  array (
    'id' => '63dc3c53-b319-8f02-8f34-67becc2cba8f',
    'relationship_name' => 'tqt_warehouses_stocks_related',
    'lhs_module' => 'TQT_Warehouses',
    'lhs_table' => 'tqt_warehouses',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Product_Stocks',
    'rhs_table' => 'tqt_product_stocks',
    'rhs_key' => 'tqt_warehouse_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'warehouse_ins_stocks_related' => 
  array (
    'id' => '64065339-7742-a79a-73cc-67becc82090a',
    'relationship_name' => 'warehouse_ins_stocks_related',
    'lhs_module' => 'Warehouse_Ins',
    'lhs_table' => 'warehouse_ins',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Product_Stocks',
    'rhs_table' => 'tqt_product_stocks',
    'rhs_key' => 'warehouse_in_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'product_lots_stocks_related' => 
  array (
    'id' => '642ffea4-f12f-82be-db09-67beccfcd577',
    'relationship_name' => 'product_lots_stocks_related',
    'lhs_module' => 'Product_Lots',
    'lhs_table' => 'product_lots',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Product_Stocks',
    'rhs_table' => 'tqt_product_stocks',
    'rhs_key' => 'product_lot_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_payments_modified_user' => 
  array (
    'id' => '656c0c72-0c3e-985e-8e2d-67becc6ab994',
    'relationship_name' => 'opportunities_payments_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities_Payments',
    'rhs_table' => 'opportunities_payments',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_payments_created_by' => 
  array (
    'id' => '6598782f-61e8-ea79-6ec8-67becc3c1947',
    'relationship_name' => 'opportunities_payments_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities_Payments',
    'rhs_table' => 'opportunities_payments',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_payments_assigned_user' => 
  array (
    'id' => '65bf970a-eb73-56c4-91a9-67becc6467e5',
    'relationship_name' => 'opportunities_payments_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities_Payments',
    'rhs_table' => 'opportunities_payments',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_related_payments' => 
  array (
    'id' => '65e57fd0-cea2-3367-4e54-67beccb9a78e',
    'relationship_name' => 'opportunities_related_payments',
    'lhs_module' => 'Opportunities',
    'lhs_table' => 'opportunities',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities_Payments',
    'rhs_table' => 'opportunities_payments',
    'rhs_key' => 'opportunity_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'departments_modified_user' => 
  array (
    'id' => '6680a4cc-aafb-b187-51be-67becc79102a',
    'relationship_name' => 'departments_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Departments',
    'rhs_table' => 'departments',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'departments_created_by' => 
  array (
    'id' => '66a77819-49f2-721c-0a60-67becc73f47d',
    'relationship_name' => 'departments_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Departments',
    'rhs_table' => 'departments',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'branches_departments' => 
  array (
    'id' => '66d02abc-e337-5d94-2492-67becccc5f6e',
    'relationship_name' => 'branches_departments',
    'lhs_module' => 'Branches',
    'lhs_table' => 'branches',
    'lhs_key' => 'id',
    'rhs_module' => 'Departments',
    'rhs_table' => 'departments',
    'rhs_key' => 'branch_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'dept_users' => 
  array (
    'id' => '66f78c7a-4bd6-90f8-aa1d-67becc8ffd6f',
    'relationship_name' => 'dept_users',
    'lhs_module' => 'Departments',
    'lhs_table' => 'departments',
    'lhs_key' => 'id',
    'rhs_module' => 'Users',
    'rhs_table' => 'users',
    'rhs_key' => 'department',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'data_locks_modified_user' => 
  array (
    'id' => '68612cdc-45f3-a6ff-8b74-67beccb48423',
    'relationship_name' => 'data_locks_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Data_Locks',
    'rhs_table' => 'data_locks',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'data_locks_created_by' => 
  array (
    'id' => '689238f5-d9e2-215e-ed02-67becc245a0e',
    'relationship_name' => 'data_locks_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Data_Locks',
    'rhs_table' => 'data_locks',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'data_locks_assigned_user' => 
  array (
    'id' => '68bbfd38-5d26-2f00-957f-67becc6a33e6',
    'relationship_name' => 'data_locks_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Data_Locks',
    'rhs_table' => 'data_locks',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'sales_lines_modified_user' => 
  array (
    'id' => '6945e833-adbd-9cce-79d8-67becc648dd1',
    'relationship_name' => 'sales_lines_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Sales_Lines',
    'rhs_table' => 'sales_lines',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'sales_lines_created_by' => 
  array (
    'id' => '6973314c-71a8-e7f8-18e0-67becc65431f',
    'relationship_name' => 'sales_lines_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Sales_Lines',
    'rhs_table' => 'sales_lines',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'sales_lines_assigned_user' => 
  array (
    'id' => '699b6d5c-7fe5-bc56-d154-67becc804935',
    'relationship_name' => 'sales_lines_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Sales_Lines',
    'rhs_table' => 'sales_lines',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'distributors_sales_lines' => 
  array (
    'id' => '69c5c4b5-9769-937e-426d-67becc7bb641',
    'relationship_name' => 'distributors_sales_lines',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'Sales_Lines',
    'rhs_table' => 'sales_lines',
    'rhs_key' => 'distributor_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_warehouses_modified_user' => 
  array (
    'id' => '6a63e11c-c6c9-3940-a176-67becc02106b',
    'relationship_name' => 'tqt_warehouses_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Warehouses',
    'rhs_table' => 'tqt_warehouses',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_warehouses_created_by' => 
  array (
    'id' => '6a8e2eb1-f527-e9ad-c42e-67becc8f1281',
    'relationship_name' => 'tqt_warehouses_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Warehouses',
    'rhs_table' => 'tqt_warehouses',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_warehouses_assigned_user' => 
  array (
    'id' => '6ab8b9c9-d8af-646c-583c-67becc33da47',
    'relationship_name' => 'tqt_warehouses_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Warehouses',
    'rhs_table' => 'tqt_warehouses',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_delivery_related' => 
  array (
    'id' => '6b6b85e6-c77a-8322-9e25-67becc03c41a',
    'relationship_name' => 'opportunities_delivery_related',
    'lhs_module' => 'Opportunities',
    'lhs_table' => 'opportunities',
    'lhs_key' => 'id',
    'rhs_module' => 'Delivery_Orders_Tasks',
    'rhs_table' => 'opportunities_tasks',
    'rhs_key' => 'opportunity_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'warehouse_ins_modified_user' => 
  array (
    'id' => '6c9924b6-12f2-e20a-650e-67becc6f59b9',
    'relationship_name' => 'warehouse_ins_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Ins',
    'rhs_table' => 'warehouse_ins',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'warehouse_ins_created_by' => 
  array (
    'id' => '6cc79daf-297e-bc37-c345-67becc3f6310',
    'relationship_name' => 'warehouse_ins_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Ins',
    'rhs_table' => 'warehouse_ins',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'warehouse_ins_assigned_user' => 
  array (
    'id' => '6cf3d6d0-2687-d0a9-ea27-67becc885b06',
    'relationship_name' => 'warehouse_ins_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Ins',
    'rhs_table' => 'warehouse_ins',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_warehouse_ins' => 
  array (
    'id' => '6d1e667b-152f-8d25-5bd5-67becc128810',
    'relationship_name' => 'accounts_warehouse_ins',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Ins',
    'rhs_table' => 'warehouse_ins',
    'rhs_key' => 'account_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_warehouse_ins_related' => 
  array (
    'id' => '6d47c745-3cd8-3623-0b73-67becc2ca248',
    'relationship_name' => 'tqt_warehouse_ins_related',
    'lhs_module' => 'TQT_Warehouses',
    'lhs_table' => 'tqt_warehouses',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Ins',
    'rhs_table' => 'warehouse_ins',
    'rhs_key' => 'tqt_warehouse_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'product_lots_warehouse_ins' => 
  array (
    'id' => '6d70e88b-241f-370e-e98b-67becc595dc3',
    'relationship_name' => 'product_lots_warehouse_ins',
    'lhs_module' => 'Product_Lots',
    'lhs_table' => 'product_lots',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Ins',
    'rhs_table' => 'warehouse_ins',
    'rhs_key' => 'product_lot_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'warehouse_types_warehouse_ins' => 
  array (
    'id' => '6d9a90ca-df1d-59b1-3f71-67becca779b5',
    'relationship_name' => 'warehouse_types_warehouse_ins',
    'lhs_module' => 'Warehouse_Types',
    'lhs_table' => 'warehouse_types',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Ins',
    'rhs_table' => 'warehouse_ins',
    'rhs_key' => 'warehouse_type_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_productgroup_warehouse_ins' => 
  array (
    'id' => '6dc822df-66ac-76e6-d166-67becc415fd1',
    'relationship_name' => 'tqt_productgroup_warehouse_ins',
    'lhs_module' => 'TQT_ProductGroup',
    'lhs_table' => 'tqt_productgroup',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Ins',
    'rhs_table' => 'warehouse_ins',
    'rhs_key' => 'tqt_productgroup_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_warehouse_ins' => 
  array (
    'id' => '6df46319-7467-2e72-3c7e-67becc135fca',
    'relationship_name' => 'opportunities_warehouse_ins',
    'lhs_module' => 'Opportunities',
    'lhs_table' => 'opportunities',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Ins',
    'rhs_table' => 'warehouse_ins',
    'rhs_key' => 'opportunity_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'warehouse_outs_warehouse_ins' => 
  array (
    'id' => '6e212cd6-78a0-0838-90bf-67becc0df3c7',
    'relationship_name' => 'warehouse_outs_warehouse_ins',
    'lhs_module' => 'Warehouse_Outs',
    'lhs_table' => 'warehouse_outs',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Ins',
    'rhs_table' => 'warehouse_ins',
    'rhs_key' => 'warehouse_out_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'todo_lists_modified_user' => 
  array (
    'id' => '6f131340-902b-2ee1-4563-67beccc1067e',
    'relationship_name' => 'todo_lists_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Todo_Lists',
    'rhs_table' => 'todo_lists',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'todo_lists_created_by' => 
  array (
    'id' => '6f428179-c04e-5f33-d3a4-67becc305c97',
    'relationship_name' => 'todo_lists_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Todo_Lists',
    'rhs_table' => 'todo_lists',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'todo_lists_assigned_user' => 
  array (
    'id' => '6f6d85c8-a18c-27c3-924d-67becc9d4c65',
    'relationship_name' => 'todo_lists_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Todo_Lists',
    'rhs_table' => 'todo_lists',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tasks_groups_todos' => 
  array (
    'id' => '6f9967b0-a03c-514c-a6fa-67becccdda17',
    'relationship_name' => 'tasks_groups_todos',
    'lhs_module' => 'Tasks_Groups',
    'lhs_table' => 'tasks_groups',
    'lhs_key' => 'code',
    'rhs_module' => 'Todo_Lists',
    'rhs_table' => 'todo_lists',
    'rhs_key' => 'code',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-one',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'list_incurred_costs_modified_user' => 
  array (
    'id' => '70cc64cb-5cf7-ef46-960c-67beccfbdd72',
    'relationship_name' => 'list_incurred_costs_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'List_Incurred_Costs',
    'rhs_table' => 'list_incurred_costs',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'list_incurred_costs_created_by' => 
  array (
    'id' => '70f884ce-ff99-2fd3-2619-67becc233941',
    'relationship_name' => 'list_incurred_costs_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'List_Incurred_Costs',
    'rhs_table' => 'list_incurred_costs',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'list_incurred_costs_assigned_user' => 
  array (
    'id' => '711d1a75-e8cf-584b-ab9a-67becc06b816',
    'relationship_name' => 'list_incurred_costs_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'List_Incurred_Costs',
    'rhs_table' => 'list_incurred_costs',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'list_incurred_costs_tqt_orderdetails' => 
  array (
    'id' => '71415d7e-caef-0055-933b-67becca51ce0',
    'relationship_name' => 'list_incurred_costs_tqt_orderdetails',
    'lhs_module' => 'List_Incurred_Costs',
    'lhs_table' => 'list_incurred_costs',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_OrderDetails',
    'rhs_table' => 'tqt_orderdetails',
    'rhs_key' => 'list_incurred_cost_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'handle_steerings_modified_user' => 
  array (
    'id' => '7437b6c6-ecdd-526e-bf6e-67becc3c18ee',
    'relationship_name' => 'handle_steerings_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Handle_Steerings',
    'rhs_table' => 'handle_steerings',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'handle_steerings_created_by' => 
  array (
    'id' => '746b1255-19bc-089c-4589-67becc6d95ee',
    'relationship_name' => 'handle_steerings_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Handle_Steerings',
    'rhs_table' => 'handle_steerings',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'handle_steerings_assigned_user' => 
  array (
    'id' => '74962944-d978-21ef-177f-67becce8321d',
    'relationship_name' => 'handle_steerings_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Handle_Steerings',
    'rhs_table' => 'handle_steerings',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_handle_steerings' => 
  array (
    'id' => '74c004ea-a330-b022-e125-67becc4f5d69',
    'relationship_name' => 'accounts_handle_steerings',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'Handle_Steerings',
    'rhs_table' => 'handle_steerings',
    'rhs_key' => 'account_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounting_codes_modified_user' => 
  array (
    'id' => '7560abc2-87cd-2aec-9cfd-67becc74273b',
    'relationship_name' => 'accounting_codes_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Accounting_Codes',
    'rhs_table' => 'accounting_codes',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounting_codes_created_by' => 
  array (
    'id' => '7599c029-f716-2c2d-56c8-67becc105abb',
    'relationship_name' => 'accounting_codes_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Accounting_Codes',
    'rhs_table' => 'accounting_codes',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'member_accounting_codes' => 
  array (
    'id' => '75c5618d-ff1d-908b-a8cd-67beccb7bd88',
    'relationship_name' => 'member_accounting_codes',
    'lhs_module' => 'Accounting_Codes',
    'lhs_table' => 'accounting_codes',
    'lhs_key' => 'id',
    'rhs_module' => 'Accounting_Codes',
    'rhs_table' => 'accounting_codes',
    'rhs_key' => 'parent_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounting_codes_cashes' => 
  array (
    'id' => '75f0ee8e-8f4a-4bad-1b14-67becc2f328d',
    'relationship_name' => 'accounting_codes_cashes',
    'lhs_module' => 'Accounting_Codes',
    'lhs_table' => 'accounting_codes',
    'lhs_key' => 'id',
    'rhs_module' => 'Cashes',
    'rhs_table' => 'cashes',
    'rhs_key' => 'accounting_code_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'group_prices_modified_user' => 
  array (
    'id' => '76affa53-0433-00f3-f326-67beccf638d9',
    'relationship_name' => 'group_prices_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Group_Prices',
    'rhs_table' => 'group_prices',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'group_prices_created_by' => 
  array (
    'id' => '76f04648-2b86-07f2-5660-67becca5f94a',
    'relationship_name' => 'group_prices_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Group_Prices',
    'rhs_table' => 'group_prices',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'group_prices_assigned_user' => 
  array (
    'id' => '771bb702-beef-0a2b-8ec8-67beccdf3092',
    'relationship_name' => 'group_prices_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Group_Prices',
    'rhs_table' => 'group_prices',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'group_prices_products_related' => 
  array (
    'id' => '774690fa-5e83-53a3-822c-67becc76b448',
    'relationship_name' => 'group_prices_products_related',
    'lhs_module' => 'Group_Prices',
    'lhs_table' => 'group_prices',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Products',
    'rhs_table' => 'tqt_products',
    'rhs_key' => 'group_price_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'project_tasks_related' => 
  array (
    'id' => '78348b25-d572-aee7-be8b-67becc30835a',
    'relationship_name' => 'project_tasks_related',
    'lhs_module' => 'Project',
    'lhs_table' => 'project',
    'lhs_key' => 'id',
    'rhs_module' => 'Project_Tasks',
    'rhs_table' => 'project_tasks',
    'rhs_key' => 'project_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'projects_tasks_assigned_user' => 
  array (
    'id' => '7861a617-8dd6-f48c-f2ff-67beccfb7e1a',
    'relationship_name' => 'projects_tasks_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Project_Tasks',
    'rhs_table' => 'project_tasks',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'projects_tasks_modified_user' => 
  array (
    'id' => '7886f6d9-b7f7-8c94-9d6b-67becc2798c8',
    'relationship_name' => 'projects_tasks_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Project_Tasks',
    'rhs_table' => 'project_tasks',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'projects_tasks_created_by' => 
  array (
    'id' => '78ac5b63-7bc6-355b-6f1b-67beccca5ff2',
    'relationship_name' => 'projects_tasks_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Project_Tasks',
    'rhs_table' => 'project_tasks',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'warehouse_outs_modified_user' => 
  array (
    'id' => '79c033ab-7432-7bdd-5275-67becc51abbb',
    'relationship_name' => 'warehouse_outs_modified_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Outs',
    'rhs_table' => 'warehouse_outs',
    'rhs_key' => 'modified_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'warehouse_outs_created_by' => 
  array (
    'id' => '79ea4531-4920-8d06-f90d-67becc38cc45',
    'relationship_name' => 'warehouse_outs_created_by',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Outs',
    'rhs_table' => 'warehouse_outs',
    'rhs_key' => 'created_by',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'warehouse_outs_assigned_user' => 
  array (
    'id' => '7a117c64-e3e2-f7a7-7a08-67becc680226',
    'relationship_name' => 'warehouse_outs_assigned_user',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Outs',
    'rhs_table' => 'warehouse_outs',
    'rhs_key' => 'assigned_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_warehouse_outs' => 
  array (
    'id' => '7a34ceb2-7c47-7a4a-af2f-67becc4910a8',
    'relationship_name' => 'accounts_warehouse_outs',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Outs',
    'rhs_table' => 'warehouse_outs',
    'rhs_key' => 'account_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_warehouses_outs_related' => 
  array (
    'id' => '7a5b1241-57e6-d633-4aa6-67becce0a19e',
    'relationship_name' => 'tqt_warehouses_outs_related',
    'lhs_module' => 'TQT_Warehouses',
    'lhs_table' => 'tqt_warehouses',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Outs',
    'rhs_table' => 'warehouse_outs',
    'rhs_key' => 'tqt_warehouse_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'warehouse_receipts_outs_related' => 
  array (
    'id' => '7a80fde7-dd62-d299-ba8e-67beccc0e0d7',
    'relationship_name' => 'warehouse_receipts_outs_related',
    'lhs_module' => 'TQT_Warehouses',
    'lhs_table' => 'tqt_warehouses',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Outs',
    'rhs_table' => 'warehouse_outs',
    'rhs_key' => 'warehouse_receipt_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'recipient_users_warehouse_outs' => 
  array (
    'id' => '7aa575e4-62c8-38e0-abf6-67beccc20bc6',
    'relationship_name' => 'recipient_users_warehouse_outs',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Outs',
    'rhs_table' => 'warehouse_outs',
    'rhs_key' => 'recipient_user_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'warehouse_types_warehouse_outs' => 
  array (
    'id' => '7ace813a-e759-81fd-d67c-67becc81b867',
    'relationship_name' => 'warehouse_types_warehouse_outs',
    'lhs_module' => 'Warehouse_Types',
    'lhs_table' => 'warehouse_types',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Outs',
    'rhs_table' => 'warehouse_outs',
    'rhs_key' => 'warehouse_type_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_productgroup_warehouse_outs' => 
  array (
    'id' => '7af45776-352f-9fad-cf85-67becc7f1c3d',
    'relationship_name' => 'tqt_productgroup_warehouse_outs',
    'lhs_module' => 'TQT_ProductGroup',
    'lhs_table' => 'tqt_productgroup',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Outs',
    'rhs_table' => 'warehouse_outs',
    'rhs_key' => 'tqt_productgroup_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_warehouse_outs' => 
  array (
    'id' => '7b1b3752-52ab-1e41-04b2-67beccbfac13',
    'relationship_name' => 'opportunities_warehouse_outs',
    'lhs_module' => 'Opportunities',
    'lhs_table' => 'opportunities',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Outs',
    'rhs_table' => 'warehouse_outs',
    'rhs_key' => 'opportunity_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'warehouse_ins_warehouse_outs_related' => 
  array (
    'id' => '7b453d64-5374-df7d-ae97-67becc6866bf',
    'relationship_name' => 'warehouse_ins_warehouse_outs_related',
    'lhs_module' => 'Warehouse_Ins',
    'lhs_table' => 'warehouse_ins',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Outs',
    'rhs_table' => 'warehouse_outs',
    'rhs_key' => 'warehouse_ins_id',
    'join_table' => NULL,
    'join_key_lhs' => NULL,
    'join_key_rhs' => NULL,
    'relationship_type' => 'one-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_bugs' => 
  array (
    'id' => 'cf6172da-006e-b397-854f-67becc4fb8e9',
    'relationship_name' => 'accounts_bugs',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'Bugs',
    'rhs_table' => 'bugs',
    'rhs_key' => 'id',
    'join_table' => 'accounts_bugs',
    'join_key_lhs' => 'account_id',
    'join_key_rhs' => 'bug_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_cases' => 
  array (
    'id' => 'cff3fa18-9d45-b377-a90a-67beccfce5a2',
    'relationship_name' => 'accounts_cases',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'Cases',
    'rhs_table' => 'cases',
    'rhs_key' => 'id',
    'join_table' => 'accounts_cases',
    'join_key_lhs' => 'account_id',
    'join_key_rhs' => 'case_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_contacts' => 
  array (
    'id' => 'd025d968-85d1-0f8c-fcb4-67becc868e1a',
    'relationship_name' => 'accounts_contacts',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'Contacts',
    'rhs_table' => 'contacts',
    'rhs_key' => 'id',
    'join_table' => 'accounts_contacts',
    'join_key_lhs' => 'account_id',
    'join_key_rhs' => 'contact_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_opportunities' => 
  array (
    'id' => 'd057edb2-cf0d-2ed3-5a0f-67becce91138',
    'relationship_name' => 'accounts_opportunities',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities',
    'rhs_table' => 'opportunities',
    'rhs_key' => 'id',
    'join_table' => 'accounts_opportunities',
    'join_key_lhs' => 'account_id',
    'join_key_rhs' => 'opportunity_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'calls_contacts' => 
  array (
    'id' => 'd091a114-3120-c0d2-5ce4-67becc33a7d2',
    'relationship_name' => 'calls_contacts',
    'lhs_module' => 'Calls',
    'lhs_table' => 'calls',
    'lhs_key' => 'id',
    'rhs_module' => 'Contacts',
    'rhs_table' => 'contacts',
    'rhs_key' => 'id',
    'join_table' => 'calls_contacts',
    'join_key_lhs' => 'call_id',
    'join_key_rhs' => 'contact_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'calls_users' => 
  array (
    'id' => 'd0c3408f-8ddf-647e-e1d7-67becc14b251',
    'relationship_name' => 'calls_users',
    'lhs_module' => 'Calls',
    'lhs_table' => 'calls',
    'lhs_key' => 'id',
    'rhs_module' => 'Users',
    'rhs_table' => 'users',
    'rhs_key' => 'id',
    'join_table' => 'calls_users',
    'join_key_lhs' => 'call_id',
    'join_key_rhs' => 'user_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'calls_leads' => 
  array (
    'id' => 'd0f5a9a5-9f22-029b-f48b-67becc56c5ee',
    'relationship_name' => 'calls_leads',
    'lhs_module' => 'Calls',
    'lhs_table' => 'calls',
    'lhs_key' => 'id',
    'rhs_module' => 'Leads',
    'rhs_table' => 'leads',
    'rhs_key' => 'id',
    'join_table' => 'calls_leads',
    'join_key_lhs' => 'call_id',
    'join_key_rhs' => 'lead_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'cases_bugs' => 
  array (
    'id' => 'd1235b2d-1f6b-3efd-ae68-67becc768f82',
    'relationship_name' => 'cases_bugs',
    'lhs_module' => 'Cases',
    'lhs_table' => 'cases',
    'lhs_key' => 'id',
    'rhs_module' => 'Bugs',
    'rhs_table' => 'bugs',
    'rhs_key' => 'id',
    'join_table' => 'cases_bugs',
    'join_key_lhs' => 'case_id',
    'join_key_rhs' => 'bug_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contacts_bugs' => 
  array (
    'id' => 'd1523c5c-29c6-e465-b1f2-67becc2e1ae8',
    'relationship_name' => 'contacts_bugs',
    'lhs_module' => 'Contacts',
    'lhs_table' => 'contacts',
    'lhs_key' => 'id',
    'rhs_module' => 'Bugs',
    'rhs_table' => 'bugs',
    'rhs_key' => 'id',
    'join_table' => 'contacts_bugs',
    'join_key_lhs' => 'contact_id',
    'join_key_rhs' => 'bug_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contacts_cases' => 
  array (
    'id' => 'd18a7a53-8fa4-2622-26b1-67becc4c5634',
    'relationship_name' => 'contacts_cases',
    'lhs_module' => 'Contacts',
    'lhs_table' => 'contacts',
    'lhs_key' => 'id',
    'rhs_module' => 'Cases',
    'rhs_table' => 'cases',
    'rhs_key' => 'id',
    'join_table' => 'contacts_cases',
    'join_key_lhs' => 'contact_id',
    'join_key_rhs' => 'case_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contacts_users' => 
  array (
    'id' => 'd1bf28ee-63a7-3a44-b6a0-67beccb5f8e9',
    'relationship_name' => 'contacts_users',
    'lhs_module' => 'Contacts',
    'lhs_table' => 'contacts',
    'lhs_key' => 'id',
    'rhs_module' => 'Users',
    'rhs_table' => 'users',
    'rhs_key' => 'id',
    'join_table' => 'contacts_users',
    'join_key_lhs' => 'contact_id',
    'join_key_rhs' => 'user_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'emails_accounts_rel' => 
  array (
    'id' => 'd1f69162-9333-7a6c-2090-67becc562820',
    'relationship_name' => 'emails_accounts_rel',
    'lhs_module' => 'Emails',
    'lhs_table' => 'emails',
    'lhs_key' => 'id',
    'rhs_module' => 'Accounts',
    'rhs_table' => 'accounts',
    'rhs_key' => 'id',
    'join_table' => 'emails_beans',
    'join_key_lhs' => 'email_id',
    'join_key_rhs' => 'bean_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'bean_module',
    'relationship_role_column_value' => 'Accounts',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'emails_bugs_rel' => 
  array (
    'id' => 'd2237003-d42c-5aa1-4c63-67becc35c494',
    'relationship_name' => 'emails_bugs_rel',
    'lhs_module' => 'Emails',
    'lhs_table' => 'emails',
    'lhs_key' => 'id',
    'rhs_module' => 'Bugs',
    'rhs_table' => 'bugs',
    'rhs_key' => 'id',
    'join_table' => 'emails_beans',
    'join_key_lhs' => 'email_id',
    'join_key_rhs' => 'bean_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'bean_module',
    'relationship_role_column_value' => 'Bugs',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'emails_cases_rel' => 
  array (
    'id' => 'd24e3b95-a87b-09f5-eb58-67becc958d77',
    'relationship_name' => 'emails_cases_rel',
    'lhs_module' => 'Emails',
    'lhs_table' => 'emails',
    'lhs_key' => 'id',
    'rhs_module' => 'Cases',
    'rhs_table' => 'cases',
    'rhs_key' => 'id',
    'join_table' => 'emails_beans',
    'join_key_lhs' => 'email_id',
    'join_key_rhs' => 'bean_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'bean_module',
    'relationship_role_column_value' => 'Cases',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'emails_contacts_rel' => 
  array (
    'id' => 'd279a2ba-f355-c60d-7ff3-67beccb3d8ee',
    'relationship_name' => 'emails_contacts_rel',
    'lhs_module' => 'Emails',
    'lhs_table' => 'emails',
    'lhs_key' => 'id',
    'rhs_module' => 'Contacts',
    'rhs_table' => 'contacts',
    'rhs_key' => 'id',
    'join_table' => 'emails_beans',
    'join_key_lhs' => 'email_id',
    'join_key_rhs' => 'bean_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'bean_module',
    'relationship_role_column_value' => 'Contacts',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'emails_leads_rel' => 
  array (
    'id' => 'd2a446c3-40a2-3aa3-7614-67becc1de77b',
    'relationship_name' => 'emails_leads_rel',
    'lhs_module' => 'Emails',
    'lhs_table' => 'emails',
    'lhs_key' => 'id',
    'rhs_module' => 'Leads',
    'rhs_table' => 'leads',
    'rhs_key' => 'id',
    'join_table' => 'emails_beans',
    'join_key_lhs' => 'email_id',
    'join_key_rhs' => 'bean_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'bean_module',
    'relationship_role_column_value' => 'Leads',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'emails_opportunities_rel' => 
  array (
    'id' => 'd2cf624a-aae5-8887-be2e-67beccc3a7ef',
    'relationship_name' => 'emails_opportunities_rel',
    'lhs_module' => 'Emails',
    'lhs_table' => 'emails',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities',
    'rhs_table' => 'opportunities',
    'rhs_key' => 'id',
    'join_table' => 'emails_beans',
    'join_key_lhs' => 'email_id',
    'join_key_rhs' => 'bean_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'bean_module',
    'relationship_role_column_value' => 'Opportunities',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'emails_tasks_rel' => 
  array (
    'id' => 'd2f9c7fd-24a2-046a-cf83-67becc1f5bac',
    'relationship_name' => 'emails_tasks_rel',
    'lhs_module' => 'Emails',
    'lhs_table' => 'emails',
    'lhs_key' => 'id',
    'rhs_module' => 'Tasks',
    'rhs_table' => 'tasks',
    'rhs_key' => 'id',
    'join_table' => 'emails_beans',
    'join_key_lhs' => 'email_id',
    'join_key_rhs' => 'bean_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'bean_module',
    'relationship_role_column_value' => 'Tasks',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'emails_users_rel' => 
  array (
    'id' => 'd322d158-6b87-8668-183d-67becc4697fa',
    'relationship_name' => 'emails_users_rel',
    'lhs_module' => 'Emails',
    'lhs_table' => 'emails',
    'lhs_key' => 'id',
    'rhs_module' => 'Users',
    'rhs_table' => 'users',
    'rhs_key' => 'id',
    'join_table' => 'emails_beans',
    'join_key_lhs' => 'email_id',
    'join_key_rhs' => 'bean_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'bean_module',
    'relationship_role_column_value' => 'Users',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'emails_project_task_rel' => 
  array (
    'id' => 'd34d87a3-a849-f024-63b3-67beccd6a5b6',
    'relationship_name' => 'emails_project_task_rel',
    'lhs_module' => 'Emails',
    'lhs_table' => 'emails',
    'lhs_key' => 'id',
    'rhs_module' => 'ProjectTask',
    'rhs_table' => 'project_task',
    'rhs_key' => 'id',
    'join_table' => 'emails_beans',
    'join_key_lhs' => 'email_id',
    'join_key_rhs' => 'bean_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'bean_module',
    'relationship_role_column_value' => 'ProjectTask',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'emails_projects_rel' => 
  array (
    'id' => 'd37812ac-f8b8-2f27-1388-67becc629b0d',
    'relationship_name' => 'emails_projects_rel',
    'lhs_module' => 'Emails',
    'lhs_table' => 'emails',
    'lhs_key' => 'id',
    'rhs_module' => 'Project',
    'rhs_table' => 'project',
    'rhs_key' => 'id',
    'join_table' => 'emails_beans',
    'join_key_lhs' => 'email_id',
    'join_key_rhs' => 'bean_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'bean_module',
    'relationship_role_column_value' => 'Project',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'emails_prospects_rel' => 
  array (
    'id' => 'd3a1e77f-d2a8-e10d-4c56-67becc24a198',
    'relationship_name' => 'emails_prospects_rel',
    'lhs_module' => 'Emails',
    'lhs_table' => 'emails',
    'lhs_key' => 'id',
    'rhs_module' => 'Prospects',
    'rhs_table' => 'prospects',
    'rhs_key' => 'id',
    'join_table' => 'emails_beans',
    'join_key_lhs' => 'email_id',
    'join_key_rhs' => 'bean_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'bean_module',
    'relationship_role_column_value' => 'Prospects',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'meetings_contacts' => 
  array (
    'id' => 'd3dcba35-6d8f-c5e4-a06d-67becc35d3a1',
    'relationship_name' => 'meetings_contacts',
    'lhs_module' => 'Meetings',
    'lhs_table' => 'meetings',
    'lhs_key' => 'id',
    'rhs_module' => 'Contacts',
    'rhs_table' => 'contacts',
    'rhs_key' => 'id',
    'join_table' => 'meetings_contacts',
    'join_key_lhs' => 'meeting_id',
    'join_key_rhs' => 'contact_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'meetings_users' => 
  array (
    'id' => 'd40c7e30-7013-f8e7-c3d4-67becc82fbfe',
    'relationship_name' => 'meetings_users',
    'lhs_module' => 'Meetings',
    'lhs_table' => 'meetings',
    'lhs_key' => 'id',
    'rhs_module' => 'Users',
    'rhs_table' => 'users',
    'rhs_key' => 'id',
    'join_table' => 'meetings_users',
    'join_key_lhs' => 'meeting_id',
    'join_key_rhs' => 'user_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'meetings_leads' => 
  array (
    'id' => 'd43df47b-745c-caea-3322-67becc496b64',
    'relationship_name' => 'meetings_leads',
    'lhs_module' => 'Meetings',
    'lhs_table' => 'meetings',
    'lhs_key' => 'id',
    'rhs_module' => 'Leads',
    'rhs_table' => 'leads',
    'rhs_key' => 'id',
    'join_table' => 'meetings_leads',
    'join_key_lhs' => 'meeting_id',
    'join_key_rhs' => 'lead_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_contacts' => 
  array (
    'id' => 'd46fe107-c3b4-0d44-d0ba-67becc5b8ebe',
    'relationship_name' => 'opportunities_contacts',
    'lhs_module' => 'Opportunities',
    'lhs_table' => 'opportunities',
    'lhs_key' => 'id',
    'rhs_module' => 'Contacts',
    'rhs_table' => 'contacts',
    'rhs_key' => 'id',
    'join_table' => 'opportunities_contacts',
    'join_key_lhs' => 'opportunity_id',
    'join_key_rhs' => 'contact_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'prospect_list_campaigns' => 
  array (
    'id' => 'd4a16648-837d-96ce-ac37-67becc58f9d2',
    'relationship_name' => 'prospect_list_campaigns',
    'lhs_module' => 'ProspectLists',
    'lhs_table' => 'prospect_lists',
    'lhs_key' => 'id',
    'rhs_module' => 'Campaigns',
    'rhs_table' => 'campaigns',
    'rhs_key' => 'id',
    'join_table' => 'prospect_list_campaigns',
    'join_key_lhs' => 'prospect_list_id',
    'join_key_rhs' => 'campaign_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'prospect_list_contacts' => 
  array (
    'id' => 'd4dae18b-743b-c3aa-6e46-67becc48525c',
    'relationship_name' => 'prospect_list_contacts',
    'lhs_module' => 'ProspectLists',
    'lhs_table' => 'prospect_lists',
    'lhs_key' => 'id',
    'rhs_module' => 'Contacts',
    'rhs_table' => 'contacts',
    'rhs_key' => 'id',
    'join_table' => 'prospect_lists_prospects',
    'join_key_lhs' => 'prospect_list_id',
    'join_key_rhs' => 'related_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'related_type',
    'relationship_role_column_value' => 'Contacts',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'prospect_list_prospects' => 
  array (
    'id' => 'd5053bc5-5846-0649-b5cc-67becc695acf',
    'relationship_name' => 'prospect_list_prospects',
    'lhs_module' => 'ProspectLists',
    'lhs_table' => 'prospect_lists',
    'lhs_key' => 'id',
    'rhs_module' => 'Prospects',
    'rhs_table' => 'prospects',
    'rhs_key' => 'id',
    'join_table' => 'prospect_lists_prospects',
    'join_key_lhs' => 'prospect_list_id',
    'join_key_rhs' => 'related_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'related_type',
    'relationship_role_column_value' => 'Prospects',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'prospect_list_leads' => 
  array (
    'id' => 'd5320b65-af5e-8d2f-332c-67becc027506',
    'relationship_name' => 'prospect_list_leads',
    'lhs_module' => 'ProspectLists',
    'lhs_table' => 'prospect_lists',
    'lhs_key' => 'id',
    'rhs_module' => 'Leads',
    'rhs_table' => 'leads',
    'rhs_key' => 'id',
    'join_table' => 'prospect_lists_prospects',
    'join_key_lhs' => 'prospect_list_id',
    'join_key_rhs' => 'related_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'related_type',
    'relationship_role_column_value' => 'Leads',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'prospect_list_users' => 
  array (
    'id' => 'd55bccf8-1114-ac6a-905a-67becc07e5ef',
    'relationship_name' => 'prospect_list_users',
    'lhs_module' => 'ProspectLists',
    'lhs_table' => 'prospect_lists',
    'lhs_key' => 'id',
    'rhs_module' => 'Users',
    'rhs_table' => 'users',
    'rhs_key' => 'id',
    'join_table' => 'prospect_lists_prospects',
    'join_key_lhs' => 'prospect_list_id',
    'join_key_rhs' => 'related_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'related_type',
    'relationship_role_column_value' => 'Users',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'prospect_list_accounts' => 
  array (
    'id' => 'd58425fb-afb3-71c0-c263-67becc148a74',
    'relationship_name' => 'prospect_list_accounts',
    'lhs_module' => 'ProspectLists',
    'lhs_table' => 'prospect_lists',
    'lhs_key' => 'id',
    'rhs_module' => 'Accounts',
    'rhs_table' => 'accounts',
    'rhs_key' => 'id',
    'join_table' => 'prospect_lists_prospects',
    'join_key_lhs' => 'prospect_list_id',
    'join_key_rhs' => 'related_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'related_type',
    'relationship_role_column_value' => 'Accounts',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'roles_users' => 
  array (
    'id' => 'd5bbd064-5ff0-ef98-5ca9-67beccf10ffb',
    'relationship_name' => 'roles_users',
    'lhs_module' => 'Roles',
    'lhs_table' => 'roles',
    'lhs_key' => 'id',
    'rhs_module' => 'Users',
    'rhs_table' => 'users',
    'rhs_key' => 'id',
    'join_table' => 'roles_users',
    'join_key_lhs' => 'role_id',
    'join_key_rhs' => 'user_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'projects_bugs' => 
  array (
    'id' => 'd5f17ca5-37e5-a712-af81-67becc9ab8b8',
    'relationship_name' => 'projects_bugs',
    'lhs_module' => 'Project',
    'lhs_table' => 'project',
    'lhs_key' => 'id',
    'rhs_module' => 'Bugs',
    'rhs_table' => 'bugs',
    'rhs_key' => 'id',
    'join_table' => 'projects_bugs',
    'join_key_lhs' => 'project_id',
    'join_key_rhs' => 'bug_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'projects_cases' => 
  array (
    'id' => 'd626c487-8f52-a9b6-2752-67becc3d6b24',
    'relationship_name' => 'projects_cases',
    'lhs_module' => 'Project',
    'lhs_table' => 'project',
    'lhs_key' => 'id',
    'rhs_module' => 'Cases',
    'rhs_table' => 'cases',
    'rhs_key' => 'id',
    'join_table' => 'projects_cases',
    'join_key_lhs' => 'project_id',
    'join_key_rhs' => 'case_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'projects_accounts' => 
  array (
    'id' => 'd6615d1d-dd2e-3800-2b82-67beccc09383',
    'relationship_name' => 'projects_accounts',
    'lhs_module' => 'Project',
    'lhs_table' => 'project',
    'lhs_key' => 'id',
    'rhs_module' => 'Accounts',
    'rhs_table' => 'accounts',
    'rhs_key' => 'id',
    'join_table' => 'projects_accounts',
    'join_key_lhs' => 'project_id',
    'join_key_rhs' => 'account_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'projects_contacts' => 
  array (
    'id' => 'd693f998-b7e8-0a7e-c018-67becce1477d',
    'relationship_name' => 'projects_contacts',
    'lhs_module' => 'Project',
    'lhs_table' => 'project',
    'lhs_key' => 'id',
    'rhs_module' => 'Contacts',
    'rhs_table' => 'contacts',
    'rhs_key' => 'id',
    'join_table' => 'projects_contacts',
    'join_key_lhs' => 'project_id',
    'join_key_rhs' => 'contact_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'projects_opportunities' => 
  array (
    'id' => 'd6c4e76b-9a2e-c84a-5c25-67becc5a3685',
    'relationship_name' => 'projects_opportunities',
    'lhs_module' => 'Project',
    'lhs_table' => 'project',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities',
    'rhs_table' => 'opportunities',
    'rhs_key' => 'id',
    'join_table' => 'projects_opportunities',
    'join_key_lhs' => 'project_id',
    'join_key_rhs' => 'opportunity_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'acl_roles_actions' => 
  array (
    'id' => 'd6f5a3dd-aebe-1975-a571-67beccb7a322',
    'relationship_name' => 'acl_roles_actions',
    'lhs_module' => 'ACLRoles',
    'lhs_table' => 'acl_roles',
    'lhs_key' => 'id',
    'rhs_module' => 'ACLActions',
    'rhs_table' => 'acl_actions',
    'rhs_key' => 'id',
    'join_table' => 'acl_roles_actions',
    'join_key_lhs' => 'role_id',
    'join_key_rhs' => 'action_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'acl_roles_users' => 
  array (
    'id' => 'd72975c3-8348-dcae-4c4e-67becce443a2',
    'relationship_name' => 'acl_roles_users',
    'lhs_module' => 'ACLRoles',
    'lhs_table' => 'acl_roles',
    'lhs_key' => 'id',
    'rhs_module' => 'Users',
    'rhs_table' => 'users',
    'rhs_key' => 'id',
    'join_table' => 'acl_roles_users',
    'join_key_lhs' => 'role_id',
    'join_key_rhs' => 'user_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'email_marketing_prospect_lists' => 
  array (
    'id' => 'd766c214-4b6c-4142-979b-67beccd763f1',
    'relationship_name' => 'email_marketing_prospect_lists',
    'lhs_module' => 'EmailMarketing',
    'lhs_table' => 'email_marketing',
    'lhs_key' => 'id',
    'rhs_module' => 'ProspectLists',
    'rhs_table' => 'prospect_lists',
    'rhs_key' => 'id',
    'join_table' => 'email_marketing_prospect_lists',
    'join_key_lhs' => 'email_marketing_id',
    'join_key_rhs' => 'prospect_list_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'leads_documents' => 
  array (
    'id' => 'd799e915-cd9a-2f9d-0ed7-67becc845bd2',
    'relationship_name' => 'leads_documents',
    'lhs_module' => 'Leads',
    'lhs_table' => 'leads',
    'lhs_key' => 'id',
    'rhs_module' => 'Documents',
    'rhs_table' => 'documents',
    'rhs_key' => 'id',
    'join_table' => 'linked_documents',
    'join_key_lhs' => 'parent_id',
    'join_key_rhs' => 'document_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Leads',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'users_departments' => 
  array (
    'id' => 'd7e9958a-3b79-1c1f-8d97-67becc55e01f',
    'relationship_name' => 'users_departments',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Departments',
    'rhs_table' => 'departments',
    'rhs_key' => 'id',
    'join_table' => 'users_departments',
    'join_key_lhs' => 'user_id',
    'join_key_rhs' => 'department_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'users_tqt_warehouses' => 
  array (
    'id' => 'd81addbe-fd2d-e84f-fa75-67becc7c4848',
    'relationship_name' => 'users_tqt_warehouses',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Warehouses',
    'rhs_table' => 'tqt_warehouses',
    'rhs_key' => 'id',
    'join_table' => 'users_tqt_warehouses',
    'join_key_lhs' => 'user_id',
    'join_key_rhs' => 'tqt_warehouse_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'saleplans_accounts' => 
  array (
    'id' => 'd84b0026-da8e-8637-307f-67becc5b5f62',
    'relationship_name' => 'saleplans_accounts',
    'lhs_module' => 'SalePlans',
    'lhs_table' => 'saleplans',
    'lhs_key' => 'id',
    'rhs_module' => 'Accounts',
    'rhs_table' => 'accounts',
    'rhs_key' => 'id',
    'join_table' => 'saleplans_accounts',
    'join_key_lhs' => 'saleplan_id',
    'join_key_rhs' => 'account_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_users' => 
  array (
    'id' => 'd87b048f-1cba-feb1-4f47-67becc4a1b4d',
    'relationship_name' => 'securitygroups_users',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Users',
    'rhs_table' => 'users',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_users',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'user_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_tqt_offlines' => 
  array (
    'id' => 'd8aa527f-93ce-3b75-c25a-67becc6e48d7',
    'relationship_name' => 'contracts_tqt_offlines',
    'lhs_module' => 'Contracts',
    'lhs_table' => 'contracts',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Products',
    'rhs_table' => 'tqt_products',
    'rhs_key' => 'id',
    'join_table' => 'contracts_tqt_offlines',
    'join_key_lhs' => 'contract_id',
    'join_key_rhs' => 'product_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_acl_roles' => 
  array (
    'id' => 'd8e36068-87a7-94dd-b25f-67becc18d70a',
    'relationship_name' => 'securitygroups_acl_roles',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'ACLRoles',
    'rhs_table' => 'acl_roles',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_acl_roles',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'role_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'warehouse_outs_opportunities' => 
  array (
    'id' => 'd91842a5-97f8-11e8-dd94-67becc7554d1',
    'relationship_name' => 'warehouse_outs_opportunities',
    'lhs_module' => 'Warehouse_Outs',
    'lhs_table' => 'warehouse_outs',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities',
    'rhs_table' => 'opportunities',
    'rhs_key' => 'id',
    'join_table' => 'warehouse_outs_opportunities',
    'join_key_lhs' => 'warehouse_out_id',
    'join_key_rhs' => 'opportunity_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_revenue_promotions' => 
  array (
    'id' => 'd9524548-a6bd-b874-91c0-67beccca0aaa',
    'relationship_name' => 'opportunities_revenue_promotions',
    'lhs_module' => 'Opportunities',
    'lhs_table' => 'opportunities',
    'lhs_key' => 'id',
    'rhs_module' => 'Revenue_Promotions',
    'rhs_table' => 'revenue_promotions',
    'rhs_key' => 'id',
    'join_table' => 'opportunities_revenue_promotions',
    'join_key_lhs' => 'opportunity_id',
    'join_key_rhs' => 'revenue_promotion_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'accounts_revenue_promotions' => 
  array (
    'id' => 'd982b4ff-340d-bfd6-2141-67becc004246',
    'relationship_name' => 'accounts_revenue_promotions',
    'lhs_module' => 'Accounts',
    'lhs_table' => 'accounts',
    'lhs_key' => 'id',
    'rhs_module' => 'Revenue_Promotions',
    'rhs_table' => 'revenue_promotions',
    'rhs_key' => 'id',
    'join_table' => 'accounts_revenue_promotions',
    'join_key_lhs' => 'account_id',
    'join_key_rhs' => 'revenue_promotion_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'sales_lines_accounts' => 
  array (
    'id' => 'd9b941a5-f119-d06b-ed2c-67becc1a2102',
    'relationship_name' => 'sales_lines_accounts',
    'lhs_module' => 'Sales_Lines',
    'lhs_table' => 'sales_lines',
    'lhs_key' => 'id',
    'rhs_module' => 'Accounts',
    'rhs_table' => 'accounts',
    'rhs_key' => 'id',
    'join_table' => 'sales_lines_accounts',
    'join_key_lhs' => 'sales_line_id',
    'join_key_rhs' => 'account_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_project_teams' => 
  array (
    'id' => 'd9ecea45-31ce-26dd-0bc7-67becc041b0b',
    'relationship_name' => 'contracts_project_teams',
    'lhs_module' => 'Contracts',
    'lhs_table' => 'contracts',
    'lhs_key' => 'id',
    'rhs_module' => 'Users',
    'rhs_table' => 'users',
    'rhs_key' => 'id',
    'join_table' => 'tqt_project_teams',
    'join_key_lhs' => 'record_id',
    'join_key_rhs' => 'user_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Contracts',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'project_project_teams' => 
  array (
    'id' => 'da1795d9-8e37-1db6-424c-67beccc87357',
    'relationship_name' => 'project_project_teams',
    'lhs_module' => 'Project',
    'lhs_table' => 'project',
    'lhs_key' => 'id',
    'rhs_module' => 'Users',
    'rhs_table' => 'users',
    'rhs_key' => 'id',
    'join_table' => 'tqt_project_teams',
    'join_key_lhs' => 'record_id',
    'join_key_rhs' => 'user_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Project',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'contracts_collections_payments' => 
  array (
    'id' => 'da4fbf5d-d64d-94ae-6916-67becc5acb21',
    'relationship_name' => 'contracts_collections_payments',
    'lhs_module' => 'Contracts_Collections',
    'lhs_table' => 'contracts_collections',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_Payments',
    'rhs_table' => 'contracts_payments',
    'rhs_key' => 'id',
    'join_table' => 'contracts_collections_payments',
    'join_key_lhs' => 'collection_id',
    'join_key_rhs' => 'payment_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'warehouse_outs_tqt_products' => 
  array (
    'id' => 'da805ec1-d671-3f57-d72b-67becc788e17',
    'relationship_name' => 'warehouse_outs_tqt_products',
    'lhs_module' => 'Warehouse_Outs',
    'lhs_table' => 'warehouse_outs',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Products',
    'rhs_table' => 'tqt_products',
    'rhs_key' => 'id',
    'join_table' => 'warehouse_outs_tqt_products',
    'join_key_lhs' => 'warehouse_out_id',
    'join_key_rhs' => 'tqt_product_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_warehouses_outs_tqt_products' => 
  array (
    'id' => 'daa8b585-0909-db1f-c3c4-67becc18cd53',
    'relationship_name' => 'tqt_warehouses_outs_tqt_products',
    'lhs_module' => 'TQT_Warehouses',
    'lhs_table' => 'tqt_warehouses',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Products',
    'rhs_table' => 'tqt_products',
    'rhs_key' => 'id',
    'join_table' => 'warehouse_outs_tqt_products',
    'join_key_lhs' => 'warehouse_id',
    'join_key_rhs' => 'tqt_product_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'warehouse_outs_ins_products' => 
  array (
    'id' => 'dad764b4-e195-11d8-290d-67becc7b79e4',
    'relationship_name' => 'warehouse_outs_ins_products',
    'lhs_module' => 'Warehouse_Outs',
    'lhs_table' => 'warehouse_outs',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Products',
    'rhs_table' => 'tqt_products',
    'rhs_key' => 'id',
    'join_table' => 'warehouse_outs_ins_products',
    'join_key_lhs' => 'warehouse_out_id',
    'join_key_rhs' => 'tqt_product_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_warehouses_ins_outs_products' => 
  array (
    'id' => 'db0ce1b5-be32-fdb4-9f85-67becc907cc7',
    'relationship_name' => 'tqt_warehouses_ins_outs_products',
    'lhs_module' => 'TQT_Warehouses',
    'lhs_table' => 'tqt_warehouses',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Product_Stocks',
    'rhs_table' => 'tqt_product_stocks',
    'rhs_key' => 'id',
    'join_table' => 'warehouse_out_in_related',
    'join_key_lhs' => 'warehouse_id',
    'join_key_rhs' => 'product_stock_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'warehouse_out_in_related' => 
  array (
    'id' => 'db35254a-1184-1fcd-5b68-67becc193369',
    'relationship_name' => 'warehouse_out_in_related',
    'lhs_module' => 'Warehouse_Ins',
    'lhs_table' => 'warehouse_ins',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Outs',
    'rhs_table' => 'warehouse_outs',
    'rhs_key' => 'id',
    'join_table' => 'warehouse_out_in_related',
    'join_key_lhs' => 'warehouse_in_id',
    'join_key_rhs' => 'warehouse_out_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_products_related_out_in_stocks' => 
  array (
    'id' => 'db5d4b2e-70a0-c4e3-1f85-67becc5f4efa',
    'relationship_name' => 'tqt_products_related_out_in_stocks',
    'lhs_module' => 'TQT_Products',
    'lhs_table' => 'tqt_products',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Product_Stocks',
    'rhs_table' => 'tqt_product_stocks',
    'rhs_key' => 'id',
    'join_table' => 'warehouse_out_in_related',
    'join_key_lhs' => 'product_id',
    'join_key_rhs' => 'product_stock_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'warehouse_inventories' => 
  array (
    'id' => 'db8f46c7-f2f4-c4a9-17a6-67becc828ea5',
    'relationship_name' => 'warehouse_inventories',
    'lhs_module' => 'Warehouse_Invs',
    'lhs_table' => 'warehouse_invs',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Products',
    'rhs_table' => 'tqt_products',
    'rhs_key' => 'id',
    'join_table' => 'warehouse_inventories',
    'join_key_lhs' => 'warehouse_inv_id',
    'join_key_rhs' => 'product_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'warehouse_product_costs' => 
  array (
    'id' => 'dbbe8221-99d0-b3c6-8ad0-67beccc134d3',
    'relationship_name' => 'warehouse_product_costs',
    'lhs_module' => 'TQT_Products',
    'lhs_table' => 'tqt_products',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Warehouses',
    'rhs_table' => 'tqt_warehouses',
    'rhs_key' => 'id',
    'join_table' => 'warehouse_product_costs',
    'join_key_lhs' => 'product_id',
    'join_key_rhs' => 'warehouse_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'products_branches_prices' => 
  array (
    'id' => 'dbef484b-bb47-0467-452a-67beccc29dfe',
    'relationship_name' => 'products_branches_prices',
    'lhs_module' => 'TQT_Products',
    'lhs_table' => 'tqt_products',
    'lhs_key' => 'id',
    'rhs_module' => 'Branches',
    'rhs_table' => 'branches',
    'rhs_key' => 'id',
    'join_table' => 'products_branches_prices',
    'join_key_lhs' => 'product_id',
    'join_key_rhs' => 'branch_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'product_beginning_costs' => 
  array (
    'id' => 'dc205572-4c31-bd68-f63b-67becc6fae58',
    'relationship_name' => 'product_beginning_costs',
    'lhs_module' => 'TQT_Products',
    'lhs_table' => 'tqt_products',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Warehouses',
    'rhs_table' => 'tqt_warehouses',
    'rhs_key' => 'id',
    'join_table' => 'product_beginning_costs',
    'join_key_lhs' => 'product_id',
    'join_key_rhs' => 'warehouse_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'cashes_collect_details' => 
  array (
    'id' => 'dc4f8481-d4c0-2fb2-663a-67becc664bbc',
    'relationship_name' => 'cashes_collect_details',
    'lhs_module' => 'Cashes',
    'lhs_table' => 'cashes',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities',
    'rhs_table' => 'opportunities',
    'rhs_key' => 'id',
    'join_table' => 'cashes_collect_details',
    'join_key_lhs' => 'cash_id',
    'join_key_rhs' => 'opportunity_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'cashes_payment_details' => 
  array (
    'id' => 'dc811a7e-49ab-53c4-785d-67becc9990fa',
    'relationship_name' => 'cashes_payment_details',
    'lhs_module' => 'Cashes',
    'lhs_table' => 'cashes',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities',
    'rhs_table' => 'opportunities',
    'rhs_key' => 'id',
    'join_table' => 'cashes_payment_details',
    'join_key_lhs' => 'cash_id',
    'join_key_rhs' => 'opportunity_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'cycle_plans_accounts' => 
  array (
    'id' => 'dcb433b8-73eb-619f-13f3-67becc700fe5',
    'relationship_name' => 'cycle_plans_accounts',
    'lhs_module' => 'Cycle_Plans',
    'lhs_table' => 'cycle_plans',
    'lhs_key' => 'id',
    'rhs_module' => 'Accounts',
    'rhs_table' => 'accounts',
    'rhs_key' => 'id',
    'join_table' => 'cycle_plans_accounts',
    'join_key_lhs' => 'cycle_plan_id',
    'join_key_rhs' => 'account_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'monthlytarget_products' => 
  array (
    'id' => 'dcf05d20-e169-200c-501b-67becc886361',
    'relationship_name' => 'monthlytarget_products',
    'lhs_module' => 'MonthlyTargets',
    'lhs_table' => 'monthlytargets',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Products',
    'rhs_table' => 'tqt_products',
    'rhs_key' => 'id',
    'join_table' => 'monthlytarget_products',
    'join_key_lhs' => 'monthlytarget_id',
    'join_key_rhs' => 'tqt_product_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_opportunities_commissions' => 
  array (
    'id' => 'dd23b617-5564-85e2-512c-67becc749aba',
    'relationship_name' => 'securitygroups_opportunities_commissions',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities_Commissions',
    'rhs_table' => 'opportunities_commissions',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Opportunities_Commissions',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_group_prices' => 
  array (
    'id' => 'dd4fcd60-3130-82c3-4e7e-67beccfa7aa8',
    'relationship_name' => 'securitygroups_group_prices',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Group_Prices',
    'rhs_table' => 'group_prices',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Group_Prices',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_time_keepings' => 
  array (
    'id' => 'dd7b9d01-100f-9f32-cb60-67becc725251',
    'relationship_name' => 'securitygroups_time_keepings',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Time_Keepings',
    'rhs_table' => 'time_keepings',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Time_Keepings',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_product_promotions' => 
  array (
    'id' => 'dda5d618-44b8-028e-1528-67beccd9c1c1',
    'relationship_name' => 'securitygroups_product_promotions',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Product_Promotions',
    'rhs_table' => 'product_promotions',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Product_Promotions',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_tqt_warehouses' => 
  array (
    'id' => 'ddd17cf7-fd19-5eaa-495e-67becce58ea9',
    'relationship_name' => 'securitygroups_tqt_warehouses',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Warehouses',
    'rhs_table' => 'tqt_warehouses',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'TQT_Warehouses',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_warehouse_ins' => 
  array (
    'id' => 'ddfd383d-24d4-563b-cfff-67beccd6e4eb',
    'relationship_name' => 'securitygroups_warehouse_ins',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Ins',
    'rhs_table' => 'warehouse_ins',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Warehouse_Ins',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_warehouse_outs' => 
  array (
    'id' => 'de2a9d84-55f2-d4d7-cc7c-67beccbd7487',
    'relationship_name' => 'securitygroups_warehouse_outs',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Warehouse_Outs',
    'rhs_table' => 'warehouse_outs',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Warehouse_Outs',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_cashes' => 
  array (
    'id' => 'de56fed9-9af2-68ea-5fde-67becc7df525',
    'relationship_name' => 'securitygroups_cashes',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Cashes',
    'rhs_table' => 'cashes',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Cashes',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_sales_lines' => 
  array (
    'id' => 'de81430a-2cc7-03ca-958b-67becc422dc8',
    'relationship_name' => 'securitygroups_sales_lines',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Sales_Lines',
    'rhs_table' => 'sales_lines',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Sales_Lines',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_handle_steerings' => 
  array (
    'id' => 'dead73c2-7f5a-0849-13ed-67becc634a67',
    'relationship_name' => 'securitygroups_handle_steerings',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Handle_Steerings',
    'rhs_table' => 'handle_steerings',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Handle_Steerings',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_cycle_plans' => 
  array (
    'id' => 'ded7464f-48ef-c30f-901e-67becc8f3663',
    'relationship_name' => 'securitygroups_cycle_plans',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Cycle_Plans',
    'rhs_table' => 'cycle_plans',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Cycle_Plans',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_accounts_targets' => 
  array (
    'id' => 'df0032b7-d717-1b39-58be-67becc4251f7',
    'relationship_name' => 'securitygroups_accounts_targets',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Accounts_Targets',
    'rhs_table' => 'accounts_targets',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Accounts_Targets',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_tqt_productseries' => 
  array (
    'id' => 'df2b9956-60d7-9092-660e-67becc3e35d4',
    'relationship_name' => 'securitygroups_tqt_productseries',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_ProductSeries',
    'rhs_table' => 'tqt_productseries',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'TQT_ProductSeries',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_tqt_productgroup' => 
  array (
    'id' => 'df582600-44c6-b4bd-92e4-67becc136f9e',
    'relationship_name' => 'securitygroups_tqt_productgroup',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_ProductGroup',
    'rhs_table' => 'tqt_productgroup',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'TQT_ProductGroup',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_tqt_products' => 
  array (
    'id' => 'df8460d8-abea-3014-1734-67becc55782d',
    'relationship_name' => 'securitygroups_tqt_products',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Products',
    'rhs_table' => 'tqt_products',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'TQT_Products',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_tqt_productprices' => 
  array (
    'id' => 'dfb1587e-ba7f-fa73-847a-67beccb28b79',
    'relationship_name' => 'securitygroups_tqt_productprices',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_ProductPrices',
    'rhs_table' => 'tqt_productprices',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'TQT_ProductPrices',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_product_lots' => 
  array (
    'id' => 'dfdcbf81-01c2-daff-e5cb-67becce9c899',
    'relationship_name' => 'securitygroups_product_lots',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Product_Lots',
    'rhs_table' => 'product_lots',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Product_Lots',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_tqt_announcement' => 
  array (
    'id' => 'e00717e8-ce51-3f19-1865-67becc8bbc58',
    'relationship_name' => 'securitygroups_tqt_announcement',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Announcement',
    'rhs_table' => 'tqt_announcement',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'TQT_Announcement',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_tqt_growthabilities' => 
  array (
    'id' => 'e03001bd-3e7d-c64d-2379-67becc0cee55',
    'relationship_name' => 'securitygroups_tqt_growthabilities',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_GrowthAbilities',
    'rhs_table' => 'tqt_growthabilities',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'TQT_GrowthAbilities',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_opportunities_payments' => 
  array (
    'id' => 'e05b6961-f50d-4d41-3d33-67becc2be02c',
    'relationship_name' => 'securitygroups_opportunities_payments',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities_Payments',
    'rhs_table' => 'opportunities_payments',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Opportunities_Payments',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_tqt_comments' => 
  array (
    'id' => 'e085564f-2c87-53b5-92ec-67becc0ecdab',
    'relationship_name' => 'securitygroups_tqt_comments',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Comments',
    'rhs_table' => 'tqt_comments',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'TQT_Comments',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_trackreview' => 
  array (
    'id' => 'e0b27d7d-2c7d-11b8-7ba0-67beccd8b097',
    'relationship_name' => 'securitygroups_trackreview',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'TrackReview',
    'rhs_table' => 'trackreview',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'TrackReview',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_tqt_orderdetails' => 
  array (
    'id' => 'e0dd4e80-beb3-cd4d-979d-67becc88060a',
    'relationship_name' => 'securitygroups_tqt_orderdetails',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_OrderDetails',
    'rhs_table' => 'tqt_orderdetails',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'TQT_OrderDetails',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_saleplans' => 
  array (
    'id' => 'e104c01a-b183-07f2-3d27-67becc4972e7',
    'relationship_name' => 'securitygroups_saleplans',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'SalePlans',
    'rhs_table' => 'saleplans',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'SalePlans',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_monthlytargets' => 
  array (
    'id' => 'e12cdfa5-6ccc-02fd-d9b9-67becc2df1d1',
    'relationship_name' => 'securitygroups_monthlytargets',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'MonthlyTargets',
    'rhs_table' => 'monthlytargets',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'MonthlyTargets',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_contracts' => 
  array (
    'id' => 'e1557463-7db4-b8cf-b343-67beccd5f7e4',
    'relationship_name' => 'securitygroups_contracts',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts',
    'rhs_table' => 'contracts',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Contracts',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_contracts_payments' => 
  array (
    'id' => 'e17f4546-f8f1-31a1-316b-67beccf2def5',
    'relationship_name' => 'securitygroups_contracts_payments',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_Payments',
    'rhs_table' => 'contracts_payments',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Contracts_Payments',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_contracts_locks' => 
  array (
    'id' => 'e1a82f8b-c277-6aeb-da92-67becc83541d',
    'relationship_name' => 'securitygroups_contracts_locks',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_Locks',
    'rhs_table' => 'contracts_locks',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Contracts_Locks',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_contracts_unlocks' => 
  array (
    'id' => 'e1d3dfa8-b67b-e033-6401-67becce6f8c0',
    'relationship_name' => 'securitygroups_contracts_unlocks',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_UnLocks',
    'rhs_table' => 'contracts_locks',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Contracts_UnLocks',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_contracts_invoice' => 
  array (
    'id' => 'e1fe75a8-8063-bbae-0c6c-67becc348eb5',
    'relationship_name' => 'securitygroups_contracts_invoice',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_Invoice',
    'rhs_table' => 'contracts_invoice',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Contracts_Invoice',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_contracts_collections' => 
  array (
    'id' => 'e22ae714-3571-9d74-f4f4-67becce4b756',
    'relationship_name' => 'securitygroups_contracts_collections',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_Collections',
    'rhs_table' => 'contracts_collections',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Contracts_Collections',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_contracts_revenuereduces' => 
  array (
    'id' => 'e256298d-e03a-5846-69c2-67becc96b2ef',
    'relationship_name' => 'securitygroups_contracts_revenuereduces',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_RevenueReduces',
    'rhs_table' => 'contracts_revenuereduces',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Contracts_RevenueReduces',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_contracts_approves' => 
  array (
    'id' => 'e2804564-499a-707c-e830-67becc1121ae',
    'relationship_name' => 'securitygroups_contracts_approves',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Contracts_Approves',
    'rhs_table' => 'contracts_approves',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Contracts_Approves',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_accounts' => 
  array (
    'id' => 'e2a9d803-4760-d1ef-e580-67becc14b97b',
    'relationship_name' => 'securitygroups_accounts',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Accounts',
    'rhs_table' => 'accounts',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Accounts',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_bugs' => 
  array (
    'id' => 'e2d40587-d18b-ac71-b6e4-67beccb5c157',
    'relationship_name' => 'securitygroups_bugs',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Bugs',
    'rhs_table' => 'bugs',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Bugs',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_calls' => 
  array (
    'id' => 'e2fe9903-93d3-b842-3000-67becc4819d0',
    'relationship_name' => 'securitygroups_calls',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Calls',
    'rhs_table' => 'calls',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Calls',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_campaigns' => 
  array (
    'id' => 'e329df63-45d2-ad20-6b40-67beccbd0979',
    'relationship_name' => 'securitygroups_campaigns',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Campaigns',
    'rhs_table' => 'campaigns',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Campaigns',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_cases' => 
  array (
    'id' => 'e3566007-9c69-9901-9534-67becc5ad78e',
    'relationship_name' => 'securitygroups_cases',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Cases',
    'rhs_table' => 'cases',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Cases',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_contacts' => 
  array (
    'id' => 'e38088ca-8ba3-42ff-f885-67becc710fee',
    'relationship_name' => 'securitygroups_contacts',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Contacts',
    'rhs_table' => 'contacts',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Contacts',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_documents' => 
  array (
    'id' => 'e3adb6ea-adf2-90af-75a7-67becc98cf08',
    'relationship_name' => 'securitygroups_documents',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Documents',
    'rhs_table' => 'documents',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Documents',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_emails' => 
  array (
    'id' => 'e3d988e6-420f-9314-eab8-67beccc78975',
    'relationship_name' => 'securitygroups_emails',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Emails',
    'rhs_table' => 'emails',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Emails',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_leads' => 
  array (
    'id' => 'e4060ecc-87ad-2dbf-785a-67becc57e38c',
    'relationship_name' => 'securitygroups_leads',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Leads',
    'rhs_table' => 'leads',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Leads',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_meetings' => 
  array (
    'id' => 'e4305484-e138-c31d-1bb5-67becc7cd801',
    'relationship_name' => 'securitygroups_meetings',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Meetings',
    'rhs_table' => 'meetings',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Meetings',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_notes' => 
  array (
    'id' => 'e459dce9-627c-fd4b-9370-67becc57f639',
    'relationship_name' => 'securitygroups_notes',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Notes',
    'rhs_table' => 'notes',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Notes',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_opportunities' => 
  array (
    'id' => 'e4875a90-916c-0071-bf2a-67becc12e638',
    'relationship_name' => 'securitygroups_opportunities',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities',
    'rhs_table' => 'accoopportunitiesnts',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Opportunities',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_project' => 
  array (
    'id' => 'e4b28800-fd7e-4f54-1aca-67becc658837',
    'relationship_name' => 'securitygroups_project',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Project',
    'rhs_table' => 'project',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Project',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_project_task' => 
  array (
    'id' => 'e4de1a01-6b1e-ea5c-7558-67becc2ac3f7',
    'relationship_name' => 'securitygroups_project_task',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'ProjectTask',
    'rhs_table' => 'project_task',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'ProjectTask',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_prospect_lists' => 
  array (
    'id' => 'e509dc98-902e-0d99-3673-67beccdf4212',
    'relationship_name' => 'securitygroups_prospect_lists',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'ProspectLists',
    'rhs_table' => 'prospect_lists',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'ProspectLists',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_prospects' => 
  array (
    'id' => 'e5346e1e-d370-4e17-7f69-67becceffe7d',
    'relationship_name' => 'securitygroups_prospects',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Prospects',
    'rhs_table' => 'prospects',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Prospects',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'securitygroups_tasks' => 
  array (
    'id' => 'e55ed999-eaa0-222f-dac5-67becc964303',
    'relationship_name' => 'securitygroups_tasks',
    'lhs_module' => 'SecurityGroups',
    'lhs_table' => 'securitygroups',
    'lhs_key' => 'id',
    'rhs_module' => 'Tasks',
    'rhs_table' => 'tasks',
    'rhs_key' => 'id',
    'join_table' => 'securitygroups_records',
    'join_key_lhs' => 'securitygroup_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'module',
    'relationship_role_column_value' => 'Tasks',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'product_promotions_discounts' => 
  array (
    'id' => 'e59d4c8b-a5af-cacb-f8c9-67becc84d0e0',
    'relationship_name' => 'product_promotions_discounts',
    'lhs_module' => 'Product_Promotions',
    'lhs_table' => 'product_promotions',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Products',
    'rhs_table' => 'tqt_products',
    'rhs_key' => 'id',
    'join_table' => 'product_promotions_discounts',
    'join_key_lhs' => 'product_promotion_id',
    'join_key_rhs' => 'tqt_product_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'users_branches' => 
  array (
    'id' => 'e5d6eeef-1f58-ab2e-ce8d-67becc8c8b6b',
    'relationship_name' => 'users_branches',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Branches',
    'rhs_table' => 'branches',
    'rhs_key' => 'id',
    'join_table' => 'users_branches',
    'join_key_lhs' => 'user_id',
    'join_key_rhs' => 'branch_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'users_access_other_enable' => 
  array (
    'id' => 'e606a020-590c-1991-2d96-67becc9d2c65',
    'relationship_name' => 'users_access_other_enable',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Users',
    'rhs_table' => 'users',
    'rhs_key' => 'id',
    'join_table' => 'users_access_others',
    'join_key_lhs' => 'user_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'access_type',
    'relationship_role_column_value' => '1',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'users_access_other_disable' => 
  array (
    'id' => 'e631b42f-1ce0-498e-4798-67becc70a7b8',
    'relationship_name' => 'users_access_other_disable',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Users',
    'rhs_table' => 'users',
    'rhs_key' => 'id',
    'join_table' => 'users_access_others',
    'join_key_lhs' => 'user_id',
    'join_key_rhs' => 'record_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'access_type',
    'relationship_role_column_value' => '2',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'opportunities_tqt_products' => 
  array (
    'id' => 'e664374f-99bd-21cc-54b5-67becc251c98',
    'relationship_name' => 'opportunities_tqt_products',
    'lhs_module' => 'Opportunities',
    'lhs_table' => 'opportunities',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Products',
    'rhs_table' => 'tqt_products',
    'rhs_key' => 'id',
    'join_table' => 'opportunities_tqt_products',
    'join_key_lhs' => 'opportunity_id',
    'join_key_rhs' => 'product_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tqt_warehouses_opportunities' => 
  array (
    'id' => 'e68fbd64-e96a-4dde-0763-67becc4d3c46',
    'relationship_name' => 'tqt_warehouses_opportunities',
    'lhs_module' => 'TQT_Warehouses',
    'lhs_table' => 'tqt_warehouses',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities',
    'rhs_table' => 'opportunities',
    'rhs_key' => 'id',
    'join_table' => 'opportunities_tqt_products',
    'join_key_lhs' => 'warehouse_id',
    'join_key_rhs' => 'opportunity_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'users_teams' => 
  array (
    'id' => 'e6c53d26-0b0d-8cc9-02f1-67becc9f4f34',
    'relationship_name' => 'users_teams',
    'lhs_module' => 'Users',
    'lhs_table' => 'users',
    'lhs_key' => 'id',
    'rhs_module' => 'Team_Groups',
    'rhs_table' => 'team_groups',
    'rhs_key' => 'id',
    'join_table' => 'users_teams',
    'join_key_lhs' => 'user_id',
    'join_key_rhs' => 'team_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tasks_notifications' => 
  array (
    'id' => 'e704cb2b-bfde-400d-d46e-67becc9002e0',
    'relationship_name' => 'tasks_notifications',
    'lhs_module' => 'Tasks',
    'lhs_table' => 'tasks',
    'lhs_key' => 'id',
    'rhs_module' => 'Users',
    'rhs_table' => 'users',
    'rhs_key' => 'id',
    'join_table' => 'notifications',
    'join_key_lhs' => 'parent_id',
    'join_key_rhs' => 'assigned_user_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => 'parent_type',
    'relationship_role_column_value' => 'Tasks',
    'reverse' => '0',
    'deleted' => '0',
  ),
  'member_tqt_products' => 
  array (
    'id' => 'e735447e-a68e-867d-d22e-67becc60ecfa',
    'relationship_name' => 'member_tqt_products',
    'lhs_module' => 'TQT_Products',
    'lhs_table' => 'tqt_products',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Products',
    'rhs_table' => 'tqt_products',
    'rhs_key' => 'id',
    'join_table' => 'member_tqt_products',
    'join_key_lhs' => 'product_id',
    'join_key_rhs' => 'member_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'vat_tqt_products' => 
  array (
    'id' => 'e763ee9e-6bb9-19e5-7eea-67becc773439',
    'relationship_name' => 'vat_tqt_products',
    'lhs_module' => 'TQT_Products',
    'lhs_table' => 'tqt_products',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Products',
    'rhs_table' => 'tqt_products',
    'rhs_key' => 'id',
    'join_table' => 'vat_tqt_products',
    'join_key_lhs' => 'product_id',
    'join_key_rhs' => 'prodvat_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'tasks_group_users' => 
  array (
    'id' => 'e792eff6-d113-a8d0-0b92-67becc856142',
    'relationship_name' => 'tasks_group_users',
    'lhs_module' => 'Tasks',
    'lhs_table' => 'tasks',
    'lhs_key' => 'id',
    'rhs_module' => 'Users',
    'rhs_table' => 'users',
    'rhs_key' => 'id',
    'join_table' => 'tasks_group_users',
    'join_key_lhs' => 'task_id',
    'join_key_rhs' => 'user_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'invoices_opportunities' => 
  array (
    'id' => 'e7c34f4f-ddf6-099c-4503-67beccad955d',
    'relationship_name' => 'invoices_opportunities',
    'lhs_module' => 'Invoices',
    'lhs_table' => 'invoices',
    'lhs_key' => 'id',
    'rhs_module' => 'Opportunities',
    'rhs_table' => 'opportunities',
    'rhs_key' => 'id',
    'join_table' => 'invoices_opportunities',
    'join_key_lhs' => 'invoice_id',
    'join_key_rhs' => 'opportunity_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'cashes_list_expenses' => 
  array (
    'id' => 'e7f91fe5-2ac2-d762-67a5-67becc3c1283',
    'relationship_name' => 'cashes_list_expenses',
    'lhs_module' => 'Cashes',
    'lhs_table' => 'cashes',
    'lhs_key' => 'id',
    'rhs_module' => 'List_Expenses',
    'rhs_table' => 'list_expenses',
    'rhs_key' => 'id',
    'join_table' => 'cashes_list_expenses',
    'join_key_lhs' => 'cash_id',
    'join_key_rhs' => 'list_expense_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
  'product_gift_approved' => 
  array (
    'id' => 'e83a28ee-df93-0ff3-4fe3-67beccfde2c0',
    'relationship_name' => 'product_gift_approved',
    'lhs_module' => 'Product_Gifts',
    'lhs_table' => 'product_gifts',
    'lhs_key' => 'id',
    'rhs_module' => 'TQT_Products',
    'rhs_table' => 'tqt_products',
    'rhs_key' => 'id',
    'join_table' => 'product_gift_approved',
    'join_key_lhs' => 'product_gift_id',
    'join_key_rhs' => 'product_id',
    'relationship_type' => 'many-to-many',
    'relationship_role_column' => NULL,
    'relationship_role_column_value' => NULL,
    'reverse' => '0',
    'deleted' => '0',
  ),
) ?>