
<section id="EditViewViewBody" class="view-module-{$module|strtolower}">

<form action="index.php" method="POST" name="{$form_name}" id="{$form_id}" class="_edit-{$instanceName}" {$enctype}>
<input type="hidden" name="module" value="{$module}" />
<input type="hidden" name="action" id="action" />
{if isset($smarty.request.isDuplicate) && $smarty.request.isDuplicate eq "true"}
<input type="hidden" name="record" id="record" value="" />
<input type="hidden" name="duplicateSave" value="true" />
{else}
<input type="hidden" name="record" id="record" value="{$fields.id.value}" />
{/if}
<input type="hidden" name="return_module" value="{$smarty.request.return_module}" />
<input type="hidden" name="return_action" value="{if $smarty.request.return_action eq 'LoadTabSubpanels'}DetailView{else}{$smarty.request.return_action}{/if}" />
<input type="hidden" name="return_id" value="{$smarty.request.return_id}" />
<input type="hidden" name="isDuplicate" value="false" />
<input type="hidden" name="module_tab" /> 
<input type="hidden" name="contact_role" />
{if !empty($smarty.request.return_module)}
<input type="hidden" name="relate_to" value="{if $smarty.request.return_relationship}{$smarty.request.return_relationship}{else}{$smarty.request.return_module}{/if}" />
<input type="hidden" name="relate_id" value="{$smarty.request.return_id}" />
{/if}
<input type="hidden" name="offset" value="{$offset}" />
{if empty($fields.id.value)}
<input type="hidden" name="_notify_random" value="{php}echo create_guid();{/php}" />
{/if}
<input type="hidden" name="isFromEditView" value="{$module}" />
<input type="hidden" name="object" value="{$fields.object.value|default:'Proposal'}" />
<input type="hidden" name="opportunity_type" id="opportunity_type" value="{$fields.opportunity_type.value}" />
<input type="hidden" name="order_discount" id="order_discount" value="{$fields.order_discount.value}" />
<input type="hidden" name="sector_vertical" id="sector_vertical" value="{$fields.sector_vertical.value}" />
<input type="hidden" name="order_number" id="order_number" value="{$fields.order_number.value}" />
<input type="hidden" name="no_currency" id="no_currency" value="0" />
<input type="hidden" name="parent_id" id="parent_id" value="{$fields.parent_id.value}" />
<input type="hidden" name="parent_name" id="parent_name" value="{$fields.parent_name.value}" />
<input type="hidden" name="estimated_revenue" id="estimated_revenue" value="{$fields.estimated_revenue.value}" />
<input type="hidden" name="incurred_amount" id="incurred_amount" value="{$fields.incurred_amount.value}" />
<input type="hidden" name="commission_amount" id="commission_amount" value="{$fields.commission_amount.value}" />
<input type="hidden" name="vat_more_amt" id="vat_more_amt" value="0" />
<input type="hidden" name="vat_other_amt" id="vat_other_amt" value="{$fields.vat_other_amt.value}" />
<input type="hidden" name="incurred_domestic" id="incurred_domestic" value="{$fields.incurred_domestic.value}" />
<input type="hidden" name="incurred_foreign" id="incurred_foreign" value="{$fields.incurred_foreign.value}" />
<input type="hidden" name="incurred_other" id="incurred_other" value="{$fields.incurred_other.value}" />
<input type="hidden" name="failure_cause" id="failure_cause" value="{$fields.failure_cause.value}" />
{incomCRM_include include=$includes}
<div id="LBL_OPPORTUNITIES_TQT_PRODUCTS" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
{counter name="panelFieldCount"}
{incomCRM_include type='php' file='custom/modules/Opportunities/EditProducts.php'}
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("LBL_OPPORTUNITIES_TQT_PRODUCTS").style.display='none';</script>
{/if}
<div id="DEFAULT" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="DEFAULT_GROUP" style="display:block">
<table width="100%" border="0" cellspacing="1" cellpadding="0">
<tr>
<td valign="top" id='opportunity_type_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_TYPE' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_opportunity_type_field' >
{counter name="panelFieldCount"}
<span style="color:red">{$fields.opportunity_type.options[$fields.opportunity_type.value]}</span>
</td>
<td valign="top" id='actual_output_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACTUAL_OUTPUT' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_actual_output_field' >
{counter name="panelFieldCount"}

{if strlen($fields.actual_output.value) <= 0}
{assign var="value" value=$fields.actual_output.default_value }
{else}
{assign var="value" value=$fields.actual_output.value }
{/if}
{if isTypeNumber($fields.actual_output.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.actual_output.name}' id='{$fields.actual_output.name}' size='30' maxlength='10' value='{$value}' title='' tabindex='101'  readonly="readonly" /> 

</td>
</tr>
<tr>
<td valign="top" id='name_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_NAME' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_name_field' >
{counter name="panelFieldCount"}

{if strlen($fields.name.value) <= 0}
{assign var="value" value=$fields.name.default_value }
{else}
{assign var="value" value=$fields.name.value }
{/if}
{if isTypeNumber($fields.name.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.name.name}' id='{$fields.name.name}' size='30' maxlength='30' value='{$value}' title='' tabindex='102'  readonly="readonly" /> 

</td>
<td valign="top" id='shipping_fee_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_SHIPPING_FEE' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_shipping_fee_field' >
{counter name="panelFieldCount"}

{if strlen($fields.shipping_fee.value) <= 0}
{assign var="value" value=$fields.shipping_fee.default_value }
{else}
{assign var="value" value=$fields.shipping_fee.value }
{/if}
{if isTypeNumber($fields.shipping_fee.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.shipping_fee.name}' id='{$fields.shipping_fee.name}' size='30'  value='{$value}' title='' tabindex='103'  onblur="OpportunityViewer.getInstance('{$instanceName}').calcTotal()" /> 

</td>
</tr>
<tr>
<td valign="top" id='contract_number_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTRACT_NUMBER' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_contract_number_field' >
{counter name="panelFieldCount"}

{if strlen($fields.contract_number.value) <= 0}
{assign var="value" value=$fields.contract_number.default_value }
{else}
{assign var="value" value=$fields.contract_number.value }
{/if}
{if isTypeNumber($fields.contract_number.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.contract_number.name}' id='{$fields.contract_number.name}' size='30' maxlength='50' value='{$value}' title='' tabindex='104'  /> 

</td>
<td valign="top" id='discount_amount_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_LIST_DISCOUNT_AMOUNT' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_discount_amount_field' >
{counter name="panelFieldCount"}

{if strlen($fields.discount_amount.value) <= 0}
{assign var="value" value=$fields.discount_amount.default_value }
{else}
{assign var="value" value=$fields.discount_amount.value }
{/if}
{if isTypeNumber($fields.discount_amount.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.discount_amount.name}' id='{$fields.discount_amount.name}' size='30'  value='{$value}' title='' tabindex='105'  readonly="readonly" /> 

</td>
</tr>
<tr>
<td valign="top" id='contract_date_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTRACT_DATE' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_contract_date_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.contract_date.value }
<input autocomplete="off" type="text" name="{$fields.contract_date.name}" id="{$fields.contract_date.name}" value="{$date_value}" title=''  tabindex='106' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.contract_date.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.contract_date.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.contract_date.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
<td valign="top" id='amount_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_AMOUNT' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_amount_field' >
{counter name="panelFieldCount"}

{if strlen($fields.amount.value) <= 0}
{assign var="value" value=$fields.amount.default_value }
{else}
{assign var="value" value=$fields.amount.value }
{/if}
{if isTypeNumber($fields.amount.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.amount.name}' id='{$fields.amount.name}' size='30'  value='{$value}' title='' tabindex='107'  readonly="readonly" /> 

</td>
</tr>
<tr>
<td valign="top" id='date_closed_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DATE_CLOSED' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='30%' id='_date_closed_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.date_closed.value }
<input autocomplete="off" type="text" name="{$fields.date_closed.name}" id="{$fields.date_closed.name}" value="{$date_value}" title=''  tabindex='108' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.date_closed.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.date_closed.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.date_closed.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
<td valign="top" id='vat_tax_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_VAT_TAX' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_vat_tax_field' >
{counter name="panelFieldCount"}

<select name="{$fields.vat_tax.name}" id="{$fields.vat_tax.name}" title='' tabindex="109"  onchange="OpportunityViewer.getInstance('{$instanceName}').calcTotal()" >
{if isset($fields.vat_tax.value) && $fields.vat_tax.value != ''}
{html_options options=$fields.vat_tax.options selected=$fields.vat_tax.value}
{else}
{html_options options=$fields.vat_tax.options selected=$fields.vat_tax.default}
{/if}
</select>
</td>
</tr>
<tr>
<td valign="top" id='date_start_process_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DATE_START_PROCESS' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_date_start_process_field' >
{counter name="panelFieldCount"}

{assign var=date_value value=$fields.date_start_process.value }
<input autocomplete="off" type="text" name="{$fields.date_start_process.name}" id="{$fields.date_start_process.name}" value="{$date_value}" title=''  tabindex='110' size="11" maxlength="10" class="input-cal" />
<img border="0" src="{incomCRM_getimagepath file='jscalendar.gif'}" alt="{$APP.LBL_ENTER_DATE}" id="{$fields.date_start_process.name}_trigger" align="absmiddle" />
<script type="text/javascript">
	Calendar.setup ({ldelim}
	inputField : "{$fields.date_start_process.name}",
	daFormat : "{$CALENDAR_FORMAT}",
	button : "{$fields.date_start_process.name}_trigger",
	singleClick : true,
	dateStr : "{$date_value}",
	step : 1,
	weekNumbers:false
{rdelim});
</script>
</td>
<td valign="top" id='vat_amount_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_VAT_AMOUNT' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_vat_amount_field' >
{counter name="panelFieldCount"}

{if strlen($fields.vat_amount.value) <= 0}
{assign var="value" value=$fields.vat_amount.default_value }
{else}
{assign var="value" value=$fields.vat_amount.value }
{/if}
{if isTypeNumber($fields.vat_amount.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.vat_amount.name}' id='{$fields.vat_amount.name}' size='30'  value='{$value}' title='' tabindex='111'  readonly="readonly" /> 

</td>
</tr>
<tr>
<td valign="top" id='sales_stage_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_SALES_STAGE' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='30%' id='_sales_stage_field' >
{counter name="panelFieldCount"}

<select name="{$fields.sales_stage.name}" id="{$fields.sales_stage.name}" title='' tabindex="112"  onchange="changeSalesStage(this, '{$instanceName}')" >
{if isset($fields.sales_stage.value) && $fields.sales_stage.value != ''}
{html_options options=$fields.sales_stage.options selected=$fields.sales_stage.value}
{else}
{html_options options=$fields.sales_stage.options selected=$fields.sales_stage.default}
{/if}
</select>
</td>
<td valign="top" id='contract_revenue_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTRACT_REVENUE' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_contract_revenue_field' >
{counter name="panelFieldCount"}

{if strlen($fields.contract_revenue.value) <= 0}
{assign var="value" value=$fields.contract_revenue.default_value }
{else}
{assign var="value" value=$fields.contract_revenue.value }
{/if}
{if isTypeNumber($fields.contract_revenue.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.contract_revenue.name}' id='{$fields.contract_revenue.name}' size='30'  value='{$value}' title='' tabindex='113'  readonly="readonly" /> 

</td>
</tr>
<tr>
<td valign="top" id='causes_failure_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CAUSES_FAILURE' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_causes_failure_field' >
{counter name="panelFieldCount"}

<select name="{$fields.causes_failure.name}" id="{$fields.causes_failure.name}" title='' tabindex="114"  >
{if isset($fields.causes_failure.value) && $fields.causes_failure.value != ''}
{html_options options=$fields.causes_failure.options selected=$fields.causes_failure.value}
{else}
{html_options options=$fields.causes_failure.options selected=$fields.causes_failure.default}
{/if}
</select>
</td>
<td valign="top" id='_label' width='20%' scope="row">
</td>
<td valign="top" width='30%' id='__field' >
</td>
</tr>
<tr>
<td valign="top" id='rate_id_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_RATE_ID' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_rate_id_field' >
{counter name="panelFieldCount"}

{$fields.rate_id.value}
</td>
<td valign="top" id='money_deposit_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_MONEY_DEPOSIT' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_money_deposit_field' >
{counter name="panelFieldCount"}

{if strlen($fields.money_deposit.value) <= 0}
{assign var="value" value=$fields.money_deposit.default_value }
{else}
{assign var="value" value=$fields.money_deposit.value }
{/if}
{if isTypeNumber($fields.money_deposit.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.money_deposit.name}' id='{$fields.money_deposit.name}' size='30'  value='{$value}' title='' tabindex='117'  onblur="OpportunityViewer.getInstance('{$instanceName}').calcMoney()" /> 

</td>
</tr>
<tr>
<td valign="top" id='rate_foreign_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_RATE_FOREIGN' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_rate_foreign_field' >
{counter name="panelFieldCount"}

{if strlen($fields.rate_foreign.value) <= 0}
{assign var="value" value=$fields.rate_foreign.default_value }
{else}
{assign var="value" value=$fields.rate_foreign.value }
{/if}
{if isTypeNumber($fields.rate_foreign.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.rate_foreign.name}' id='{$fields.rate_foreign.name}' size='30'  value='{$value}' title='' tabindex='118'  class="right" onblur="callbackUpdateCurrencyForeignRate(this.form, this.form.rate_id.value);" /> 

</td>
<td valign="top" id='is_target_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_IS_TARGET' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_is_target_field' >
{counter name="panelFieldCount"}

{if strval($fields.is_target.value) == "1" || strval($fields.is_target.value) == "yes" || strval($fields.is_target.value) == "on"} 
{assign var="checked" value="CHECKED"}
{else}
{assign var="checked" value=""}
{/if}
<input type="hidden" name="{$fields.is_target.name}" value="0" /> 
<input type="checkbox" id="{$fields.is_target.name}" name="{$fields.is_target.name}" value="1" title='' tabindex="119" {$checked}  />

</td>
</tr>
<tr>
<td valign="top" id='account_name_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACCOUNT_NAME' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" colspan='3' id='_account_name_field' class="yui-ac-format">
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{else}{/if}<input type="hidden" name="{$fields.account_name.id_name}" id="{$fields.account_name.id_name}" value="{$fields.account_id.value}" />
<input type="text" name="{$fields.account_name.name}" class="sqsEnabled" tabindex="120" id="{$fields.account_name.name}" size="50" value="{$fields.account_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{else}{/if}
<input type="button" name="btn_{$fields.account_name.name}" id="btn_{$fields.account_name.name}" tabindex="120" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.account_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return_focus","form_name":"EditView","field_to_name_array":{"id":"account_id","name":"account_name","order_discount":"order_discount","receivable_debts":"receivable_debts"},"passthru_data":{"callback":"OpportunityViewer.selectedAccount"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.account_name.name}" id="btn_clr_{$fields.account_name.name}" tabindex="120" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.account_name.name}.value=""; this.form.{$fields.account_name.id_name}.value=""; this.form.{$fields.account_name.name}.focus();OpportunityViewer.getInstance("{$instanceName}").clearAccountInfo();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
{if !empty($isSmartView)}</div>{else}{/if}<script type="text/javascript">enableQS(false);</script>
&nbsp; <a href="#" onclick="{literal}var aid = $('input#account_id').val(); if(aid){ this.href='index.php?module=Accounts&action=DetailView&record='+ aid; return true; } else{ ajaxStatus.flashStatus('Vui lòng chọn một Khách hàng!'); this.href='#'; return false;}{/literal}" target="_blank">[ Thông tin chi tiết ]</a>
</td>
</tr>
<tr>
<td valign="top" id='contact_name_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CONTACT_NAME' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" colspan='3' id='_contact_name_field' >
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{else}{/if}<input type="hidden" name="{$fields.contact_name.id_name}" id="{$fields.contact_name.id_name}" value="{$fields.contact_id.value}" />
<input type="text" name="{$fields.contact_name.name}" class="sqsEnabled" tabindex="121" id="{$fields.contact_name.name}" size="50" value="{$fields.contact_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{else}{/if}
<input type="button" name="btn_{$fields.contact_name.name}" id="btn_{$fields.contact_name.name}" tabindex="121" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.contact_name.module}", 1000, 600, "&account_id=\""+ encodeURIComponent(this.form.account_id.value) +"\"", true, false, {literal}{"call_back_function":"set_return","form_name":"EditView","field_to_name_array":{"id":"contact_id","last_name":"contact_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.contact_name.name}" id="btn_clr_{$fields.contact_name.name}" tabindex="121" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.contact_name.name}.value=""; this.form.{$fields.contact_name.id_name}.value=""; this.form.{$fields.contact_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
{if !empty($isSmartView)}</div>{else}{/if}<script type="text/javascript">enableQS(false);</script>
</td>
</tr>
<tr>
<td valign="top" id='receivable_debts_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_RECEIVABLE_DEBTS' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_receivable_debts_field' >
{counter name="panelFieldCount"}

{if strlen($fields.receivable_debts.value) <= 0}
{assign var="value" value=$fields.receivable_debts.default_value }
{else}
{assign var="value" value=$fields.receivable_debts.value }
{/if}
{if isTypeNumber($fields.receivable_debts.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.receivable_debts.name}' id='{$fields.receivable_debts.name}' size='30'  value='{$value}' title='' tabindex='122'  readonly="readonly" /> 

</td>
<td valign="top" id='payment_type_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_PAYMENT_TYPE' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_payment_type_field' >
{counter name="panelFieldCount"}

<select name="{$fields.payment_type.name}" id="{$fields.payment_type.name}" title='' tabindex="123"  >
{if isset($fields.payment_type.value) && $fields.payment_type.value != ''}
{html_options options=$fields.payment_type.options selected=$fields.payment_type.value}
{else}
{html_options options=$fields.payment_type.options selected=$fields.payment_type.default}
{/if}
</select>
</td>
</tr>
<tr>
<td valign="top" id='overdue_debts_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_OVERDUE_DEBTS' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_overdue_debts_field' >
{counter name="panelFieldCount"}

{if strlen($fields.overdue_debts.value) <= 0}
{assign var="value" value=$fields.overdue_debts.default_value }
{else}
{assign var="value" value=$fields.overdue_debts.value }
{/if}
{if isTypeNumber($fields.overdue_debts.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.overdue_debts.name}' id='{$fields.overdue_debts.name}' size='30'  value='{$value}' title='' tabindex='124'  readonly="readonly" /> 

</td>
<td valign="top" id='bank_account_id_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_BANK_ACCOUNT_NAME' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_bank_account_id_field' >
{counter name="panelFieldCount"}

{$fields.bank_account_id.value}
</td>
</tr>
<tr>
<td valign="top" id='assigned_user_name_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='30%' id='_assigned_user_name_field' >
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{else}{/if}<input type="hidden" name="{$fields.assigned_user_name.id_name}" id="{$fields.assigned_user_name.id_name}" value="{$fields.assigned_user_id.value}" />
<input type="text" name="{$fields.assigned_user_name.name}" class="sqsEnabled" tabindex="126" id="{$fields.assigned_user_name.name}" size="16" value="{$fields.assigned_user_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{else}{/if}
<input type="button" name="btn_{$fields.assigned_user_name.name}" id="btn_{$fields.assigned_user_name.name}" tabindex="126" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.assigned_user_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"EditView","field_to_name_array":{"id":"assigned_user_id","user_name":"assigned_user_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.assigned_user_name.name}" id="btn_clr_{$fields.assigned_user_name.name}" tabindex="126" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.assigned_user_name.name}.value=""; this.form.{$fields.assigned_user_name.id_name}.value=""; this.form.{$fields.assigned_user_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
{if !empty($isSmartView)}</div>{else}{/if}<script type="text/javascript">enableQS(false);</script>
</td>
<td valign="top" id='payment_period_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_PAYMENT_PERIOD' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_payment_period_field' >
{counter name="panelFieldCount"}

<select name="{$fields.payment_period.name}" id="{$fields.payment_period.name}" title='' tabindex="127"  >
{if isset($fields.payment_period.value) && $fields.payment_period.value != ''}
{html_options options=$fields.payment_period.options selected=$fields.payment_period.value}
{else}
{html_options options=$fields.payment_period.options selected=$fields.payment_period.default}
{/if}
</select>
</td>
</tr>
<tr>
<td valign="top" id='description_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DESCRIPTION' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_description_field' >
{counter name="panelFieldCount"}

{if empty($fields.description.value)}
{assign var="value" value=$fields.description.default_value }
{else}
{assign var="value" value=$fields.description.value }
{/if}
<textarea id="{$fields.description.name}" name="{$fields.description.name}" rows="2" cols="45" title='' tabindex="128"   style="width:90%">{$value}</textarea>
</td>
<td valign="top" id='order_type_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ORDER_TYPE' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_order_type_field' >
{counter name="panelFieldCount"}

<select name="{$fields.order_type.name}" id="{$fields.order_type.name}" title='' tabindex="129"  onchange="OpportunityViewer.getInstance('{$instanceName}').calcTotal()" >
{if isset($fields.order_type.value) && $fields.order_type.value != ''}
{html_options options=$fields.order_type.options selected=$fields.order_type.value}
{else}
{html_options options=$fields.order_type.options selected=$fields.order_type.default}
{/if}
</select>
</td>
</tr>
<tr>
<td valign="top" id='project_name_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_PROJECT_NAME' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_project_name_field' >
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{else}{/if}<input type="hidden" name="{$fields.project_name.id_name}" id="{$fields.project_name.id_name}" value="{$fields.project_id.value}" />
<input type="text" name="{$fields.project_name.name}" class="sqsEnabled" tabindex="130" id="{$fields.project_name.name}" size="16" value="{$fields.project_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{else}{/if}
<input type="button" name="btn_{$fields.project_name.name}" id="btn_{$fields.project_name.name}" tabindex="130" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.project_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"EditView","field_to_name_array":{"id":"project_id","name":"project_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.project_name.name}" id="btn_clr_{$fields.project_name.name}" tabindex="130" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.project_name.name}.value=""; this.form.{$fields.project_name.id_name}.value=""; this.form.{$fields.project_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
{if !empty($isSmartView)}</div>{else}{/if}<script type="text/javascript">enableQS(false);</script>
</td>
<td valign="top" id='warranty_period_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_WARRANTY_PERIOD' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='30%' id='_warranty_period_field' >
{counter name="panelFieldCount"}

<select name="{$fields.warranty_period.name}" id="{$fields.warranty_period.name}" title='' tabindex="131"  >
{if isset($fields.warranty_period.value) && $fields.warranty_period.value != ''}
{html_options options=$fields.warranty_period.options selected=$fields.warranty_period.value}
{else}
{html_options options=$fields.warranty_period.options selected=$fields.warranty_period.default}
{/if}
</select>
</td>
</tr>
<tr>
<td valign="top" id='point_current_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_POINT_CURRENT' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" colspan='3' id='_point_current_field' >
{counter name="panelFieldCount"}

{if strlen($fields.point_current.value) <= 0}
{assign var="value" value=$fields.point_current.default_value }
{else}
{assign var="value" value=$fields.point_current.value }
{/if}
{if isTypeNumber($fields.point_current.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.point_current.name}' id='{$fields.point_current.name}' size='30'  value='{$value}' title='' tabindex='132'  /> 

</td>
</tr>
<tr>
<td valign="top" id='share_users_label' width='20%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_SHARING_USERS' module='Opportunities'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" colspan='3' id='_share_users_field' >
{counter name="panelFieldCount"}
<input type="hidden" id="{$fields.share_users.name}_multiselect" name="{$fields.share_users.name}_multiselect" value="true" />
{multienum_to_array string=$fields.share_users.value default=$fields.share_users.default assign="values"}
<div style="" id="{$fields.share_users.name}_multi_grp" class="multi-enum-grp multi-enum-grp-default">
{if isset($fields.share_users.groupBy) and isset($fields.share_users.groupDom)}
<label id="_grp_choose">
<select name="_{$fields.share_users.name}_grp" size="1" onchange="changeFilterSelectedGroup(this.value, '{$fields.share_users.name}_multi_grp' )">
{html_options options=$fields.share_users.groupDom}
</select>
</label>
{/if}
<label id="_grp_all_" class="_grp_all_">
<input type="checkbox" value="__all__" onclick="checkedChangeSel(this, '{$fields.share_users.name}', true)" />
-- {$APP.LBL_LINK_ALL} --
</label>
{counter name="rowCount" start=0 print=false assign="rowCount"}
{foreach from=$fields.share_users.options item=option key=value}
{if $value}
<label id="_grp_{if isset($fields.share_users.groupBy) and isset($fields.share_users.groupBy[$value])}{ $fields.share_users.groupBy[$value]}{/if}" style="white-space: nowrap">
<input type="checkbox" id="{$fields.share_users.name}_checkbox{$rowCount}" name="{$fields.share_users.name}[]" value="{$value}" {if in_array(strval($value), $values)}checked="checked"{/if}  /> {$option}
</label>
{counter name="rowCount"}
{/if}
{/foreach}
</div>
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("DEFAULT").style.display='none';</script>
{/if}

</form>
{$set_focus_block}
</section>{literal}
<script type="text/javascript">
addToValidate('EditView', 'id', 'id', false, '{/literal}{incomCRM_translate label='LBL_ID' module='Opportunities'}{literal}' );
addToValidate('EditView', 'date_entered_date', 'date', false, 'Ngày tạo' );
addToValidate('EditView', 'date_modified_date', 'date', false, 'Ngày cập nhật' );
addToValidate('EditView', 'modified_user_id', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_ID' module='Opportunities'}{literal}' );
addToValidate('EditView', 'modified_by_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_NAME' module='Opportunities'}{literal}' );
addToValidate('EditView', 'created_by', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_CREATED_ID' module='Opportunities'}{literal}' );
addToValidate('EditView', 'created_by_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_CREATED' module='Opportunities'}{literal}' );
addToValidate('EditView', 'deleted', 'bool', false, '{/literal}{incomCRM_translate label='LBL_DELETED' module='Opportunities'}{literal}' );
addToValidate('EditView', 'name', 'name', false, '{/literal}{incomCRM_translate label='LBL_NAME' module='Opportunities'}{literal}' );
addToValidate('EditView', 'description', 'text', false, '{/literal}{incomCRM_translate label='LBL_DESCRIPTION' module='Opportunities'}{literal}' );
addToValidate('EditView', 'branch_id', 'enum', false, '{/literal}{incomCRM_translate label='LBL_BRANCH_ID' module='Opportunities'}{literal}' );
addToValidate('EditView', 'department_id', 'enum', false, '{/literal}{incomCRM_translate label='LBL_DEPARTMENT_ID' module='Opportunities'}{literal}' );
addToValidate('EditView', 'assigned_user_id', 'relate', false, '{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO_ID' module='Opportunities'}{literal}' );
addToValidate('EditView', 'assigned_user_name', 'relate', true, '{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO_NAME' module='Opportunities'}{literal}' );
addToValidate('EditView', 'overdue_debts', 'float', false, '{/literal}{incomCRM_translate label='LBL_OVERDUE_DEBTS' module='Opportunities'}{literal}' );
addToValidate('EditView', 'order_purchase', 'enum', false, '{/literal}{incomCRM_translate label='LBL_ORDER_PURCHASE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'opportunity_type', 'enum', false, '{/literal}{incomCRM_translate label='LBL_TYPE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'account_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_ID' module='Opportunities'}{literal}' );
addToValidate('EditView', 'account_name', 'relate', true, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_NAME' module='Opportunities'}{literal}' );
addToValidate('EditView', 'campaign_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_CAMPAIGN_ID' module='Opportunities'}{literal}' );
addToValidate('EditView', 'campaign_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_CAMPAIGN' module='Opportunities'}{literal}' );
addToValidate('EditView', 'lead_source', 'enum', false, '{/literal}{incomCRM_translate label='LBL_LEAD_SOURCE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'amount_usdollar', 'currency', false, '{/literal}{incomCRM_translate label='LBL_AMOUNT_USDOLLAR' module='Opportunities'}{literal}' );
addToValidate('EditView', 'currency_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_CURRENCY_ID' module='Opportunities'}{literal}' );
addToValidate('EditView', 'currency_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_CURRENCY_NAME' module='Opportunities'}{literal}' );
addToValidate('EditView', 'currency_symbol', 'relate', false, '{/literal}{incomCRM_translate label='LBL_CURRENCY_SYMBOL' module='Opportunities'}{literal}' );
addToValidate('EditView', 'date_closed', 'date', true, '{/literal}{incomCRM_translate label='LBL_DATE_CLOSED' module='Opportunities'}{literal}' );
addToValidate('EditView', 'sales_stage', 'enum', true, '{/literal}{incomCRM_translate label='LBL_SALES_STAGE' module='Opportunities'}{literal}' );
addToValidateRange('EditView', 'probability', 'int', false, '{/literal}{incomCRM_translate label='LBL_PROBABILITY' module='Opportunities'}{literal}', 0, 100 );
addToValidate('EditView', 'rate_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_RATE_ID' module='Opportunities'}{literal}' );
addToValidate('EditView', 'rate_foreign', 'double', false, '{/literal}{incomCRM_translate label='LBL_RATE_FOREIGN' module='Opportunities'}{literal}' );
addToValidate('EditView', 'product_name', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TQT_PRODUCT_FULL_NAME' module='Opportunities'}{literal}' );
addToValidate('EditView', 'is_auto_cpps', 'bool', false, '{/literal}{incomCRM_translate label='LBL_IS_AUTO_CPPS' module='Opportunities'}{literal}' );
addToValidate('EditView', 'quotes_note', 'text', false, '{/literal}{incomCRM_translate label='LBL_QUOTES_NOTE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'shipping_time', 'text', false, '{/literal}{incomCRM_translate label='LBL_SHIPPING_TIME' module='Opportunities'}{literal}' );
addToValidate('EditView', 'payment_method', 'text', false, '{/literal}{incomCRM_translate label='LBL_PAYMENT_METHOD' module='Opportunities'}{literal}' );
addToValidate('EditView', 'shipping_note', 'text', false, '{/literal}{incomCRM_translate label='LBL_SHIPPING_NOTE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'failure_cause', 'text', false, '{/literal}{incomCRM_translate label='LBL_FAILURE_CAUSE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'accounting_category_type', 'bool', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNTING_CATEGORY_TYPE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'incurred_other', 'currency', false, '{/literal}{incomCRM_translate label='LBL_INCURRED_OTHER' module='Opportunities'}{literal}' );
addToValidate('EditView', 'incurred_domestic', 'currency', false, '{/literal}{incomCRM_translate label='LBL_INCURRED_DOMESTIC' module='Opportunities'}{literal}' );
addToValidate('EditView', 'incurred_foreign', 'currency', false, '{/literal}{incomCRM_translate label='LBL_INCURRED_FOREIGN' module='Opportunities'}{literal}' );
addToValidate('EditView', 'processing_stage', 'enum', false, '{/literal}{incomCRM_translate label='LBL_PROCESSING_STAGE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'processing_content', 'text', false, '{/literal}{incomCRM_translate label='LBL_PROCESSING_CONTENT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'amount_collected', 'currency', false, '{/literal}{incomCRM_translate label='LBL_AMOUNT_COLLECTED' module='Opportunities'}{literal}' );
addToValidate('EditView', 'amount_owed', 'currency', false, '{/literal}{incomCRM_translate label='LBL_AMOUNT_OWED' module='Opportunities'}{literal}' );
addToValidate('EditView', 'receivable_debts', 'float', false, '{/literal}{incomCRM_translate label='LBL_RECEIVABLE_DEBTS' module='Opportunities'}{literal}' );
addToValidate('EditView', 'debt_norm_min', 'double', false, '{/literal}{incomCRM_translate label='LBL_DEBT_NORM_MIN' module='Opportunities'}{literal}' );
addToValidate('EditView', 'debt_norm_max', 'double', false, '{/literal}{incomCRM_translate label='LBL_DEBT_NORM_MAX' module='Opportunities'}{literal}' );
addToValidate('EditView', 'debt_delay_day', 'int', false, '{/literal}{incomCRM_translate label='LBL_DEBT_DELAY_DAY' module='Opportunities'}{literal}' );
addToValidate('EditView', 'handle_user_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_HANDLE_USER_ID' module='Opportunities'}{literal}' );
addToValidate('EditView', 'handle_user_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_HANDLE_USER_NAME' module='Opportunities'}{literal}' );
addToValidate('EditView', 'parent_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_PARENT_ID' module='Opportunities'}{literal}' );
addToValidate('EditView', 'parent_code', 'relate', false, '{/literal}{incomCRM_translate label='LBL_PARENT_CODE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'parent_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_PARENT_NAME' module='Opportunities'}{literal}' );
addToValidate('EditView', 'parent_project_name', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_PROJECT_NAME' module='Opportunities'}{literal}' );
addToValidate('EditView', 'warranty_period', 'enum', false, '{/literal}{incomCRM_translate label='LBL_WARRANTY_PERIOD' module='Opportunities'}{literal}' );
addToValidate('EditView', 'bank_account_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_BANK_ACCOUNT_NAME' module='Opportunities'}{literal}' );
addToValidate('EditView', 'bank_account_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_BANK_ACCOUNT_NAME' module='Opportunities'}{literal}' );
addToValidate('EditView', 'contract_number', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_CONTRACT_NUMBER' module='Opportunities'}{literal}' );
addToValidate('EditView', 'contract_date', 'date', false, '{/literal}{incomCRM_translate label='LBL_CONTRACT_DATE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'order_number', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ORDER_NUMBER' module='Opportunities'}{literal}' );
addToValidate('EditView', 'order_date', 'date', false, '{/literal}{incomCRM_translate label='LBL_ORDER_DATE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'quote_number', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_QUOTE_NUMBER' module='Opportunities'}{literal}' );
addToValidate('EditView', 'estimated_revenue', 'currency', false, '{/literal}{incomCRM_translate label='LBL_ESTIMATED_REVENUE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'priority', 'enum', false, '{/literal}{incomCRM_translate label='LBL_PRIORITY' module='Opportunities'}{literal}' );
addToValidate('EditView', 'opportunity_scope', 'enum', false, '{/literal}{incomCRM_translate label='LBL_OPPORTUNITY_SCOPE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'object', 'enum', false, '{/literal}{incomCRM_translate label='LBL_OBJECT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'date_start_process', 'date', false, '{/literal}{incomCRM_translate label='LBL_DATE_START_PROCESS' module='Opportunities'}{literal}' );
addToValidate('EditView', 'date_converter', 'date', false, '{/literal}{incomCRM_translate label='LBL_DATE_CONVERTER' module='Opportunities'}{literal}' );
addToValidate('EditView', 'is_target', 'bool', false, '{/literal}{incomCRM_translate label='LBL_IS_TARGET' module='Opportunities'}{literal}' );
addToValidate('EditView', 'account_targets', 'bool', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_TARGETS' module='Opportunities'}{literal}' );
addToValidate('EditView', 'causes_failure', 'enum', false, '{/literal}{incomCRM_translate label='LBL_CAUSES_FAILURE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'failure_notes', 'text', false, '{/literal}{incomCRM_translate label='LBL_FAILURE_NOTES' module='Opportunities'}{literal}' );
addToValidate('EditView', 'debt_order', 'bool', false, '{/literal}{incomCRM_translate label='LBL_DEBT_ORDER' module='Opportunities'}{literal}' );
addToValidate('EditView', 'vat_stock', 'bool', false, '{/literal}{incomCRM_translate label='LBL_VAT_STOCK' module='Opportunities'}{literal}' );
addToValidate('EditView', 'project_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_PROJECT_ID' module='Opportunities'}{literal}' );
addToValidate('EditView', 'project_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_PROJECT_NAME' module='Opportunities'}{literal}' );
addToValidate('EditView', 'quotation_date', 'date', false, '{/literal}{incomCRM_translate label='LBL_QUOTATION_DATE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'quotation_version', 'tinyint', false, '{/literal}{incomCRM_translate label='LBL_QUOTATION_VERSION' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_date_shipment', 'date', false, '{/literal}{incomCRM_translate label='LBL_TTT_DATE_SHIPMENT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_date_contract', 'date', false, '{/literal}{incomCRM_translate label='LBL_TTT_DATE_CONTRACT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_date_declaration', 'date', false, '{/literal}{incomCRM_translate label='LBL_TTT_DATE_DECLARATION' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_date_storage', 'date', false, '{/literal}{incomCRM_translate label='LBL_TTT_DATE_STORAGE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_date_arrival', 'date', false, '{/literal}{incomCRM_translate label='LBL_TTT_DATE_ARRIVAL' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_date_clearance', 'date', false, '{/literal}{incomCRM_translate label='LBL_TTT_DATE_CLEARANCE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_fee_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_FEE_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_insurance_number', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_INSURANCE_NUMBER' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_insurance_date', 'date', false, '{/literal}{incomCRM_translate label='LBL_TTT_INSURANCE_DATE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_insurance_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_INSURANCE_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_insurance_extension', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_INSURANCE_EXTENSION' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_insurance_coop', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_INSURANCE_COOP' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_date_lc_payment', 'date', false, '{/literal}{incomCRM_translate label='LBL_TTT_DATE_LC_PAYMENT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_lc_number', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_LC_NUMBER' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_date_lc_expired', 'date', false, '{/literal}{incomCRM_translate label='LBL_TTT_DATE_LC_EXPIRED' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_lc_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_LC_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_lc_reiss_amt', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_LC_REISS_AMT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_lc_repair_amt', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_LC_REPAIR_AMT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_lc_payment_amt', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_LC_PAYMENT_AMT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_lc_bank_name', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_LC_BANK_NAME' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_invoice_number', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_INVOICE_NUMBER' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_invoice_date', 'date', false, '{/literal}{incomCRM_translate label='LBL_TTT_INVOICE_DATE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_bol', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_BOL' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_bol_date', 'date', false, '{/literal}{incomCRM_translate label='LBL_TTT_BOL_DATE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_packinglist', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_PACKINGLIST' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_co', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_CO' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_milltest', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_MILLTEST' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_dost', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_DOST' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_condi_trading', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_CONDI_TRADING' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_declaration', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_DECLARATION' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_declaration_number', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_DECLARATION_NUMBER' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_declaration_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_DECLARATION_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_vat_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_VAT_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_verification', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_VERIFICATION' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_verification_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_VERIFICATION_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_vat_import', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_VAT_IMPORT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_customs_fee', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_CUSTOMS_FEE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_clearance', 'text', false, '{/literal}{incomCRM_translate label='LBL_TTT_CLEARANCE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_arrival_notice', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_ARRIVAL_NOTICE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_discharging_port', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_DISCHARGING_PORT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_vessel', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_VESSEL' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_vessel_vn', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_VESSEL_VN' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_owed_contract', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_TTT_OWED_CONTRACT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_storage_fee', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_STORAGE_FEE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_vessel_fee', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_VESSEL_FEE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_repair_cont_fee', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_REPAIR_CONT_FEE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_deposit_cont_fee', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TTT_DEPOSIT_CONT_FEE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_expected_output', 'float', false, '{/literal}{incomCRM_translate label='LBL_TTT_EXPECTED_OUTPUT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_cnt_amount', 'float', false, '{/literal}{incomCRM_translate label='LBL_TTT_CNT_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_foreign_amount', 'float', false, '{/literal}{incomCRM_translate label='LBL_TTT_FOREIGN_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_actual_output', 'float', false, '{/literal}{incomCRM_translate label='LBL_TTT_ACTUAL_OUTPUT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_commodity', 'text', false, '{/literal}{incomCRM_translate label='LBL_TTT_COMMODITY' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_tolerance', 'text', false, '{/literal}{incomCRM_translate label='LBL_TTT_TOLERANCE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_shipment_method', 'text', false, '{/literal}{incomCRM_translate label='LBL_TTT_SHIPMENT_METHOD' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_partial_shipment', 'text', false, '{/literal}{incomCRM_translate label='LBL_TTT_PARTIAL_SHIPMENT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_transhipment', 'text', false, '{/literal}{incomCRM_translate label='LBL_TTT_TRANSHIPMENT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_packing', 'text', false, '{/literal}{incomCRM_translate label='LBL_TTT_PACKING' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_quality', 'text', false, '{/literal}{incomCRM_translate label='LBL_TTT_QUALITY' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ttt_order_condi', 'text', false, '{/literal}{incomCRM_translate label='LBL_TTT_ORDER_CONDI' module='Opportunities'}{literal}' );
addToValidate('EditView', 'holding_time', 'enum', false, '{/literal}{incomCRM_translate label='LBL_HOLDING_TIME' module='Opportunities'}{literal}' );
addToValidate('EditView', 'holding_start', 'date', false, '{/literal}{incomCRM_translate label='LBL_HOLDING_START' module='Opportunities'}{literal}' );
addToValidate('EditView', 'holding_end', 'date', false, '{/literal}{incomCRM_translate label='LBL_HOLDING_END' module='Opportunities'}{literal}' );
addToValidate('EditView', 'payment_type', 'enum', false, '{/literal}{incomCRM_translate label='LBL_PAYMENT_TYPE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'payment_status', 'enum', false, '{/literal}{incomCRM_translate label='LBL_PAYMENT_STATUS' module='Opportunities'}{literal}' );
addToValidate('EditView', 'cashes_link', 'text', false, '{/literal}{incomCRM_translate label='LBL_CASHES_LINK' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ignore_payment', 'bool', false, '{/literal}{incomCRM_translate label='LBL_IGNORE_PAYMENT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'is_locked', 'bool', false, '{/literal}{incomCRM_translate label='LBL_IS_LOCKED' module='Opportunities'}{literal}' );
addToValidate('EditView', 'warehouse_link', 'text', false, '{/literal}{incomCRM_translate label='LBL_WAREHOUSE_LINK' module='Opportunities'}{literal}' );
addToValidate('EditView', 'order_discount', 'double', false, '{/literal}{incomCRM_translate label='LBL_ORDER_DISCOUNT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'delivered_all', 'bool', false, '{/literal}{incomCRM_translate label='LBL_DELIVERED_ALL' module='Opportunities'}{literal}' );
addToValidate('EditView', 'expected_output', 'float', false, '{/literal}{incomCRM_translate label='LBL_EXPECTED_OUTPUT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'actual_output', 'float', false, '{/literal}{incomCRM_translate label='LBL_ACTUAL_OUTPUT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'contract_revenue', 'currency', false, '{/literal}{incomCRM_translate label='LBL_CONTRACT_REVENUE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'actual_receipts', 'currency', false, '{/literal}{incomCRM_translate label='LBL_ACTUAL_RECEIPTS' module='Opportunities'}{literal}' );
addToValidate('EditView', 'total_amount_in', 'currency', false, '{/literal}{incomCRM_translate label='LBL_TOTAL_AMOUNT_IN' module='Opportunities'}{literal}' );
addToValidate('EditView', 'prod_lock', 'bool', false, '{/literal}{incomCRM_translate label='LBL_PROD_LOCK' module='Opportunities'}{literal}' );
addToValidate('EditView', 'contact_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_ID' module='Opportunities'}{literal}' );
addToValidate('EditView', 'contact_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_NAME' module='Opportunities'}{literal}' );
addToValidate('EditView', 'contact_phone', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_CONTACT_PHONE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'vat_tax', 'enum', false, '{/literal}{incomCRM_translate label='LBL_VAT_TAX' module='Opportunities'}{literal}' );
addToValidate('EditView', 'vat_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_VAT_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'vat_more_amt', 'currency', false, '{/literal}{incomCRM_translate label='LBL_VAT_MORE_AMT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'vat_other_amt', 'currency', false, '{/literal}{incomCRM_translate label='LBL_VAT_OTHER_AMT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'discount_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_LIST_DISCOUNT_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'cost_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_COST_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'comm_more_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_PROD_COMM_MORE_AMT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'incurred_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_INCURRED_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'commission_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_COMMISSION_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'arise_cost_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_ARISE_COST_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'profit_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_PROFIT_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'rate_usd', 'float', false, '{/literal}{incomCRM_translate label='LBL_RATE_USD' module='Opportunities'}{literal}' );
addToValidate('EditView', 'date_payment', 'date', false, '{/literal}{incomCRM_translate label='LBL_DATE_PAYMENT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'payment_period', 'enum', false, '{/literal}{incomCRM_translate label='LBL_PAYMENT_PERIOD' module='Opportunities'}{literal}' );
addToValidate('EditView', 'order_type', 'enum', false, '{/literal}{incomCRM_translate label='LBL_ORDER_TYPE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'acc_reference_code', 'relate', false, '{/literal}{incomCRM_translate label='LBL_ACC_REFERENCE_CODE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'account_email', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_EMAIL' module='Opportunities'}{literal}' );
addToValidate('EditView', 'account_phone', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_PHONE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'account_address', 'text', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_ADDRESS' module='Opportunities'}{literal}' );
addToValidate('EditView', 'account_tax_code', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_TAX_CODE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'sector_vertical', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_SECTOR_VERTICAL' module='Opportunities'}{literal}' );
addToValidate('EditView', 'shipping_fee', 'currency', false, '{/literal}{incomCRM_translate label='LBL_SHIPPING_FEE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'no_currency', 'bool', false, '{/literal}{incomCRM_translate label='LBL_NO_CURRENCY' module='Opportunities'}{literal}' );
addToValidate('EditView', 'disable_auto_price', 'bool', false, '{/literal}{incomCRM_translate label='LBL_DISABLE_AUTO_PRICE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ordered_merge', 'bool', false, '{/literal}{incomCRM_translate label='LBL_ORDERED_MERGE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'total_foreign_amount', 'double', false, '{/literal}{incomCRM_translate label='LBL_TOTAL_FOREIGN_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'vat_spec_amt', 'currency', false, '{/literal}{incomCRM_translate label='LBL_VAT_SPEC_AMT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'vat_imp_amt', 'currency', false, '{/literal}{incomCRM_translate label='LBL_VAT_IMP_AMT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'vat_total_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_VAT_TOTAL_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'ext_incurred_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_EXT_INCURRED_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'int_incurred_amount', 'currency', false, '{/literal}{incomCRM_translate label='LBL_INT_INCURRED_AMOUNT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'is_import', 'bool', false, '{/literal}{incomCRM_translate label='LBL_IS_IMPORT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'money_payable', 'double', false, '{/literal}{incomCRM_translate label='LBL_MONEY_PAYABLE' module='Opportunities'}{literal}' );
addToValidate('EditView', 'money_deposit', 'currency', false, '{/literal}{incomCRM_translate label='LBL_MONEY_DEPOSIT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'money_refund', 'currency', false, '{/literal}{incomCRM_translate label='LBL_MONEY_REFUND' module='Opportunities'}{literal}' );
addToValidate('EditView', 'location_nearby', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_LOCATION_NEARBY' module='Opportunities'}{literal}' );
addToValidate('EditView', 'service_total', 'int', false, '{/literal}{incomCRM_translate label='LBL_SERVICE_TOTAL' module='Opportunities'}{literal}' );
addToValidate('EditView', 'service_used', 'int', false, '{/literal}{incomCRM_translate label='LBL_SERVICE_USED' module='Opportunities'}{literal}' );
addToValidate('EditView', 'service_left', 'int', false, '{/literal}{incomCRM_translate label='LBL_SERVICE_LEFT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'date_return', 'date', false, '{/literal}{incomCRM_translate label='LBL_DATE_RETURN' module='Opportunities'}{literal}' );
addToValidate('EditView', 'prod_returned', 'bool', false, '{/literal}{incomCRM_translate label='LBL_PROD_RETURNED' module='Opportunities'}{literal}' );
addToValidate('EditView', 'share_users[]', 'enum', false, '{/literal}{incomCRM_translate label='LBL_SHARING_USERS' module='Opportunities'}{literal}' );
addToValidate('EditView', 'share_users_ex[]', 'enum', false, '{/literal}{incomCRM_translate label='LBL_SHARING_USERS' module='Opportunities'}{literal}' );
addToValidate('EditView', 'membership_card', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_MEMBERSHIP_CARD' module='Opportunities'}{literal}' );
addToValidate('EditView', 'point_current', 'double', false, '{/literal}{incomCRM_translate label='LBL_POINT_CURRENT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'table_params', 'text', false, '{/literal}{incomCRM_translate label='Params' module='Opportunities'}{literal}' );
addToValidate('EditView', 'acc_bill_addr', 'text', false, '{/literal}{incomCRM_translate label='LBL_ACC_BILL_ADDR' module='Opportunities'}{literal}' );
addToValidate('EditView', 're_calc_profit', 'bool', false, '{/literal}{incomCRM_translate label='LBL_RE_CALC_PROFIT' module='Opportunities'}{literal}' );
addToValidate('EditView', 'date_record', 'date', false, '{/literal}{incomCRM_translate label='LBL_DATE_RECORD' module='Opportunities'}{literal}' );
addToValidate('EditView', 'is_export_vat', 'bool', false, '{/literal}{incomCRM_translate label='LBL_IS_EXPORT_VAT' module='Opportunities'}{literal}' );
addToValidateBinaryDependency('EditView', 'assigned_user_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Opportunities'}{literal}{/literal}{incomCRM_translate label='LBL_ASSIGNED_TO' module='Opportunities'}{literal}', 'assigned_user_id' );
addToValidateBinaryDependency('EditView', 'account_name', 'alpha', true, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Opportunities'}{literal}{/literal}{incomCRM_translate label='LBL_ACCOUNT_NAME' module='Opportunities'}{literal}', 'account_id' );
addToValidateBinaryDependency('EditView', 'project_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Opportunities'}{literal}{/literal}{incomCRM_translate label='LBL_PROJECT_NAME' module='Opportunities'}{literal}', 'project_id' );
addToValidateBinaryDependency('EditView', 'contact_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Opportunities'}{literal}{/literal}{incomCRM_translate label='LBL_CONTACT_NAME' module='Opportunities'}{literal}', 'contact_id' );
</script>
<script type="text/javascript">if(typeof sqs_objects == 'undefined'){var sqs_objects = new Array;}
sqs_objects['EditView_contact_name'] = {"form":"EditView","method":"get_contact_array","modules":["Contacts"],"field_list":["salutation","first_name","last_name","id"],"populate_list":["contact_name","contact_id","contact_id","contact_id"],"required_list":["contact_id"],"group":"or","conditions":[{"name":"first_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"last_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['EditView_assigned_user_name'] = {"form":"EditView","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["assigned_user_name","assigned_user_id"],"required_list":["assigned_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['EditView_project_name'] = {"form":"EditView","method":"query","modules":["Project"],"group":"or","field_list":["name","id"],"populate_list":["project_name","project_id"],"required_list":["parent_id"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
</script>{/literal}
