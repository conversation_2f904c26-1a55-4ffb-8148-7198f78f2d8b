incomCRM.language.setLanguage('Bank_Accounts', {"LBL_ID":"ID","LBL_DATE_ENTERED":"Ng\u00e0y t\u1ea1o","LBL_DATE_MODIFIED":"Ng\u00e0y c\u1eadp nh\u1eadt","LBL_MODIFIED":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_MODIFIED_ID":"ID ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_MODIFIED_NAME":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_CREATED":"Ng\u01b0\u1eddi t\u1ea1o","LBL_CREATED_ID":"ID ng\u01b0\u1eddi t\u1ea1o","LBL_DESCRIPTION":"<PERSON>hi ch\u00fa","LBL_DELETED":"\u0110\u00e3 x\u00f3a?","LBL_NAME":"STT","LBL_CREATED_USER":"Ng\u01b0\u1eddi t\u1ea1o","LBL_MODIFIED_USER":"Ng\u01b0\u1eddi c\u1eadp nh\u1eadt","LBL_ASSIGNED_TO":"Nv.Ql\u00fd","LBL_ASSIGNED_TO_ID":"ID Nv.Ql\u00fd","LBL_ASSIGNED_TO_NAME":"Nv.Ql\u00fd","LBL_ASSIGNED_TO_USER":"Nv.Ql\u00fd","LBL_ASSIGNED_USER":"Nv.Ql\u00fd","LBL_MODULE_NAME":"T\u00e0i kho\u1ea3n ng\u00e2n h\u00e0ng","LBL_MODULE_TITLE":"T\u00e0i kho\u1ea3n ng\u00e2n h\u00e0ng: Trang ch\u1ee7","LNK_NEW_RECORD":"Th\u00eam m\u1edbi","LNK_LIST":"Danh s\u00e1ch","LBL_SEARCH_FORM_TITLE":"T\u00ecm ki\u1ebfm","LBL_LIST_FORM_TITLE":"Danh s\u00e1ch t\u00e0i kho\u1ea3n ng\u00e2n h\u00e0ng","LBL_NEW_FORM_TITLE":"Th\u00eam m\u1edbi","LBL_BANK_NAME":"T\u00ean ng\u00e2n h\u00e0ng","LBL_ABBREVIATION":"T\u00ean vi\u1ebft t\u1eaft","LBL_BANK_OWNER":"Ch\u1ee7 t\u00e0i kho\u1ea3n","LBL_BANK_NUMBER":"S\u1ed1 t\u00e0i kho\u1ea3n","LBL_BANK_BRANCH":"Chi nh\u00e1nh","LBL_ACCOUNTING_CODE":"TK k\u1ebf to\u00e1n","LBL_CASHES":"Thu\/Chi","LBL_OPPORTUNITIES":"\u0110\u01a1n h\u00e0ng","LBL_ERR_DUPLICATE":"%s [%s] \u0111\u00e3 t\u1ed3n t\u1ea1i, vui l\u00f2ng nh\u1eadp l\u1ea1i.","LBL_WARNING_DELETED":"T\u00e0i kho\u1ea3n ng\u00e2n h\u00e0ng n\u00e0y \u0111\u00e3 t\u1ed3n t\u1ea1i Phi\u1ebfu thu\/chi, b\u1ea1n kh\u00f4ng th\u1ec3 x\u00f3a."});