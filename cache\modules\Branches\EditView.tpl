
<section id="EditViewViewBody" class="view-module-{$module|strtolower}">

<form action="index.php" method="POST" name="{$form_name}" id="{$form_id}" class="_edit-{$instanceName}" {$enctype}>
<input type="hidden" name="module" value="{$module}" />
<input type="hidden" name="action" id="action" />
{if isset($smarty.request.isDuplicate) && $smarty.request.isDuplicate eq "true"}
<input type="hidden" name="record" id="record" value="" />
<input type="hidden" name="duplicateSave" value="true" />
{else}
<input type="hidden" name="record" id="record" value="{$fields.id.value}" />
{/if}
<input type="hidden" name="return_module" value="{$smarty.request.return_module}" />
<input type="hidden" name="return_action" value="{if $smarty.request.return_action eq 'LoadTabSubpanels'}DetailView{else}{$smarty.request.return_action}{/if}" />
<input type="hidden" name="return_id" value="{$smarty.request.return_id}" />
<input type="hidden" name="isDuplicate" value="false" />
<input type="hidden" name="module_tab" /> 
<input type="hidden" name="contact_role" />
{if !empty($smarty.request.return_module)}
<input type="hidden" name="relate_to" value="{if $smarty.request.return_relationship}{$smarty.request.return_relationship}{else}{$smarty.request.return_module}{/if}" />
<input type="hidden" name="relate_id" value="{$smarty.request.return_id}" />
{/if}
<input type="hidden" name="offset" value="{$offset}" />
{if empty($fields.id.value)}
<input type="hidden" name="_notify_random" value="{php}echo create_guid();{/php}" />
{/if}
<input type="hidden" name="isFromEditView" value="{$module}" />
<table width="100%" cellpadding="0" cellspacing="0" border="0">
<tr>
<td class="buttons">
{if $bean->aclAccess("save")}<input title="{$APP.LBL_SAVE_BUTTON_TITLE}" accessKey="{$APP.LBL_SAVE_BUTTON_KEY}" class="button save" onclick="{if $isDuplicate}this.form.return_id.value=''; {/if}this.form.action.value='Save'; return check_form('EditView');" type="submit" name="button" value="{$APP.LBL_SAVE_BUTTON_LABEL}" />{/if} 
{if !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($fields.id.value))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {elseif !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($smarty.request.return_id))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {else}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='index'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {/if}
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=Branches", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</td>
</tr>
</table>
{incomCRM_include include=$includes}
<table width="100%" cellspacing="0" cellpadding="0" class='detail view' id='tabFormPagination'>
{$PAGINATION}
</table>
<div id="DEFAULT" class="editview">
{counter name="panelFieldCount" start=0 print=false assign="panelFieldCount"}
<div class="{$def.templateMeta.panelClass|default:'edit view'} {if $maxColumns > 1}view-multi-cols view-multi-col-{$maxColumns}{/if}" id="DEFAULT_GROUP" style="display:block">
<table width="100%" border="0" cellspacing="1" cellpadding="0">
<tr>
<td valign="top" id='code_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_CODE' module='Branches'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_code_field' >
{counter name="panelFieldCount"}

{if strlen($fields.code.value) <= 0}
{assign var="value" value=$fields.code.default_value }
{else}
{assign var="value" value=$fields.code.value }
{/if}
{if isTypeNumber($fields.code.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.code.name}' id='{$fields.code.name}' size='30' maxlength='20' value='{$value}' title='' tabindex='100'  /> 

</td>
<td valign="top" id='block_login_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_BLOCK_LOGIN' module='Branches'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_block_login_field' >
{counter name="panelFieldCount"}
{if $current_user->user_name eq "thuyqt"}<input tabindex="101"  type="hidden" name="block_login" value="0" /><input tabindex="101"  type="checkbox" name="block_login" id="block_login" value="1" {if !empty($fields.block_login.value)}checked{/if} />{elseif !empty($fields.block_login.value)}<span class="error">x</span>{/if}
</td>
</tr>
<tr>
<td valign="top" id='name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_NAME' module='Branches'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_name_field' >
{counter name="panelFieldCount"}

{if strlen($fields.name.value) <= 0}
{assign var="value" value=$fields.name.default_value }
{else}
{assign var="value" value=$fields.name.value }
{/if}
{if isTypeNumber($fields.name.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.name.name}' id='{$fields.name.name}' size='30' maxlength='200' value='{$value}' title='' tabindex='102'  /> 

</td>
<td valign="top" id='order_perfix_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ORDER_PERFIX' module='Branches'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_order_perfix_field' >
{counter name="panelFieldCount"}

{if strlen($fields.order_perfix.value) <= 0}
{assign var="value" value=$fields.order_perfix.default_value }
{else}
{assign var="value" value=$fields.order_perfix.value }
{/if}
{if isTypeNumber($fields.order_perfix.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.order_perfix.name}' id='{$fields.order_perfix.name}' size='30' maxlength='5' value='{$value}' title='' tabindex='103'  /> 

</td>
</tr>
<tr>
<td valign="top" id='short_name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_SHORT_NAME' module='Branches'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_short_name_field' >
{counter name="panelFieldCount"}

{if strlen($fields.short_name.value) <= 0}
{assign var="value" value=$fields.short_name.default_value }
{else}
{assign var="value" value=$fields.short_name.value }
{/if}
{if isTypeNumber($fields.short_name.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.short_name.name}' id='{$fields.short_name.name}' size='30' maxlength='50' value='{$value}' title='' tabindex='104'  /> 

</td>
<td valign="top" id='bank_account_id_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_BANK_ACCOUNT_ID' module='Branches'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_bank_account_id_field' >
{counter name="panelFieldCount"}

{$fields.bank_account_id.value}
</td>
</tr>
<tr>
<td valign="top" id='limit_users_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_LIMIT_USERS' module='Branches'}
{/capture}
{$label|strip_semicolon}:
<span class="required">*</span>
</td>
<td valign="top" width='35%' id='_limit_users_field' >
{counter name="panelFieldCount"}
{if $current_user->user_name eq "thuyqt"}<input tabindex="106"  type="text" name="limit_users" id="limit_users" value="{$fields.limit_users.value}" size="30" maxlength="6" />{else}<span class="error">{$fields.limit_users.value}</span>{/if}
</td>
<td valign="top" id='bank_account_ignore_id_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_BANK_ACCOUNT_IGNORE_ID' module='Branches'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_bank_account_ignore_id_field' >
{counter name="panelFieldCount"}

{$fields.bank_account_ignore_id.value}
</td>
</tr>
<tr>
<td valign="top" id='phone_number_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_PHONE_NUMBER' module='Branches'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_phone_number_field' >
{counter name="panelFieldCount"}

{if strlen($fields.phone_number.value) <= 0}
{assign var="value" value=$fields.phone_number.default_value }
{else}
{assign var="value" value=$fields.phone_number.value }
{/if}
{if isTypeNumber($fields.phone_number.type) }
{assign var="inputmode" value='inputmode="numeric"' }
{else}
{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.phone_number.name}' id='{$fields.phone_number.name}' size='30' maxlength='50' value='{$value}' title='' tabindex='108'  /> 

</td>
<td valign="top" id='company_name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_COMPANY_NAME' module='Branches'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_company_name_field' >
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{else}{/if}<input type="hidden" name="{$fields.company_name.id_name}" id="{$fields.company_name.id_name}" value="{$fields.company_id.value}" />
<input type="text" name="{$fields.company_name.name}" class="sqsEnabled" tabindex="109" id="{$fields.company_name.name}" size="16" value="{$fields.company_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{else}{/if}
<input type="button" name="btn_{$fields.company_name.name}" id="btn_{$fields.company_name.name}" tabindex="109" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.company_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"EditView","field_to_name_array":{"id":"company_id","name":"company_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.company_name.name}" id="btn_clr_{$fields.company_name.name}" tabindex="109" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.company_name.name}.value=""; this.form.{$fields.company_name.id_name}.value=""; this.form.{$fields.company_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
{if !empty($isSmartView)}</div>{else}{/if}<script type="text/javascript">enableQS(false);</script>
</td>
</tr>
<tr>
<td valign="top" id='address_street_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ADDRESS_STREET' module='Branches'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_address_street_field' >
{counter name="panelFieldCount"}

{if empty($fields.address_street.value)}
{assign var="value" value=$fields.address_street.default_value }
{else}
{assign var="value" value=$fields.address_street.value }
{/if}
<textarea id="{$fields.address_street.name}" name="{$fields.address_street.name}" rows="3" cols="45" title='' tabindex="110"  >{$value}</textarea>
</td>
<td valign="top" id='account_name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_ACCOUNT_NAME' module='Branches'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_account_name_field' >
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{else}{/if}<input type="hidden" name="{$fields.account_name.id_name}" id="{$fields.account_name.id_name}" value="{$fields.account_id.value}" />
<input type="text" name="{$fields.account_name.name}" class="sqsEnabled" tabindex="111" id="{$fields.account_name.name}" size="16" value="{$fields.account_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{else}{/if}
<input type="button" name="btn_{$fields.account_name.name}" id="btn_{$fields.account_name.name}" tabindex="111" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.account_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"EditView","field_to_name_array":{"id":"account_id","name":"account_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.account_name.name}" id="btn_clr_{$fields.account_name.name}" tabindex="111" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.account_name.name}.value=""; this.form.{$fields.account_name.id_name}.value=""; this.form.{$fields.account_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
{if !empty($isSmartView)}</div>{else}{/if}<script type="text/javascript">enableQS(false);</script>
</td>
</tr>
<tr>
<td valign="top" id='quotes_description_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_QUOTES_DESCRIPTION' module='Branches'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_quotes_description_field' >
{counter name="panelFieldCount"}

{if empty($fields.quotes_description.value)}
{assign var="value" value=$fields.quotes_description.default_value }
{else}
{assign var="value" value=$fields.quotes_description.value }
{/if}
<textarea id="{$fields.quotes_description.name}" name="{$fields.quotes_description.name}" rows="3" cols="45" title='' tabindex="112"  >{$value}</textarea>
</td>
<td valign="top" id='warehouse_name_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_WAREHOUSE_NAME' module='Branches'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_warehouse_name_field' >
{counter name="panelFieldCount"}

{if !empty($isSmartView)}<div class="row"><div class="col- col-s2-12 pr-0 pr-s2-10">{else}{/if}<input type="hidden" name="{$fields.warehouse_name.id_name}" id="{$fields.warehouse_name.id_name}" value="{$fields.warehouse_id.value}" />
<input type="text" name="{$fields.warehouse_name.name}" class="sqsEnabled" tabindex="113" id="{$fields.warehouse_name.name}" size="16" value="{$fields.warehouse_name.value}" title='' autocomplete="off"   />
{if !empty($isSmartView)}</div>{else}{/if}
<input type="button" name="btn_{$fields.warehouse_name.name}" id="btn_{$fields.warehouse_name.name}" tabindex="113" title="{$APP.LBL_SELECT_BUTTON_TITLE}" accessKey="{$APP.LBL_SELECT_BUTTON_KEY}" class="button btn-select" value="{$APP.LBL_SELECT_BUTTON_LABEL}" onclick='open_popup("{$fields.warehouse_name.module}", 1000, 600, "", true, false, {literal}{"call_back_function":"set_return","form_name":"EditView","field_to_name_array":{"id":"warehouse_id","name":"warehouse_name"}}{/literal}, "single", true);'  />
<input type="button" name="btn_clr_{$fields.warehouse_name.name}" id="btn_clr_{$fields.warehouse_name.name}" tabindex="113" title="{$APP.LBL_CLEAR_BUTTON_TITLE}" accessKey="{$APP.LBL_CLEAR_BUTTON_KEY}" class="button btn-clear" onclick='this.form.{$fields.warehouse_name.name}.value=""; this.form.{$fields.warehouse_name.id_name}.value=""; this.form.{$fields.warehouse_name.name}.focus();' value="{$APP.LBL_CLEAR_BUTTON_LABEL}"  />
{if !empty($isSmartView)}</div>{else}{/if}<script type="text/javascript">enableQS(false);</script>
</td>
</tr>
<tr>
<td valign="top" id='title_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_TITLE' module='Branches'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_title_field' >
{counter name="panelFieldCount"}

{if empty($fields.title.value)}
{assign var="value" value=$fields.title.default_value }
{else}
{assign var="value" value=$fields.title.value }
{/if}
<textarea id="{$fields.title.name}" name="{$fields.title.name}" rows="3" cols="45" title='' tabindex="114"  >{$value}</textarea>
</td>
<td valign="top" id='filename_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_FILENAME' module='Branches'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" width='35%' id='_filename_field' >
{counter name="panelFieldCount"}
<span id="new_attachment" style="display:{if !empty($fields.filename.value)}none{/if}"><input tabindex="115"  type="file" name="uploadfile" size="30" /></span><span id="old_attachment" style="display:{if empty($fields.filename.value)}none{/if}">{if !empty($fields.filename.value)}<img align="absmiddle" alt="{$fields.filename.value}" height="60" src="{$fields.file_url.value}" /> {/if}<input tabindex="115"  type="hidden" name="deleteAttachment" value="0" /><input tabindex="115"  type="hidden" name="old_id" value="{$fields.id.value}" /><input tabindex="115"  type="hidden" name="old_filename" value="{$fields.filename.value}" /><input tabindex="115"  type="button" class="button" name="btn" value="{$APP.LBL_REMOVE}" onclick='{if !empty($isDuplicate)}deleteAttachmentCallBack(-1);{else}ajaxStatus.showStatus(incomCRM.language.get("app_strings", "LBL_ATT_REMOVING_ATTACHMENT")); this.form.deleteAttachment.value=1; this.form.action.value="UploadFileRemove"; incomCRM.dashlets.postForm(this.form, deleteAttachmentCallBack); this.form.deleteAttachment.value=0; this.form.action.value="";{/if}' /></span>
</td>
</tr>
<tr>
<td valign="top" id='description_label' width='15%' scope="row">
{capture name="label" assign="label}
{incomCRM_translate label='LBL_DESCRIPTION' module='Branches'}
{/capture}
{$label|strip_semicolon}:
</td>
<td valign="top" colspan='3' id='_description_field' >
{counter name="panelFieldCount"}

{if empty($fields.description.value)}
{assign var="value" value=$fields.description.default_value }
{else}
{assign var="value" value=$fields.description.value }
{/if}
<textarea id="{$fields.description.name}" name="{$fields.description.name}" rows="3" cols="45" title='' tabindex="116"  >{$value}</textarea>
</td>
</tr>
</table>
</div>
</div>
{if $panelFieldCount == 0}
<script>document.getElementById("DEFAULT").style.display='none';</script>
{/if}

<div class="buttons" style="margin-top:15px;">
{if $bean->aclAccess("save")}<input title="{$APP.LBL_SAVE_BUTTON_TITLE}" accessKey="{$APP.LBL_SAVE_BUTTON_KEY}" class="button save" onclick="{if $isDuplicate}this.form.return_id.value=''; {/if}this.form.action.value='Save'; return check_form('EditView');" type="submit" name="button" value="{$APP.LBL_SAVE_BUTTON_LABEL}" />{/if} 
{if !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($fields.id.value))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {elseif !empty($smarty.request.return_action) && ($smarty.request.return_action == "DetailView" && !empty($smarty.request.return_id))}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='DetailView'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {else}<input title="{$APP.LBL_CANCEL_BUTTON_TITLE}" accessKey="{$APP.LBL_CANCEL_BUTTON_KEY}" class="button cancel" onclick="this.form.action.value='index'; this.form.module.value='{$smarty.request.return_module}'; this.form.record.value='{$smarty.request.return_id}';" type="submit" name="button" value="{$APP.LBL_CANCEL_BUTTON_LABEL}" /> {/if}
{if $bean->aclAccess("detail")}{if !empty($fields.id.value) && $isAuditEnabled}<input title="{$APP.LNK_VIEW_CHANGE_LOG}" class="button audit" onclick='open_popup("Audit", 900, 550, "&record={$fields.id.value}&module_name=Branches", true, false,  {ldelim} "call_back_function":"set_return","form_name":"EditView","field_to_name_array":[] {rdelim} ); return false;' type="button" value="{if !empty($isMobileDevice)}{$APP.LNK_VIEW_CHANGE_LOG2}{else}{$APP.LNK_VIEW_CHANGE_LOG}{/if}" />{/if}{/if}
</div>
</form>
{$set_focus_block}
<!-- Begin Meta-Data Javascript -->
<script type="text/javascript">
{literal}
function deleteAttachmentCallBack(text) {
	if( text == "true" || text == -1 ) {
		if( text == "true" ) ajaxStatus.hideStatus();
		document.getElementById("new_attachment").style.display = "";
		document.getElementById("old_attachment").innerHTML = "";
	}
	else {
		document.getElementById("new_attachment").style.display = "none";
		ajaxStatus.flashStatus(incomCRM.language.get("app_strings", "ERR_ATT_REMOVING_ATTACHMENT"), 2000);
	}
}
{/literal}
</script>
<!-- End Meta-Data Javascript -->
</section>{literal}
<script type="text/javascript">
addToValidate('EditView', 'id', 'id', false, '{/literal}{incomCRM_translate label='LBL_ID' module='Branches'}{literal}' );
addToValidate('EditView', 'date_entered_date', 'date', false, 'Ngày tạo' );
addToValidate('EditView', 'date_modified_date', 'date', false, 'Ngày cập nhật' );
addToValidate('EditView', 'modified_user_id', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_ID' module='Branches'}{literal}' );
addToValidate('EditView', 'modified_by_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_MODIFIED_NAME' module='Branches'}{literal}' );
addToValidate('EditView', 'created_by', 'assigned_user_name', false, '{/literal}{incomCRM_translate label='LBL_CREATED_ID' module='Branches'}{literal}' );
addToValidate('EditView', 'created_by_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_CREATED' module='Branches'}{literal}' );
addToValidate('EditView', 'deleted', 'bool', false, '{/literal}{incomCRM_translate label='LBL_DELETED' module='Branches'}{literal}' );
addToValidate('EditView', 'name', 'name', true, '{/literal}{incomCRM_translate label='LBL_NAME' module='Branches'}{literal}' );
addToValidate('EditView', 'description', 'text', false, '{/literal}{incomCRM_translate label='LBL_DESCRIPTION' module='Branches'}{literal}' );
addToValidate('EditView', 'code', 'varchar', true, '{/literal}{incomCRM_translate label='LBL_CODE' module='Branches'}{literal}' );
addToValidate('EditView', 'short_name', 'varchar', true, '{/literal}{incomCRM_translate label='LBL_SHORT_NAME' module='Branches'}{literal}' );
addToValidate('EditView', 'order_perfix', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ORDER_PERFIX' module='Branches'}{literal}' );
addToValidate('EditView', 'order_number', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_ORDER_NUMBER' module='Branches'}{literal}' );
addToValidate('EditView', 'limit_users', 'int', true, '{/literal}{incomCRM_translate label='LBL_LIMIT_USERS' module='Branches'}{literal}' );
addToValidate('EditView', 'block_login', 'bool', false, '{/literal}{incomCRM_translate label='LBL_BLOCK_LOGIN' module='Branches'}{literal}' );
addToValidate('EditView', 'phone_number', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_PHONE_NUMBER' module='Branches'}{literal}' );
addToValidate('EditView', 'address_street', 'text', false, '{/literal}{incomCRM_translate label='LBL_ADDRESS_STREET' module='Branches'}{literal}' );
addToValidate('EditView', 'bank_account_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_BANK_ACCOUNT_ID' module='Branches'}{literal}' );
addToValidate('EditView', 'account_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_ID' module='Branches'}{literal}' );
addToValidate('EditView', 'account_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_ACCOUNT_NAME' module='Branches'}{literal}' );
addToValidate('EditView', 'company_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_COMPANY_ID' module='Branches'}{literal}' );
addToValidate('EditView', 'company_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_COMPANY_NAME' module='Branches'}{literal}' );
addToValidate('EditView', 'title', 'text', false, '{/literal}{incomCRM_translate label='LBL_TITLE' module='Branches'}{literal}' );
addToValidate('EditView', 'quotes_description', 'text', false, '{/literal}{incomCRM_translate label='LBL_QUOTES_DESCRIPTION' module='Branches'}{literal}' );
addToValidate('EditView', 'filename', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_FILENAME' module='Branches'}{literal}' );
addToValidate('EditView', 'file_mime_type', 'varchar', false, '{/literal}{incomCRM_translate label='LBL_FILE_MIME_TYPE' module='Branches'}{literal}' );
addToValidate('EditView', 'file_url', 'function', false, '{/literal}{incomCRM_translate label='LBL_FILE_URL' module='Branches'}{literal}' );
addToValidate('EditView', 'bank_account_ignore_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_BANK_ACCOUNT_IGNORE_ID' module='Branches'}{literal}' );
addToValidate('EditView', 'warehouse_id', 'id', false, '{/literal}{incomCRM_translate label='LBL_WAREHOUSE_ID' module='Branches'}{literal}' );
addToValidate('EditView', 'warehouse_name', 'relate', false, '{/literal}{incomCRM_translate label='LBL_WAREHOUSE_NAME' module='Branches'}{literal}' );
addToValidate('EditView', 'price', 'float', false, '{/literal}{incomCRM_translate label='LBL_PRICE' module='Branches'}{literal}' );
addToValidate('EditView', 'date_updated_date', 'date', false, 'Ngày cập nhật' );
addToValidateBinaryDependency('EditView', 'account_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Branches'}{literal}{/literal}{incomCRM_translate label='LBL_ACCOUNT_NAME' module='Branches'}{literal}', 'account_id' );
addToValidateBinaryDependency('EditView', 'company_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Branches'}{literal}{/literal}{incomCRM_translate label='LBL_COMPANY_NAME' module='Branches'}{literal}', 'company_id' );
addToValidateBinaryDependency('EditView', 'warehouse_name', 'alpha', false, '{/literal}{incomCRM_translate label='ERR_SQS_NO_MATCH_FIELD' module='Branches'}{literal}{/literal}{incomCRM_translate label='LBL_WAREHOUSE_NAME' module='Branches'}{literal}', 'warehouse_id' );
</script>
<script type="text/javascript">if(typeof sqs_objects == 'undefined'){var sqs_objects = new Array;}
sqs_objects['EditView_company_name'] = {"form":"EditView","method":"query","modules":["Accounts"],"group":"or","field_list":["name","id"],"populate_list":["EditView_company_name","company_id"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"required_list":["company_id"],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['EditView_account_name'] = {"form":"EditView","method":"query","modules":["Accounts"],"group":"or","field_list":["name","id"],"populate_list":["EditView_account_name","account_id"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"required_list":["account_id"],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['EditView_warehouse_name'] = {"form":"EditView","method":"query","modules":["TQT_Warehouses"],"group":"or","field_list":["name","id"],"populate_list":["warehouse_name","warehouse_id"],"required_list":["parent_id"],"conditions":[{"name":"name","op":"contains","end":"%","value":""}],"order":"name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
</script>{/literal}
