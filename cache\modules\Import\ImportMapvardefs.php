<?php
// created: 2025-02-26 15:10:10
$GL<PERSON><PERSON>LS["dictionary"]["ImportMap"] = array (
  'table' => 'import_maps',
  'comment' => 'Import mapping control table',
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => false,
      'comment' => 'Unique identifier',
    ),
    'name' => 
    array (
      'name' => 'name',
      'vname' => 'LBL_NAME',
      'type' => 'varchar',
      'len' => '254',
      'required' => true,
      'comment' => 'Name of import map',
    ),
    'source' => 
    array (
      'name' => 'source',
      'vname' => 'LBL_SOURCE',
      'type' => 'varchar',
      'len' => '36',
      'required' => true,
      'comment' => '',
    ),
    'enclosure' => 
    array (
      'name' => 'enclosure',
      'vname' => 'LBL_CUSTOM_ENCLOSURE',
      'type' => 'varchar',
      'len' => '1',
      'required' => true,
      'comment' => '',
      'default' => ' ',
    ),
    'delimiter' => 
    array (
      'name' => 'delimiter',
      'vname' => 'LBL_CUSTOM_DELIMITER',
      'type' => 'varchar',
      'len' => '1',
      'required' => true,
      'comment' => '',
      'default' => ',',
    ),
    'module' => 
    array (
      'name' => 'module',
      'vname' => 'LBL_MODULE',
      'type' => 'varchar',
      'len' => '36',
      'required' => true,
      'comment' => 'Module used for import',
    ),
    'content' => 
    array (
      'name' => 'content',
      'vname' => 'LBL_CONTENT',
      'type' => 'text',
      'comment' => 'Mappings for all columns',
    ),
    'default_values' => 
    array (
      'name' => 'default_values',
      'vname' => 'LBL_CONTENT',
      'type' => 'text',
      'comment' => 'Default Values for all columns',
    ),
    'has_header' => 
    array (
      'name' => 'has_header',
      'vname' => 'LBL_HAS_HEADER',
      'type' => 'bool',
      'default' => '1',
      'required' => true,
      'comment' => 'Indicator if source file contains a header row',
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'required' => true,
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'required' => true,
      'comment' => 'Date record created',
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'required' => true,
      'comment' => 'Date record last modified',
    ),
    'assigned_user_id' => 
    array (
      'name' => 'assigned_user_id',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'vname' => 'LBL_ASSIGNED_TO',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'reportable' => false,
      'comment' => 'Assigned-to user',
    ),
    'is_published' => 
    array (
      'name' => 'is_published',
      'vname' => 'LBL_IS_PUBLISHED',
      'type' => 'varchar',
      'len' => '3',
      'required' => true,
      'default' => 'no',
      'comment' => 'Indicator if mapping is published',
    ),
  ),
  'indices' => 
  array (
    0 => 
    array (
      'name' => 'import_mapspk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_owner_module_name',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'assigned_user_id',
        1 => 'module',
        2 => 'name',
        3 => 'deleted',
      ),
    ),
  ),
);
?>
