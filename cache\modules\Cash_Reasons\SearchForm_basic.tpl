

{math equation="floor(left / right)"
	left=12
		right=$templateMeta.maxColumns
			assign=colClass
	}

<section class="container2 search-form ml-0 search_form_basic">
	<div class="row">
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12">
			<div class="row">
				<label class="col-5 pr-0" for="name_basic">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_NAME' module='Cash_Reasons'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{if strlen($fields.name_basic.value) <= 0}
	{assign var="value" value=$fields.name_basic.default_value }
{else}
	{assign var="value" value=$fields.name_basic.value }
{/if}
{if isTypeNumber($fields.name_basic.type) }
	{assign var="inputmode" value='inputmode="numeric"' }
{else}
	{assign var="inputmode" value='' }
{/if}
<input type='text' {$inputmode} name='{$fields.name_basic.name}' id='{$fields.name_basic.name}' size='30' maxlength='250' value='{$value}' title='' tabindex=''  /> 
									</div>
			</div>
		</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12">
			<div class="row">
				<label class="col-5 pr-0" for="reason_type_basic">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_REASON_TYPE' module='Cash_Reasons'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="reason_type_basic[]" id="{$fields.reason_type_basic.name}" size="1"   >
{html_options options=$fields.reason_type_basic.options selected=$fields.reason_type_basic.value}
</select>
									</div>
			</div>
		</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12">
			<div class="row">
				<label class="col-5 pr-0" for="accounting_type_basic">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_ACCOUNTING_TYPE' module='Cash_Reasons'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
<select name="accounting_type_basic[]" id="{$fields.accounting_type_basic.name}" size="1"   >
{html_options options=$fields.accounting_type_basic.options selected=$fields.accounting_type_basic.value}
</select>
									</div>
			</div>
		</div>
		<div class="col-{$colClass|default:'4'} col-md-6 col-sm-12">
			<div class="row">
				<label class="col-5 pr-0" for="ignore_summary_basic">
											{capture name="label" assign="label"}
						{incomCRM_translate label='LBL_LIST_IGNORE_SUMMARY' module='Cash_Reasons'}
						{/capture}
						{$label|strip_semicolon}:
									</label>
				<div class="col-7 ">
											
{assign var="yes" value=""}
{assign var="no" value=""}
{assign var="default" value=""}

{if strval($fields.ignore_summary_basic.value) == "1"}
	{assign var="yes" value="SELECTED"}
{elseif strval($fields.ignore_summary_basic.value) == "0"}
	{assign var="no" value="SELECTED"}
{else}
	{assign var="default" value="SELECTED"}
{/if}

<select id="{$fields.ignore_summary_basic.name}" name="{$fields.ignore_summary_basic.name}" tabindex="" style="width:80px !important;" >
 <option value="" {$default}></option>
 <option value = "0" {$no}> {$APP.LBL_SEARCH_DROPDOWN_NO}</option>
 <option value = "1" {$yes}> {$APP.LBL_SEARCH_DROPDOWN_YES}</option>
</select>

									</div>
			</div>
		</div>
	</div>
</section>

{if $HAS_ADVANCED_SEARCH}
<table cellspacing="0" cellpadding="0" border="0" class="mt-10">
<tr>
	<td scope="row" nowrap="nowrap" class="pt-10">
	<a style="font-size:10pt; text-decoration:none;" onclick="incomCRM.searchForm.searchFormSelect('{$module}|advanced_search','{$module}|basic_search')" href="#">[ {$APP.LNK_ADVANCED_SEARCH} ]</a>
	</td>
</tr>
</table>
{/if}

{literal}<script type="text/javascript">if(typeof sqs_objects == 'undefined'){var sqs_objects = new Array;}
sqs_objects['search_form_modified_by_name_basic'] = {"form":"search_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["modified_by_name_basic","modified_user_id_basic"],"required_list":["modified_user_id"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
sqs_objects['search_form_created_by_name_basic'] = {"form":"search_form","method":"get_user_array","modules":["Users"],"group":"or","field_list":["user_name","id"],"populate_list":["created_by_name_basic","created_by_basic"],"required_list":["created_by"],"conditions":[{"name":"user_name","op":"contains","end":"%","value":""},{"name":"last_name","op":"contains","end":"%","value":""}],"order":"user_name","limit":"30","no_match_text":"Kh\u00f4ng ph\u00f9 h\u1ee3p"};
</script>{/literal}