<?php
// created: 2025-02-27 15:04:30
$G<PERSON><PERSON><PERSON><PERSON>["dictionary"]["Product_Lot"] = array (
  'table' => 'product_lots',
  'audited' => true,
  'unified_search' => true,
  'duplicate_merge' => false,
  'fields' => 
  array (
    'id' => 
    array (
      'name' => 'id',
      'vname' => 'LBL_ID',
      'type' => 'id',
      'required' => true,
      'reportable' => true,
      'comment' => 'Unique identifier',
    ),
    'date_entered' => 
    array (
      'name' => 'date_entered',
      'vname' => 'LBL_DATE_ENTERED',
      'type' => 'datetime',
      'group' => 'created_by_name',
      'comment' => 'Date record created',
      'audited' => false,
    ),
    'date_modified' => 
    array (
      'name' => 'date_modified',
      'vname' => 'LBL_DATE_MODIFIED',
      'type' => 'datetime',
      'group' => 'modified_by_name',
      'comment' => 'Date record last modified',
      'audited' => false,
    ),
    'modified_user_id' => 
    array (
      'name' => 'modified_user_id',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_MODIFIED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'group' => 'modified_by_name',
      'dbType' => 'id',
      'reportable' => true,
      'comment' => 'User who last modified record',
    ),
    'modified_by_name' => 
    array (
      'name' => 'modified_by_name',
      'vname' => 'LBL_MODIFIED_NAME',
      'type' => 'relate',
      'source' => 'non-db',
      'rname' => 'user_name',
      'table' => 'users',
      'id_name' => 'modified_user_id',
      'module' => 'Users',
      'link' => 'modified_user_link',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'created_by' => 
    array (
      'name' => 'created_by',
      'rname' => 'user_name',
      'id_name' => 'modified_user_id',
      'vname' => 'LBL_CREATED_ID',
      'type' => 'assigned_user_name',
      'table' => 'users',
      'isnull' => 'false',
      'dbType' => 'id',
      'group' => 'created_by_name',
      'comment' => 'User who created record',
    ),
    'created_by_name' => 
    array (
      'name' => 'created_by_name',
      'vname' => 'LBL_CREATED',
      'type' => 'relate',
      'link' => 'created_by_link',
      'rname' => 'user_name',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'created_by',
      'module' => 'Users',
      'importable' => 'false',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'deleted' => 
    array (
      'name' => 'deleted',
      'vname' => 'LBL_DELETED',
      'type' => 'bool',
      'default' => '0',
      'reportable' => false,
      'comment' => 'Record deletion indicator',
    ),
    'name' => 
    array (
      'name' => 'name',
      'type' => 'name',
      'dbType' => 'varchar',
      'vname' => 'LBL_NAME',
      'len' => 50,
      'acl' => true,
      'audited' => true,
      'unified_search' => true,
      'merge_filter' => 'selected',
      'importable' => 'required',
    ),
    'description' => 
    array (
      'name' => 'description',
      'vname' => 'LBL_DESCRIPTION',
      'type' => 'text',
      'audited' => true,
      'comment' => 'Full text of the note',
    ),
    'created_by_link' => 
    array (
      'name' => 'created_by_link',
      'type' => 'link',
      'relationship' => 'product_lots_created_by',
      'vname' => 'LBL_CREATED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'modified_user_link' => 
    array (
      'name' => 'modified_user_link',
      'type' => 'link',
      'relationship' => 'product_lots_modified_user',
      'vname' => 'LBL_MODIFIED_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
    ),
    'branch_id' => 
    array (
      'name' => 'branch_id',
      'vname' => 'LBL_BRANCH_ID',
      'type' => 'enum',
      'options' => 'users_branch_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'BranchID of assigned user',
    ),
    'department_id' => 
    array (
      'name' => 'department_id',
      'vname' => 'LBL_DEPARTMENT_ID',
      'type' => 'enum',
      'options' => 'users_department_dom',
      'dbType' => 'id',
      'audited' => false,
      'massupdate' => false,
      'unified_search' => false,
      'reportable' => true,
      'group' => 'user_permission',
      'studio' => 'false',
      'importable' => 'false',
      'merge_filter' => 'disabled',
      'duplicate_merge' => false,
      'comment' => 'DepartmentID of assigned user',
    ),
    'assigned_user_id' => 
    array (
      'name' => 'assigned_user_id',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'vname' => 'LBL_ASSIGNED_TO_ID',
      'group' => 'assigned_user_name',
      'type' => 'relate',
      'table' => 'users',
      'module' => 'Users',
      'isnull' => 'false',
      'dbType' => 'id',
      'audited' => true,
      'reportable' => true,
      'duplicate_merge' => 'disabled',
      'comment' => 'User ID assigned to record',
    ),
    'assigned_user_name' => 
    array (
      'name' => 'assigned_user_name',
      'link' => 'assigned_user_link',
      'vname' => 'LBL_ASSIGNED_TO_NAME',
      'rname' => 'user_name',
      'type' => 'relate',
      'source' => 'non-db',
      'table' => 'users',
      'id_name' => 'assigned_user_id',
      'module' => 'Users',
      'duplicate_merge' => 'disabled',
      'reportable' => false,
    ),
    'assigned_user_link' => 
    array (
      'name' => 'assigned_user_link',
      'type' => 'link',
      'relationship' => 'product_lots_assigned_user',
      'vname' => 'LBL_ASSIGNED_TO_USER',
      'link_type' => 'one',
      'module' => 'Users',
      'bean_name' => 'User',
      'source' => 'non-db',
      'duplicate_merge' => 'enabled',
      'rname' => 'user_name',
      'id_name' => 'assigned_user_id',
      'table' => 'users',
    ),
    'code' => 
    array (
      'name' => 'code',
      'type' => 'int',
      'vname' => 'LBL_CODE',
      'len' => 4,
      'audited' => true,
      'unified_search' => true,
    ),
    'date_perform' => 
    array (
      'name' => 'date_perform',
      'vname' => 'LBL_DATE_PERFORM',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
      'display_default' => 'now',
    ),
    'date_expired' => 
    array (
      'name' => 'date_expired',
      'vname' => 'LBL_DATE_EXPIRED',
      'type' => 'date',
      'audited' => true,
      'massupdate' => false,
    ),
    'num_days' => 
    array (
      'name' => 'num_days',
      'vname' => 'LBL_NUM_DAYS',
      'type' => 'int',
      'len' => '6',
      'audited' => true,
      'default' => 0,
    ),
    'tqt_productgroup_id' => 
    array (
      'name' => 'tqt_productgroup_id',
      'vname' => 'LBL_TQT_PRODUCTGROUP_ID',
      'type' => 'id',
      'function' => 
      array (
        'name' => 'getProductGroupOptions',
        'returns' => 'html',
        'include' => 'modules/TQT_Products/ProductOptions.php',
      ),
      'audited' => true,
      'required' => false,
      'reportable' => false,
    ),
    'tqt_productgroup_name' => 
    array (
      'name' => 'tqt_productgroup_name',
      'rname' => 'name',
      'id_name' => 'tqt_productgroup_id',
      'vname' => 'LBL_TQT_PRODUCTGROUP_NAME',
      'table' => 'tqt_productgroup',
      'type' => 'relate',
      'link' => 'tqt_productgroup',
      'join_name' => 'tqt_productgroup',
      'isnull' => 'true',
      'module' => 'TQT_ProductGroup',
      'source' => 'non-db',
      'massupdate' => true,
    ),
    'tqt_productgroup' => 
    array (
      'name' => 'tqt_productgroup',
      'type' => 'link',
      'relationship' => 'tqt_productgroup_product_lots',
      'vname' => 'LBL_TQT_PRODUCTGROUP',
      'source' => 'non-db',
    ),
    'SecurityGroups' => 
    array (
      'name' => 'SecurityGroups',
      'type' => 'link',
      'relationship' => 'securitygroups_product_lots',
      'module' => 'SecurityGroups',
      'bean_name' => 'SecurityGroup',
      'source' => 'non-db',
      'vname' => 'LBL_SECURITYGROUPS',
    ),
    'tqt_product_stocks' => 
    array (
      'name' => 'tqt_product_stocks',
      'type' => 'link',
      'relationship' => 'product_lots_stocks_related',
      'source' => 'non-db',
      'vname' => 'LBL_TQT_PRODUCT_STOCKS',
    ),
  ),
  'indices' => 
  array (
    'id' => 
    array (
      'name' => 'product_lotspk',
      'type' => 'primary',
      'fields' => 
      array (
        0 => 'id',
      ),
    ),
    'idx_product_lots_branch_id' => 
    array (
      'name' => 'idx_product_lots_branch_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
      ),
    ),
    'idx_product_lots_department_id' => 
    array (
      'name' => 'idx_product_lots_department_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'department_id',
      ),
    ),
    'idx_product_lots_branch_dept' => 
    array (
      'name' => 'idx_product_lots_branch_dept',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'branch_id',
        1 => 'department_id',
      ),
    ),
    'idx_product_lots_assigned' => 
    array (
      'name' => 'idx_product_lots_assigned',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'assigned_user_id',
      ),
    ),
    0 => 
    array (
      'name' => 'idx_lot_id_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'id',
        1 => 'deleted',
      ),
    ),
    1 => 
    array (
      'name' => 'idx_lot_assigned_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'assigned_user_id',
      ),
    ),
    2 => 
    array (
      'name' => 'idx_lot_name_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'name',
        1 => 'deleted',
      ),
    ),
    3 => 
    array (
      'name' => 'idx_lot_perform_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'date_perform',
        1 => 'deleted',
      ),
    ),
    4 => 
    array (
      'name' => 'idx_lot_expired_del',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'date_expired',
        1 => 'deleted',
      ),
    ),
    5 => 
    array (
      'name' => 'idx_lot_del_group_id',
      'type' => 'index',
      'fields' => 
      array (
        0 => 'deleted',
        1 => 'tqt_productgroup_id',
      ),
    ),
  ),
  'relationships' => 
  array (
    'product_lots_modified_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Product_Lots',
      'rhs_table' => 'product_lots',
      'rhs_key' => 'modified_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'product_lots_created_by' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Product_Lots',
      'rhs_table' => 'product_lots',
      'rhs_key' => 'created_by',
      'relationship_type' => 'one-to-many',
    ),
    'product_lots_assigned_user' => 
    array (
      'lhs_module' => 'Users',
      'lhs_table' => 'users',
      'lhs_key' => 'id',
      'rhs_module' => 'Product_Lots',
      'rhs_table' => 'product_lots',
      'rhs_key' => 'assigned_user_id',
      'relationship_type' => 'one-to-many',
    ),
    'tqt_productgroup_product_lots' => 
    array (
      'lhs_module' => 'TQT_ProductGroup',
      'lhs_table' => 'tqt_productgroup',
      'lhs_key' => 'id',
      'rhs_module' => 'Product_Lots',
      'rhs_table' => 'product_lots',
      'rhs_key' => 'tqt_productgroup_id',
      'relationship_type' => 'one-to-many',
    ),
  ),
  'optimistic_lock' => true,
  'templates' => 
  array (
    'assignable' => 'assignable',
    'groups' => 'groups',
    'basic' => 'basic',
  ),
  'custom_fields' => false,
);
?>
